/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - SoundPoolUtil.java
 ** Description: Sound pool Utils
 ** Version: 1.0
 ** Date : 2020/12/16
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>        <version >    <desc>
 **  wangweihua      2020/04/13      1.0          create
 ****************************************************************/

package com.filemanager.common.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.SoundPool
import java.io.FileDescriptor
import java.util.*

class SoundPoolUtil private constructor() {
    companion object {
        val sInstance by lazy {
            SoundPoolUtil()
        }
    }

    init {
        initSoundPool()
    }

    private val mSoundMap: HashMap<Int, Int> = HashMap()
    private var mSoundPool: SoundPool? = null

    private fun initSoundPool() {
        val poolBuilder = SoundPool.Builder()
        val attr = AudioAttributes.Builder().setLegacyStreamType(1).build()
        poolBuilder.setMaxStreams(1)
        poolBuilder.setAudioAttributes(attr)
        mSoundPool = poolBuilder.build()
    }

    fun loadSoundFile(context: Context?, fileId: Int): Int {
        var soundId = 0
        if (mSoundMap.containsKey(fileId)) {
            soundId = mSoundMap[fileId]!!
        } else {
            soundId = mSoundPool!!.load(context, fileId, 0)
            mSoundMap[fileId] = soundId
        }
        return soundId
    }

    fun loadFile(fd: FileDescriptor?, offset: Long, length: Long, priority: Int): Int {
        return mSoundPool!!.load(fd, offset, length, priority)
    }

    fun loadFile(path: String?, priority: Int): Int {
        return mSoundPool!!.load(path, priority)
    }

    fun play(soundID: Int, leftVolume: Float, rightVolume: Float, priority: Int, loop: Int, rate: Float) {
        mSoundPool!!.play(soundID, leftVolume, rightVolume, priority, loop, rate)
    }

    fun setCompleteListener(listener: SoundPool.OnLoadCompleteListener) {
        mSoundPool!!.setOnLoadCompleteListener(listener)
    }
}