/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : LiteEventBus
 ** Description : 事件总线类
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.filemanager.common.databus

import android.text.TextUtils
import androidx.lifecycle.ExternalLiveData
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.utils.Log

class LiteEventBus private constructor() {

    private var eventObserversMap: HashMap<EventObserver, ExternalLiveData<Any>> = HashMap()
    private val lock = Any()

    companion object {

        const val TAG = "LiteEventBus"

        @JvmStatic
        val instance: LiteEventBus by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            LiteEventBus()
        }
    }

    /**
     * 关联（关联后，发送的事件才能收到）
     *
     * @param eventId 事件标识
     * @param observerId 注册标识
     * @return
     */
    fun with(eventId: String, observerId: String): ExternalLiveData<Any>? {
        return with(EventObserver(observerId, eventId))
    }

    /**
     * 关联（关联后，发送的事件才能收到）
     * 同一个事件监听只允许注册一次
     * @param registerId 事件标识
     * @return
     */
    fun with(eventObserver: EventObserver): ExternalLiveData<Any>? {
        Log.i(TAG, "with $eventObserver start")
        val result = runCatching {
            synchronized(lock) {
                if (eventObserversMap.containsKey(eventObserver)) {
                    eventObserversMap[eventObserver]
                } else {
                    val data = ExternalLiveData<Any>()
                    eventObserversMap[eventObserver] = data
                    data
                }
            }
        }.onFailure {
            Log.e(TAG, "with eventObserver $eventObserver", it)
        }.getOrNull()
        Log.i(TAG, "with $eventObserver result $result")
        return result
    }

    /**
     * 发送事件,广播
     *
     * @param eventId    事件标识
     * @param evcent 附带内容
     */
    fun send(eventId: String, evcent: Any? = null) {
        Log.d(TAG, "send event = $eventId, event $evcent")
        synchronized(lock) {
            eventObserversMap.forEach { (event, livedata) ->
                if (TextUtils.equals(event.eventId, eventId)) {
                    livedata.postValue(evcent)
                    Log.i(TAG, "send postValue $evcent")
                }
            }
        }
    }

    /**
     * 释放解注册Observer
     *
     * @param observerId  观察者id
     * @param owner  生命周期owner
     */
    fun releaseObserver(observerId: String, owner: LifecycleOwner) {
        synchronized(lock) {
            val filter = eventObserversMap.filter { (event) ->
                TextUtils.equals(event.observerId, observerId)
            }
            if (filter.isNotEmpty()) {
                filter.forEach { (t) ->
                    eventObserversMap[t]?.removeObservers(owner)
                    Log.w(TAG, "releaseObserver owner = $owner, t $t, eventObserversMap[t] ${eventObserversMap[t]}")
                    eventObserversMap.remove(t)
                }
            }
        }
    }

    /**
     * 释放解注册Observer
     *
     * @param eventId  事件id
     */
    fun releaseEvent(eventId: String) {
        val filter = eventObserversMap.filter { (event) ->
            TextUtils.equals(event.eventId, eventId)
        }
        if (filter.isNotEmpty()) {
            filter.forEach { (t) -> eventObserversMap.remove(t) }
        }
    }

    fun releaseAllObservers() {
        synchronized(lock) {
            eventObserversMap.clear()
        }
    }
}

data class EventObserver constructor(var observerId: String, var eventId: String) {
    override fun hashCode(): Int {
        return observerId.hashCode() + 31 * eventId.hashCode()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) {
            return true
        }
        if (other is EventObserver && TextUtils.equals(other.eventId, eventId)
            && TextUtils.equals(other.observerId, observerId)
        ) {
            return true
        }
        return false
    }
}