/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:CustomToast.kt
 * * Description:the object to achieve toast_send method
 * * Version:1.0
 * * Date :2020.1.9
 * * Author:liuzeming
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * liuzeming    20200109   1.0       the object to achieve toast_send method
 ****************************************************************/
package com.filemanager.common.utils

import android.widget.Toast
import com.filemanager.common.MyApplication

object CustomToast {
    var sToast: Toast? = null

    @JvmStatic
    fun showShort(res: Int) {
        val s = MyApplication.sAppContext.getString(res)
        showToast(s, true)
    }

    @JvmStatic
    fun showShort(s: CharSequence?) {
        showToast(s, true)
    }

    @JvmStatic
    fun showLong(res: Int) {
        showToast(MyApplication.sAppContext.getString(res), false)
    }

    @JvmStatic
    fun showLong(s: CharSequence?) {
        showToast(s, false)
    }

    private fun showToast(s: CharSequence?, isShort: Boolean) {
        if (s.isNullOrEmpty()) {
            return
        }
        val toastLength = if (isShort) Toast.LENGTH_SHORT else Toast.LENGTH_LONG
        sToast?.cancel()
        sToast = Toast.makeText(MyApplication.sAppContext, s, toastLength)
        sToast!!.show()
    }
}