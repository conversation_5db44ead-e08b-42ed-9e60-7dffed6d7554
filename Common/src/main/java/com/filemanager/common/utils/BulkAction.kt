/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - BulkAction.kt
 ** Description: Bulk Action
 ** Version: 1.0
 ** Date : 2020/03/11
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/03/11    1.0     create
 ****************************************************************/

package com.filemanager.common.batch

/**
 * Simple interface for batch action
 */
interface BatchAtion<T> {
    /**
     * @param pending paramters to add
     */
    fun add(pending: T)

    /**
     * flush pending operations
     */
    fun flush()
}

/**
 * Simple interface for batch result
 */
interface BatchActionResult<RESULT> {
    /**
     * get batch result
     */
    fun getBatchResult(): RESULT
}

/**
 * abstract class for single paramter, no need return result
 * @param mPrimaryBulkCount, bulk limit size for Primary Paramter
 */
abstract class SingleBatchAction<Params>(protected val mPrimaryBulkCount: Int)
    : BatchAtion<Params> {
    protected val mPending: ArrayList<Params>

    init {
        mPending = ArrayList()
    }

    override fun add(pending: Params) {
        pending?.let {
            mPending.add(pending)
            if (mPending.size >= mPrimaryBulkCount) {
                flush()
            }
        }
    }

    abstract override fun flush()
}

/**
 * abstract class for single paramter,and return result
 * @param mPrimaryBulkCount, bulk limit size for Primary Paramter
 */
abstract class SingleBatchForResult<Params, Result>(protected val mPrimaryBulkCount: Int)
    : BatchAtion<Params>, BatchActionResult<Result> {
    protected val mPending: ArrayList<Params>

    init {
        mPending = ArrayList()
    }

    override fun add(pending: Params) {
        pending?.let {
            mPending.add(pending)
            if (mPending.size >= mPrimaryBulkCount) {
                flush()
            }
        }
    }

    abstract override fun flush()

    /**
     * get Result of Batch
     */
    abstract override fun getBatchResult(): Result
}

/**
 * abstract class for batch, String as parametes to do batch operation
 */
abstract class StringBatch(bulkCount: Int)
    : SingleBatchAction<String>(bulkCount) {
    protected val mWhereClause: StringBuilder
    protected var mLen = 0

    init {
        mWhereClause = StringBuilder()
    }

    override fun add(pending: String) {
        if (!pending.isNullOrEmpty()) {
            if (mWhereClause.length != 0) {
                mWhereClause.append(",")
            }
            mWhereClause.append(pending)
            mLen++

            if (mLen >= mPrimaryBulkCount) {
                flush()
            }
        }
    }

    abstract override fun flush()
}

/**
 * abstract class for batch, String as parametes to do batch operation
 * return batch result
 */
abstract class StringBatchForResult<Result>(bulkCount: Int)
    : SingleBatchForResult<String, Result>(bulkCount) {
    protected val mWhereClause: StringBuilder
    protected var mLen = 0

    init {
        mWhereClause = StringBuilder()
    }

    override fun add(pending: String) {
        if (!pending.isNullOrEmpty()) {
            if (mWhereClause.length != 0) {
                mWhereClause.append(",")
            }
            mWhereClause.append(pending)
            mLen++

            if (mLen >= mPrimaryBulkCount) {
                flush()
            }
        }
    }

    abstract override fun flush()

    /**
     * get Result of Batch
     */
    abstract override fun getBatchResult(): Result
}

/**
 * abstract class for batch with two parameters, no result return
 */
abstract class DoubleBatchAction<Params1, Params2>(protected val mPrimaryBulkCount: Int,
                                                   protected val mSecondaryBulkCount: Int)
    : BatchAtion<Params1> {
    protected val mPending1: ArrayList<Params1>
    protected val mPending2: ArrayList<Params2>

    init {
        mPending1 = ArrayList()
        mPending2 = ArrayList()
    }

    open fun add(pending1: Params1?, pending2: Params2?) {
        pending1?.let {
            mPending1.add(it)
        }

        pending2?.let {
            mPending2.add(it)
        }

        if (mPending1.size >= mPrimaryBulkCount) {
            flush(true)
        }

        if (mPending2.size >= mSecondaryBulkCount) {
            flush(false)
        }
    }

    override fun add(pending1: Params1) {
        // do nothing
    }

    /**
     * flush all pending
     */
    override fun flush() {
        flushParams1()
        flushParams2()
    }

    /**
     * flush Specify pending queue
     * @param isParam1 if true,flush pending1,or flush pending2
     */
    fun flush(isParam1: Boolean) {
        if (isParam1) {
            flushParams1()
        } else {
            flushParams2()
        }
    }

    /**
     * flush Primary pending queue
     */
    abstract fun flushParams1()

    /**
     * flush Secondary pending operations
     */
    abstract fun flushParams2()
}