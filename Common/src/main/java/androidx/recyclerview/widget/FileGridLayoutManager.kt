/*********************************************************************
 * Copyright (C), 2010-2020, Oplus mobile comm crop. All rights reserved.
 ** File        :FileGridLayoutManager.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/06/20
 ** Author      : dalin
 **
 **  ---------------------Revision History: ----------------------------
 ** <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package androidx.recyclerview.widget

import android.content.Context
import android.view.View
import com.filemanager.common.utils.Log

class FileGridLayoutManager(context: Context?, spanCount: Int) :
    GridLayoutManager(context, spanCount) {
    companion object {
        const val TAG = "FileGridLayoutManager"
    }

    /**
     * 代码来源于GridLayoutManager.getSpanIndex，未做任何修改，仅为了配合重写findReferenceChild
     */
    private fun getSpanIndex(
        recycler: RecyclerView.Recycler?,
        state: RecyclerView.State?,
        pos: Int
    ): Int {
        if (state?.isPreLayout != true) {
            return mSpanSizeLookup.getCachedSpanIndex(pos, spanCount)
        }
        val cached = mPreLayoutSpanIndexCache[pos, -1]
        if (cached != -1) {
            return cached
        }
        val adapterPosition = recycler?.convertPreLayoutPositionToPostLayout(pos) ?: -1
        if (adapterPosition == -1) {
            Log.w(
                TAG,
                ("Cannot find span size for pre layout position. It is not cached, not in the adapter. Pos:$pos")
            )
            return 0
        }
        return mSpanSizeLookup.getCachedSpanIndex(adapterPosition, mSpanCount)
    }

    /**
     * 代码主要来源于GridLayoutManager.findReferenceChild，仅修改为mOrientationHelper.getDecoratedEnd(view) <= boundsStart，其他未做修改
     * bugId：7411652。
     * 在overScroll时，或后续点击，会触发dispatchLayoutStep
     * 1. recyclerview在dispatchLayoutStep1时，根据当前的child，计算出了可见区域visibleRange1(例如4-9)
     * 2. recyclerview在dispatchLayoutStep2时，调用onLayoutChildren，让child去计算布局
     * 3. recyclerview的LinearLayoutManager中，先通过updateAnchorInfoForLayout尝试更新，内部调用updateAnchorFromChildren，
     * 先通过findReferenceChild找到第一个可见的view(GridLayoutManager)。
     * 4. findReferenceChild计算中，GridLayoutManager实现在计算边界时，
     *    if (mOrientationHelper.getDecoratedStart(view) >= boundsEnd || mOrientationHelper.getDecoratedEnd(view) < boundsStart)，
     *    此时使用getDecoratedEnd(view) < boundsStart进行判断，完全小于才视为不可见，‘ = ’被视为可见，而在一定条件下，第一个view的getDecoratedEnd和boundsStart的值相等。所以就返回了这个view。
     *    实际上这个view是贴着可见区域的那个像素行的。在这次被为了可见。
     * 5. updateAnchorFromChildren从4拿到view后，判断mOrientationHelper.getDecoratedEnd(view) <= boundsStart，认为超出边界。重新计算了锚点mCoordinate.mCoordinate。
     *    ！！！注意，这里就有了冲突，在4中这个view被视为了可见，在5中又被视为了不可见，逻辑不同，在getDecoratedEnd(view) == boundsStart时导致出现bug！！！代码上来说，4是<，5是<=，代码逻辑不统一！
     * 6. 在onLayoutChildren的后续计算中，先执行updateLayoutStateToFillEnd，这个是正常的。然后由于mCoordinate.mCoordinate的值，
     *    触发了updateLayoutStateToFillStart内部的逻辑，给recyclerview顶部又增加了一个view
     * 7. recyclerview在dispatchLayoutStep3时，计算child的可见区域，visibleRange2(例如3-9)。由于和visibleRange1不相等，所以执行了dispatchOnScrolled(0, 0)。 列表滑动到了顶部。
     */
    override fun findReferenceChild(
        recycler: RecyclerView.Recycler?,
        state: RecyclerView.State?,
        layoutFromEnd: Boolean,
        traverseChildrenInReverseOrder: Boolean
    ): View? {
        var start = 0
        var end = childCount
        var diff = 1
        if (traverseChildrenInReverseOrder) {
            start = childCount - 1
            end = -1
            diff = -1
        }
        val itemCount = state?.itemCount ?: 0
        ensureLayoutState()
        var invalidMatch: View? = null
        var outOfBoundsMatch: View? = null
        val boundsStart = mOrientationHelper.startAfterPadding
        val boundsEnd = mOrientationHelper.endAfterPadding
        var i = start
        while (i != end) {
            val view = getChildAt(i)
            val position = getPosition(view!!)
            if (position in 0 until itemCount) {
                val span = getSpanIndex(recycler, state, position)
                if (span != 0) {
                    i += diff
                    continue
                }
                if ((view.layoutParams as RecyclerView.LayoutParams).isItemRemoved) {
                    if (invalidMatch == null) {
                        invalidMatch = view // removed item, least preferred
                    }
                } else if (mOrientationHelper.getDecoratedStart(view) >= boundsEnd
                    || mOrientationHelper.getDecoratedEnd(view) <= boundsStart // 这里必须是<=，和LinearLayoutManager的updateAnchorFromChildren逻辑保持一致
                ) {
                    if (outOfBoundsMatch == null) {
                        outOfBoundsMatch = view // item is not visible, less preferred
                    }
                } else {
                    return view
                }
            }
            i += diff
        }
        return outOfBoundsMatch ?: invalidMatch
    }
}