/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: BaseRecentItemDecoration
 ** Description: ItemDecoration for COUIRecyclerView.COUIRecyclerViewItemDecoration
 ** Version: 1.0
 ** Date : 2024/11/15
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.base

import android.content.Context
import androidx.recyclerview.widget.COUIRecyclerView

abstract class BaseRecentItemDecoration(val context: Context) :
    COUIRecyclerView.COUIRecyclerViewItemDecoration(context) {
    var spanCount = 0
        set(value) {
            field = value
            updateSpanCount()
        }

    @Suppress("Empty")
    open fun updateSpanCount() {
    }
}