/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/25, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.DragEvent
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.R
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.CommonConstants.DEFAULT_DOWNLOAD_PATH
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.Companion.parseFiles
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

object DropPrivacySafeAction : DropListener {

    private const val TAG = "DropPrivacySafeAction"
    @SuppressLint("NewApi")
    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem categoryType $targetCategoryType $dropSideItem")

        //分布式文管文件，不支持加密
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dfmFragmentTag == dragTag) {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }
        if (DragUtils.hasAndroidDataFile) {
            CustomToast.showShort(R.string.drag_cloud_not_support_position)
            Log.w(TAG, "handleDropAction hasAndroidDataFile")
            return false
        }
        val parseData = DropUtil.parseDragFile(dragEvent)
        val uriList = parseData.uriList
        val delayReloadData = dropSideItem && DropUtil.checkIsMediaFragmentTag(dragTag)
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            dragEvent.clipData?.apply {
                val onMacDownloadCallBack: (Boolean, ArrayList<RemoteFileBean>) -> Unit = { result, files ->
                        if (result) {
                            Log.d(TAG, "onDragMacResult true")
                            val list = ArrayList<String>()
                            files.forEach { bean ->
                                list.add(DEFAULT_DOWNLOAD_PATH + File.separator + bean.mDisplayName)
                            }
                            parseData.noneMediaPathList.clear()
                            parseData.mediaPathList.addAll(list)
                            encryptFiles(activity, parseData, delayReloadData = false, dragInternal = true)
                        } else {
                            //处理下载失败
                            Log.d(TAG, "onDragMacResult false")
                        }
                    }
                parseFiles(
                    activity = activity,
                    onMacDownloadCallBack = onMacDownloadCallBack,
                    description = description
                )
            }
            return true
        }
        //不支持文件夹拖拽
        if (parseData.hasFolders() && dropSideItem) {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }

        //内部文件，支持加密
        if (dragTag != null && uriList.isNotEmpty()) {
            encryptFiles(activity, parseData, delayReloadData, true)
            return true
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty()) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
                Log.d(TAG, "drag out private files, return!")
                return false
            }
            //不支持文件夹拖拽
            if (data.folderList.isNotEmpty()) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
                return false
            }
            //公有媒体库文件
            if (data.mediaPathList.isNotEmpty()) {
                encryptFiles(activity, data, delayReloadData = false, dragInternal = false)
                return true
            }
            return false
        }

        val privacyFileCallback: () -> Boolean = {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            false
        }
        //外部的媒体库文件，支持加密
        val mediaFileCallback = {
            encryptFiles(activity, parseData, delayReloadData = false, dragInternal = false)
            true
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem categoryType $targetCategoryType $dropSideItem")

        //分布式文管文件，不支持加密
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dfmFragmentTag == dragTag) {
            return DropDispatchAction.PROHIBIT_STATE
        }
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            return DropDispatchAction.COPY_STATE
        }
        if (DragUtils.hasAndroidDataFile) {
            Log.w(TAG, "getCornerMarkStateCode hasAndroidDataFile")
            return DropDispatchAction.PROHIBIT_STATE
        }
        val parseData = DropUtil.parseDragFile(dragEvent)
        //不支持文件夹拖拽
        if (parseData.hasFolders() && dropSideItem) {
            return DropDispatchAction.PROHIBIT_STATE
        }

        //内部文件，支持加密
        if (dragTag != null) {
            return DropDispatchAction.NORMAL_STATE
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty()) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //不支持文件夹拖拽
            if (data.folderList.isNotEmpty()) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //公有媒体库文件
            if (data.mediaPathList.isNotEmpty()) {
                return DropDispatchAction.NORMAL_STATE
            }
            return DropDispatchAction.PROHIBIT_STATE
        }

        return DropDispatchAction.NORMAL_STATE
    }

    private fun encryptFiles(activity: Activity, parseData: DragParseData, delayReloadData: Boolean, dragInternal: Boolean) {
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val fileOperateApi = Injector.injectFactory<IFileOperateApi>()
            val fileBeanList = fileOperateApi?.getFileBeanList(activity, parseData, isMediaFiles = true) ?: arrayListOf()
            withContext(Dispatchers.Main) {
                val viewModel = DropViewModel(fileBeanList)
                viewModel.loadData()
                val lifecycle = activity.lifecycle
                val categoryType = CategoryHelper.CATEGORY_RECENT
                val fileOperate = Injector.injectFactory<IFileOperateApi>()?.createNormalFileOperate(lifecycle, categoryType, viewModel)
                setResultListener(activity, fileOperate, delayReloadData, dragInternal)
                fileOperate?.onEncrypt(activity)
            }
        }
    }

    private fun setResultListener(activity: BaseVMActivity, fileOperate: IFileOperate?, delayReloadData: Boolean, dragInternal: Boolean) {
        fileOperate?.setResultListener(object : IFileOperate.OperateResultListener {
            override fun onActionDone(opType: Int, result: Boolean, data: Any?) {
                super.onActionDone(opType, result, data)
                if (delayReloadData) {
                    activity.lifecycleScope.launch(Dispatchers.Main) {
                        delay(KtConstants.DELAY_LOAD_DATA_TIME)
                        activity.onRefreshData()
                    }
                }
                //外部拖入的文件，加密后，需要通知拖入方刷新
                if (!dragInternal) {
                    val intent = Intent(KtConstants.ACTION_DRAG_FILE_CHANGED)
                    intent.putExtra(KtConstants.DRAG_APP_PACKAGE, activity.packageName)
                    activity.sendBroadcast(intent, KtConstants.PROTECT_PERMISSION)
                }
            }
        })
    }
}