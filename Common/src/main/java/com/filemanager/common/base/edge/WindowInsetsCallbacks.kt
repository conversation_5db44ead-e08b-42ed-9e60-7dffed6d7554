/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - EdgeToEdgeManager
 ** Description:
 ** Version: 1.0
 ** Date : 2024/6/2
 ** Author: W9059186
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  W9059186    2024/6/2     1.0     create file
 ****************************************************************/
package com.filemanager.common.base.edge

import android.view.View
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.updatePadding
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatusBarUtil
import java.util.Objects

abstract class DeDuplicateInsetsCallback : OnApplyWindowInsetsListener {
    private var lastWindowInsets: WindowInsetsCompat? = null
    override fun onApplyWindowInsets(v: View, insets: WindowInsetsCompat): WindowInsetsCompat {
        if (!Objects.equals(lastWindowInsets, insets)) {
            lastWindowInsets = insets
            onApplyInsets(v, insets)
        }
        // Return the insets so that they keep going down the view hierarchy
        return insets
    }

    /**
     * onApplyInsets
     */
    abstract fun onApplyInsets(v: View, insets: WindowInsetsCompat)
}

open class RootViewPersistentInsetsCallback(private val updatePaddingBottom: UpdatePaddingBottom) :
    DeDuplicateInsetsCallback() {

    override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
        val systemBarInsets = insets.getInsets(WindowInsetsCompat.Type.systemBars())
        val paddingBottom = if (StatusBarUtil.checkIsGestureNavMode()) {
            0
        } else {
            systemBarInsets.bottom
        }
        v.updatePadding(
            left = systemBarInsets.left,
            right = systemBarInsets.right,
            bottom = paddingBottom
        )

        val navPaddingBottom = if (StatusBarUtil.checkIsGestureNavMode()) {
            systemBarInsets.bottom
        } else {
            0
        }
        Log.d(TAG, "onApplyInsets paddingBottom $paddingBottom, navPaddingBottom $navPaddingBottom")
        updatePaddingBottom.update(paddingBottom, navPaddingBottom)
        WindowInsetsCompat.CONSUMED
    }

    companion object {
        private const val TAG: String = "RootViewPersistentInsetsCallback"
    }
}

interface UpdatePaddingBottom {
    fun update(paddingBottom: Int, navPaddingBottom: Int)
}
