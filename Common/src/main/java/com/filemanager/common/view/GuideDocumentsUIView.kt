/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/3/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.storage.StorageManager
import android.os.storage.StorageVolume
import android.provider.DocumentsContract
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.Button
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.KtConstants.PKG_NAME_DOCUMENTS_UI_MAINLINE
import com.filemanager.common.constants.KtConstants.PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.AppUtils.getBuildConfigValue
import com.filemanager.common.utils.FileEmptyUtils
import com.filemanager.common.utils.FileEmptyUtils.FILE_EMPTY_ANIMATION_JSON
import com.filemanager.common.utils.FileEmptyUtils.FILE_EMPTY_ANIMATION_JSON_NIGHT
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.destroy
import com.oplus.anim.EffectiveAnimationView
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import java.io.File
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class GuideDocumentsUIView : ConstraintLayout {
    companion object {
        const val TAG = "GuideDocumentsUIView"
        const val FLAVOR_REGION = "FLAVOR_region"
        const val DOMESTIC = "domestic"
    }

    private var mGuideLayout: View? = null
    private var mGuideIcon: EffectiveAnimationView? = null
    private var mGuideTitle: TextView? = null
    private var mGuideDes: TextView? = null
    private var mGuideBtn: TextView? = null
    private var documentsUiTipsTv: TextView? = null
    private var mCurrentPath: String? = null
    var openAndroidDataViewListener: OnClickListener? = null

    //在分屏模式下，需要隐藏ICON，在分屏恢复到全屏时，需要此判断，恢复ICON
    private var mVisibility: Int = View.GONE

    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attr: AttributeSet) : super(context, attr) {
        initView(context)
    }

    constructor(context: Context, attr: AttributeSet, defStyleAttr: Int) : super(context, attr, defStyleAttr) {
        initView(context)
    }

    fun initView(context: Context) {
        mGuideLayout = LayoutInflater.from(context).inflate(R.layout.guide_document_ui_layout, this)
        mGuideLayout?.apply {
            mGuideIcon = findViewById(R.id.guide_icon)
            mGuideTitle = findViewById(R.id.guide_title)
            mGuideDes = findViewById(R.id.guide_des)
            mGuideBtn = findViewById(R.id.guide_btn)
            documentsUiTipsTv = findViewById(R.id.go_documents_ui_tips_tv)
            COUITextViewCompatUtil.setPressRippleDrawable(mGuideBtn)
        }
    }

    fun showPermissionView(showPermissionView: Boolean) {
        if (showPermissionView) {
            documentsUiTipsTv?.text =
                AndroidDataHelper.buildDeepSearchTips(context, R.string.open_android_data_documents_tips_1, R.string.go_now) {
                    jumpToDocumentsUI()
                }
            documentsUiTipsTv?.movementMethod = LinkMovementMethod.getInstance()
            documentsUiTipsTv?.visibility = View.VISIBLE
            mGuideDes?.text = appContext.resources.getString(R.string.open_android_data_des)
            mGuideBtn?.text = appContext.resources.getString(R.string.open_android_data)
            mGuideBtn?.setOnClickListener {
                openAndroidDataViewListener?.onClick(it)
            }
        } else {
            documentsUiTipsTv?.visibility = View.GONE
            mGuideDes?.text = appContext.resources.getString(R.string.not_accessible_info)
            mGuideBtn?.text = appContext.resources.getString(R.string.btn_view)
            mGuideBtn?.setOnClickListener {
                jumpToDocumentsUI()
            }
        }
    }

    private fun jumpToDocumentsUI() {
        (context as? BaseVMActivity)?.launch(Dispatchers.Main) {
            mCurrentPath?.let { currentPath ->
                val storageManager = appContext.getSystemService(Context.STORAGE_SERVICE) as? StorageManager
                val storageVolume: StorageVolume? = withContext(Dispatchers.IO) {
                    storageManager?.getStorageVolume(File(currentPath))
                }
                if (storageVolume != null) {
                    val rootId: String? = if (storageVolume.isEmulated) {
                        "primary"
                    } else {
                        storageVolume.uuid
                    }
                    val rootUri = DocumentsContract.buildRootUri("com.android.externalstorage.documents", rootId)
                    val intent = Intent(Intent.ACTION_VIEW)
                    val isMainlineProject = isMainlineProject()
                    /*
                    only domestic from android T will exit mainline project, documentsUI package name will be com.android.documentsui
                     other rom version still in mainline project, documentsUI package name will be com.google.android.documentsui
                     */
                    if (isMainlineProject) {
                        intent.setPackage(PKG_NAME_DOCUMENTS_UI_MAINLINE)
                        CollectPrivacyUtils.collectInstalledAppList(PKG_NAME_DOCUMENTS_UI_MAINLINE)
                    } else {
                        intent.setPackage(PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE)
                        CollectPrivacyUtils.collectInstalledAppList(PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE)
                    }
                    intent.addCategory(Intent.CATEGORY_DEFAULT)
                    intent.setDataAndType(rootUri, DocumentsContract.Root.MIME_TYPE_ITEM)
                    intent.putExtra("android.provider.extra.SHOW_ADVANCED", true)
                    try {
                        context.startActivity(intent)
                    } catch (e: ActivityNotFoundException) {
                        Log.e(TAG, e.message)
                        if (isMainlineProject.not()) {
                            intent.setPackage(PKG_NAME_DOCUMENTS_UI_MAINLINE)
                        } else {
                            intent.setPackage(PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE)
                        }
                        try {
                            context.startActivity(intent)
                        } catch (e: ActivityNotFoundException) {
                            Log.e(TAG, e.message)
                        }
                    }
                }
            }
        }
    }

    /**
     * only domestic from android T will exit mainline project
     * other rom version still in mainline project
     * **/
    @VisibleForTesting
    fun isMainlineProject(): Boolean {
        val isAtLeastAndroidT = SdkUtils.isAtLeastT()
        val region = getBuildConfigValue(FLAVOR_REGION)
        return !(isAtLeastAndroidT && DOMESTIC == region)
    }

    fun setCurrentPath(currentPath: String) {
        Log.d(TAG, "setCurrentPath = $currentPath")
        mCurrentPath = currentPath
    }

    fun getGuideDocumentsUIVisibility() = mVisibility

    fun setGuideVisible(visible: Int, emptyMarginTop: Int = -1) {
        mVisibility = visible
        mGuideLayout?.visibility = visible
        if (emptyMarginTop != -1) {
            mGuideLayout?.let { emptyLayout ->
                val layoutParams = emptyLayout.layoutParams
                if (layoutParams is CoordinatorLayout.LayoutParams) {
                    layoutParams.topMargin = emptyMarginTop
                } else if (layoutParams is RelativeLayout.LayoutParams) {
                    layoutParams.topMargin = emptyMarginTop
                }
            }
        }
        if (Utils.isNightMode(context)) {
            mGuideIcon?.setAnimation(FILE_EMPTY_ANIMATION_JSON_NIGHT)
        } else {
            mGuideIcon?.setAnimation(FILE_EMPTY_ANIMATION_JSON)
        }
        mGuideIcon?.apply {
            if (View.VISIBLE == visible) {
                playAnimation()
            } else {
                if (isAnimating) {
                    cancelAnimation()
                }
                resumeAnimation()
            }
        }
    }

    override fun onDetachedFromWindow() {
        mGuideIcon?.destroy()
        super.onDetachedFromWindow()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val height = MeasureSpec.getSize(heightMeasureSpec)
        val width = MeasureSpec.getSize(widthMeasureSpec)
        val contentHeight = getComponentHeight(mGuideDes) + getComponentHeight(mGuideBtn) + getComponentHeight(mGuideIcon) + getComponentHeight(
            mGuideTitle) + resources.getDimension(R.dimen.dimen_8dp).toInt()
        mGuideIcon?.visibility = FileEmptyUtils.getEmptyIconVisible(context, height, contentHeight)
        FileEmptyUtils.updateIconScale(mGuideIcon, width, height)
    }

    private fun getComponentHeight(view: View?): Int {
        return view?.measuredHeight ?: 0
    }
}