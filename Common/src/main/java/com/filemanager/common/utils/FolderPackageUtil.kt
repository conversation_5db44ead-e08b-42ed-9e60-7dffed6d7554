/*********************************************************************
 * * Copyright (C), 2010-2026, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.filemanager.common.utils.FolderPackageUtil
 * * Version     : 1.0
 * * Date        : 2023/10/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.VolumeEnvironment
import java.io.File
import java.util.HashMap

object FolderPackageUtil {
    const val TAG = "FolderPackageUtil"
    val COMMON_DIR_NAMES = arrayOf(
        "DCIM",
        "Documents",
        "Download",
        "Movies",
        "Music",
        "Pictures",
        "Recordings",
        "Android"
    )
    private val externalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext) ?: ""
    private val internalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
    val specialFolder = "Android" + File.separator + "media"
    val specialFolder2 = "DCIM" + File.separator + "Camera"
    val LOGO_APPS = arrayOf(
        "com.heytap.cloud",
        "com.android.email",
        "com.heytap.music",
        "com.heytap.yoli",
        "com.xunmeng.pinduoduo",
        "com.ss.android.ugc.aweme",
        "com.eg.android.AlipayGphone",
        "com.kuaishou.nebula",
        "com.heytap.reader",
        "com.heytap.book",
        "com.qiyi.video",
        "com.youku.phone",
        "com.ss.android.article.video",
        "com.alibaba.android.rimet",
        "com.ss.android.ugc.live",
        "com.xingin.xhs",
        "com.dragon.read",
        "com.ss.android.article.lite",
        "com.lemon.lv",
        "com.tencent.tmgp.sgame",
        "tv.danmaku.bili",
        "com.tencent.tmgp.pubgmhd",
        "com.tencent.karaoke",
        "com.ximalaya.ting.android",
        "com.tencent.wemeet.app",
        "com.ss.android.lark",
        "com.netease.cloudmusic",
        "com.baidu.haokan",
        "com.quark.browser",
        "com.kmxs.reader",
        "com.xs.fm",
        "com.tencent.androidqqmail",
        "com.kwai.videoeditor",
        "com.qiyi.video.lite",
        "com.tencent.wework",
        "com.oplus.logkit",
        "com.oplus.camera",
        "com.dewmobile.kuaiya",
        "com.tencent.weishi",
        "com.achievo.vipshop",
        "com.UCMobile",
        "com.baidu.tieba",
        "com.tencent.qqlive",
        "com.qzone",
        "com.tencent.mobileqqi",
        "com.tencent.mobileqq",
        "com.teamtalk.im",
        "com.sina.weibo",
        "com.sina.news",
        "com.qq.reader",
        "com.tencent.qqmusic",
        "com.tencent.mtt",
        "com.tencent.news",
        "com.taobao.taobao",
        "com.oplus.screenshot",
        "com.sankuai.meituan",
        "com.douban.frodo",
        "com.zhihu.android",
        "com.ss.android.article.news",
        "com.mt.mtxx.mtxx",
        "cn.kuwo.player",
        "com.kugou.android",
        "com.immomo.momo",
        "com.smile.gifmaker",
        "com.tencent.mm",
        "com.android.mms",
        "com.dianping.v1",
        "com.tmall.wireless",
        "com.jingdong.app.mall",
        "com.coloros.note",
        "com.heytap.market",
        "com.nearme.gamecenter",
        "com.heytap.browser",
        "com.android.bluetooth",
        "com.baidu.netdisk",
        "com.baidu.browser.apps",
        "cmccwm.mobilemusic",
        "com.baidu.searchbox"
    )
    const val  FIELD_OWNER_PACKAGE_NAME = "owner_package_name"
    const val EXTERNAL_VOLUME = "external"

    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun getAllPathAndName(): HashMap<String, String> {
        Log.d(TAG, "getAllPathAndName start")
        val selection = getSelection()
        val uri: Uri = MediaStore.Files.getContentUri(EXTERNAL_VOLUME)
        val cr = MyApplication.sAppContext.contentResolver
        val projectStrings = arrayOf(FIELD_OWNER_PACKAGE_NAME, MediaStore.Files.FileColumns.DATA)
        val pathPackages = HashMap<String, String>()
        var cursor: Cursor? = null
        try {
            cursor = cr.query(uri, projectStrings, selection, null, null)
            Log.d(TAG, "getAllPathAndName cursor count = ${cursor?.count}")
            if (cursor != null && cursor.count > 0) {
                while (cursor.moveToNext()) {
                    val originPackage = cursor.getString(0)
                    val data = cursor.getString(1)
                    val rootDir = getRootDir(data)
                    var folderName = data.replace(rootDir, "")
                    if (folderName.startsWith(File.separator)) {
                        folderName = folderName.substring(1)
                    }
                    val validFolder = getValidFolder(folderName)
                    if (validFolder.equals(specialFolder2, true)) {
                        pathPackages[data] = originPackage
                    } else {
                        val fullPath = rootDir + File.separator + validFolder
                        pathPackages[fullPath] = originPackage
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        } finally {
            cursor?.close()
        }
        Log.d(TAG, "getAllPathAndName end")
        return pathPackages
    }

    @JvmStatic
    @VisibleForTesting
    fun getSelection(): String {
        val sb = StringBuilder()
        sb.append("owner_package_name in ( ")
        for ((index, name) in LOGO_APPS.withIndex()) {
            sb.append("'")
            sb.append(name)
            sb.append("'")
            if (index != (LOGO_APPS.size - 1)) {
                sb.append(",")
            }
        }
        sb.append(" )")
        return sb.toString()
    }

    @JvmStatic
    fun getRootDir(path: String): String {
        var rootDir = internalPath
        if (path.startsWith(internalPath, true)) {
            rootDir = internalPath
        } else if (path.startsWith(externalPath, true)) {
            rootDir = externalPath
        } else {
            val otgs = VolumeEnvironment.getOTGPath(MyApplication.sAppContext)
            for (otg in otgs) {
                if (path.startsWith(otg, true)) {
                    rootDir = otg
                    break
                }
            }
        }
        return rootDir
    }

    @JvmStatic
    @VisibleForTesting
    fun getValidFolder(folderName: String): String {
        val names = folderName.split(File.separator)
        if (folderName.startsWith(specialFolder, true) && names.size > 2) {
            return names[0] + File.separator + names[1] + File.separator + names[2]
        }
        return if (names.isEmpty()) {
            folderName
        } else if (isCommonDirName(names[0]) && names.size > 1) {
            names[0] + File.separator + names[1]
        } else {
            names[0]
        }
    }

    @JvmStatic
    @VisibleForTesting
    fun isCommonDirName(name: String): Boolean {
        for (dir in COMMON_DIR_NAMES) {
            if (dir.equals(name, true)) {
                return true
            }
        }
        return false
    }
}