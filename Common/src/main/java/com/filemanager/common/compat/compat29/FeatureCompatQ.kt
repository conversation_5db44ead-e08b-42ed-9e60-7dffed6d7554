/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File: FeatureCompat.kt
 * * Description:Query and save the value of common features and query whether the feature exists
 * * Version:1.0
 * * Date :2020/8/17
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/17,        v1.0,           Create
 ****************************************************************/
package com.filemanager.common.compat.compat29

import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat.FEATURE_FLIP_DEVICE
import com.filemanager.common.compat.compat30.FeatureCompatR.ACTION_PHONE_MANAGER_CLEAN
import com.filemanager.common.compat.compat30.FeatureCompatR.PKG_NAME_PHONE_MANAGER
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils

object FeatureCompatQ : FeatureCompat.IOPlusFeatureInterface {
    private const val TAG = "OPlusFeatureCompatQ"

    //feature key
    private const val FEATURE_PHONE_MANAGER_DISABLE_CLEAN = "oppo.phonemanager.disable.clean"
    private const val FEATURE_EXP_ROM = "oppo.version.exp"
    private const val FEATURE_LIGHT_VERSION = "oppo.sys.light.func"
    private const val FEATURE_SDCARD_NOT_SUPPORT = "oppo.filemanager.sdcard.not.support"
    private const val FEATURE_DRM_SUPPORT = "oppo.drm.support"
    private const val FEATURE_NOT_SUPPORT_ENCRYPTION = "oppo.filemanager.encryption.not.support"
    private const val FEATURE_SYSTEM_MUSIC_PLAY_SUPPORT = "oppo.filemanager.music.play.support"
    private const val FEATURE_RUNTIME_PERMISSION_ALERT_SUPPORT = "oppo.runtime.permission.alert.support"
    private const val FEATURE_NOT_SUPPORT_UNKNOWNFILE = "oppo.filemanager.unknownfile.not.support"
    private const val FEATURE_SUPPORT_OTG = "android.hardware.usb.host"
    private const val FEATURE_UNIT_IN_NORMAL = "oppo.memory.unit.in.poweroften"
    private const val FEATURE_ENCRYPT_SUPPORT_ONLY = "oppo.package.encrypt.support.only"
    private const val FEATURE_MULTIAPP_SUPPORT = "oppo.multiapp.support"
    private const val FEATURE_TASKBAR_ENABLE = "com.android.launcher.TASKBAR_ENABLE"

    override fun isExpRom() = (MyApplication.flavorRegion != "domestic")

    override fun isLightVersion() = hasFeature(FEATURE_LIGHT_VERSION)

    override fun isNotSupportSD(): Boolean {
        val feature = hasFeature(FEATURE_SDCARD_NOT_SUPPORT)
        var sysProp = PropertyCompat.sIsSupportSDCard
        return (feature && sysProp).also {
            Log.d(TAG, "isNotSupportSD: $feature && $sysProp")
        }
    }

    override fun isSupportDrm() = hasFeature(FEATURE_DRM_SUPPORT)

    override fun isSupportEncryption(): Boolean {
        val result = !hasFeature(FEATURE_NOT_SUPPORT_ENCRYPTION)
        return result && if (Utils.getUserId() == 0) {
            !Utils.isContainerUser(MyApplication.sAppContext)
        } else false
    }

    override fun getPhoneManagerStartInfo(): Pair<String, String>? {
        return if (hasFeature(FEATURE_PHONE_MANAGER_DISABLE_CLEAN)) {
            null
        } else {
            Pair(PKG_NAME_PHONE_MANAGER, ACTION_PHONE_MANAGER_CLEAN)
        }
    }

    override fun isStorageUnitNormal() = hasFeature(FEATURE_UNIT_IN_NORMAL)

    override fun isSupportRuntimePermissionAlert() = hasFeature(FEATURE_RUNTIME_PERMISSION_ALERT_SUPPORT)

    override fun isStorageHidden(): Boolean = false

    override fun isOnlyUseBuildInMusicPlay() = hasFeature(FEATURE_SYSTEM_MUSIC_PLAY_SUPPORT)

    override fun isSupportOTG() = hasFeature(FEATURE_SUPPORT_OTG)

    override fun isNotSupportUnknownFile() = hasFeature(FEATURE_NOT_SUPPORT_UNKNOWNFILE)

    override fun isSupportEncryptOTAOnly() = hasFeature(FEATURE_ENCRYPT_SUPPORT_ONLY)

    override fun isSupportMultiApp() = hasFeature(FEATURE_MULTIAPP_SUPPORT)

    override fun isSupportTaskbar(): Boolean {
        return hasFeature(FEATURE_TASKBAR_ENABLE)
    }

    override fun isFlipDevice(): Boolean {
        return hasFeature(FEATURE_FLIP_DEVICE)
    }

    override fun isSupportPCConnect(): Boolean {
        return false
    }

    override fun isHighLevelLightOS(): Boolean {
        return false
    }

    private fun hasFeature(key: String): Boolean {
        val result = try {
            MyApplication.sAppContext.packageManager.hasSystemFeature(key)
        } catch (e: Throwable) {
            Log.w(TAG, "hasFeature failed: $key, ${e.message}")
            false
        }
        Log.d(TAG, "hasFeature $key $result")
        return result
    }

    override fun isSupportLinearmotorVibrator() = false

    override fun isSupportLuxunVibrator() = false
}