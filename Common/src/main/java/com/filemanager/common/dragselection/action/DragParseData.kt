/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/30, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.net.Uri

class DragParseData {
    val uriList = ArrayList<Uri>()
    val mediaPathList = ArrayList<String>()
    val folderList = ArrayList<String>()
    val textList = ArrayList<String>()
    val htmlList = ArrayList<String>()
    val noneMediaPathList = ArrayList<String>()
    var isMacDragData = false
    /**
     * 是否是外部拖拽的文本
     */
    fun isExternalTextFiles(): Boolean {
        return uriList.isEmpty() && folderList.isEmpty() && mediaPathList.isEmpty()
                && noneMediaPathList.isEmpty() && (textList.isNotEmpty() || htmlList.isNotEmpty())
    }

    fun hasFolders(): Boolean {
        return folderList.isNotEmpty()
    }

    fun isEmpty(): Boolean {
        return uriList.isEmpty() && folderList.isEmpty() && mediaPathList.isEmpty() && noneMediaPathList.isEmpty()
                && textList.isEmpty() && htmlList.isEmpty()
    }
}
