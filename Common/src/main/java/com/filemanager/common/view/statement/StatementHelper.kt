/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StatementHelper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2024/2/20 16:03
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2024/2/20       1.0      create
 ***********************************************************************/
package com.filemanager.common.view.statement

import android.content.Context
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.view.View
import com.coui.appcompat.statement.COUIStatementClickableSpan

object StatementHelper {

    @JvmStatic
    fun createSpannableString(
        context: Context,
        fullText: String,
        spanTextList: List<String>?,
        spanListenerList: List<OnSpanClickListener>?
    ): SpannableStringBuilder = SpannableStringBuilder(fullText).apply {
        spanTextList?.forEachIndexed { index, spanText ->
            val startIndex = fullText.indexOf(spanText)
            val endIndex = startIndex + spanText.length
            setSpan(object : COUIStatementClickableSpan(context) {
                override fun onClick(widget: View) {
                    super.onClick(widget)
                    spanListenerList?.get(index)?.onSpanClick()
                }
            }, startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
    }

    interface OnSpanClickListener {
        fun onSpanClick()
    }
}