/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - SdkUtils.java
 ** Description: Sdk Utils
 ** Version: 1.0
 ** Date : 2020/04/13
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/04/13    1.0     create
 ****************************************************************/
package com.filemanager.common.utils;

import android.os.Build;

import com.filemanager.common.compat.OplusBuildCompat;
import com.filemanager.common.compat.PropertyCompat;

public class SdkUtils {

    /**
     * update Version_R to 30 on android R release version
     */
    private static final int SDK_VERSION_R = 30;

    /**
     * update Version_S to 31 on android R release version
     */
    private static final int SDK_VERSION_S = 31;

    /**
     * update Version_S to 31 on android R release version
     */
    private static final int SDK_VERSION_T = 33;

    private static final int SDK_VERSION_U = 34;

    /**
     * @return true if current sdk version at least on android R
     */
    public static boolean isAtLeastR() {
        return Build.VERSION.SDK_INT >= SDK_VERSION_R;
    }

    /**
     * @return true if current sdk version at least on android S
     */
    public static boolean isAtLeastS() {
        return Build.VERSION.SDK_INT >= SDK_VERSION_S;
    }

    /**
     * @return true if current sdk version at least on android T
     */
    public static boolean isAtLeastT() {
        return Build.VERSION.SDK_INT >= SDK_VERSION_T;
    }

    /**
     * @return true if current sdk version at least on android U
     */
    public static boolean isAtLeastU() {
        return Build.VERSION.SDK_INT >= SDK_VERSION_U;
    }

    /**
     * To test the mock
     *
     * @return
     */
    public static int getSDKVersion() {
        return Build.VERSION.SDK_INT;
    }

    public static boolean isAtLeastOS11Point3() {
        return PropertyCompat.getSColorOSVersionCode() >= OplusBuildCompat.OS_11_3;
    }

    public static boolean isAtLeastOS12() {
        return PropertyCompat.getSColorOSVersionCode() >= OplusBuildCompat.OS_12_0;
    }

    /**
     * 判断系统版本是否是13.0及以上
     *
     * @return
     */
    public static boolean isAtLeastOS13() {
        return PropertyCompat.getSColorOSVersionCode() >= OplusBuildCompat.OS_13_0;
    }

    public static boolean isAtLeastOS13Point1() {
        return PropertyCompat.getSColorOSVersionCode() >= OplusBuildCompat.OS_13_1;
    }

    public static boolean isAtLeastOS14() {
        int osVersion = PropertyCompat.getSColorOSVersionCode();
        Log.d("isAtLeastOS14 os:" + osVersion);
        return osVersion >= OplusBuildCompat.OS_14_0;
    }

    public static boolean isAtLeastOS14Point1() {
        return PropertyCompat.getSColorOSVersionCode() >= OplusBuildCompat.OS_14_1_0;
    }

    public static boolean isAtLeastOS15() {
        int colorOsVersion = PropertyCompat.getSColorOSVersionCode();
        Log.d("isAtLeastOS15 os:" + colorOsVersion);
        return colorOsVersion >= OplusBuildCompat.OS_15_0_0;
    }

    public static boolean isAtLeastOS16() {
        int colorOsVersion = PropertyCompat.getSColorOSVersionCode();
        Log.d("isAtLeastOS16 os:" + colorOsVersion);
        return colorOsVersion >= OplusBuildCompat.OS_16_0;
    }

    public static boolean isAtLargestOS15Point1() {
        int colorOsVersion = PropertyCompat.getSColorOSVersionCode();
        Log.d("isAtLeastOS15Point1 os:" + colorOsVersion);
        return colorOsVersion < OplusBuildCompat.OS_15_0_1;
    }

    /**
     * 获取 os 版本
     * To test the mock
     */
    public static int getOSVersion() {
        return OplusBuildCompat.getOplusOSVERSION();
    }

    /**
     * 获取 os sdk 版本中第一位版本号
     * 版本号为两位，从android-T 13.0 以上开始定义，13.0 以下无法获取
     * to test the mock
     */
    public static int getOSSdkVersion() {
        return OplusBuildCompat.getOSSdkVersion();
    }

    /**
     * 获取 os sdk sub 版本中第二位版本号
     * 版本号为两位，从android-T 13.0 以上开始定义，13.0 以下无法获取
     * to test the mock
     */
    public static int getOSSdkSubVersion() {
        return OplusBuildCompat.getOSSdkSubVersion();
    }
}
