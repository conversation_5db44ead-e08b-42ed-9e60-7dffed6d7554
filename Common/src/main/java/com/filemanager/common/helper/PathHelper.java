/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.helper;

import android.content.Context;
import android.text.TextUtils;

import com.filemanager.common.utils.Log;

import org.apache.commons.io.FilenameUtils;

import java.lang.ref.WeakReference;
import java.util.LinkedList;
import java.util.List;

public class PathHelper {
    private static final String TAG = "PathHelper";
    public static String sRootExternalPath = null;
    public static String sRootInternalPath = null;
    public static String sRootPath = null;
    private static List<String> sRootOtgPath = null;
    private WeakReference<Context> mContextRefrence;
    private String mRootPath;
    private PathInfo mRootPathInfo;
    private LinkedList<PathInfo> mPathStack = new LinkedList<PathInfo>();

    public PathHelper(Context context) {
        mContextRefrence = new WeakReference<>(context);
        sRootExternalPath = VolumeEnvironment.getExternalSdPath(mContextRefrence.get());
        sRootInternalPath = VolumeEnvironment.getInternalSdPath(mContextRefrence.get());
        sRootOtgPath = VolumeEnvironment.getOTGPath(mContextRefrence.get());
        if (null != sRootInternalPath) {
            Log.d(TAG, "ROOT_INTERNAL_PATH = " + sRootInternalPath);
            String temp = sRootInternalPath.substring(1);
            sRootPath = "/" + temp.substring(0, temp.indexOf("/"));
        }
        mRootPath = sRootPath;
        if ((TextUtils.isEmpty(sRootExternalPath) && ((sRootOtgPath == null) || (sRootOtgPath.size() == 0)))) {
            mRootPathInfo = new PathInfo(sRootInternalPath);
            mRootPath = sRootInternalPath;
        } else {
            mRootPathInfo = new PathInfo(mRootPath);
        }
        if (!mPathStack.contains(mRootPathInfo)) {
            mPathStack.push(mRootPathInfo);
        }
    }

    public String getInternalPath() {
        return sRootInternalPath;
    }

    public String getRootPath() {
        if ((TextUtils.isEmpty(sRootExternalPath) && ((sRootOtgPath == null) || (sRootOtgPath.size() == 0)))) {
            return sRootInternalPath;
        }
        return mRootPath;
    }


    public PathInfo pop() {
        if (getCount() == 0) {
            return mRootPathInfo;
        }

        final PathInfo path = mPathStack.pop();
        if (null == path) {
            return mRootPathInfo;
        }
        return path;
    }

    /**
     * Fetch the specified index path info.
     *
     * @param index
     * @return the specified index path info, or null if the index great than or
     * equals the stack size.
     */
    public PathInfo getPath(int index) {
        int size = mPathStack.size();
        if ((index >= 0) && (index < size)) {
            return mPathStack.get(index);
        } else {
            return null;
        }
    }


    public int getCount() {
        return mPathStack.size();
    }

    public static class PathInfo {
        private String mPath;
        /**
         * The last list view position.
         */
        private int mPosition;
        private String mName;
        private int mY = 0;

        public PathInfo(String path) {
            this.mPath = path;
            mName = FilenameUtils.getName(this.mPath);
        }

        public PathInfo(String path, int position, int y) {
            this(path);
            this.mPosition = position;
            this.mY = y;
        }

        public int getY() {
            return mY;
        }

        public String getPath() {
            return mPath;
        }

        public void setPath(String path) {
            this.mPath = path;
            mName = FilenameUtils.getName(this.mPath);
        }

        /**
         * Return the last list view position.
         */
        public int getPosition() {
            return mPosition;
        }

        public void setPosition(int position) {
            this.mPosition = position;
        }

        public void setY(int y) {
            this.mY = y;
        }

        public String getName() {
            return mName;
        }

        @Override
        public int hashCode() {
            final int prime = 31;
            int result = 1;
            result = prime * result + ((mPath == null) ? 0 : mPath.hashCode());
            result = prime * result + mPosition;
            return result;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) {
                return true;
            }
            if (obj == null) {
                return false;
            }
            if (getClass() != obj.getClass()) {
                return false;
            }
            PathInfo other = (PathInfo) obj;
            if (mPath == null) {
                if (other.mPath != null) {
                    return false;
                }
            } else if (!mPath.equals(other.mPath)) {
                return false;
            }
            return true;
        }
    }

}