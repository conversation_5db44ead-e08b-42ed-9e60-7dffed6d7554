package com.filemanager.common.imageloader.glide;

import android.graphics.Bitmap;
import android.text.TextUtils;
import android.widget.ImageView;

import com.bumptech.glide.request.target.ImageViewTarget;
import com.filemanager.common.R;

public class BitmapViewTarget extends ImageViewTarget<Bitmap> {
    private String mOldUrlString;

    public BitmapViewTarget(ImageView view, String oldUrl) {
        super(view);
        mOldUrlString = oldUrl;
    }

    @Override
    protected void setResource(Bitmap resource) {
        if ((view != null) && (!TextUtils.isEmpty(mOldUrlString)) && (mOldUrlString.equals(view.getTag(R.id.glide_tag_id)))) {
            if (resource != null) {
                view.setImageBitmap(resource);
            }
        }
    }
}
