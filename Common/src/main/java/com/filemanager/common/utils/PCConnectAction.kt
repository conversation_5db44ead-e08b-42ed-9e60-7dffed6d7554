/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: PCConnectAction.kt
 ** Description:  PCConnectAction
 ** Version: 1.0
 ** Date: 2021/6/18
 ** Author: <PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils

import android.net.Uri
import android.view.MotionEvent
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.RecyclerView.OnItemTouchListener
import com.filemanager.common.base.BaseFileBean
import com.oplus.filemanager.interfaze.pcconnect.IPCConnect

object PCConnectAction {

    private val pcConnect: IPCConnect? by lazy {
        Injector.injectFactory<IPCConnect>()
    }

    fun isScreenCast(): Boolean {
        return pcConnect?.isScreenCast() ?: false
    }

    fun attachToLifecycle(owner: LifecycleOwner) {
        pcConnect?.attachToLifecycle(owner)
    }

    fun onFragmentHiddenChanged(owner: LifecycleOwner, hidden: Boolean) {
        pcConnect?.onFragmentHiddenChanged(owner, hidden)
    }

    fun onActivityResume() {
        pcConnect?.onActivityResume()
    }

    fun getMultiScreenConnectDirList(): Array<String> {
        return pcConnect?.getMultiScreenConnectDirList() ?: arrayOf()
    }

    fun isMultiScreenConnectSupport(): Boolean  {
        return pcConnect?.isMultiScreenConnectSupport() ?: false
    }

    fun openFileOnRemote(file: BaseFileBean, event: MotionEvent?): Boolean  {
        return pcConnect?.openFileOnRemote(file, event) ?: false
    }

    fun getItemTouchInterceptor(): OnItemTouchListener?  {
        return pcConnect?.getItemTouchInterceptor()
    }

    fun checkViewCanLongPress(editMode: Boolean): Boolean  {
        return pcConnect?.checkViewCanLongPress(editMode) ?: true
    }

    fun isPadScreenCast(): Boolean {
        return pcConnect?.isPadScreenCast() ?: false
    }

    fun grantUriPermissionsForPad(uriList: ArrayList<Uri?>) {
        pcConnect?.grantUriPermissionForPad(uriList)
    }

    fun isTouchFromPC(event: MotionEvent?): Boolean {
        return pcConnect?.isTouchFromPC(event) ?: false
    }

    fun isMultiScreenDirExist(): Boolean  {
        return pcConnect?.isMultiScreenDirExist() ?: false
    }
}