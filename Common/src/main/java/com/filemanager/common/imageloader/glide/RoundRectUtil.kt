/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RoundRectUtil.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/27
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/4/27      1.0        create
 ***********************************************************************/
package com.filemanager.common.imageloader.glide

import android.graphics.Path
import android.graphics.RectF
import com.coui.appcompat.roundRect.COUIShapePath

object RoundRectUtil {
    private const val PATH_CACHE_SIZE = 10
    private val paths = Array(PATH_CACHE_SIZE) { PerformanceRoundPath() }
    private var currentIndex = 0

    @Suppress("LongParameterList")
    @JvmStatic
    fun getPath(
        left: Float,
        top: Float,
        right: Float,
        bottom: Float,
        radius: Float,
        tl: Boolean,
        tr: Boolean,
        bl: Boolean,
        br: Boolean
    ): Path {
        return getPath(RectF(left, top, right, bottom), radius, tl, tr, bl, br)
    }

    @Suppress("ComplexCondition")
    @Synchronized
    @JvmStatic
    fun getPath(
        rect: RectF,
        radius: Float,
        tl: Boolean,
        tr: Boolean,
        bl: Boolean,
        br: Boolean
    ): Path {
        paths.forEach {
            if ((it.currentRect == rect) && (it.cornerRadius == radius)
                && (it.tl == tl) && (it.tr == tr) && (it.bl == bl) && (it.br == br)
            ) {
                return it.path
            }
        }

        currentIndex++
        currentIndex %= PATH_CACHE_SIZE
        return paths[currentIndex].getPath(rect, radius, tl, tr, bl, br)
    }

    private class PerformanceRoundPath {
        val currentRect = RectF()
        var cornerRadius = 0f
        var tr: Boolean = true
        var tl: Boolean = true
        var bl: Boolean = true
        var br: Boolean = true
        var path = Path()

        @Suppress("ComplexCondition", "UtilMustStaticRule")
        fun getPath(
            rect: RectF,
            radius: Float,
            tl: Boolean,
            tr: Boolean,
            bl: Boolean,
            br: Boolean
        ): Path {
            if ((currentRect != rect) || (cornerRadius != radius)
                || (this.tl != tl) || (this.tr != tr) || (this.bl != bl) || (this.br != br)
            ) {
                currentRect.set(rect)
                cornerRadius = radius
                path = Path()
                COUIShapePath.getRoundRectPath(path, currentRect, cornerRadius, tl, tr, bl, br)
            }
            return path
        }
    }
}