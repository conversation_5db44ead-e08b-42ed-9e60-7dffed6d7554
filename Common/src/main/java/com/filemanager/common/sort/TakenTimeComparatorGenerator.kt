/*********************************************************************
 * * Copyright (C), 2010-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2024/11/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.wrapper.ImageFileWrapper

open class TakenTimeComparatorGenerator : AbstractFileComparator(), Comparator<BaseFileBean> {

    override fun compare1(o1: BaseFileBean, o2: BaseFileBean, isDesc: Boolean): Int {
        val result = if (o1 is ImageFileWrapper && o2 is ImageFileWrapper) {
            val t1 = o1.getDateTaken().let { time ->
                if (time == 0L) o1.mDateModified else time
            }
            val t2 = o2.getDateTaken().let { time ->
                if (time == 0L) o1.mDateModified else time
            }
            t1 - t2
        } else o1.mDateModified - o2.mDateModified
        return when {
            result < 0 -> -1

            result > 0 -> 1

            else -> {
                val newResult = o1.mDateModified - o2.mDateModified
                if (newResult != 0L) {
                    if (newResult > 0) {
                        1
                    } else {
                        -1
                    }
                } else {
                    NameComparatorGenerator.comparator.compare1(o1, o2, isDesc)
                }
            }
        }
    }

    override fun compare(file: BaseFileBean, t1: BaseFileBean): Int {
        return compare1(file, t1, true)
    }

    companion object {

        val comparator = TakenTimeComparatorGenerator()
        val REVERSE_COMPARATOR = comparator.genReverseComparator()

        fun getComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return if (isDesc) {
                comparator.genReverseComparator()
            } else {
                comparator.genComparator()
            }
        }
    }
}