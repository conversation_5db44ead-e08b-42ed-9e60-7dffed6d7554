/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - ObjectConstructInjectUtils.kt
 * Description:
 *     Make some static methods to replace some constructor.
 *     And the we can mock it in junit test.
 *
 * Version: 1.0
 * Date: 2024-04-11
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <EMAIL>    2024-04-11   1.0    Create this module
 *********************************************************************************/
@file:JvmName("ObjectConstructInjectUtils")

package com.filemanager.common.utils

import android.content.ContentValues
import android.os.Bundle
import org.jetbrains.annotations.VisibleForTesting
import java.io.File

/**
 * The class name for this kt file.
 * Use it to mockkStatic in unit test.
 */
@VisibleForTesting
const val CONSTRUCT_INJECT_CLASS = "com.filemanager.common.utils.ObjectConstructInjectUtils"

/**
 * Use this static method to replace the constructor of [Bundle]
 * And the we can use mockStatic to mock it as constructor in junit test.
 */
fun constructBundle(): Bundle = Bundle()

/**
 * Use this static method to replace the constructor of [ContentValues]
 * And the we can use mockStatic to mock it as constructor in junit test.
 */
fun constructContentValues(): ContentValues = ContentValues()

/**
 * Use this static method to replace the constructor of [File]
 * And the we can use mockStatic to mock it as constructor in junit test.
 */
fun constructFile(pathname: String): File = File(pathname)

/**
 * Use this static method to replace the constructor of [File]
 * And the we can use mockStatic to mock it as constructor in junit test.
 */
fun constructFile(parent: File, child: String): File = File(parent, child)