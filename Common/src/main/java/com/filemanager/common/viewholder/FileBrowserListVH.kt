/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.viewholder
 * * Version     : 1.0
 * * Date        : 2020/9/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.setPadding
import androidx.core.view.updateLayoutParams
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.CheckBoxAnimateInput
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.FileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.HighlightUtil
import com.filemanager.common.utils.KtAppUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.KtUtils.STRING_FOUR_SPACES
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.LabelVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.wrapper.PathFileWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*
import kotlin.math.max

class FileBrowserListVH : BaseFileBrowserVH {
    companion object {
        private const val TAG = "FileBrowserListVH"
        private const val SECOND = 1000L
        fun getLayoutId(): Int {
            return R.layout.file_browser_recycler_item
        }
    }

    private var mImg: FileThumbView
    private var mJumpImg: ImageView
    private var mTitle: TextViewSnippet
    private var mDetail: TextView
    private var mAnotherName: TextViewSnippet
    private var mDuration: TextView
    private var apkIcon: ImageView
    private var mImgRadius: Int = 0
    private var mIsLabelFileList: Boolean = false
    private val docRadius = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_2dp)
    private var rootView: ConstraintLayout
    private var iconContainer: View
    private var fileListAdapterFolderMaxSize = MyApplication.appContext.resources
        .getDimensionPixelOffset(R.dimen.file_list_adapter_folder_max_size)
    private var fileListItemInfoSelectedWidthNew =
        MyApplication.appContext.resources.getDimensionPixelOffset(R.dimen.file_list_item_info_selected_width_new)

    constructor(convertView: View, imgRadius: Int = 0, isLabelFileList: Boolean = false) : super(convertView) {
        rootView = convertView.findViewById(R.id.file_browser_item_list_root)
        mImg = convertView.findViewById(R.id.file_list_item_icon)
        mJumpImg = convertView.findViewById(R.id.jump_mark)
        mTitle = convertView.findViewById(R.id.file_list_item_title)
        mDetail = convertView.findViewById(R.id.mark_file_list_item_detail)
        mAnotherName = convertView.findViewById(R.id.another_name_view)
        mCheckBox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
        mDuration = convertView.findViewById(R.id.file_duration_tv)
        mImgRadius = imgRadius
        mIsLabelFileList = isLabelFileList
        apkIcon = convertView.findViewById(R.id.apk_icon)
        iconContainer = convertView.findViewById(R.id.file_list_item_icon_container)
    }

    //大小屏切换时，需要重新获取屏幕宽度，根据屏幕宽度确认文件夹的最大标题宽度
    private fun getFolderTitleMaxSize(context: Context): Int = if (context is Activity) {
        val layoutWidth = if (rootView.width > fileListAdapterFolderMaxSize) {
            rootView.width
        } else {
            KtViewUtils.getWindowSize(context).x
        }
        layoutWidth - fileListAdapterFolderMaxSize
    } else {
        fileListItemInfoSelectedWidthNew
    }

    override fun updateViewHolder(context: Context, key: Int?, file: BaseFileBean, choiceMode: Boolean, selectionArray: MutableList<Int>
                                  , sizeCache: HashMap<String, String>, threadManager: ThreadManager, adapter: BaseSelectionRecycleAdapter<*, *>) {
        mJumpImg.visibility = View.INVISIBLE
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        updateLeftRightMargin()
        var isDir = false
        val folderTitleMaxSize = getFolderTitleMaxSize(context)
        var titleMaxWidth = adapter.mNormalTitleMaxSize
        if (type == MimeTypeHelper.DIRECTORY_TYPE) {
            if (folderTitleMaxSize > 0) {
                titleMaxWidth = folderTitleMaxSize
            }
            mAnotherName.tag = path
            mAnotherName.visibility = View.VISIBLE
            if (!FeatureCompat.sIsExpRom) {
                mUpdateViewListener?.updateViewByAlias(mAnotherName, path)
            } else {
                mAnotherName.text = ""
            }
            if (mAnotherName.text.isEmpty() && file.originPackage.isNullOrEmpty().not()) {
                GlobalScope.launch(Dispatchers.IO) {
                    val alias = AndroidDataHelper.installedNameMap?.get(file.originPackage) ?: ""
                    withContext(Dispatchers.Main) {
                        if (alias != file.originPackage) {
                            mAnotherName.text = "${STRING_FOUR_SPACES}$alias"
                        }
                        if (mAnotherName.text.isEmpty()) {
                            mAnotherName.visibility = View.GONE
                            titleMaxWidth = max(adapter.mNormalTitleMaxSize, folderTitleMaxSize)
                        }
                    }
                }
            } else if (mAnotherName.text.isEmpty()) {
                mAnotherName.visibility = View.GONE
                titleMaxWidth = max(adapter.mNormalTitleMaxSize, folderTitleMaxSize)
            }
            mJumpImg.setTag(R.id.mark_dir, true)
            isDir = true
        } else {
            if (folderTitleMaxSize > 0) {
                //显示文件夹及文件的列表中，因文件夹有别名需要显示（文件夹名称的宽度=当前屏幕宽度-180dp），
                // 在手机上及小屏上，文件的名称要比文件夹的大，取用一个228dp的固定值。
                // 当比文件夹名称大时（即大屏），文件名称采用文件夹的最大宽度
                titleMaxWidth = max(titleMaxWidth, folderTitleMaxSize)
            }
            mAnotherName.visibility = View.GONE
            mJumpImg.visibility = View.INVISIBLE
            mJumpImg.setTag(R.id.mark_dir, false)
        }
        mTitle.maxWidth = titleMaxWidth
        mTitle.tag = path
        mDetail.tag = path
        mTitle.text = file.mDisplayName
        mTitle.setTextViewStyle(ignorePoint = type == MimeTypeHelper.DIRECTORY_TYPE)
        LabelVHUtils.displayLabelFlag(file, context, mTitle, isLabelFileList = mIsLabelFileList)
        FileImageVHUtils.updateFileListImgSize(context, mImg, file)
        if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE || file.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
            mImg.setStrokeStyle(FileThumbView.STROKE_2DP)
        } else {
            mImg.setStrokeStyle(FileThumbView.STROKE_NONE)
        }
        mImg.setDrmState(type == MimeTypeHelper.DRM_TYPE)
        if (type == MimeTypeHelper.APPLICATION_TYPE) {
            mImg.let {
                it.setBorderStyle(0F, 0F)
                FileImageLoader.sInstance.clear(context, it)
                FileImageLoader.sInstance.displayApplicationWithDetail(file, it,
                    { applicationInfoDetail ->
                        val tagDetail = mDetail.tag as? String
                        if (applicationInfoDetail?.mPath == tagDetail && !TextUtils.isEmpty(applicationInfoDetail?.mApkVersion)) {
                            mDetail.visibility = View.VISIBLE
                            applicationInfoDetail?.mPath?.apply {
                                val apkDetail = Utils.formatSize(FileWrapper(this))
                                if (!TextUtils.isEmpty(apkDetail)) {
                                    mDetail.text = Utils.getApplicationDetailFormat(
                                        context,
                                        applicationInfoDetail.mApkName,
                                        applicationInfoDetail.mApkVersion,
                                        FileWrapper(applicationInfoDetail.mPath)
                                    )
                                }
                            }
                        } else {
                            showDetail(context, file, mDetail, path, sizeCache, threadManager, true)
                        }
                    })
            }
        } else {
            mImg.let {
                val padding = when (type) {
                    MimeTypeHelper.VIDEO_TYPE -> {
                        MyApplication.sAppContext.resources.getDimension(R.dimen.file_list_image_padding)
                            .toInt()
                    }
                    else -> 0
                }
                val radius = ViewUtils.getIconRadius(type, docRadius, mImgRadius)
                it.setPadding(padding)
                FileImageLoader.sInstance.clear(context, it)
                FileImageLoader.sInstance.displayDefault(
                    file,
                    it,
                    0,
                    radius,
                    loadDocThumbnail = true,
                    isCoverError = true,
                    isSmallDoc = true
                )
            }
            mDetail.apply {
                visibility = View.VISIBLE
                showDetail(context, file, this, path, sizeCache, threadManager, true)
            }
        }
        mImg.scaleType = ImageView.ScaleType.FIT_CENTER
        mCheckBox?.let {
            if (KtAppUtils.mIsOnePlusOverSea) {
                adapter.setCheckBoxAnim(CheckBoxAnimateInput(isDir, choiceMode, null, it, layoutPosition))
            } else {
                adapter.setCheckBoxAnim(CheckBoxAnimateInput(isDir, choiceMode, mJumpImg, it, layoutPosition))
            }
        }
        if (choiceMode) {
            if (!adapter.mIsAnimRunning) {
                mJumpImg.visibility = View.INVISIBLE
            }
        } else {
            if (isDir) {
                if (!KtAppUtils.mIsOnePlusOverSea) {
                    mJumpImg.visibility = View.VISIBLE
                }
                
            } else {
                if (!adapter.mIsAnimRunning) {
                    mJumpImg.visibility = View.INVISIBLE
                }
            }
        }
        val alpha = HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, adapter.mIsDarkModel)
        mTitle.alpha = alpha
        mDetail.alpha = alpha
        mImg.alpha = alpha
        if (file.mLocalType == MimeTypeHelper.VIDEO_TYPE && (file as? PathFileWrapper)?.mMediaDuration == 0L) {
            GlobalScope.launch(Dispatchers.IO) {
                (file as? PathFileWrapper)?.getMediaDuration()
                withContext(Dispatchers.Main) {
                    showDurationIfNeed(file, mDuration)
                }
            }
        } else {
            showDurationIfNeed(file, mDuration)
        }
        showApkIconIfNeed(file, apkIcon, false)
        updateDividerVisible(adapter.getRealFileItemCount() - 1, position = position)
        HighlightUtil.addItemHighlight(context, this@FileBrowserListVH, bindingAdapterPosition)
    }

    private fun setIconConstraintSet(isMoreThanOneLine: Boolean) {
        (mTitle.parent as? RelativeLayout)?.let { titleRL ->
            //1行且图标和标题顶部对齐，两行且图标和标题居中对齐，此时才需要更新
            if (isMoreThanOneLine && mImg.y == titleRL.y) {
                return
            }
            if (isMoreThanOneLine.not() && mImg.y != titleRL.y) {
                return
            }
        }

        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.file_list_item_icon, ConstraintSet.TOP)
            clear(R.id.file_list_item_icon, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    R.id.rl_item_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    override fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        val checkBoxRect = Rect()
        mCheckBox?.getGlobalVisibleRect(checkBoxRect) ?: return false
        return checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }

    /**
     * 列表页需要调用以下方法
     * CloudConfigAction.updateViewByAlias(textViewSnippet, path)
     * CloudConfigAction定义在 module: ModuleRouter 中，直接在此Common module中引用会导致循环引用
     * 故声明此接口，在调用module实现（FileBrowser,LabelManager）
     */
    interface UpdateViewListener {
        fun updateViewByAlias(textViewSnippet: TextViewSnippet, path: String)
    }

    private var mUpdateViewListener: UpdateViewListener? = null

    fun setUpdateViewList(listener: UpdateViewListener) {
        mUpdateViewListener = listener
    }

    private fun showDurationIfNeed(file: BaseFileBean, mDuration: TextView) {
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType && file.mMediaDuration != 0L) {
            mDuration.visibility = View.VISIBLE
            mDuration.text = KtUtils.formatVideoTime(file.mMediaDuration / SECOND)
        } else {
            mDuration.visibility = View.GONE
        }
    }

    fun setSelected(isSelected: Boolean) {
        rootView.isSelected = isSelected
    }

    private fun updateLeftRightMargin() {
        iconContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
        }
        mJumpImg.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }
}