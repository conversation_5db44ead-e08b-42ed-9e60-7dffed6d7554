/***********************************************************
 * * Copyright (C), 2020 - 2020, Oplus. All rights reserved.
 * * File: ImageLoaderFactory
 * * Description: the factory for image loader
 * * Version: 1.0
 * * Date : 2020/11/13
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/11/13       1.0         the factory for image loader
 ****************************************************************/
package com.filemanager.common.imageloader

import com.filemanager.common.imageloader.glide.GlideLoader
import com.filemanager.common.imageloader.ImageLoaderInterface

object ImageLoaderFactory {
    /**
     * return imageLoader implements ImageLoaderInterface
     */
    fun buildImageLoader() : ImageLoaderInterface {
        return GlideLoader
    }
}