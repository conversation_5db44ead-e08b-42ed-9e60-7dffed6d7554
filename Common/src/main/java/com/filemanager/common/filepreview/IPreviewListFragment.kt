/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: IPreviewListFragment
 ** Description: Preview List Fragment interface
 ** Version: 1.0
 ** Date : 2024/10/04
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview

import android.os.Bundle
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import androidx.fragment.app.Fragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.interfaces.fileoprate.IFileOperate

interface IPreviewListFragment {

    fun getFragmentInstance(): Fragment

    fun setFragmentArguments(arguments: Bundle?)

    fun setPreviewToolbar(toolbar: COUIToolbar?)

    fun onResumeLoadData()

    fun onCreateOptionsMenu(menu: Menu, menuInflater: MenuInflater)

    fun onMenuItemSelected(item: MenuItem): Boolean

    fun pressBack(): Boolean

    fun onNavigationItemSelected(item: MenuItem): Boolean

    fun fromSelectPathResult(code: Int, paths: List<String>?)

    fun setIsHalfScreen(isHalfScreen: Boolean)

    fun backToTop()

    fun updatedLabel()

    fun permissionSuccess()

    fun setCurrentFromOtherSide(currentPath: String)

    fun getCurrentPath(): String

    fun getScanMode(): Int

    fun setScanMode(mode: Int)

    fun isSelectionMode(): Boolean

    fun setPreviewOperate(operate: IPreviewOperate)

    fun getOperatorResultListener(
        recentOperateBridge: IFileOperate.IRecentOperateBridge
    ): IFileOperate.OperateResultListener?

    fun getOperateInterceptor(): IFileOperate? = null

    fun exitSelectionMode()

    fun onSideNavigationClicked(isOpen: Boolean): Boolean

    fun updateLeftRightMargin()

    fun isEmptyList(): Boolean

    fun handleDragScroll(event: DragEvent): Boolean

    fun resetScrollStatus()

    fun getSelectedItemView(): ArrayList<View>?

    fun setNavigateItemAble()
}