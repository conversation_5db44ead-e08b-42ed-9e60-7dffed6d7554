/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File: FeatureCompat.kt
 * * Description:Query and save the value of common features and query whether the feature exists
 * * Version:1.0
 * * Date :2020/8/17
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/8/17,        v1.0,           Create
 ****************************************************************/
package com.filemanager.common.compat.compat30

import android.content.Intent
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.AddonSdkCompat
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.FeatureCompat.FEATURE_FLIP_DEVICE
import com.filemanager.common.compat.OplusFeatureConfigManagerCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import com.oplus.coreapp.appfeature.AppFeatureProviderUtils


object FeatureCompatR : FeatureCompat.IOPlusFeatureInterface {
    private const val TAG = "OPlusFeatureCompatR"

    //feature key
    private const val FEATURE_INNER_ROM = "com.oplus.filemanager.inner_sales_version"
    private const val FEATURE_DRM_SUPPORT = "com.oplus.filemanager.drm_support"
    private const val FEATURE_SDCARD_NOT_SUPPORT = "com.oplus.filemanager.sdcard_not_support"
    private const val FEATURE_NOT_SUPPORT_ENCRYPTION = "com.oplus.filemanager.encryption_not_support"
    private const val FEATURE_NOT_SUPPORT_UNKNOWNFILE = "com.oplus.filemanager.unknownfile_not_support"
    private const val FEATURE_SUPPORT_OTG = "android.hardware.usb.host"
    private const val FEATURE_LIGHT_VERSION = "com.oplus.filemanager.light_os"
    private const val FEATURE_HIDE_STORAGE = "oplus.all.client_hide_storage"
    // other value
    internal const val PKG_NAME_PHONE_MANAGER = "com.coloros.phonemanager"
    internal const val PKG_NAME_PHONE_MANAGER_ONEPLUS_EXP = "com.oplus.phonemanager"
    internal const val ACTION_PHONE_MANAGER_CLEAN = "com.oppo.cleandroid.ui.ClearMainActivity"
    private const val ACTION_PHONE_MANAGER_CLEAN_OPLUS = "oplus.intent.action.CLEAR_MAIN_ACTIVITY"
    private const val FEATURE_TASKBAR_ENABLE = "com.android.launcher.TASKBAR_ENABLE"
    private const val FEATURE_LUXUNVIBRATOR_SUPPORT = "oplus.software.vibrator_luxunvibrator"
    private const val FEATURE_PCCONNECT_SUPPORT = "oplus.software.pcconnect.support"
    private const val FEATURE_PADCONNECT_SUPPORT_TABLE = "oplus.software.padconnect.support"
    private const val FEATURE_HIGH_LEVEL_LIGHT_OS = "andes.oplus.documentsreader.high_level_light_os" //使用文档配置的feature

    override fun isExpRom() = (MyApplication.flavorRegion != "domestic")

    override fun isLightVersion() = hasFeature(FEATURE_LIGHT_VERSION)

    /**
     * Special for LiaoNing CMCC, this feature not config in AppCenterFeature currently,
     * If the late need, config it in AppCenterFeature.
     */
    override fun isNotSupportSD() = hasFeature(FEATURE_SDCARD_NOT_SUPPORT)

    override fun isSupportDrm() = hasFeature(FEATURE_DRM_SUPPORT)

    override fun isSupportEncryption(): Boolean {
        val result = hasFeature(FEATURE_NOT_SUPPORT_ENCRYPTION).not()
        return result && if (Utils.getUserId() == 0) {
            !Utils.isContainerUser(MyApplication.sAppContext)
        } else false
    }

    /**
     * In R, the feature has been removed. Instead of check the activity is exist or not.
     * Confirmed with ZhangWeichao(80059204): http://doc.adc.com/sheets/YpHckHxwkJpKQwRq/u6QdJ
     */
    override fun getPhoneManagerStartInfo(): Pair<String, String>? {
        fun checkPhoneManagerExist(pkg: String, action: String): Pair<String, String>? {
            try {
                if (MyApplication.sAppContext.packageManager.queryIntentActivities(Intent(action).apply {
                        `package` = pkg
                    }, 0).isNotEmpty()) {
                    return Pair(pkg, action)
                }
            } catch (e: Exception) {
                Log.e(TAG, "checkPhoneManagerExist failed: $action, ${e.message}")
            }
            return null
        }

        var result = checkPhoneManagerExist(PKG_NAME_PHONE_MANAGER, ACTION_PHONE_MANAGER_CLEAN_OPLUS)
        if (result == null) {
            result = checkPhoneManagerExist(PKG_NAME_PHONE_MANAGER, ACTION_PHONE_MANAGER_CLEAN)
        }
        if (result == null) {
            result = checkPhoneManagerExist(PKG_NAME_PHONE_MANAGER_ONEPLUS_EXP, ACTION_PHONE_MANAGER_CLEAN_OPLUS)
        }
        return result
    }

    /**
     * Confirmed with WeiRuipeng(80242685), this feature was not be config never.
     */
    override fun isStorageUnitNormal() = false

    override fun isStorageHidden(): Boolean = hasFeature(FEATURE_HIDE_STORAGE, false)

    /**
     * In R, the statement dialog will be shown both in EXP and Inner ROM at the first time
     */
    override fun isSupportRuntimePermissionAlert() = true

    /**
     * Build-in Music not used this feature in R
     */
    override fun isOnlyUseBuildInMusicPlay() = false

    override fun isSupportOTG(): Boolean {
        return try {
            MyApplication.sAppContext.packageManager.hasSystemFeature(FEATURE_SUPPORT_OTG)
        } catch (e: Exception) {
            Log.w(TAG, "isSupportOTG failed: ${e.message}")
            true
        }.also {
            Log.d(TAG, "isSupportOTG: $it")
        }
    }

    override fun isNotSupportUnknownFile() = hasFeature(FEATURE_NOT_SUPPORT_UNKNOWNFILE)

    /**
     * Confirmed with JiZhengKang(80051950), this feature not used in android R,
     * In R, all OTA packages are not encrypted. so set the value to FALSE default.
     */
    override fun isSupportEncryptOTAOnly() = false

    override fun isSupportMultiApp() = hasFeature(OplusFeatureConfigManagerCompat.FEATURE_MULTI_APP_DISABLED, false).not()

    override fun isSupportTaskbar(): Boolean {
        return hasFeature(FEATURE_TASKBAR_ENABLE)
    }

    override fun isFlipDevice(): Boolean {
        return hasFeature(FEATURE_FLIP_DEVICE)
    }

    override fun isSupportPCConnect(): Boolean {
        return hasFeature(if (ModelUtils.isTablet()) FEATURE_PADCONNECT_SUPPORT_TABLE else FEATURE_PCCONNECT_SUPPORT, false)
    }

    override fun isHighLevelLightOS(): Boolean {
        val result = kotlin.runCatching {
            AppFeatureProviderUtils.getBoolean(
                MyApplication.sAppContext.contentResolver,
                FEATURE_HIGH_LEVEL_LIGHT_OS,
                false
            )
        }.onFailure {
            Log.w(TAG, "hasFeature failed: FEATURE_HIGH_LEVEL_LIGHT_OS, ${it.message}")
        }.getOrDefault(false)
        Log.d(TAG, "isHighLevelLightOS: $result")
       return result
    }

    /**
     * @param appFeature true means AppFeature, false means OplusFeature (system feature)
     */
    private fun hasFeature(key: String, appFeature: Boolean = true): Boolean {
        val result = try {
            val context = MyApplication.appContext
            if (appFeature) {
                AppFeatureProviderUtils.isFeatureSupport(context.contentResolver, key)
            } else {
                AddonSdkCompat.hasFeature(key)
            }
        } catch (e: Throwable) {
            Log.w(TAG, "hasFeature failed: $key, $appFeature, ${e.message}")
            false
        }
        Log.d(TAG, "hasFeature: appFeature=$appFeature, key=$key, result=$result")
        return result
    }

    override fun isSupportLinearmotorVibrator() = hasFeature(OplusFeatureConfigManagerCompat.FEATURE_LMVIBRATOR_SUPPORT, false)

    override fun isSupportLuxunVibrator() = hasFeature(FEATURE_LUXUNVIBRATOR_SUPPORT, false)
}