/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.utils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.res.Resources
import com.filemanager.common.R

object RenameErrorTipUtil {

    // Error type
    const val ERROR_FILENAME_INPUT_ILLEGAL = 1
    const val ERROR_FILENAME_ILLEGAL = 2
    const val ERROR_FILE_NAME_TOO_LONG = 8
    const val ERROR_FILE_NAME_DUPLICATE = 9

    @JvmStatic
    fun getRenameErrorTips(type: Int, resources: Resources): String {
        val notice = when (type) {
            ERROR_FILENAME_ILLEGAL -> resources.getString(R.string.unsupported_input_the_char)
            ERROR_FILE_NAME_TOO_LONG -> resources.getString(R.string.input_over_upper_limit)
            ERROR_FILENAME_INPUT_ILLEGAL -> resources.getString(R.string.error_symbol_in_input_text, " \\ / : * ? \" < > |")
            ERROR_FILE_NAME_DUPLICATE -> resources.getString(R.string.toast_folder_exist)
            else -> resources.getString(R.string.unsupported_input_the_char)
        }
        return notice
    }
}