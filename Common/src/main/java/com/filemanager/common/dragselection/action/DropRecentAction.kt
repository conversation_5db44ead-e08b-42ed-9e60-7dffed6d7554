/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/24, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.view.DragEvent
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.Companion.parseFiles
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.dragselection.util.DropUtil.DOWNLOAD_PATH
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
object DropRecentAction : DropListener {

    private const val TAG = "DropRecentAction"

    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            dragEvent.clipData?.apply {
                parseFiles(
                    activity = activity,
                    onMacDownloadCallBack = null,
                    description = description
                )
            }
            return true
        }
        //该tag不为空，内部文件，不支持移动到最近文件
        if (dragTag != null) {
            Log.d(TAG, "handleDropAction -> not support handle, return!")
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }

        val parseData = DropUtil.parseDragFile(dragEvent)

        //不支持从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }
        //外部拖拽的文本
        if (parseData.isExternalTextFiles()) {
            Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, DOWNLOAD_PATH)
            return false
        }

        val privacyFileCallback = {
            Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, DOWNLOAD_PATH)
            true
        }
        val mediaFileCallback = {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            false
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            return DropDispatchAction.COPY_STATE
        }
        //该tag不为空，内部文件，不支持移动到最近文件
        if (dragTag != null) {
            return DropDispatchAction.PROHIBIT_STATE
        }

        val parseData = DropUtil.parseDragFile(dragEvent)

        //不支持从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            return DropDispatchAction.PROHIBIT_STATE
        }
        //外部拖拽的文本
        if (parseData.isExternalTextFiles()) {
            return DropDispatchAction.COPY_STATE
        }
        return DropDispatchAction.COPY_STATE
    }
}