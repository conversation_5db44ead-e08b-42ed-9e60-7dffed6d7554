package com.filemanager.common.thread

/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/2/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
import android.text.TextUtils
import androidx.lifecycle.Lifecycle
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.utils.Log
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.Future
import java.util.concurrent.FutureTask


const val THREAD_MAX_RETRY_TIME = 3

class ThreadManager : BaseLifeController, RemoveWorkInterface {
    companion object {
        @JvmStatic
        val sThreadManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            ThreadManager()
        }
    }

    private val mCacheLock = Any()
    private val mFutureCache = ConcurrentHashMap<String, Future<*>>()
    private val mCacheRetryTime = ConcurrentHashMap<String, Int>()

    constructor(lifecycle: Lifecycle? = null) {
        lifecycle?.addObserver(this)
    }

    fun execute(runnable: FileRunnable?, threadType: ThreadType?, threadPriority: ThreadPriority?): String? {
        return execute(runnable, threadType, threadPriority, null)
    }

    fun execute(runnable: FileRunnable?): String? {
        return execute(runnable, ThreadType.NORMAL_THREAD, ThreadPriority.LOW)
    }

    fun execute(runnable: FileRunnable?, threadType: ThreadType?, threadPriority: ThreadPriority?, threadCancelInterface: ThreadCancelInterface?): String? {
        var threadType = threadType
        var threadPriority = threadPriority
        if (null == runnable) {
            return null
        }
        if (null == threadPriority) {
            threadPriority = ThreadPriority.LOW
        }
        if (null == threadType) {
            threadType = ThreadType.NORMAL_THREAD
        }
        val task = ThreadTask(runnable, threadCancelInterface, null, this)
        task.setPriority(threadPriority)
        task.setThreadType(threadType)
        return execute(task)
    }

    fun execute(task: BaseThreadTask<*>?): String? {
        if (null == task) {
            Log.i("ThreadManager", "submitCancelable task null")
            return null
        }
        synchronized(mCacheLock) {
            val future = mFutureCache[task.getUniqueFlag()]
            val time = mCacheRetryTime.get(task.getUniqueFlag()) ?: 0
            if ((future != null) && (time < THREAD_MAX_RETRY_TIME)) {
                mCacheRetryTime.put(task.getUniqueFlag(), time + 1)
                return null
            }
        }
        val future = submitThread(task, task.getThreadType())
        var key = task.getUniqueFlag()
        if (null != future) {
            synchronized(mCacheLock) {
                mFutureCache[key] = future
            }
        }
        return key
    }

    private fun submitThread(task: FutureTask<*>, type: ThreadType): Future<*>? {
        val executor = ExecutorFactory.getExecutor(type)
        return executor.submit(task)
    }

    fun containThread(key: String?): Boolean {
        return mFutureCache.containsKey(key)
    }

    fun cancelThread(key: String?): Boolean {
        if (TextUtils.isEmpty(key)) {
            return false
        }
        var ret = false
        synchronized(mCacheLock) {
            val future = mFutureCache[key]
            if (null != future) {
                ret = future.isCancelled || future.isDone || future.cancel(true)
                mFutureCache.remove(key)
                mCacheRetryTime.remove(key)
            }
            if (future is BaseThreadTask) {
                val executor = ExecutorFactory.getExecutor(future.getThreadType())
                executor.remove(future)
                future.mRunnable?.mRecycler?.invoke(future.getUniqueFlag())
                future.mIsCallCancel = true
            }
        }

        return ret
    }

    fun cancelAllThread() {
        for (key in mFutureCache.keys) {
            cancelThread(key)
        }
    }

    override fun removeWork(key: String) {
        if (TextUtils.isEmpty(key)) {
            return
        }
        synchronized(mCacheLock) {
            mFutureCache.remove(key)
            mCacheRetryTime.remove(key)
        }
    }


    override fun onDestroy() {
        synchronized(mCacheLock) {
            for (key in mFutureCache.keys) {
                cancelThread(key)
            }
        }
    }

}

interface RemoveWorkInterface {
    fun removeWork(key: String)
}