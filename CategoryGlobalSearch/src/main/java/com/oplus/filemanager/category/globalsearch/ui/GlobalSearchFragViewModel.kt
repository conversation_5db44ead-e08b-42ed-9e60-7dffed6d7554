/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.search.GlobalSearchFragmentViewModel
 * * Version     : 1.0
 * * Date        : 2020/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui

import android.util.ArrayMap
import android.view.MotionEvent
import androidx.activity.ComponentActivity
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.base.ThirdAppFileWrapper
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.LIST_NORMAL_MODE
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.navigation.NavigationType
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.EmojiUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.isNetworkAvailable
import com.oplus.filemanager.category.globalsearch.bean.CategoryFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.MoreFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchCardWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchDeepTipsWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterConditionManager
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterDataHelperFactory
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.util.Util
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import kotlinx.coroutines.*
import java.util.*
import kotlin.collections.ArrayList

private const val TAG = "GlobalSearchFragmentViewModel"

abstract class GlobalSearchFragmentViewModel : SelectionViewModel<BaseFileBean, BaseUiModel<BaseFileBean>>(),
    IFileOperate {
    val mModeState = BaseStateModel(MutableLiveData(LIST_NORMAL_MODE))
    val mItemClickState = MutableLiveData<BaseFileBean>()
    private var mFragmentCategory = GlobalSearchActivity.TAB_ALL
    private var filterCategory = GlobalSearchActivity.TAB_ALL
    private var mSourceData: SearchResult? = null
    private var mLoadDataMethod: (() -> Unit)? = null
    private var mFilterDataJob: Job? = null
    private var mSelectFilter: ArrayMap<Int, FilterItem>? = null
    // 是否是更多页面
    private var isMorePage = false

    internal fun init(loadData: () -> Unit, fragmentCategory: Int, filterCategory: Int = fragmentCategory, isMorePage: Boolean = false) {
        mLoadDataMethod = loadData
        mFragmentCategory = fragmentCategory
        this.filterCategory = filterCategory
        this.isMorePage = isMorePage
        // When change dark mode/font size, the data will be cached, so should clear it first
        mSourceData = null
    }

    override fun onCleared() {
        mFilterDataJob?.cancel()
        mFilterDataJob = null
    }

    override fun enterSelectionMode(key: Int): Boolean {
        // long press SearchLabelWrapper no need to enter select mode
        val item = mUiState.value?.mKeyMap?.get(key)
        if (item is SearchLabelWrapper) {
            return true
        }
        if (EmojiUtils.containsIllegalCharFileName(item?.mDisplayName)) {
            Log.d(TAG, "enterSelectionMode $item is illegal filename")
            return true
        }
        return super.enterSelectionMode(key)
    }

    override fun selectItems(keys: List<Int>): Boolean {
        val filterKeys = filterSelectRemoteFile(keys)
        // SearchLabelWrapper can not be selected
        val noneSearchLabelWrapperList = filterKeys.filterNot { key ->
            mUiState.value?.mKeyMap?.get(key) is SearchLabelWrapper ||
                    mUiState.value?.mKeyMap?.get(key) is ThirdAppFileWrapper
        }
        Log.i(TAG, "selectItems inputKeys $keys, noneSearchLabelWrapperList $noneSearchLabelWrapperList")
        return super.selectItems(noneSearchLabelWrapperList)
    }

    /**
     * 拦截处理选中远程电脑:
     * 远程电脑和其他分离文件只能选中一类
     * 分为以下场景：
     * 1. 之前没有选中，此次选中远程电脑文件-》只能选中远程电脑文件，其他分类置灰
     * 2. 之前没有选中，此次选中非远程电脑文件-》其他分类文件可选，远程电脑置灰
     * 3. 之前选中远程电脑文件，此次选中其他分类文件-》其他分类文件不可选
     * 4. 之前选中其他分类文件，此次选中远程电脑文件-》远程电脑文件置灰
     */
    private fun filterSelectRemoteFile(keys: List<Int>): List<Int> {
        // 如果有远程文件，则处理远程文件
        val containRemoteFile = isSelectRemoteFile(uiState.value?.fileList)
        if (!containRemoteFile) {
            return keys
        }
        // 已选中的文件列表
        val selectItems = getSelectItems()
        // 将要选中的文件列表
        val willSelectItems = keys.map { uiState.value?.keyMap?.get(it) }.filterNotNull().toList()
        val isLastSelectRemoteFile = if (selectItems.isEmpty()) { // 上次没有选中
            isSelectRemoteFile(willSelectItems)
        } else {
            isSelectRemoteFile(selectItems)
        }
        Log.d(TAG, "filterSelectRemoteFile last select remote file:$isLastSelectRemoteFile")
        val remoteFileList = keys.filter { key ->
            val item = mUiState.value?.mKeyMap?.get(key)
            val illegal = EmojiUtils.containsIllegalCharFileName(item?.mDisplayName)
            if (illegal) {
                Log.d(TAG, "filterSelectRemoteFile $item is illegal name")
                return@filter false
            }
            if (isLastSelectRemoteFile) {
                item is RemoteFileBean
            } else {
                (item is RemoteFileBean).not()
            }
        }
        Log.i(TAG, "filterSelectRemoteFile inputKeys $keys, select File List: $remoteFileList")
        return remoteFileList
    }

    fun getSearchKey(): String? {
        return mSourceData?.searchKey
    }

    fun onSearchDataUpdate(result: GlobalSearchViewModel.GlobalSearchResult?, searchKey: String? = result?.searchKey) {
        Log.i(TAG, "onSearchDataUpdate category $mFragmentCategory, this $this")
        mModeState.mInitState = true
        mSourceData = null
        if (result != null) {
            val dataList = result.searchResultSubList.getSubListByCategoryType(mFragmentCategory).toMutableList()
            /*Log.i(TAG, "onSearchDataUpdate category $mFragmentCategory sourceDataList Begin value ${mUiState.value}, size ${dataList.size}")
            dataList.forEach {
                Log.i(TAG, "onSearchDataUpdate filterResult ${it.mDisplayName} hasLabel:${it.mHasLabel} hash ${System.identityHashCode(it)}, it $it")
            }
            Log.i(TAG, "onSearchDataUpdate category $mFragmentCategory sourceDataList end mUiState.value ${mUiState.value}")*/
            mSourceData = SearchResult(dataList, result.uriLoadResult.mResultMap, result.searchKey)
            mSourceData?.allData = result.allResultData
            Log.i(TAG, "onSearchDataUpdate dataList ${dataList.size}, allData: ${mSourceData?.allData?.size}}")
            Log.i(TAG, "onSearchDataUpdate dataList ${dataList.map { it.mDisplayName }}, allData: ${mSourceData?.allData?.map { it.mDisplayName }}")
            launch {
                withContext(Dispatchers.IO) {
                    val selectedList = arrayListOf<Int>()
                    if ((mSourceData != null) && (mUiState.value?.mSelectedList?.isNullOrEmpty() == false)) {
                        for (selected in mUiState.value!!.mSelectedList) {
                            if (result.uriLoadResult.mResultMap.containsKey(selected)) {
                                selectedList.add(selected)
                            }
                        }
                    }
                    val filterResult = filterData()
                    withContext(Dispatchers.Main) {
                        if (filterResult.first == getSelectFiltersDescHashCode()) {
                            val resultList = mSourceData?.mResultList
                            val dataList = filterResult.second
                            resultList?.apply {
                                //删选后添加深度搜索的入口
                                if (resultList.size > 0) {
                                    val resultLastBean = resultList.last()
                                    if (dataList.isNotEmpty() && (dataList.last() is SearchDeepTipsWrapper).not()
                                        && resultLastBean is SearchDeepTipsWrapper
                                    ) {
                                        Log.d(TAG, "onSearchDataUpdate: add deepSearchTips")
                                        dataList.add(resultLastBean)
                                    }
                                }
                            }
                           /* Log.i(TAG, "onSearchDataUpdate category $mFragmentCategory BaseUiModel fileList Begin value ${mUiState.value}")
                            filterResult.second.forEach {
                                Log.i(TAG, "onSearchDataUpdate filterResult ${it.mDisplayName} hasLabel:${it.mHasLabel}, it $it")
                            }
                            Log.i(TAG, "onSearchDataUpdate category $mFragmentCategory BaseUiModel fileList End ")
                            Log.i(TAG, "onSearchDataUpdate BaseUiModel stateModel $mModeState")
                            Log.i(TAG, "onSearchDataUpdate BaseUiModel selectedList: $selectedList")
                            Log.i(TAG, "onSearchDataUpdate BaseUiModel keyMap ${result.uriLoadResult.mResultMap}")
                            Log.i(TAG, "onSearchDataUpdate BaseUiModel keyWord ${result.searchKey ?: ""}")*/
                            mUiState.value = BaseUiModel(filterResult.second, mModeState, selectedList,
                                    result.uriLoadResult.mResultMap, result.searchKey ?: "")
                        }
                    }
                }
            }
        } else {
            mUiState.value = BaseUiModel(arrayListOf(), mModeState, arrayListOf(), hashMapOf(), searchKey ?: "")
        }
    }

    fun onFilterSelectUpdate(result: ArrayMap<Int, FilterItem>, filterData: Boolean = true) {
        Log.i(TAG, "onFilterSelectUpdate input result $result, filterData $filterData")
        mSelectFilter = result
        if (!filterData) {
            return
        }
        // Start to filter data
        mSourceData?.apply {
            if (mFilterDataJob?.isActive == true) {
                mFilterDataJob!!.cancel()
            }
            mFilterDataJob = GlobalScope.launch {
                withContext(Dispatchers.IO) {
                    val filterResult = filterData()
                    withContext(Dispatchers.Main) {
                        val dataList = filterResult.second
                        mResultList.apply {
                            //删选后添加深度搜索的入口
                            if (size > 0) {
                                val resultLastBean = last()
                                if (dataList.isNotEmpty() && (dataList.last() is SearchDeepTipsWrapper).not()
                                    && resultLastBean is SearchDeepTipsWrapper
                                ) {
                                    Log.d(TAG, "onFilterSelectUpdate: add deepSearchTips")
                                    dataList.add(resultLastBean)
                                }
                            }
                        }
                        // Fix bug 844307, compare the filter desc to avoid multi-thread problem
                        if (filterResult.first == getSelectFiltersDescHashCode()) {
                            mUiState.value = BaseUiModel(dataList, mModeState, arrayListOf(),
                                    mResultMap, searchKey ?: "")
                        }
                    }
                }
            }
        }
    }

    private fun checkNeedOriginalDataToFilter(filterMap: ArrayMap<Int, FilterItem>?): Boolean {
        if (isMorePage) { // 在更多页面不需要过滤原始数据
            return false
        }
        val categoryMeet = mFragmentCategory == GlobalSearchActivity.TAB_ALL
        val filterNotEmpty = !filterMap.isNullOrEmpty()
        val result = categoryMeet && filterNotEmpty
        Log.i(TAG, "checkNeedReloadData categoryMeet $categoryMeet, filterNotEmpty $filterNotEmpty, result $result")
        return result
    }

    private fun filterData(): Pair<Long, MutableList<BaseFileBean>> {
        fun innerFilterData(): MutableList<BaseFileBean> {
            Log.d(TAG, "innerFilterData begin category:$filterCategory")
            var dataList = if (checkNeedOriginalDataToFilter(mSelectFilter)) {
                //第一个tab页同时选择的filter按钮不为空时，采用第一个
                mSourceData?.allData ?: run {
                    Log.w(TAG, "innerFilterData failed: allData data is null")
                    return mutableListOf()
                }
            } else {
                //正常情况下使用mSourceData中的分类好的数据
                mSourceData?.mResultList ?: run {
                    Log.w(TAG, "innerFilterData failed: origin data is null")
                    return mutableListOf()
                }
            }
            /*Log.i(TAG, "innerFilterData sourceDataList Begin mUiState.value ${mUiState.value}")
            dataList.forEach {
                Log.i(TAG, "innerFilterData filterResult ${it.mDisplayName} class:${it.javaClass}, it $it")
            }
            Log.i(TAG, "innerFilterData sourceDataList end mUiState.value ${mUiState.value}")*/
            // reset filter (cancel all conditions)
            dataList.forEach {
                if (it is SearchLabelWrapper) {
                    it.fileListFilter.clear()
                    it.fileListFilter.addAll(it.fileList)
                    Log.v(TAG, "innerFilterData reset label = ${it.labelEntity.name}, " +
                            "size = ${it.fileList.size}, filterSize = ${it.fileListFilter.size}")
                }
            }
            val supportFilter = FilterConditionManager.getSupportFilter(filterCategory)
            if (supportFilter.isNullOrEmpty()) {
                Log.d(TAG, "innerFilterData: supportFilter is empty")
                return dataList
            }
            // Find the support filter and filter data (Filter condition is the parent of filter item)
            supportFilter.forEach { condition ->
                mSelectFilter?.get(condition.id)?.also { item ->
                    val itemVisible = item.checkVisibleInCategory(filterCategory)
                    Log.d(TAG, "innerFilterData: find filter [${item.desc}], start filter data dataSize ${dataList.size} " +
                                "itemVisible $itemVisible, data List ${dataList.map { it.mDisplayName }}")
                    if (itemVisible) {
                        FilterDataHelperFactory.getFilterDataHelper(condition.id)?.let { dh ->
                            dataList = dh.filterData(item, dataList)
                        }
                    } else {
                        /**
                         * 当前选择的时一个不可见的filter，这里就不做筛选，使用原有数据
                         * 比如筛选中企业微信时，切换到图片/视频/音频/安装包/压缩包的tab页时，此时页面表现：列表中的数据不用企业微信来做筛选
                         */
                        Log.w(TAG, "select an invisible item $item, do not fileter, use original dataList")
                        dataList
                    }
                }
            }
            return dataList
        }

        return Pair(getSelectFiltersDescHashCode(), innerFilterData())
    }

    /**
     * Return the total of all selected filter item desc's hashcode, we not need to care its order,
     * because same filters can have the different order.
     */
    private fun getSelectFiltersDescHashCode(): Long {
        var result = 0L
        mSelectFilter?.values?.forEach {
            result += it.desc.hashCode()
        }
        return result
    }

    private fun getItemKey(item: BaseFileBean): Int? {
        return Util.getItemKey(item)
    }

    fun clickToolbarSelectAll() {
        mUiState.value?.apply {
            val isSelectRemoteFile = isRemoteFileSelected()
            val canSelectCount = getCanSelectFileSize()
            val selectedCount = mSelectedList.size
            Log.d(TAG, "selectAll remote Select：$isSelectRemoteFile, canSelect:$canSelectCount selected:$selectedCount")
            if (canSelectCount == selectedCount) { // 取消全选
                mSelectedList.clear()
            } else {
                mSelectedList.clear()
                if (isSelectRemoteFile == true) { // 当选中远程电脑文件时，全选就只全选远程电脑文件
                    val filterList = mFileList.filter {
                        it is RemoteFileBean && !EmojiUtils.containsIllegalCharFileName(it.mDisplayName)
                    }.map { getItemKey(it) }.filterNotNull().toList()
                    mUiState.value?.mSelectedList?.addAll(filterList)
                } else if (isSelectRemoteFile == false) { // 当选中非远程电脑文件时，全选就只全选非远程电脑文件(本地，云文档)
                    val filterList = mFileList.filter { isRealFile(it) }.filterNot { it is ThirdAppFileWrapper || it is RemoteFileBean }
                        .map { getItemKey(it) }.filterNotNull().toList()
                    mUiState.value?.mSelectedList?.addAll(filterList)
                } else { // 什么都没有选
                    val selectKeys = mFileList.map { getItemKey(it) }.filterNotNull().toList()
                    selectItems(selectKeys)
                }
            }
        }
        mUiState.value = mUiState.value
    }

    /**
     * 判断是否是真正的文件
     * SearchLabelWrapper can not be selected
     * CategoryFileWrapper: 分类的标题
     * MoreFileWrapper： 更多的文件
     * SearchCardWrapper: 授权卡片的bean
     */
    protected fun isRealFile(file: BaseFileBean?): Boolean {
        if (file == null) {
            return false
        }
        return (file !is SearchLabelWrapper) && (file !is CategoryFileWrapper)
                && (file !is MoreFileWrapper) && (file !is SearchCardWrapper)
                && (file !is SearchDeepTipsWrapper)
    }

    /**
     * 判断是否选中了本地文件
     */
    fun isSelectLocalFile(list: List<BaseFileBean>?): Boolean {
        if (list == null) {
            return false
        }
        return list.filter {
            it is SearchLabelWrapper || it is UriFileWrapper
        }.isNotEmpty()
    }

    /**
     * 判断是否选中的云文档
     */
    fun isSelectDriveFile(list: List<BaseFileBean>?): Boolean {
        if (list == null) {
            return false
        }
        return list.filter {
            it is DriveFileWrapper
        }.isNotEmpty()
    }

    /**
     * 是否选中了远程电脑文件
     */
    fun isSelectRemoteFile(): Boolean {
        val selectList = getSelectItems()
        return isSelectRemoteFile(selectList)
    }

    /**
     * 远程电脑文键是否选中了，分为三个状态：true,false,null
     * true:表示选中了远程电脑文件
     * false:表示选中了非远程电脑文件
     * null：表示什么都没有选中
     */
    fun isRemoteFileSelected(): Boolean? {
        val selectList = getSelectItems()
        if (selectList.isEmpty()) {
            return null
        }
        return isSelectRemoteFile(selectList)
    }

    /**
     * 是否选中了远程电脑文件
     */
    fun isSelectRemoteFile(list: List<BaseFileBean>?): Boolean {
        if (list == null) {
            return false
        }
        return list.filter { it is RemoteFileBean }.isNotEmpty()
    }

    /**
     * 获取navigation的类型
     */
    open fun getNavigationType(category: Int): NavigationType {
        Log.d(TAG, "getNavigationType category:$category")
        return when (category) {
            CategoryHelper.CATEGORY_RECYCLE_BIN -> NavigationType.RECYCLE_EDIT
            CategoryHelper.CATEGORY_DFM -> NavigationType.DFM
            CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC -> NavigationType.REMOTE_MAC
            else -> NavigationType.DEFAULT
        }
    }

    open fun getTotalFileSize(): Int {
        return getRealFileSize()
    }

    open fun getCanSelectFileSize(): Int {
        return getRealFileSize()
    }

    override fun onFileClick(
        activity: ComponentActivity,
        file: BaseFileBean,
        event: MotionEvent?,
        mediaIds: ArrayList<String>?
    ): Boolean {
        mItemClickState.postValue(file)
        return false
    }

    fun pressBack(): Boolean {
        mModeState.let {
            if (it.mListModel.value == KtConstants.LIST_SELECTED_MODE) {
                changeListMode(LIST_NORMAL_MODE)
                return true
            }
        }
        return false
    }

    override fun loadData() {
        mLoadDataMethod?.invoke()
    }

    /**
     * 下载远程电脑文件
     * @param activity activity
     * @param isOpen 是否是打开
     * @param downloadFiles 要下载的文件列表
     */
    fun downloadRemoteFile(activity: ComponentActivity, isOpen: Boolean, downloadFiles: List<BaseFileBean>) {
        // 判断是否有网
        if (!isNetworkAvailable(activity)) {
            Log.w(TAG, "downloadRemoteFile no network!!!")
            CustomToast.showLong(com.filemanager.common.R.string.no_internet_connection)
            return
        }
        // 判断是否设备连接
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val deviceId = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()?.deviceId
        if (deviceId.isNullOrEmpty()) {
            Log.w(TAG, "downloadRemoteFile device offline")
            CustomToast.showLong(com.filemanager.common.R.string.device_offline)
            return
        }
        // 跳转到下载界面
        val downloadPaths = ArrayList<Pair<String, Long>>()
        downloadFiles.forEach {
            if (it is RemoteFileBean && !it.mIsDirectory) {
                downloadPaths.add(Pair(it.originalPath, it.mSize))
            }
        }
        val code = if (isOpen || downloadPaths.size == 1) {
            MessageConstant.MSG_OPEN_REMOTE_FILE
        } else {
            MessageConstant.MSG_DOWNLOAD_REMOTE_FILE
        }
        val categoryRemoteDeviceApi = Injector.injectFactory<ICategoryRemoteDeviceApi>()
        categoryRemoteDeviceApi?.jumpDownloadActivity(activity, deviceId ?: "", downloadPaths, code)
    }

    private fun findCommon(first: List<FileLabelMappingEntity>, second: List<FileLabelMappingEntity>): List<FileLabelMappingEntity> {
        return first.filter(second::contains)
    }

    private data class SearchResult(
        val mResultList: MutableList<BaseFileBean>,
        val mResultMap: HashMap<Int, BaseFileBean>,
        var searchKey: String?
    ) {
        var allData: MutableList<BaseFileBean> = mResultList
    }
}