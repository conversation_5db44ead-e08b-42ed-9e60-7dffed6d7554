/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.module.imageloader.glide
 * * Version     : 1.0
 * * Date        : 2020/5/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.imageloader.glide;

import androidx.annotation.NonNull;

import com.bumptech.glide.load.model.ModelLoader;
import com.bumptech.glide.load.model.ModelLoaderFactory;
import com.bumptech.glide.load.model.MultiModelLoaderFactory;

import java.io.InputStream;

public class DrmCoverLoaderFactory implements ModelLoaderFactory<DrmCover, InputStream> {

    @NonNull
    @Override
    public ModelLoader<DrmCover, InputStream> build(@NonNull MultiModelLoaderFactory multiFactory) {
        return new DrmModelLoader();
    }

    @Override
    public void teardown() {

    }
}
