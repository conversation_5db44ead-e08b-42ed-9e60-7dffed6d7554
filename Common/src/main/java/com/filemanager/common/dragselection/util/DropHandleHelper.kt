/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/22, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.util

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.app.Activity
import android.graphics.Color
import android.graphics.Rect
import android.os.Looper
import android.view.DragEvent
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import com.filemanager.common.R
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.interfaces.PerformClickDir
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WindowUtils

class DropHandleHelper {

    companion object {
        private const val TAG = "DropHandleHelper"
        const val TASK_DELAY_TIME = 1400L
        private const val ANIMATION_DURATION = 200L
        private const val ANIM_REPEAT_COUNT = 2
        private const val ALPHA_VALUE_ONE = 0.08f
        private const val ALPHA_VALUE_TWO = 0.2f
        private const val CRUNCH_NUMBERS = 2
    }

    private val dragTypeList = hashSetOf(
        DropTag.Type.ITEM_VIEW_FOLDER, DropTag.Type.ITEM_VIEW, DropTag.Type.TOOLBAR_MENU,
        DropTag.Type.ITEM_VIEW_PROHIBIT, DropTag.Type.TOOLBAR_MENU_BACK, DropTag.Type.RECENT_TAB,
        DropTag.Type.MAIN_TAB
    )

    private var targetFragmentView: View? = null
    private var rootView: View? = null

    var targetItemView: View? = null
    var task: Runnable? = null
    val handler: android.os.Handler = android.os.Handler(Looper.getMainLooper())
    var itemDropTag: DropTag? = null
    var fragmentDropTag: DropTag? = null

    var dragInCallback: (view: View?, dropTag: DropTag?) -> Unit = { _, _ -> }
    var dragOutCallback: (view: View?, dropTag: DropTag?) -> Unit = { _, _ -> }
    var isScrolling = false
    var dropSideItem = false
    fun handleDropLocation(
        rootView: View?,
        detectView: View?,
        event: DragEvent?,
        activity: Activity? = null,
        isScrolling: Boolean = false,
        dropSideItem: Boolean
    ): Boolean {
        this.isScrolling = isScrolling
        this.dropSideItem = dropSideItem
        this.rootView = rootView
        val x = event?.x ?: 0f
        val y = event?.y ?: 0f
        val view = findTargetView(x, y, detectView, dragTypeList)
        if (targetItemView != view) {
            removeCallbackTask()
            val dropTag = view?.tag as? DropTag
            task = Runnable {
                view?.let { setDelayTask(dropTag, view, activity) }
            }
            task?.let {
                simulatePressed(view, dropTag?.type)
                handler.postDelayed(it, TASK_DELAY_TIME)
            }
            dragOutCallback(targetItemView, itemDropTag)
            targetItemView = view
            itemDropTag = targetItemView?.tag as? DropTag
            Log.d(TAG, "handleDropLocation itemDropTag=$itemDropTag")
            dragInCallback(targetItemView, itemDropTag)
        } else if (targetItemView == null) {
            itemDropTag = null
        }

        targetFragmentView = findTargetView(x, y, detectView, hashSetOf(DropTag.Type.FRAGMENT_VIEW, DropTag.Type.MAC_FRAGMENT_VIEW))
        fragmentDropTag = targetFragmentView?.tag as? DropTag
        Log.d(TAG, "handleDropLocation fragmentDropTag=$fragmentDropTag")
        return true
    }

    fun removeCallbackTask() {
        task?.let { handler.removeCallbacks(it) }
    }

    private fun setDelayTask(
        dropTag: DropTag?,
        view: View,
        activity: Activity?
    ) {
        val notPenetrate = (dropTag?.type != DropTag.Type.TOOLBAR_MENU && dropTag?.type != DropTag.Type.TOOLBAR_MENU_BACK)
        if (this.isScrolling && notPenetrate) return
        when (dropTag?.type) {
            DropTag.Type.ITEM_VIEW -> {
                if (dropTag.canPenetrate) {
                    view.performClick()
                    dragOutCallback(dropTag, view, activity)
                }
            }

            DropTag.Type.MAIN_TAB -> {
                view.performClick()
                dragOutCallback(dropTag, view, activity)
            }

            DropTag.Type.RECENT_TAB -> {
                view.performClick()
                dragOutCallback(dropTag, view, activity)
            }

            DropTag.Type.TOOLBAR_MENU -> {
                performRippleClick(view)
                dragOutCallback(dropTag, view, activity)
            }

            DropTag.Type.TOOLBAR_MENU_BACK -> {
                performRippleClick(view)
                targetItemView = null
                dragOutCallback(dropTag, view, activity)
            }

            DropTag.Type.ITEM_VIEW_FOLDER -> {
                startBlinkAnimation(view).addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        if (activity is PerformClickDir) {
                            dropTag.filePath?.let { activity.onClickDir(it) }
                        }
                        view.performClick()
                        super.onAnimationEnd(animation)
                    }

                    override fun onAnimationCancel(animation: Animator) {
                        if (activity is PerformClickDir) {
                            dropTag.filePath?.let { activity.onClickDir(it) }
                        }
                        view.performClick()
                        super.onAnimationCancel(animation)
                    }
                })
            }

            else -> {}
        }
    }

    private fun dragOutCallback(
        dropTag: DropTag?,
        view: View,
        activity: Activity?
    ) {
        if (!dropSideItem || activity?.let { WindowUtils.isSmallScreen(it) } == true) {
            dragOutCallback(view, dropTag)
        }
    }

    /**
     * 模拟按压状态并强制刷新
     */
    private fun simulatePressed(view: View?, type: DropTag.Type?) {
        if (type != DropTag.Type.TOOLBAR_MENU && type != DropTag.Type.TOOLBAR_MENU_BACK) {
            return
        }
        view?.apply {
            isPressed = true
            refreshDrawableState()
        }
    }

    private fun performRippleClick(view: View) {
        view.apply {
            isPressed = false
            refreshDrawableState()
            performClick()
        }
    }

    private fun findTargetView(x: Float, y: Float, view: View?, tagSet: HashSet<DropTag.Type>): View? {
        if (checkDragInView(x, y, view, tagSet)) {
            return view
        }
        if (view is ViewGroup) {
            val childCount = view.childCount
            for (i in 0 until childCount) {
                val child = view.getChildAt(i)
                val targetView = findTargetView(x, y, child, tagSet)
                if (targetView != null) {
                    return targetView
                }
            }
        }
        return null
    }

    private fun checkDragInView(x: Float, y: Float, view: View?, tagSet: HashSet<DropTag.Type>): Boolean {
        val loc = IntArray(CRUNCH_NUMBERS)
        view?.getLocationOnScreen(loc)
        val rootViewLoc = IntArray(CRUNCH_NUMBERS)
        rootView?.getLocationOnScreen(rootViewLoc)
        view?.let {
            val viewX = loc[0]
            val viewY = loc[1]
            val tag = view.tag

            val rawX = x + rootViewLoc[0]
            val rawY = y + rootViewLoc[1]
            val hasTag = tag is DropTag && tagSet.contains(tag.type)
            val isVisible = isVisible(view)
            return rawX >= viewX && rawX <= viewX + view.width && rawY >= viewY && rawY <= viewY + view.height && hasTag && isVisible
        }
        return false
    }

    private fun isVisible(view: View): Boolean {
        if (view.visibility == View.GONE || view.visibility == View.INVISIBLE) {
            return false
        }
        if (view.width <= 0 || view.height <= 0) {
            return false
        }
        val parent = view.parent as? View
        if (parent == null) {
            return false
        }
        val parentVisibleRect = Rect()
        parent.getLocalVisibleRect(parentVisibleRect)
        // view 在 parent 上方
        if (view.bottom < parent.paddingTop) {
            return false
        }
        // view 在parent 下方
        if (view.top > parentVisibleRect.bottom - parent.paddingBottom) {
            return false
        }
        return true
    }

    private fun startBlinkAnimation(view: View): ValueAnimator {
        val colorOne = ContextCompat.getColor(view.context, R.color.black_8_percent)
        val colorTwo = ContextCompat.getColor(view.context, R.color.drag_shadow_item_whole_shadow_color)
        val valueAnimator = ValueAnimator.ofObject(ArgbEvaluator(), colorOne, colorTwo).apply {
            duration = ANIMATION_DURATION
            repeatCount = ANIM_REPEAT_COUNT
            repeatMode = ValueAnimator.REVERSE
            addUpdateListener { animator ->
                view.setBackgroundColor(animator.animatedValue as Int)
            }
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animation: Animator) {
                }

                override fun onAnimationEnd(animation: Animator) {
                    view.setBackgroundColor(Color.TRANSPARENT)
                }

                override fun onAnimationCancel(animation: Animator) {
                    view.setBackgroundColor(Color.TRANSPARENT)
                }

                override fun onAnimationRepeat(animation: Animator) {
                }
            })
        }
        valueAnimator.start()
        return valueAnimator
    }

    fun resetView() {
        targetItemView = null
        itemDropTag = null
        targetFragmentView = null
        fragmentDropTag = null
    }
}