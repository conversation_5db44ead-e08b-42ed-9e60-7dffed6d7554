/*********************************************************************************
 * Copyright (C), 2008-2023, Oplus, All rights reserved.
 *
 * File: - TextPressRippleDrawable
 * Description:
 * v1.0:   copy from com.coui.appcompat.textviewcompatutil.COUITextPressRippleDrawable
 *
 * Version: 1.0
 * Date: 2024/01/05
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                 <date>       <version>      <desc>
 * ------------------------------------------------------------------------------
 * Ji<PERSON><PERSON>.<PERSON>@ROM.Apps.OppoNote       2024/1/5   1.0      Create this module
 **********************************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.RippleDrawable
import androidx.annotation.ColorInt
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.statelistutil.COUIStateListUtil
import com.coui.appcompat.textviewcompatutil.COUITextPressMaskDrawable
import com.filemanager.common.R

class TextPressRippleDrawable(context: Context) : RippleDrawable(
    COUIStateListUtil.createColorStateList(
        COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorRipplePressBackground),
        TRANSPARENT),
    ColorDrawable(TRANSPARENT),
    COUITextPressMaskDrawable()) {

    companion object {
        @ColorInt
        private val TRANSPARENT = Color.parseColor("#00000000")
    }
}