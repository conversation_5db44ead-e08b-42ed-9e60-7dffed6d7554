/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/5/6
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.constants

object EncryptConstants {
    const val FILE_ENCRYPTION_PACKGE = "com.coloros.encryption"
    const val FILE_ENCRYPTION_ACTION = "com.coloros.encryption.EncyptionService"
    const val FILE_ENCRYPTION_PACKGE_OPLUS = "com.oplus.encryption"
    const val FILE_ENCRYPTION_ACTION_OPLUS = "com.oplus.encryption.EncyptionService"
    const val BIND_ALLOW_ACTIVITY_STARTS = 512
    const val BIND_AUTO_CREATE = 1
}