/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : BitmapUtils
 * * Description : Bitmap 相关工具类
 * * Version     : 1.0
 * * Date        : 2025/05/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.PixelFormat
import android.graphics.drawable.Drawable
import android.net.Uri
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.core.graphics.drawable.toDrawable
import androidx.core.graphics.scale
import com.filemanager.common.fileutils.JavaFileHelper
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream

object BitmapUtils {

    private const val TAG = "BitmapUtils"
    private const val BITMAP_QUALITY_100 = 100
    private const val BUFFER_CAPACITY = 8 * 1024
    private const val BITMAP_WIDTH: Int = 100

    /**
     * 从uri 中创建Bitmap
     */
    @JvmStatic
    fun createBitmap(context: Context, uri: Uri): Bitmap? {
        val startTime = System.currentTimeMillis()
        try {
            val input = context.contentResolver.openInputStream(uri)
            return BitmapFactory.decodeStream(input).apply {
                Log.e(TAG, "createBitmap cost ${System.currentTimeMillis() - startTime} ms")
            }
        } catch (e: IOException) {
            Log.e(TAG, "createBitmap error", e)
            return null
        }
    }

    /**
     * 通过drawable 转成 bitmap
     */
    @JvmStatic
    fun createBitmap(drawable: Drawable): Bitmap {
        val width = drawable.intrinsicWidth
        val height = drawable.intrinsicHeight
        val config = if (drawable.opacity != PixelFormat.OPAQUE) {
            Bitmap.Config.ARGB_8888
        } else {
            Bitmap.Config.RGB_565
        }
        val bitmap = Bitmap.createBitmap(width, height, config)
        val canvas = Canvas(bitmap)
        // 必须设置
        drawable.setBounds(0, 0, width, height)
        drawable.draw(canvas)
        return bitmap
    }

    /**
     * 获取指定大小的图片
     * @param context 上下文
     * @param drawableId 图片资源id
     * @param width 指定的宽度
     * @param height 指定的高度
     */
    @JvmStatic
    fun getScaleDrawable(context: Context, @DrawableRes drawableId: Int, width: Int, height: Int): Drawable? {
        val drawable = ContextCompat.getDrawable(context, drawableId) ?: return null
        return getScaleDrawable(context, drawable, width, height)
    }

    /**
     * 获取指定大小的图片
     * @param context 上下文
     * @param drawable 图片
     * @param width 指定的宽度
     * @param height 指定的高度
     */
    @JvmStatic
    fun getScaleDrawable(context: Context, drawable: Drawable, width: Int, height: Int): Drawable? {
        val bitmap = createBitmap(drawable)
        // 调整bitmap大小
        val resizeBitmap = bitmap.scale(width, height, false)
        // 创建一个新的bitmapDrawable
        return resizeBitmap.toDrawable(context.resources)
    }

    /**
     * 保存bitmap到文件
     * @param bitmap bitmap
     * @param path 保存文件的路径
     */
    @JvmStatic
    fun save(bitmap: Bitmap, path: String) {
        val startTime = System.currentTimeMillis()
        val file = File(path)
        // 确保文件存在
        JavaFileHelper.ensureFileExist(file)
        // 保存bitmap 到文件
        kotlin.runCatching {
            FileOutputStream(path).use { out ->
                bitmap.compress(Bitmap.CompressFormat.PNG, BITMAP_QUALITY_100, out)
                out.flush()
                Log.d(TAG, "save $path cost ${System.currentTimeMillis() - startTime} ms")
            }
        }.onFailure {
            Log.e(TAG, "save error", it)
        }
    }

    /**
     * 保存输入流到路径中
     */
    @JvmStatic
    fun save(input: InputStream, path: String) {
        val startTime = System.currentTimeMillis()
        val file = File(path)
        // 确保文件存在
        JavaFileHelper.ensureFileExist(file)
        kotlin.runCatching {
            val buffer = ByteArray(BUFFER_CAPACITY)
            var len = 0
            FileOutputStream(path).use { out ->
                while ((input.read(buffer).also { len = it }) != -1) {
                    out.write(buffer, 0, len)
                }
                out.flush()
                Log.d(TAG, "save $path cost ${System.currentTimeMillis() - startTime} ms")
            }
        }.onFailure {
            Log.e(TAG, "save error", it)
        }
    }
}