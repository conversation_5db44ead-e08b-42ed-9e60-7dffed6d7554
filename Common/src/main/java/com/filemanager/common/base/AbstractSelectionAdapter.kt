/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - AbstractSelectionAdapter.kt
 ** Description: AbstractSelectionAdapter
 ** Version: 1.0
 ** Date : 2020/06/04
 ** Author: Jiafei.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/04    1.0     create
 ****************************************************************/

package com.filemanager.common.base

import androidx.recyclerview.widget.RecyclerView

abstract class AbstractSelectionAdapter<V : RecyclerView.ViewHolder, K, T> : RecyclerView.Adapter<V>() {

    var mSelectionArray: MutableList<K> = mutableListOf()

    abstract fun getItemKey(item: T, position: Int): Int?

    abstract fun getItemKeyByPosition(position: Int): Int?

    /**
     * 让action在recyclerview没有计算时执行
     */
    abstract fun checkComputingAndExecute(method: () -> Unit)

    var clickPreviewFile: BaseFileBean? = null

    fun setPreviewClickedFile(file: BaseFileBean?) {
        this.clickPreviewFile = file
        checkComputingAndExecute { notifyDataSetChanged() }
    }
}