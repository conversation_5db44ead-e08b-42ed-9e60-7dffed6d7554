package com.filemanager.common.utils;

import android.content.pm.ApplicationInfo;
import android.graphics.Bitmap;

public class AppInfo {
    private int versionCode;
    private CharSequence packageName;
    private CharSequence appName;
    private String versionName;
    private Bitmap icon;
    private ApplicationInfo appInfo;

    public AppInfo() {
    }

    public CharSequence getPackageName() {
        return this.packageName;
    }

    public void setPackageName(CharSequence packageName) {
        this.packageName = packageName;
    }

    public CharSequence getAppName() {
        return this.appName;
    }

    public void setAppName(CharSequence appName) {
        this.appName = appName;
    }

    public Bitmap getIcon() {
        return this.icon;
    }

    public void setIcon(Bitmap icon) {
        this.icon = icon;
    }

    public String getVersionName() {
        return this.versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public ApplicationInfo getApplicationInfo() {
        return this.appInfo;
    }

    public void setApplicationInfo(ApplicationInfo appInfo) {
        this.appInfo = appInfo;
    }

    public int getVersionCode() {
        return this.versionCode;
    }

    public void setVersionCode(int versionCode) {
        this.versionCode = versionCode;
    }
}
