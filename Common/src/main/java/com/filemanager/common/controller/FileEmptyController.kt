/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.util.Log
import android.view.View
import android.view.ViewGroup
import androidx.annotation.StringRes
import androidx.annotation.VisibleForTesting
import androidx.core.view.contains
import androidx.lifecycle.Lifecycle
import com.filemanager.common.R
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.AndroidDataHelper.PREF_ANDROID_DATA_ACCESS
import com.filemanager.common.utils.FileEmptyUtils
import com.filemanager.common.utils.FileEmptyUtils.FILE_EMPTY_ANIMATION_JSON
import com.filemanager.common.utils.FileEmptyUtils.FILE_EMPTY_ANIMATION_JSON_NIGHT
import com.filemanager.common.utils.FileEmptyUtils.SEARCH_EMPTY_ANIMATION_FILE
import com.filemanager.common.utils.FileEmptyUtils.SEARCH_EMPTY_ANIMATION_FILE_NIGHT
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.noMoreAction
import com.filemanager.common.view.FileEmptyView
import com.filemanager.common.view.GuideDocumentsUIView

class FileEmptyController : BaseLifeController {
    companion object {
        private const val TAG = "FileEmptyController"
        private const val DELAY_TIME = 50L
    }

    @VisibleForTesting
    var mFileEmptyView: FileEmptyView? = null
        get() = field.noMoreAction()
    @VisibleForTesting
    var mGuideDocumentsUIView: GuideDocumentsUIView? = null
        get() = field.noMoreAction()
    constructor(lifecycle: Lifecycle) {
        lifecycle.addObserver(this)
    }

    @VisibleForTesting
    fun initFileEmptyView(context: Context, rootView: ViewGroup, index: Int = 0) {
        if (mFileEmptyView == null || !rootView.contains(mFileEmptyView!!)) {
            mFileEmptyView = FileEmptyView(context)
            val layoutParams = ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT
            )
            rootView.addView(mFileEmptyView, index, layoutParams)
        }
    }

    /**
     * 显示空文件视图
     */
    @Suppress("ParameterStyleBracesRule")
    fun showFileEmptyView(
        context: Context, rootView: ViewGroup,
        asset: String = FILE_EMPTY_ANIMATION_JSON,
        emptyTitle: Int = R.string.empty_file,
        isLabelFilePage: Boolean = false,
        isFromSearchToLabelFilePage: Boolean = false
    ): View? {
        initFileEmptyView(context, rootView)
        setFileEmptyAnimation(asset)
        if (isLabelFilePage) {
            if (isFromSearchToLabelFilePage) {
                setEmptySummaryVisibilityAndContent(View.GONE, "")
                setEmptyGuidVisibilityAndContent(View.GONE, "", null)
            } else {
                setEmptySummaryVisibilityAndContent(View.VISIBLE, context.getString(R.string.label_has_no_mapping_file_tip_summary))
                setEmptyGuidVisibilityAndContent(View.VISIBLE, context.getString(R.string.label_files_add_file)) {
                    jumpToRecentFiles(context as Activity)
                }
            }
        } else {
            rootView.postDelayed({
                setFileEmptyTitle(emptyTitle)
            }, DELAY_TIME)
            setEmptySummaryVisibilityAndContent(View.GONE, "")
            setEmptyGuidVisibilityAndContent(View.GONE, "", null)
        }
        mFileEmptyView?.setEmptyVisible(View.VISIBLE)
        mGuideDocumentsUIView?.setGuideVisible(View.GONE)
        return mFileEmptyView
    }

    fun showFileEmptyView(
        context: Context,
        rootView: ViewGroup,
        asset: String = FILE_EMPTY_ANIMATION_JSON,
        title: CharSequence,
        index: Int = -1
    ): View? {
        initFileEmptyView(context, rootView, index)
        setFileEmptyAnimation(asset)
        rootView.postDelayed({
            mFileEmptyView?.setEmptyText(title)
        }, DELAY_TIME)
        setEmptySummaryVisibilityAndContent(View.GONE, "")
        setEmptyGuidVisibilityAndContent(View.GONE, "", null)
        mFileEmptyView?.setEmptyVisible(View.VISIBLE)
        mGuideDocumentsUIView?.setGuideVisible(View.GONE)
        return mFileEmptyView
    }

    @Suppress("ParameterStyleBracesRule")
    fun showFileEmptyView(
        context: Context,
        rootView: ViewGroup,
        asset: String = FILE_EMPTY_ANIMATION_JSON,
        isLabelFilePage: Boolean = false,
        isFromSearchToLabelFilePage: Boolean = false,
        addFileClickEvent: AddFileClickEvent?
    ): View? {
        initFileEmptyView(context, rootView)
        setFileEmptyAnimation(asset)
        if (isLabelFilePage) {
            if (isFromSearchToLabelFilePage) {
                setFileEmptyTitle(R.string.empty_file)
                setEmptyGuidVisibilityAndContent(View.GONE, "", null)
            } else {
                rootView.postDelayed({
                    setFileEmptyTitle(R.string.empty_file)
                    setEmptyGuidVisibilityAndContent(View.VISIBLE, context.getString(R.string.label_files_add_file)) {
                        addFileClickEvent?.onClick()
                    }
                }, DELAY_TIME)
            }
        } else {
            setEmptyGuidVisibilityAndContent(View.GONE, "", null)
        }
        mFileEmptyView?.setEmptyVisible(View.VISIBLE)
        mGuideDocumentsUIView?.setGuideVisible(View.GONE)
        setEmptySummaryVisibilityAndContent(View.GONE, "")
        return mFileEmptyView
    }

    @Suppress("ParameterStyleBracesRule")
    fun showFileEmptyView(
        context: Context,
        rootView: ViewGroup,
        asset: String = FILE_EMPTY_ANIMATION_JSON,
        addFileClickEvent: AddFileClickEvent?
    ): View? {
        initFileEmptyView(context, rootView)
        setFileEmptyTitle(R.string.empty_file,)
        setFileEmptyAnimation(asset)
        setEmptyGuidVisibilityAndContent(View.VISIBLE, context.getString(R.string.retry_string)) {
            addFileClickEvent?.onClick()
        }

        mFileEmptyView?.setEmptyVisible(View.VISIBLE)
        mGuideDocumentsUIView?.setGuideVisible(View.GONE)
        setEmptySummaryVisibilityAndContent(View.GONE, "")
        return mFileEmptyView
    }

    @VisibleForTesting
    fun setEmptyGuidVisibilityAndContent(
        visible: Int,
        content: String,
        clickListener: View.OnClickListener?
    ) {
        mFileEmptyView?.setEmptyGuidVisibilityAndContent(visible, content, clickListener)
    }

    fun setEmptyGuidContent(visible: Int, @StringRes contentRes: Int) {
        mFileEmptyView?.let {
            it.setEmptyGuidContent(visible, it.context.getString(contentRes))
        }
    }

    fun setEmptySummaryVisibilityAndContent(visible: Int, content: String) {
        mFileEmptyView?.setEmptySummaryVisibilityAndContent(visible, content)
    }

    @VisibleForTesting
    fun jumpToRecentFiles(activity: Activity) {
        val intent = Intent(Constants.START_FILE_MANAGER_ACTION)
        PreferencesUtils.put(key = Constants.CHANGE_TO_SELECT_MODE, value = true)
        intent.putExtra(Constants.KEY_IS_FROM_LABEL_FILE_LIST, true)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        activity.startActivity(intent)
        activity.finish()
    }

    fun showGuideDocumentsUIView(
        context: Context,
        rootView: ViewGroup,
        currentPath: String,
        emptyMarginTop: Int = -1,
        reloadDataFunction: (() -> Unit)? = null
    ) {
        if (mGuideDocumentsUIView == null) {
            mGuideDocumentsUIView = GuideDocumentsUIView(context).apply {
                val layoutParams = ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                openAndroidDataViewListener = View.OnClickListener {
                    PreferencesUtils.put(key = PREF_ANDROID_DATA_ACCESS, value = true)
                    AndroidDataHelper.openAndroidData = true
                    reloadDataFunction?.invoke()
                }
                rootView.addView(this, layoutParams)
            }
        }
        val showPermissionView =
            AndroidDataHelper.checkSupportViewAndroidData() &&
                    AndroidDataHelper.openAndroidData != true &&
                    AndroidDataHelper.isAndroidDataPath(currentPath) &&
                    AndroidDataHelper.canViewAndroidDataFiles
        mGuideDocumentsUIView?.showPermissionView(showPermissionView)
        mGuideDocumentsUIView?.apply {
            setCurrentPath(currentPath)
            setGuideVisible(View.VISIBLE, emptyMarginTop)
        }
        mFileEmptyView?.setEmptyVisible(View.GONE)
    }

    fun hideFileEmptyView() {
        Log.d(TAG, "hideFileEmptyView ")
        mFileEmptyView?.setEmptyVisible(View.GONE)
        mGuideDocumentsUIView?.setGuideVisible(View.GONE)
    }

    @VisibleForTesting
    fun setFileEmptyAnimation(asset: String) {
        mFileEmptyView?.let {
            when (asset) {
                FILE_EMPTY_ANIMATION_JSON -> {
                    if (Utils.isNightMode(mFileEmptyView?.context)) {
                        it.setEmptyAnimation(FILE_EMPTY_ANIMATION_JSON_NIGHT)
                    } else {
                        it.setEmptyAnimation(FILE_EMPTY_ANIMATION_JSON)
                    }
                }
                SEARCH_EMPTY_ANIMATION_FILE -> {
                    if (Utils.isNightMode(mFileEmptyView?.context)) {
                        it.setEmptyAnimation(SEARCH_EMPTY_ANIMATION_FILE_NIGHT)
                    } else {
                        it.setEmptyAnimation(SEARCH_EMPTY_ANIMATION_FILE)
                    }
                }
                FileEmptyUtils.NO_CONNECTION_ANIMATION_JSON -> {
                    if (Utils.isNightMode(mFileEmptyView?.context)) {
                        it.setEmptyAnimation(FileEmptyUtils.NO_CONNECTION_ANIMATION_JSON_NIGHT)
                    } else {
                        it.setEmptyAnimation(FileEmptyUtils.NO_CONNECTION_ANIMATION_JSON)
                    }
                }
                //支持新的empty的json动画
                FileEmptyUtils.LOAD_FAILED_ANIMATION_JSON -> {
                    if (Utils.isNightMode(mFileEmptyView?.context)) {
                        it.setEmptyAnimation(FileEmptyUtils.LOAD_FAILED_ANIMATION_JSON_NIGHT)
                    } else {
                        it.setEmptyAnimation(FileEmptyUtils.LOAD_FAILED_ANIMATION_JSON)
                    }
                }
            }
        }
    }

    fun setFileEmptyTitle(titleId: Int) {
        mFileEmptyView?.setEmptyTextViewId(titleId)
    }

    fun addExtraView(child: View) {
        mFileEmptyView?.addContentView(child)
    }

    override fun onDestroy() {
        mFileEmptyView?.setAnimationReset()
        mFileEmptyView = null
    }

    /**
     * 在分屏切换到全屏时，要刷新EmptyFile中的控件，把Icon重新显示出来
     * tablet do not need this function
     */
    fun changeEmptyFileIcon() {
        mFileEmptyView?.let {
            if (it.getEmptyVisibility() == View.VISIBLE) {
                it.setEmptyVisible(View.VISIBLE)
            }
        }
        mGuideDocumentsUIView?.let {
            if (it.getGuideDocumentsUIVisibility() == View.VISIBLE) {
                it.setGuideVisible(View.VISIBLE)
            }
        }
    }
}
interface AddFileClickEvent {
    fun onClick()
}