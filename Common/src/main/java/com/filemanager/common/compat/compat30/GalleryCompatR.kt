/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - GalleryCompat.java
 ** Description: Compat class for Gallery on android R
 ** Version: 1.0
 ** Date : 2020/05/08
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Jiafei.<PERSON>@Apps.FileManager      2020/05/08    1.0     create
 ****************************************************************/

package com.filemanager.common.compat.compat30

import android.annotation.SuppressLint
import android.content.ContentResolver.*
import android.content.ContentValues
import android.database.Cursor
import android.database.MergeCursor
import android.net.Uri
import android.os.Bundle
import android.os.CancellationSignal
import android.provider.MediaStore.Files.FileColumns
import android.provider.MediaStore.Images
import android.provider.MediaStore.Images.ImageColumns
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.compat.MediaStoreCompat.AlbumComparable
import com.filemanager.common.compat.MediaStoreCompat.DEFAULT_CAPACITY
import com.filemanager.common.compat.MediaStoreCompat.MEDIA_STORE_CAMERA_RELATIVE_PATH
import com.filemanager.common.compat.MediaStoreCompat.MYALBUM_PATH
import com.filemanager.common.compat.MediaStoreCompat.formatSqlQuerySelectionArgument
import com.filemanager.common.compat.MediaStoreCompat.getCshotId
import com.filemanager.common.compat.MediaStoreCompat.getIdsForCameraCshot
import com.filemanager.common.compat.MediaStoreCompat.getIdsForMyAlbumCshot
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_COVER_PATH
import com.filemanager.common.constants.KtConstants.KEY_IMAGE_RELATIVE_PATH
import com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.filter.IFilter
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.FolderNote
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WhiteList
import com.filemanager.common.wrapper.AlbumLoadResult
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.WebAlbumLoadResult
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.util.*
import java.util.regex.Pattern

object GalleryCompatR {
    private const val TAG = "GalleryCompatR"

    private val ALBUMSET_PROJECTION = arrayOf(
        Images.Media.BUCKET_ID,
        Images.Media.DATA,
        Images.Media.RELATIVE_PATH,
        Images.Media.BUCKET_DISPLAY_NAME,
        Images.Media.ORIENTATION,
        getMaxFunctionSqlString(Images.Media.DATE_MODIFIED),
        getMaxFunctionSqlString(Images.Media.DATE_TAKEN)
    )

    private const val ALBUMSET_COLUMN_INDEX_BUCKET_ID = 0
    private const val ALBUMSET_COLUMN_INDEX_DATA = 1
    private const val ALBUMSET_COLUMN_INDEX_RELATIVE_PATH = 2
    private const val ALBUMSET_COLUMN_INDEX_BUCKET_DISPLAY_NAME = 3
    private const val ALBUMSET_COLUMN_INDEX_ORIENTATION = 4
    private const val ALBUMSET_COLUMN_INDEX_DATE_MODIFIED = 5
    private const val ALBUMSET_COLUMN_INDEX_DATE_TAKEN = 6
    private const val ALBUMSET_COLUMN_INDEX_SIZE = 2

    /**
     * count limit to avoid OOM of cursor
     */
    private const val ALBUMSET_COUTN_LIMIT = 60000
    private const val PARAM_COLLATE_NOCASE = " COLLATE NOCASE "

    private val IMAGE_PROJECTION = arrayOf(
            Images.Media._ID,
            Images.Media.DATA,
            Images.Media.DATE_TAKEN,
            Images.Media.RELATIVE_PATH,
            Images.Media.ORIENTATION,
            Images.Media.SIZE,
            Images.Media.DATE_MODIFIED,
            Images.Media.DISPLAY_NAME
    )

    private const val IMAGE_COLUMN_INDEX_ID = 0
    private const val IMAGE_COLUMN_INDEX_DATA = 1
    private const val IMAGE_COLUMN_INDEX_DATA_TAKEN = 2
    private const val IMAGE_COLUMN_INDEX_RELATIVE_PATH = 3
    private const val IMAGE_COLUMN_INDEX_ORIENTATION = 4
    private const val IMAGE_COLUMN_INDEX_SIZE = 5
    private const val IMAGE_COLUMN_INDEX_DATE_MODIFIED = 6
    private const val IMAGE_COLUMN_INDEX_DISPLAY_NAME = 7

    /**
     * count limit to avoid OOM of cursor
     */
    private const val ALBUM_COUNT_LIMIT = 50000

    private val PATTERN_MYALBUM_RELATIVE_PATH by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        // Pattern.compile("(?i)(DCIM/MyAlbums/)([^/]+/)(Cshot/[1-9][0-9]*/)?")
        val builder = StringBuilder(DEFAULT_CAPACITY)
        builder.append("(?i)(")
        builder.append(MYALBUM_PATH)
        builder.append(")([^/]+/)(Cshot/[1-9][0-9]*/)?")
        Pattern.compile(builder.toString())
    }
    private val PATTERN_CAMERA_RELATIVE_PATH by lazy {
        Pattern.compile("(?i)(DCIM/Camera/)(Cshot/[1-9][0-9]*/)?")
    }

    private const val FILTER_SQL = "(_size > 0) AND (_display_name not like '%.psd')"

    fun getAlbumSet(): ArrayList<AlbumItem> {
        Log.d(TAG, "getAlbumSetCompatR start")
        val albumSet: ArrayList<AlbumItem> = ArrayList()
        val order = "COALESCE(${FileColumns.DATE_TAKEN}, ${FileColumns.DATE_MODIFIED} * 1000) DESC, ${FileColumns.DATE_MODIFIED} DESC"
        try {
            val queryArgs = Bundle().apply {
                putString(QUERY_ARG_SQL_SELECTION, getDateModifiedSelection())
                putString(QUERY_ARG_SQL_GROUP_BY, FileColumns.RELATIVE_PATH)
                putString(QUERY_ARG_SQL_SORT_ORDER, order)
            }
            batchQuery(
                Images.Media.EXTERNAL_CONTENT_URI,
                ALBUMSET_PROJECTION,
                queryArgs,
                cancellationSignal = null,
                ALBUMSET_COUTN_LIMIT
            )?.use { cursor ->
                var bucketId: String?
                var coverPath: String?
                var relativePath: String?
                var noteName: String?
                val note = FolderNote.getInstance()
                val whiteListMap = WhiteList.getInstance().whiteListMap
                val whiteAlbumSet = LinkedHashMap<Int, AlbumComparable>()
                val oppoAlbumSet = LinkedHashMap<Int, AlbumItem>()
                val leftAlbumSet = LinkedHashMap<Int, AlbumItem>()
                var albumItem: AlbumItem?
                var isMyAlbum: Boolean
                // 防止类似/storage/emulated/0/DCIM/Camera/Cshot/1708940533306/IMG20240226151213_BURST016.jpg 去重失败
                var relativeBucketId: Int
                var orientation: Int
                var dateModified: Long

                while (cursor.moveToNext()) {
                    bucketId = cursor.getString(ALBUMSET_COLUMN_INDEX_BUCKET_ID)
                    coverPath = cursor.getString(ALBUMSET_COLUMN_INDEX_DATA)
                    relativePath = cursor.getString(ALBUMSET_COLUMN_INDEX_RELATIVE_PATH)
                    orientation = cursor.getInt(ALBUMSET_COLUMN_INDEX_ORIENTATION)
                    dateModified = cursor.getLong(ALBUMSET_COLUMN_INDEX_DATE_MODIFIED)
                    if (dateModified == 0L) {
                        Log.d(TAG, "dateModified is 0")
                        dateModified = (FileTimeUtil.getFileTime(coverPath)?.div(SECONDS_TO_MILLISECONDS)) ?: 0
                    }
                    val dateTaken = cursor.getString(ALBUMSET_COLUMN_INDEX_DATE_TAKEN)
                    Log.i(TAG, "dateModified：$dateModified dateTaken：$dateTaken relativePath=$relativePath")
                    if (relativePath.isNullOrEmpty()) {
                        Log.d(TAG, "getAlbumSetCompatR  empty relative path")
                        continue
                    }
                    isMyAlbum = false
                    // covert the cshot directory to album directory
                    val matcher = PATTERN_MYALBUM_RELATIVE_PATH.matcher(relativePath)
                    if (matcher.matches()) {
                        relativePath = matcher.group(1)?.plus(matcher.group(2))
                        isMyAlbum = true
                    } else {
                        val matcher1 = PATTERN_CAMERA_RELATIVE_PATH.matcher(relativePath)
                        if (matcher1.matches()) {
                            relativePath = MEDIA_STORE_CAMERA_RELATIVE_PATH
                        }
                    }
                    Log.d(TAG, "getAlbumSetCompatR: $relativePath, coverPath: $coverPath")
                    if (relativePath.isNullOrEmpty()) {
                        Log.d(TAG, "getAlbumSetCompatR  empty relative path")
                        continue
                    }

                    // get notename for album
                    noteName = note.getNoteName(relativePath)
                    if (noteName.isNullOrEmpty()) {
                        if (File.separator == relativePath) {
                            noteName = appContext.resources.getString(R.string.root_album_name)
                        } else if (isMyAlbum) {
                            val parentName = matcher.group(2)
                            if (parentName?.endsWith(File.separator) == true) {
                                noteName = parentName.substring(0, parentName.length - 1)
                            }
                        } else {
                            noteName = cursor.getString(ALBUMSET_COLUMN_INDEX_BUCKET_DISPLAY_NAME)
                            if (noteName.isNullOrEmpty() && coverPath != null) {
                                val file = File(coverPath)
                                noteName = file.parentFile?.name
                            }
                        }
                    }

                    relativeBucketId = relativePath.lowercase(Locale.getDefault()).hashCode()
                    // check white list first to make sure all album in control of white list
                    val entry = whiteListMap[relativeBucketId]
                    if (entry != null) {
                        if (!whiteAlbumSet.containsKey(relativeBucketId)) {
                            albumItem = AlbumItem(coverPath, 0, noteName, relativePath, bucketId, orientation, dateModified)
                            Log.d(TAG, "getAlbumSetCompatR order: ${entry.order}")
                            whiteAlbumSet[relativeBucketId] = AlbumComparable(albumItem, entry.order)
                        }
                    } else if (isMyAlbum) {
                        if (!oppoAlbumSet.containsKey(relativeBucketId)) {
                            albumItem = AlbumItem(coverPath, 0, noteName, relativePath, bucketId, orientation, dateModified)
                            oppoAlbumSet[relativeBucketId] = albumItem
                        }
                    } else if (!leftAlbumSet.containsKey(relativeBucketId)) {
                        albumItem = AlbumItem(coverPath, 0, noteName, relativePath, bucketId, orientation, dateModified)
                        leftAlbumSet[relativeBucketId] = albumItem
                    }
                }

                // sort
                val whiteAlbumSort = ArrayList<AlbumComparable>(whiteAlbumSet.values)
                whiteAlbumSort.sort()
                for (whiteAlbum in whiteAlbumSort) {
                    Log.d(TAG, "getAlbumSetCompatR order: ${whiteAlbum.mAlbum.key}")
                    albumSet.add(whiteAlbum.mAlbum)
                }

                albumSet.addAll(oppoAlbumSet.values)
                albumSet.addAll(leftAlbumSet.values)
                Log.d(TAG, "getAlbumSetCompatR end")
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getAlbumSetCompatR error: $ex")
        }
        return albumSet
    }

    fun getLocalAlbum(value: ContentValues): AlbumLoadResult<Int> {
        val imageList = ArrayList<ImageFileWrapper>()
        val imageHashMap = HashMap<Int, ImageFileWrapper>()
        val albumLoadResult = AlbumLoadResult(imageList, imageHashMap)
        var relativePath = value.getAsString(KEY_IMAGE_RELATIVE_PATH)
        val coverPath = value.getAsString(KEY_IMAGE_COVER_PATH)
        if (relativePath.isNullOrEmpty()) {
            return albumLoadResult
        }
        Log.d(TAG, "getLocalAlbum relativePath $relativePath coverPath $coverPath")

        val matcher = PATTERN_MYALBUM_RELATIVE_PATH.matcher(relativePath)
        val builder = StringBuilder(DEFAULT_CAPACITY)
        var hasCshotImage = false
        if (matcher.matches()) {
            relativePath = matcher.group(1)?.plus(matcher.group(2)) ?: return albumLoadResult
            builder.append(FileColumns.RELATIVE_PATH)
            builder.append(PARAM_COLLATE_NOCASE)
            builder.append(" = ?")

            // apppend argumemt of the my album cshot id
            val appendSelection = formatSqlQuerySelectionArgument(getIdsForMyAlbumCshot(relativePath))
            if (appendSelection.isNotEmpty()) {
                builder.append(" OR ")
                builder.append(FileColumns._ID)
                builder.append(" IN (")
                builder.append(appendSelection)
                builder.append(")")
                hasCshotImage = true
            }
        } else {
            val matcher1 = PATTERN_CAMERA_RELATIVE_PATH.matcher(relativePath)
            if (matcher1.matches()) {
                relativePath = MEDIA_STORE_CAMERA_RELATIVE_PATH
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = ?")
                // apppend the camera album cshot
                val appendSelection = formatSqlQuerySelectionArgument(getIdsForCameraCshot())
                if (appendSelection.isNotEmpty()) {
                    builder.append(" OR ")
                    builder.append(FileColumns._ID)
                    builder.append(" IN (")
                    builder.append(appendSelection)
                    builder.append(")")
                    hasCshotImage = true
                }
            } else {
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = ?")
            }
        }
        val sortOrder = getSortOrder()
        MediaStoreCompat.addFilterMultiAppClause(builder)
        Log.d(TAG, "getLocalAlbumCompatR : sql = $builder")
        try {
            val queryArgs = Bundle()
            queryArgs.putString(QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putStringArray(QUERY_ARG_SQL_SELECTION_ARGS, arrayOf(relativePath))
            queryArgs.putString(QUERY_ARG_SQL_SORT_ORDER, sortOrder)
            batchQuery(
                Images.Media.EXTERNAL_CONTENT_URI,
                IMAGE_PROJECTION,
                queryArgs,
                null,
                ALBUM_COUNT_LIMIT
            )?.use { cursor ->
                var imageItem: ImageFileWrapper?
                var path: String?
                var datetaken: Long
                var rPath: String?
                var id: Int
                var cShotId = 0L
                var orientation: Int
                var size: Long
                var dateModified: Long
                var displayName: String

                while (cursor.moveToNext()) {
                    id = cursor.getInt(IMAGE_COLUMN_INDEX_ID)
                    path = cursor.getString(IMAGE_COLUMN_INDEX_DATA)
                    datetaken = cursor.getLong(IMAGE_COLUMN_INDEX_DATA_TAKEN)
                    rPath = cursor.getString(IMAGE_COLUMN_INDEX_RELATIVE_PATH)
                    orientation = cursor.getInt(IMAGE_COLUMN_INDEX_ORIENTATION)
                    size = cursor.getLong(IMAGE_COLUMN_INDEX_SIZE)
                    dateModified = cursor.getLong(IMAGE_COLUMN_INDEX_DATE_MODIFIED)
                    displayName = cursor.getString(IMAGE_COLUMN_INDEX_DISPLAY_NAME)
                    if (hasCshotImage) {
                        cShotId = getCshotId(rPath)
                    }
                    if (dateModified == 0L) {
                        Log.d(TAG, "dateModified is 0")
                        dateModified = (FileTimeUtil.getFileTime(path)?.div(SECONDS_TO_MILLISECONDS)) ?: 0
                    }
                    val ext = FilenameUtils.getExtension(path)
                    //若传进来的coverPath为空，此处不对文件存在性进行判断
                    if (IFilter.isTargetImageType(ext).not() && size > 0L) {
                        imageItem = ImageFileWrapper(datetaken, path, cShotId, orientation, id, size, dateModified, displayName)
                        imageList.add(imageItem)
                        imageHashMap[id] = imageItem
                    }
                }
            }
        } catch (ex: Exception) {
            Log.e(TAG, "getLocalAlbum error: $ex")
        }
        Log.d(TAG, "getLocalAlbum size:${albumLoadResult.mResultList.size}")
        return albumLoadResult
    }

    @SuppressLint("Range")
    fun getAllLocalAlbum(relativePath: String, pageNo: Int, pageSize: Int, limitPages: Int): WebAlbumLoadResult {
        val imageList = ArrayList<ImageFileWrapper>()
        var total = -1
        if ((pageSize < 1) || (pageNo < 1)) {
            Log.w(TAG, "getAllLocalAlbum -> pageSize: $pageSize or pageNo: $pageNo invalid")
            return WebAlbumLoadResult(imageList, pageNo, total)
        }
        if (relativePath.isEmpty()) {
            Log.w(TAG, "getAllLocalAlbum -> relativePath is invalid.")
            return WebAlbumLoadResult(imageList, pageNo, total)
        }
        var temPageNo = pageNo
        val builder = StringBuilder(DEFAULT_CAPACITY)
        var relativePathTemp = relativePath
        val matcher = PATTERN_MYALBUM_RELATIVE_PATH.matcher(relativePathTemp)
        var hasCshotImage = false
        if (matcher.matches()) {
            relativePathTemp = matcher.group(1)?.plus(matcher.group(2)) ?: return WebAlbumLoadResult(imageList, pageNo, total)
            builder.append(FileColumns.RELATIVE_PATH)
            builder.append(PARAM_COLLATE_NOCASE)
            builder.append(" = ?")

            // apppend argumemt of the my album cshot id
            val appendSelection = formatSqlQuerySelectionArgument(getIdsForMyAlbumCshot(relativePathTemp))
            if (appendSelection.isNotEmpty()) {
                builder.append(" OR ")
                builder.append(FileColumns._ID)
                builder.append(" IN (")
                builder.append(appendSelection)
                builder.append(")")
                hasCshotImage = true
            }
        } else {
            val matcher1 = PATTERN_CAMERA_RELATIVE_PATH.matcher(relativePathTemp)
            if (matcher1.matches()) {
                relativePathTemp = MEDIA_STORE_CAMERA_RELATIVE_PATH
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = ?")
                // apppend the camera album cshot
                val appendSelection = formatSqlQuerySelectionArgument(getIdsForCameraCshot())
                if (appendSelection.isNotEmpty()) {
                    builder.append(" OR ")
                    builder.append(FileColumns._ID)
                    builder.append(" IN (")
                    builder.append(appendSelection)
                    builder.append(")")
                    hasCshotImage = true
                }
            } else {
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = ?")
            }
        }

        MediaStoreCompat.addFilterMultiAppClause(builder)
        Log.d(TAG, "getAllLocalAlbumCompatR : sql = $builder")

        try {
            val queryArgs = Bundle()
            queryArgs.putInt(QUERY_ARG_OFFSET, pageNo * pageSize)
            queryArgs.putString(QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putStringArray(QUERY_ARG_SQL_SELECTION_ARGS, arrayOf(relativePathTemp))
            queryArgs.putString(QUERY_ARG_SQL_SORT_ORDER, ImageColumns.DATE_MODIFIED + " DESC")
            batchQuery(
                Images.Media.EXTERNAL_CONTENT_URI,
                IMAGE_PROJECTION,
                queryArgs,
                null,
                ALBUM_COUNT_LIMIT
            )?.use { cursor ->
                Log.d(TAG, "getAllLocalAlbum, count = ${cursor.count}")
                total = cursor.count
                val maxPageNo = if ((total % pageSize) > 0) {
                    total / pageSize + 1
                } else {
                    total / pageSize
                }
                if (temPageNo > maxPageNo) {
                    temPageNo = maxPageNo
                }
                cursor.moveToPosition((temPageNo - 1) * pageSize)
                addImageItems(cursor, imageList, hasCshotImage)
                while (cursor.moveToNext() && (imageList.size < pageSize * limitPages)) {
                    addImageItems(cursor, imageList, hasCshotImage)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getAllLocalAlbum error: $ex")
        }
        Log.d(TAG, "getAllLocalAlbum total: $total")
        return WebAlbumLoadResult(imageList, temPageNo, total)
    }

    private fun addImageItems(
        cursor: Cursor,
        imageList: ArrayList<ImageFileWrapper>,
        hasCshotImage: Boolean = false
    ) {
        val imageItem = ImageFileWrapper(
            cursor.getLong(IMAGE_COLUMN_INDEX_DATA_TAKEN),
            cursor.getString(IMAGE_COLUMN_INDEX_DATA),
            if (hasCshotImage) {
                getCshotId(cursor.getString(IMAGE_COLUMN_INDEX_RELATIVE_PATH))
            } else {
                0L
            },
            cursor.getInt(IMAGE_COLUMN_INDEX_ORIENTATION),
            cursor.getInt(IMAGE_COLUMN_INDEX_ID),
            cursor.getLong(IMAGE_COLUMN_INDEX_SIZE),
            cursor.getLong(IMAGE_COLUMN_INDEX_DATE_MODIFIED),
            cursor.getString(IMAGE_COLUMN_INDEX_DISPLAY_NAME)
        )
        imageList.add(imageItem)
    }

    @SuppressLint("Range")
    fun getImageItems(pageNumber: Int, pageSize: Int, sortOrder: String?): WebAlbumLoadResult {
        val imageList = ArrayList<ImageFileWrapper>()
        var total = -1
        if ((pageSize < 1) || (pageNumber < 1)) {
            return WebAlbumLoadResult(imageList, pageNumber, total)
        }
        var temPageNo = pageNumber
        val builder = StringBuilder(DEFAULT_CAPACITY)
        MediaStoreCompat.addFilterMultiAppClause(builder)
        Log.d(TAG, "getAllLocalAlbumCompatR : sql = $builder")
        try {
            val queryArgs = Bundle()
            if (sortOrder != null) {
                queryArgs.putInt(QUERY_ARG_OFFSET, pageNumber * pageSize)
            }
            queryArgs.putString(QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putString(QUERY_ARG_SQL_SORT_ORDER, sortOrder)
            batchQuery(
                Images.Media.EXTERNAL_CONTENT_URI,
                IMAGE_PROJECTION,
                queryArgs,
                null,
                ALBUM_COUNT_LIMIT
            )?.use { cursor ->
                total = cursor.count
                val maxPageNo = if ((total % pageSize) > 0) {
                    total / pageSize + 1
                } else {
                    total / pageSize
                }
                if (temPageNo > maxPageNo) {
                    temPageNo = maxPageNo
                }
                Log.d(TAG, "getImageItems -> total = $total")
                cursor.moveToPosition((temPageNo - 1) * pageSize)
                addImageItems(cursor, imageList)
                while (cursor.moveToNext() && (imageList.size < pageSize)) {
                    addImageItems(cursor, imageList)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "getImageFiles -> error: $e")
        }
        return WebAlbumLoadResult(imageList, temPageNo, total)
    }

    fun getAlbumItemCount(relativePath: String?): Int {
        if (relativePath.isNullOrEmpty()) {
            Log.d(TAG, "getAlbumItemCount relativePath is empty")
            return 0
        }
        Log.d(TAG, "新增：getAlbumItemCount relativePath is $relativePath")
        // covert the cshot directory to album directory\
        var queryPath = relativePath
        val builder = StringBuilder(DEFAULT_CAPACITY)
        var cShotItemCount = 0
        val matcher = PATTERN_MYALBUM_RELATIVE_PATH.matcher(relativePath)
        if (matcher.matches()) {
            val myAlbumRelativePath = matcher.group(1)?.plus(matcher.group(2)) ?: return 0
            builder.append(FileColumns.RELATIVE_PATH)
            builder.append(PARAM_COLLATE_NOCASE)
            builder.append(" = ?")
            queryPath = myAlbumRelativePath

            // apppend the my album cshot
            val cshotIds = getIdsForMyAlbumCshot(myAlbumRelativePath)
            cShotItemCount = cshotIds.size
            Log.d(TAG, "getAlbumItemCount relativePath1: $relativePath cshotItemCount:$cShotItemCount")
        } else {
            val matcher1 = PATTERN_CAMERA_RELATIVE_PATH.matcher(relativePath)
            if (matcher1.matches()) {
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = ?")
                queryPath = MEDIA_STORE_CAMERA_RELATIVE_PATH

                // apppend the camera album cshot
                val cShotIds = getIdsForCameraCshot()
                cShotItemCount = cShotIds.size
                Log.d(TAG, "getAlbumItemCount relativePath2: $relativePath cShotItemCount:$cShotItemCount")
            } else {
                Log.d(TAG, "getAlbumItemCount relativePath3: $relativePath")
                builder.append(FileColumns.RELATIVE_PATH)
                builder.append(PARAM_COLLATE_NOCASE)
                builder.append(" = ?")
            }
        }
        MediaStoreCompat.addFilterMultiAppClause(builder)
        Log.d(TAG, "getAlbumItemCount selection: $builder")
        try {
            val queryArgs = Bundle()
            queryArgs.putString(QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putStringArray(QUERY_ARG_SQL_SELECTION_ARGS, arrayOf(queryPath))
            val context = appContext
            context.contentResolver.query(
                Images.Media.EXTERNAL_CONTENT_URI, arrayOf(
                    FileColumns._ID, Images.Media.DATA, Images.Media.SIZE
                ), queryArgs, null
            )?.use { cursor ->
                var fileCount = 0
                while (cursor.moveToNext()) {
                    val path = cursor.getString(ALBUMSET_COLUMN_INDEX_DATA)
                    val ext = FilenameUtils.getExtension(path)
                    val size = cursor.getLong(ALBUMSET_COLUMN_INDEX_SIZE)
                    if (IFilter.isTargetImageType(ext).not() && size > 0L) {
                        fileCount++
                    }
                }
                val count = cShotItemCount + fileCount
                Log.d(TAG, "getAlbumItemCount count:$count cShotItemCount:$cShotItemCount fileCount:$fileCount")
                return count
            }
        } catch (ex: Exception) {
            Log.e(TAG, "getAlbumItemCount query: $builder")
        }
        Log.d(TAG, "getAlbumItemCount cShotItemCount:$cShotItemCount")
        return cShotItemCount
    }

    /**
     * get the string of MAX([column])
     * @param column column that need add max()
     * @return the string of MAX([column])
     */
    private fun getMaxFunctionSqlString(column: String): String = "MAX($column)"

    private fun getDateModifiedSelection(): String? {
        try {
            val queryArgs = Bundle().apply {
                putString(QUERY_ARG_SQL_SELECTION, FILTER_SQL)
                putString(QUERY_ARG_SQL_GROUP_BY, FileColumns.RELATIVE_PATH)
                putString(QUERY_ARG_SQL_SORT_ORDER, "${FileColumns.DATE_MODIFIED} DESC")
            }
            appContext.contentResolver.query(
                Images.Media.EXTERNAL_CONTENT_URI,
                arrayOf(getMaxFunctionSqlString(FileColumns.DATE_MODIFIED)),
                queryArgs,
                null
            )?.use { cursor ->
                val dateBuilder = StringBuilder(DEFAULT_CAPACITY)
                var date: String?
                var dateDeduplication: String? = null

                while (cursor.moveToNext()) {
                    date = cursor.getString(0)
                    //For Deduplication
                    dateDeduplication = if (dateBuilder.isEmpty()) {
                        date
                    } else if (dateDeduplication != date){
                        date
                    } else {
                        continue
                    }
                    dateBuilder.append(date)
                    dateBuilder.append(",")
                }

                if (dateBuilder.isEmpty()) {
                    return null
                }
                dateBuilder.deleteCharAt(dateBuilder.length - 1)
                val dates = dateBuilder.toString()
                dateBuilder.clear()
                dateBuilder.append(FileColumns.DATE_MODIFIED)
                dateBuilder.append(" IN (")
                dateBuilder.append(dates)
                dateBuilder.append(")")
                MediaStoreCompat.addFilterMultiAppClause(dateBuilder)
                //不是这里引起的
                //增加文件大小过滤
                MediaStoreCompat.addFilterSize(dateBuilder)
                //增加类型过滤
                MediaStoreCompat.addFilterType(dateBuilder)
                Log.d(TAG, "getDateModifiedSelection : sql = $dateBuilder")
                return dateBuilder.toString()
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getDateModifiedSelection query: $ex")
        }
        return null
    }

    /**
     * Batch query according to limitNum
     *
     * @param uri The URI, using the content:// scheme, for the content to
     *         retrieve.
     * @param projection A list of which columns to return. Passing null will
     *         return all columns, which is inefficient.
     * @param queryArgs A Bundle containing any arguments to the query.
     * @param cancellationSignal A signal to cancel the operation in progress, or null if none.
     * If the operation is canceled, then {@link OperationCanceledException} will be thrown
     * when the query is executed.
     * @param limitNum A number of which maximum rows per query
     * @return A Cursor object or MergeCursor object, which is positioned before the first entry. May return
     *         <code>null</code> if the underlying content provider returns <code>null</code>,
     *         or if it crashes.
     */
    @SuppressLint("Recycle")
    fun batchQuery(uri: Uri, projection: Array<String>?, queryArgs: Bundle, cancellationSignal: CancellationSignal?
                   , limitNum: Int): Cursor? {
        var offset = 0
        var batchQueryHasError = false
        var needNextBatch = true
        val cursors = ArrayList<Cursor>()
        val context = appContext

        fun clearAllCursor() {
            if (cursors.isNotEmpty()) {
                cursors.forEach {
                    it.close()
                }
                cursors.clear()
            }
        }

        try {
            while (needNextBatch && batchQueryHasError.not()) {
                queryArgs.putString(QUERY_ARG_SQL_LIMIT, "$limitNum OFFSET $offset")
                context.contentResolver.query(uri, projection, queryArgs, cancellationSignal)?.let { cursor ->
                    val count = cursor.count
                    cursors.add(cursor)
                    when {
                        //If count is equal to limit, then the next batch of queries is required
                        (count == limitNum) -> offset += limitNum
                        //If count is greater than limitNum, batchQuery has error
                        (count > limitNum) -> batchQueryHasError = true
                        //If count is less than limitNum, batchQuery finish
                        else -> needNextBatch = false
                    }
                } ?: kotlin.run {
                    batchQueryHasError = true
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "batchQuery has error:${e.message}")
            batchQueryHasError = true
        }

        if (batchQueryHasError) {
            clearAllCursor()
        }

        return when {
            cursors.size > 1 -> {
                MergeCursor(cursors.toTypedArray())
            }
            cursors.size == 1 -> {
                cursors[0]
            }
            batchQueryHasError -> {
                queryArgs.remove(QUERY_ARG_SQL_LIMIT)
                context.contentResolver.query(uri, projection, queryArgs, cancellationSignal)
            }
            else -> {
                clearAllCursor()
                null
            }
        }
    }

    fun getSortOrder(): String {
        return getOrderWithType(
            SortModeUtils.getSharedSortMode(appContext, SortModeUtils.ALBUM_FILE_SORT_RECORD),
        )
    }

    private fun getOrderWithType(sort: Int): String {
        val orderBy = when (sort) {

            SortHelper.FILE_DATE_TAKEN_ORDER -> "${ImageColumns.DATE_TAKEN} DESC"

            SortHelper.FILE_TIME_REVERSE_ORDER -> "${ImageColumns.DATE_MODIFIED} DESC"

            SortHelper.FILE_NAME_ORDER -> "${ImageColumns.DISPLAY_NAME} DESC"

            else -> "${ImageColumns.DATE_MODIFIED} DESC"
        }
        return orderBy
    }
}