/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DelegatePagerAdapter
 ** Description : Delegate Pager Adapter
 ** Version     : 1.0
 ** Date        : 2023/3/7
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2023/3/7       1.0        create
 ***********************************************************************/

package com.filemanager.common.view.viewpager;

import android.database.DataSetObserver;
import android.os.Parcelable;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

public class DelegatePagerAdapter extends PagerAdapter {

    private final PagerAdapter mAdapter;

    DelegatePagerAdapter(@NonNull final PagerAdapter adapter) {
        this.mAdapter = adapter;
        adapter.registerDataSetObserver(new SimpleDataSetObserver(this));
    }

    PagerAdapter getDelegate() {
        return mAdapter;
    }

    @Override
    public int getCount() {
        return mAdapter.getCount();
    }

    @Override
    public void startUpdate(@NonNull ViewGroup container) {
        mAdapter.startUpdate(container);
    }

    @Override
    public @NonNull
    Object instantiateItem(@NonNull ViewGroup container, int position) {
        return mAdapter.instantiateItem(container, position);
    }

    @Override
    public void destroyItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        mAdapter.destroyItem(container, position, object);
    }

    @Override
    public void setPrimaryItem(@NonNull ViewGroup container, int position, @NonNull Object object) {
        mAdapter.setPrimaryItem(container, position, object);
    }

    @Override
    public void finishUpdate(@NonNull ViewGroup container) {
        mAdapter.finishUpdate(container);
    }

    @Deprecated
    @Override
    public void startUpdate(@NonNull View container) {
        mAdapter.startUpdate(container);
    }

    @Deprecated
    @Override
    public @NonNull
    Object instantiateItem(@NonNull View container, int position) {
        return mAdapter.instantiateItem(container, position);
    }

    @Deprecated
    @Override
    public void destroyItem(@NonNull View container, int position, @NonNull Object object) {
        mAdapter.destroyItem(container, position, object);
    }

    @Deprecated
    @Override
    public void setPrimaryItem(@NonNull View container, int position, @NonNull Object object) {
        mAdapter.setPrimaryItem(container, position, object);
    }

    @Deprecated
    @Override
    public void finishUpdate(@NonNull View container) {
        mAdapter.finishUpdate(container);
    }

    @Override
    public boolean isViewFromObject(@NonNull View view, @NonNull Object object) {
        return mAdapter.isViewFromObject(view, object);
    }

    @Override
    public Parcelable saveState() {
        return mAdapter.saveState();
    }

    @Override
    public void restoreState(Parcelable state, ClassLoader loader) {
        mAdapter.restoreState(state, loader);
    }

    @Override
    public int getItemPosition(@NonNull Object object) {
        return mAdapter.getItemPosition(object);
    }

    @Override
    public void notifyDataSetChanged() {
        mAdapter.notifyDataSetChanged();
    }

    @Override
    public void registerDataSetObserver(@NonNull DataSetObserver observer) {
        mAdapter.registerDataSetObserver(observer);
    }

    @Override
    public void unregisterDataSetObserver(@NonNull DataSetObserver observer) {
        mAdapter.unregisterDataSetObserver(observer);
    }

    @Override
    public CharSequence getPageTitle(int position) {
        return mAdapter.getPageTitle(position);
    }

    @Override
    public float getPageWidth(int position) {
        return mAdapter.getPageWidth(position);
    }

    private void superNotifyDataSetChanged() {
        super.notifyDataSetChanged();
    }

    private static class SimpleDataSetObserver extends DataSetObserver {

        final DelegatePagerAdapter mAdapter;

        private SimpleDataSetObserver(DelegatePagerAdapter adapter) {
            this.mAdapter = adapter;
        }

        @Override
        public void onChanged() {
            if (mAdapter != null) {
                mAdapter.superNotifyDataSetChanged();
            }
        }

        @Override
        public void onInvalidated() {
            this.onChanged();
        }

    }

}
