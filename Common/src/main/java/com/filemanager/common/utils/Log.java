/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils;


import android.os.Bundle;
import android.text.TextUtils;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.ConstraintSet;

import com.filemanager.common.compat.PropertyCompat;
import com.filemanager.common.constants.Constants;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Set;


public class Log {
    private static final String TAG = Constants.LOG_DEFAULT_TAG;
    private static int sLevel = Constants.LOG_LEVEL;

    static {
        if (!PropertyCompat.getSLogEnable()) {
            sLevel = android.util.Log.VERBOSE;
        }
    }

    public static int getLevel() {
        return sLevel;
    }

    public static void println(int priority, String tag, String msg) {
        if (priority >= sLevel) {
            android.util.Log.println(priority, tag, handleEmptyMsg(msg));
        }
    }

    public static void v(String tag, String msg) {
        if (sLevel <= android.util.Log.VERBOSE) {
            android.util.Log.v(TAG + tag, handleEmptyMsg(msg));
        }
    }

    public static void v(String msg) {
        if (sLevel <= android.util.Log.VERBOSE) {
            android.util.Log.v(TAG, handleEmptyMsg(msg));
        }
    }

    public static void d(String tag, String msg) {
        if (sLevel <= android.util.Log.DEBUG) {
            android.util.Log.d(TAG + tag, handleEmptyMsg(msg));
        }
    }

    public static void d(String msg) {
        if (sLevel <= android.util.Log.DEBUG) {
            android.util.Log.d(TAG, handleEmptyMsg(msg));
        }
    }

    public static void i(String tag, String msg) {
        if (sLevel <= android.util.Log.INFO) {
            android.util.Log.i(TAG + tag, handleEmptyMsg(msg));
        }
    }

    public static void i(String msg) {
        if (sLevel <= android.util.Log.INFO) {
            android.util.Log.i(TAG, handleEmptyMsg(msg));
        }
    }

    public static void w(String tag, String msg) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(TAG + tag, handleEmptyMsg(msg));
        }
    }

    public static void w(String msg) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(TAG, handleEmptyMsg(msg));
        }
    }

    public static void w(String tag, String msg, Throwable ex) {
        if (sLevel <= android.util.Log.WARN) {
            android.util.Log.w(TAG + tag, handleEmptyMsg(msg), ex);
        }
    }

    public static void e(String tag, String msg) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG + tag, handleEmptyMsg(msg));
        }
    }

    public static void e(String msg) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG, handleEmptyMsg(msg));
        }
    }

    public static void e(String msg, Throwable ex) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG, handleEmptyMsg(msg), ex);
        }
    }

    public static void e(String tag, String msg, Throwable ex) {
        if (sLevel <= android.util.Log.ERROR) {
            android.util.Log.e(TAG + tag, handleEmptyMsg(msg), ex);
        }
    }

    private static String handleEmptyMsg(String msg) {
        String result = msg;
        if (TextUtils.isEmpty(msg)) {
            result = "null or empty";
        }
        return result;
    }


    public static String desensitizeUrl(String uri) {
        if (TextUtils.isEmpty(uri)) {
            return uri;
        }
        String[] split = uri.split("\\?");
        if (split.length > 1) {
            return split[1];
        }
        return uri;
    }

    public static void log(String tag, ConstraintLayout layout) {
        ConstraintSet constraintSet = new ConstraintSet();
        constraintSet.clone(layout);
        StringWriter writer = new StringWriter();
        try {
            constraintSet.writeState(writer, layout, 0);
            Log.d(TAG + tag, "logConstraint" + writer);
        } catch (IOException e) {
            Log.e(TAG + tag, "logConstraint error", e);
        }
    }

    public static void log(String tag, Bundle bundle) {
        Set<String> keySet = bundle.keySet();
        int lastKeyIndex = keySet.size() - 1;
        StringBuilder content = new StringBuilder("Bundle {");
        int index = 0;
        for (String key : keySet) {
            Object value = bundle.get(key);
            String valueType = "Null";
            if (value != null) {
                valueType = value.getClass().getTypeName();
            }
            content.append(key).append(":").append(value).append(", type:").append(valueType);
            if (index != lastKeyIndex) {
                content.append("; ");
            }
            index++;
        }
        content.append("}");
        Log.d(TAG + tag, "logBundle: " + content);
    }
}
