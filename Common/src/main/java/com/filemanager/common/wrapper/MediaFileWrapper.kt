/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.wrapper
 * * Version     : 1.0
 * * Date        : 2020/2/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.wrapper

import android.database.Cursor
import android.net.Uri
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.Log
import org.apache.commons.io.FilenameUtils
import java.io.File

open class MediaFileWrapper : UriFileWrapper {
    var mOrientation: Int = 0
    var mHeight: Int? = null
    var mWidth: Int? = null

    constructor()

    constructor(id: Int?, absPath: String?, displayName: String?, mimeType: String?, size: Long, dateModified: Long, uri: Uri?) : super() {
        createData(id, absPath, displayName, mimeType, size, dateModified, uri)
    }


    @Deprecated("Not impl",
            ReplaceWith("createData(id, absPath, displayName, mimeType, size, dateModified, uri)"),
            DeprecationLevel.HIDDEN)
    override fun createData(cursor: Cursor, uri: Uri?) {
        Log.w(TAG, "Not impl, please use the method createData(id, absPath, displayName, mimeType, size, dateModified, uri) ")
    }

    protected fun createData(id: Int?, absPath: String?, displayName: String?, mimeType: String?, size: Long, dateModified: Long, uri: Uri?) {
        mId = id
        mData = absPath
        mDisplayName = displayName
        mSize = size
        mDateModified = dateModified
        mMimeType = mimeType
        mLocalFileUri = uri?.buildUpon()?.appendPath(mId.toString())?.build()
        mData?.let {
            //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
            if (mSize == 0L) {
                val file = File(it)
                mSize = file.length()
                mDateModified = file.lastModified()
            }
        }
        // TODO: Should check the file is directory or not. (Currently we ignore the dir type because of
        //  the file properties interface has performance problem in android R)
        mLocalType = MimeTypeHelper.getTypeFromExtension(FilenameUtils.getExtension(mDisplayName))
                ?: MimeTypeHelper.UNKNOWN_TYPE
    }

    override fun getOrientation(): Int {
        return mOrientation
    }

    override fun toString(): String {
        return "MediaFileWrapper(mOrientation=$mOrientation, mHeight=$mHeight, mWidth=$mWidth)+${super.toString()}"
    }


}