/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/9
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller.navigation

import android.app.Activity
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.annotation.IdRes
import androidx.annotation.VisibleForTesting
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.coroutineScope
import com.coui.appcompat.bottomnavigation.COUINavigationItemView
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.coui.appcompat.bottomnavigation.COUINavigationView.DEFAULT_ITEM_LAYOUT_TYPE
import com.coui.appcompat.bottomnavigation.COUINavigationView.VERTICAL_ITEM_LAYOUT_TYPE
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.coui.appcompat.material.navigation.NavigationBarView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.controller.MoreItemController
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.interfaces.ActionActivityResultListener
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.view.NavigationView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class NavigationController(
    val lifecycle: Lifecycle,
    val type: NavigationType = NavigationType.DEFAULT,
    @IdRes id: Int
) : BaseLifeController, NavigationControllerInterface {
    companion object {
        private const val TAG = "NavigationController"
        /** 工具栏Item方向宽度，NavigationTool的宽度超过600，左右显示Item，否则上下 */
        private const val NAVIGATION_ORIENTATION_WIDTH = 600

        const val SCREEN_LARGE_ITEM_SIZE = 7
        const val SCREEN_MEDIUM_ITEM_SIZE = 6
        const val SCREEN_SMALL_ITEM_SIZE = 5

        fun setMenuItemEnable(menuItem: MenuItem?, enable: Boolean) {
            menuItem?.let {
                it.isEnabled = enable
            }
        }
    }

    private val mViewId = id
    @VisibleForTesting
    var mMenuType: NavigationType = type
    @VisibleForTesting
    var mNaviTool: COUINavigationView? = null
    val mHandler by lazy { Handler(Looper.getMainLooper()) }
    private var mBottomMarginView: BottomMarginDecorator? = null
    @VisibleForTesting
    var mMenuItems: HashMap<Int, MenuItem>? = null
    private var listerner: NavigationListener? = null
    private var animEndListener: NavigationAnimEndListener? = null

    @VisibleForTesting
    var opList = mutableListOf<NavigationOp>()

    @VisibleForTesting
    var isDisplay = false

    init {
        lifecycle.addObserver(this)
    }

    @VisibleForTesting
    fun initNavigationView(activity: Activity, needHideFirstTimeShow: Boolean = false) {
        if (mNaviTool == null) {
            mNaviTool = activity.findViewById<COUINavigationView>(mViewId)?.apply {
                Log.e(TAG, "initNavigationView buildMenu start...")
                mMenuItems = mMenuType.buildMenu(this)
                mMenuItems?.let { items ->
                    Log.e(TAG, "initNavigationView opList:${opList.size} ${Thread.currentThread()}")
                    opList.forEach {
                        it.invoke(mMenuType, items)
                    }
                }
                setOnItemSelectedListener(activity as? NavigationBarView.OnItemSelectedListener)
                mBottomMarginView = BottomMarginDecorator().addView(this)
                setNavigationMargin(this)
                setOnAnimatorShowHideListener(object : COUINavigationView.OnNavigationShowHideListener {

                    override fun onAnimationShowStart() {
                        Log.d(TAG, "onAnimationShowStart")
                        listerner?.animatorStartListener()
                    }

                    override fun onAnimationHideStart() {
                        listerner?.animatorStartListener()
                    }

                    override fun onAnimationShowUpdate(p0: Int) {
                    }

                    override fun onAnimationHideUpdate(p0: Int) {
                    }

                    override fun onAnimationShowEnd() {
                        Log.d(TAG, "onAnimationShowEnd")
                        listerner?.animatorEndListener()
                        animEndListener?.onShowAnimationEnd()
                    }

                    override fun onAnimationHideEnd() {
                        listerner?.animatorEndListener()
                        animEndListener?.onHideAnimationEnd()
                    }
                })
                if (needHideFirstTimeShow) {
                    visibility = View.INVISIBLE
                    post {
                        hide(false)
                    }
                }
            }
        } else {
            mNaviTool?.setOnItemSelectedListener(activity as? NavigationBarView.OnItemSelectedListener)
        }
        MoreItemController.currentNavToolSize = checkItemSize()
        Log.d(TAG, "initNavigationView currentNavToolSize = ${MoreItemController.currentNavToolSize}")
        updateToolVisibleItems()
    }

    /**
     * 根据当前容器的宽度，判断是否大屏
     * @return 大屏返回7项，中屏6项，小屏5项
     */
    @VisibleForTesting
    fun checkItemSize(): Int {
        val isLargeScreen = COUIResponsiveUtils.isLargeScreen(appContext, getContainerWidth())
        return if (isLargeScreen) {
            SCREEN_LARGE_ITEM_SIZE
        } else {
            val isMediumScreen =
                COUIResponsiveUtils.isMediumScreen(appContext, getContainerWidth())
            if (isMediumScreen) {
                SCREEN_MEDIUM_ITEM_SIZE
            } else {
                SCREEN_SMALL_ITEM_SIZE
            }
        }
    }

    fun getContainerWidth(): Int {
        val parentWidth = ((mNaviTool?.parent as? ViewGroup)?.width ?: 0)
        Log.d(TAG, "getContainerWidth parentWidth:$parentWidth")
        return parentWidth
    }

    fun prepare(activity: Activity, callback: (() -> Unit)? = null) {
        Log.d(TAG, "prepare")
        initNavigationView(activity)
        mNaviTool?.visibility = View.INVISIBLE
        mNaviTool?.hide(false)
        mNaviTool?.post {
            callback?.invoke()
        }
    }

    override fun showNavigation(activity: Activity, width: Int) {
        var widthSize = width
        initNavigationView(activity, true)
        if (mNaviTool?.visibility == View.INVISIBLE) {
            //首次显示时为隐藏状态，待hide方法执行完再show()
            mNaviTool?.post {
                setAnimationVisibility(activity, true)
            }
        } else {
            setAnimationVisibility(activity, true)
        }
        if (!StatusBarUtil.checkIsMainActivity(activity)) {
            activity.window.navigationBarColor = StatusBarUtil.getNavBarColor(activity)
        }
        if (widthSize == 0) {
            widthSize = if (SdkUtils.isAtLeastR()) {
                activity.windowManager.currentWindowMetrics.bounds.width()
            } else {
                activity.windowManager.defaultDisplay.width
            }
        }
        StatusBarUtil.resetAdaptingNavigationBarForOS12(activity)
        if (widthSize > 0) {
            val screenWidthDp = ViewHelper.px2dip(activity, widthSize)
            if (screenWidthDp > NAVIGATION_ORIENTATION_WIDTH) {
                mNaviTool?.setItemLayoutType(DEFAULT_ITEM_LAYOUT_TYPE)
            } else {
                mNaviTool?.setItemLayoutType(VERTICAL_ITEM_LAYOUT_TYPE)
            }
        }
        Log.d(TAG, "showNavigation menuType:$mMenuType")
    }

    fun setVisibility(visibility: Int) {
        mNaviTool?.visibility = visibility
    }

    override fun hideNavigation(activity: Activity) {
        opList.clear()
        mNaviTool?.apply {
            setOnItemSelectedListener(null)
            setAnimationVisibility(activity, false)
            if (!StatusBarUtil.checkIsMainActivity(activity)) {
                activity.window.navigationBarColor = COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorBackgroundWithCard)
            }
        }
        StatusBarUtil.adaptNavigationBarForOS12(activity)
    }

    override fun setNavigateItemAble(
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean,
        mHasSelectedLabelsAllPin: Boolean,
        mHasSelectedFileEmpty: Boolean,
        hasAndroidData: Boolean
    ) {
        val currentSize = checkItemSize()
        if (currentSize != MoreItemController.currentNavToolSize) {
            MoreItemController.currentNavToolSize = currentSize
            updateToolVisibleItems()
        }
        //根据控件所占宽度，决定Item显示方向（左右或上下）
        if (mMenuItems != null) {
            mMenuType.setNavigateItemAble(
                mMenuItems!!, isEnable, mHasDrm, mHasSelectedMultiLabels, mHasSelectedLabelsAllPin,
                mHasSelectedFileEmpty, hasAndroidData
            )
        } else {
            opList.removeIf {
                it is NavigationOpImpl.EnableItemOp
            }
            opList.add(
                NavigationOpImpl.EnableItemOp(
                    isEnable,
                    mHasDrm,
                    mHasSelectedMultiLabels,
                    mHasSelectedLabelsAllPin,
                    mHasSelectedFileEmpty,
                    hasAndroidData
                )
            )
        }
    }

    /**
     * 设置工具栏的每个menuitem的enable状态
     */
    fun setNavigateItemAble(
        menuEnable: IMenuEnable
    ) {
        val currentSize = checkItemSize()
        if (currentSize != MoreItemController.currentNavToolSize) {
            MoreItemController.currentNavToolSize = currentSize
            updateToolVisibleItems()
        }
        if (mMenuItems != null) {
            mMenuType.setNavigateItemAble(
                mMenuItems!!, menuEnable
            )
        }
    }

    /**
     * 调整工具栏的可见性
     */
    fun adjustToolItemsVisible() {
        MoreItemController.currentNavToolSize = checkItemSize()
        updateToolVisibleItems()
    }

    /**
     * 更新工具栏的可见项，显示7列时，复制及详情两个item可见
     */
    @VisibleForTesting
    fun updateToolVisibleItems() {
        val itemSize = checkItemSize()
        updateCopyAndDetail(itemSize)
    }

    @VisibleForTesting
    fun updateCopyAndDetail(itemSize: Int) {
        Log.d(TAG, "updateCopyAndDetail itemSize= $itemSize")
        mMenuItems?.let { menus ->
            when (itemSize) {
                SCREEN_LARGE_ITEM_SIZE -> {
                    menus[R.id.navigation_copy]?.isVisible = true
                    menus[R.id.navigation_detail]?.isVisible = true
                }
                SCREEN_MEDIUM_ITEM_SIZE -> {
                    menus[R.id.navigation_copy]?.isVisible = true
                    menus[R.id.navigation_detail]?.isVisible = false
                }
                SCREEN_SMALL_ITEM_SIZE -> {
                    menus[R.id.navigation_copy]?.isVisible = false
                    menus[R.id.navigation_detail]?.isVisible = false
                }
            }
            val width = getContainerWidth()
            if (width > 0) {
                val screenWidthDp = ViewHelper.px2dip(appContext, width)
                Log.d(TAG, "screenWidthDp = $screenWidthDp")
                if (screenWidthDp > NAVIGATION_ORIENTATION_WIDTH) {
                    mNaviTool?.setItemLayoutType(DEFAULT_ITEM_LAYOUT_TYPE)
                } else {
                    mNaviTool?.setItemLayoutType(VERTICAL_ITEM_LAYOUT_TYPE)
                }
            }
        }
    }

    private fun setNavigationMargin(naviTool: COUINavigationView) {
        val layoutParams = naviTool.layoutParams as? CoordinatorLayout.LayoutParams
        layoutParams?.apply {
            leftMargin = 0
            rightMargin = 0
        }
    }

    @VisibleForTesting
    fun setAnimationVisibility(activity: Activity?, isDisplay: Boolean) {
        if (activity == null) {
            return
        }
        activity.runOnUiThread {
            Log.d(TAG, "setAnimationVisibility display:$isDisplay $mNaviTool")
            this.isDisplay = isDisplay
            mNaviTool?.visibility = View.VISIBLE
            if (isDisplay) {
                mNaviTool?.show()
            } else {
                mNaviTool?.hide()
            }
        }
    }

    override fun onDestroy() {
        mNaviTool = null
    }

    fun modifyMenuType(type: NavigationType, activity: Activity) {
        Log.d(TAG, "modifyMenuType type = $type, mMenuType = $mMenuType")
        val menuTypeChange = mMenuType != type
        mMenuType = type
        if (mNaviTool == null) {
            initNavigationView(activity)
        } else {
            if (menuTypeChange) {
                mNaviTool?.apply {
                    mMenuItems = mMenuType.buildMenu(this)
                }
            }
        }
    }

    /**
     * 标签列表工具栏使用
     * 根据云盘是否显示、大小屏，判断显示的Items
     * 小屏：最多四个按钮，若全齐，显示更多按钮，点击更多按钮弹出：云盘、删除
     */
    fun displayItemsByParentChild(isChild: Boolean, supportCloudDisk: Boolean = false) {
        if (mMenuType != NavigationType.FILE_LABEL) return
        lifecycle.coroutineScope.launch(Dispatchers.IO) {
            val cloudDiskVisible = supportCloudDisk && checkIsDisplayCloudDisk()
            withContext(Dispatchers.Main) {
                if (mMenuItems != null) {
                    val cloudDiskItem = mMenuItems?.get(R.id.navigation_upload_cloud)
                    val deleteLabelItem = mMenuItems?.get(R.id.navigation_delete_label)
                    val labelMoreItem = mMenuItems?.get(R.id.navigation_label_more)
                    Log.e(
                        TAG,
                        "displayItemsByParentChild this:${<EMAIL>} child:$isChild cloud:$cloudDiskVisible"
                    )
                    if (isChild) {
                        //小屏，如果云盘可见，显示more。云盘可见但不可用，不显示more及云盘
                        if (cloudDiskVisible) {
                            val cloudIsEnable = cloudDiskItem?.isEnabled == true
                            cloudDiskItem?.isVisible = false
                            deleteLabelItem?.isVisible = !cloudIsEnable
                            labelMoreItem?.isVisible = cloudIsEnable
                            mNaviTool?.setItemLayoutType(VERTICAL_ITEM_LAYOUT_TYPE)
                        } else {
                            cloudDiskItem?.isVisible = false
                            labelMoreItem?.isVisible = false
                            deleteLabelItem?.isVisible = true
                        }
                    } else {
                        //大屏，不显示more
                        labelMoreItem?.isVisible = false
                        deleteLabelItem?.isVisible = true
                        cloudDiskItem?.isVisible = cloudDiskVisible
                    }
                } else {
                    opList.removeIf {
                        it is NavigationOpImpl.DisplayItemsOp
                    }
                    opList.add(NavigationOpImpl.DisplayItemsOp(isChild, cloudDiskVisible))
                }
            }
        }
    }


    fun hideNavigationDirectly(activity: Activity) {
        this.isDisplay = false
        opList.clear()
        mNaviTool?.apply {
            setOnItemSelectedListener(null)
            visibility = View.GONE
            if (!StatusBarUtil.checkIsMainActivity(activity)) {
                activity.window.navigationBarColor = COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorBackgroundWithCard)
            }
        }
        StatusBarUtil.adaptNavigationBarForOS12(activity)
    }

    @VisibleForTesting
    fun checkIsDisplayCloudDisk(): Boolean = PreferencesUtils.getBoolean(key = CommonConstants.CLOUD_FUNCTION_SHOW, default = true)

    fun setListener(l: NavigationListener?) {
        listerner = l
    }

    fun setAnimEndListener(listener: NavigationAnimEndListener?) {
        this.animEndListener = listener
    }

    /**
     * 检测此点击的Item是否属于此控制器，当一个页面有多个NavigationController时使用
     */
    fun checkIsSelfItem(menuItem: MenuItem): Boolean {
        var isSelf = false
        mMenuItems?.forEach { (_, item) ->
            if (item == menuItem) {
                isSelf = true
                return@forEach
            }
        }
        return isSelf
    }

    fun isShowNavigation(): Boolean {
        mNaviTool?.let {
            val visible = it.visibility
            if (visible == View.GONE) {
                return false
            }
            return isDisplay
        }
        return false
    }

    fun isDefaultDisplay(): Boolean {
        return mMenuType == NavigationType.DECOMPRESS_PREVIEW || mMenuType == NavigationType.RECYCLE_NORMAL
    }

    fun getNavMenuType(): NavigationType {
        return mMenuType
    }

    fun findMenu(menuId: Int): MenuItem? {
        return mMenuItems?.get(menuId)
    }

    fun updateNavigationToolPadding(navPaddingBottom: Int) {
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            return
        }
        (mNaviTool as? NavigationView)?.let {
            if (it.bottomPadding != navPaddingBottom) {
                it.updatePaddingBottom(navPaddingBottom)
            }
        }
    }
}

interface NavigationControllerInterface {
    fun showNavigation(activity: Activity, width: Int = 0)
    fun setNavigateItemAble(
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean = false,
        mHasSelectedLabelsAllPin: Boolean = false,
        mHasSelectedFileEmpty: Boolean = false,
        hasAndroidData: Boolean = false
    )

    fun hideNavigation(activity: Activity)
}

interface NavigationInterface {
    fun showNavigation()
    fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean, hasAndroidData: Boolean = false)
    fun hideNavigation()
    fun registerActionResultListener(actionActivityResultListener: ActionActivityResultListener)
}

interface NavigationInterfaceForMain : NavigationInterface {

    override fun setNavigateItemAble(isEnable: Boolean, mHasDrm: Boolean, hasAndroidData: Boolean) {
    }

    fun showNavigation(type: NavigationType, showTab: Boolean)

    fun setNavigateItemAble(
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean = false,
        mHasSelectedLabelsAllPin: Boolean = false,
        mHasSelectedFileEmpty: Boolean = false,
        hasAndroidData: Boolean = false
    )

    /**
     * 隐藏navigation动画执行完成后，通过after来回调之后要处理的事情
     * @param after 回调接口
     */
    fun hideNavigation(after: Runnable?)
}

interface NavigationListener {
    fun animatorStartListener() {
    }

    fun animatorEndListener() {
    }
}

/**
 * Navigation 动画结束的监听
 */
interface NavigationAnimEndListener {
    /**
     * 当显示动画执行完成
     */
    fun onShowAnimationEnd() {
    }

    /**
     * 当隐藏动画执行完成
     */
    fun onHideAnimationEnd() {
    }
}