/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/2/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.constants

import android.net.Uri
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.OplusUsbEnvironmentCompat
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils

object KtConstants {

    // 2. Mark is list in select/preview mode
    const val LIST_NORMAL_MODE = 1
    const val LIST_SELECTED_MODE = 2

    //Mark is it in search/normal mode of view scan way
    const val SCAN_MODE_LIST = 1
    const val SCAN_MODE_LIST_LARGE = -1
    const val SCAN_MODE_GRID = 2
    const val TOOL_BAR_CHECKBOX_MARGIN_SIZE = 9

    const val ACTION_MODE_ALPHA_DURATION = 160L

    const val MAX_PROGRESS_1000 = 1000
    const val SECONDS_TO_MILLISECONDS = 1000L

    const val STATE_UNMOUNTED = 0
    const val STATE_CHECKING = 1
    const val STATE_MOUNTED = 2
    const val P_URI = "URI"
    const val P_SQL = "SQL"
    const val P_IS_NEED_FILTER = "IS_NEED_FILTER"
    const val P_CATEGORY_TYPE = "CATEGORY_TYPE"
    const val P_CATEGORY_COUNT = "p_category_count"
    const val P_SUPER_APP_TYPE = "SUPPER_APP_TYPE"
    const val P_TAB_POSITION = "TAB_POSITION"
    const val P_CURRENT_PATH = "CurrentDir"
    const val SUPPORT_MULTIPLE_SELECTION = "SUPPORT_MULTIPLE_SELECTION"
    const val P_TITLE = "P_TITLE"
    const val P_TITLE_ID = "P_TITLE_ID"
    const val P_BUNDLE = "P_BUNDLE"
    const val P_SUPER_PATH_LIST = "P_SUPER_PATH_LIST"
    const val SUPER_DIR_DEPTH = "SUPER_DIR_DEPTH"
    const val P_INIT_LOAD = "P_INIT_LOAD"
    const val P_EXTENSION = "P_Extension"
    const val P_PATH_LIST = "P_PATH_LIST"
    const val P_SIZE_LIST = "P_SIZE_LIST"
    const val P_PACKAGE = "P_PACKAGE"
    const val P_DEVICE_ID = "P_device_id"
    const val P_DEST_PATH = "P_dest_path"
    const val P_TASK_ID = "P_task_id"
    const val P_IS_OPEN = "isOpen"
    const val P_CODE = "code"
    const val P_PENDING_INTENT = "P_Pending_Intent"
    const val P_CONTINUE_DOWNLOAD = "P_continue_download"
    const val P_HAS_FOUND_MORE_APP = "HAS_FOUND_MORE_APP"
    const val P_REMOTE_DEVICE_ID = "P_REMOTE_DEVICE_ID"
    const val P_REMOTE_DEVICE_NAME = "P_REMOTE_DEVICE_NAME"
    const val P_REMOTE_DEVICE_STATUS = "P_REMOTE_DEVICE_STATUS"
    const val P_REMOTE_DEVICE_SAME_ACCOUNT = "P_REMOTE_DEVICE_SAME_ACCOUNT"
    const val DEFAULT_OPEN_PACKAGE_NAME = "android"
    const val CURRENT_DIR = "CurrentDir"
    const val CURRENT_PATH = "CurrentPath"
    const val FILEMANAGER_TO_SECURESAFE_STRING = "filemanager_to_securesafe_string"
    const val IS_SINGLE_STORAGE = "is single storage"
    const val PKG_NAME_DOCUMENTS_UI_MAINLINE = "com.google.android.documentsui"
    const val PKG_NAME_DOCUMENTS_UI_NOT_MAINLINE = "com.android.documentsui"
    const val ALBUM_SCAN_MODE_SP_KEY = "album_scan_mode"

    const val LOCAL_VOLUME_MULTI_APP_PATH_Q_S = "/storage/emulated/999"
    const val LOCAL_VOLUME_MULTI_APP_PATH_R = "/storage/ace-999"


    const val SELECTED_KEYS = "selectedKeys"
    const val IS_SELECTED = "isSelected"
    const val SELF = "self"
    const val FROM = "from"
    const val FROM_DETAIL = "fromDetail"
    const val FROM_SHORTCUT_FOLDER = "fromShortcutFolder"
    const val FROM_OTG_LIST = "fromOTGList"
    const val DISABLE_EXIT_ALPHA_ANIMATION = "disable_exit_alpha_animation"
    const val PREVIOUS_TYPE = "previousType"
    const val P_NEED_LOAD_DATA = "loaddata"
    const val P_CHILD_DISPLAY = "childdisplay"
    const val P_SELECT_CATEGORY_TYPE = "select_category_type"
    const val P_PREVIEW_ROOT_TITLE = "p_preview_root_title"
    const val EXTRA_NOTIFICATION_ID = "extra_notification_id"
    const val P_RESET_TOOLBAR = "reset_toolbar"
    const val P_KEY_WORD = "CATEGORY_KEY_WORD"

    const val FILE_PREVIEW_NAME = "file_name_key"
    const val FILE_PREVIEW_SIZE = "file_size_key"
    const val FILE_MODIFY_TIME = "file_modify_key"
    const val FILE_ABSOLUTE_PATH = "file_absolute_path"
    const val FILE_HAS_LABEL = "file_has_label"
    const val FILE_PATH = "file_path"
    const val IS_DFM = "is_dfm"
    const val IS_DRAG_FROM_MAC = "isFromMac"
    /**
     * 跳转到远程电脑文件Activity
     * 传递参数：key为 file_path，value为目录
     */
    const val DEEPLINK_REMOTE_PC = "filemanager://deeplink.super.remotepc"
    const val SIZE = "size"
    const val FILE_URI = "file_uri"
    const val FROM_THIRD_APP = "from_third_app"
    const val FILE_NAME = "file_name"
    const val FILE_EXT = "file_ext"
    const val HAS_STORAGE_PERMISSION = "has_storage_permission"

    const val SEARCH_LOAD_TIME_GAP = 1000L
    const val TIME_GAP_ONE_SECOND = 1000L
    const val REGION_GDPR = "gdpr"

    const val ACTION_USER_INFO = "oplus.filemanager.action.personal.user.information"
    const val ACTION_AGREEMENT_PAGE = "com.coloros.filemanager.action.user.agreement"
    const val ACTION_PERSONAL_INFO = "com.coloros.filemanager.action.personal.info"

    const val QUICK_PREVIEW_PACKAGE = "andes.oplus.documentsreader"
    const val SETTING_PACKAGE = "com.android.settings"

    const val DFM_DEVICE_NAME = "device_name"
    const val DFM_MOUNT_PATH = "mount_path"
    const val DFM_TOTAL_SIZE = "total_size"
    const val DFM_AVAILABLE_SIZE = "available_size"
    const val DFM_DEVICE_TYPE = "device_type"
    const val ACTION_DFS_DISCONNECT = "com.oplus.filemanger.dfm.ACTION_DFS_DISCONNECT"
    const val DFM_MOUNT_PATH_SUFFIX = "/mnt/dfs/"
    const val DFM_PHONE_TYPE = 8
    const val DFM_PAD_TYPE = 10
    const val DFM_ROOT_PATH_SIZE = 4
    const val CONNECT_TIME = "connect_time"
    const val DFM_JUMP_ALL_TAB = "jump_all_tab"
    const val SEARCH_WORD = "search_word"

    const val SIDE_NAVIGATION_OPEN = 1
    const val SIDE_NAVIGATION_CLOSE = 2

    const val PREVIEW_OPEN = 1
    const val PREVIEW_CLOSE = 2

    const val DELAY_LOAD_DATA_TIME = 300L
    const val KEY_IMAGE_RELATIVE_PATH = "images_relative_path"
    const val KEY_IMAGE_COVER_PATH = "image_cover_path"

    const val FILE_MANAGER_URI_PREFIX = "content://com.coloros.filemanager/root"
    const val FILE_MANAGER_URI_PREFIX_ONEPLUS = "content://com.oneplus.filemanager/root"
    const val DOCUMENT_READER_URI_PREFIX = "content://andes.oplus.documentsreader/"

    const val ACTION_DRAG_FILE_CHANGED = "oplus.intent.action.ACTION_DRAG_FILE_CHANGED"
    const val DRAG_APP_PACKAGE = "DRAG_APP_PACKAGE"
    const val DRAG_OPERATE = "DRAG_OPERATE"
    const val DRAG_OP_DELETE_TO_RECYCLE_BIN = "DRAG_OP_DELETE_TO_RECYCLE_BIN"
    const val DRAG_OP_CUT = "DRAG_OP_CUT"
    const val DRAG_OP_ENCRYPT = "DRAG_OP_ENCRYPT"
    const val PROTECT_PERMISSION = "com.oplus.permission.safe.PROTECT"

    const val FILE_NOT_EXIST = 105
    const val FILE_NO_EXIST = 101
    const val FILE_NAME_NULL = 102
    const val OPERATE_OK = 200
    const val OPERATE_FAILED = 300

    const val CLASS_ITEM_TYPE = "class_item_type"
    const val CLASS_ITEM_TYPE_NEED_LOAD = "class_item_type_is_need_load"
    const val LIMIT_COUNT = "LIMIT_COUNT"
    const val BADGE_SKIP_ANIMA = "badge_skip_anima"
    const val COM_OPLUS_VIEW_DRAGBADGE = "com.oplus.view.DRAGBADGE"
    const val BADGE_STATUS = "badge_status"

    //三方应用包名
    const val PKG_BLUE_TOOTH =  "com.oplus.blue.tooth"
    const val PKG_DOWNLOAD =   "com.oplus.download"
    const val PKG_PCCONNECT =  "com.oplus.pcconnect"
    const val PKG_OWORK =  "com.oplus.owork"
    const val PKG_OPPO_SHARE =  "com.oplus.oppo.share"
    const val PKG_REALME_SHARE =   "com.oplus.realme.share"
    const val PKG_ONE_PLUS_SHARE =   "com.oplus.oneplus.share"

    /**
     * Uncertain properties, may be discarded on Android-R in the feature
     */
    @JvmField
    var LOCAL_VOLUME_MULTI_APP_PATH: String = when {
        // todo S maybe can use OplusUsbEnvironment.getMultiappSdDirectory()
        SdkUtils.isAtLeastS() -> LOCAL_VOLUME_MULTI_APP_PATH_Q_S
        SdkUtils.isAtLeastR() -> LOCAL_VOLUME_MULTI_APP_PATH_R
        else -> {
            try {
                OplusUsbEnvironmentCompat.getMultiappSdDirectory() ?: LOCAL_VOLUME_MULTI_APP_PATH_Q_S
            } catch (e: Exception) {
                Log.e("getMultiappSdDirectory error -> ${e.message}")
                LOCAL_VOLUME_MULTI_APP_PATH_Q_S
            }
        }
    }

    @JvmStatic
    val OTA_URI: Uri = Uri.parse("content://com.oppo.ota")

    @JvmStatic
    val OTA_OPLUS_URI: Uri = Uri.parse("content://com.oplus.ota")

    @JvmStatic
    val AUTHORITIES by lazy {
        MyApplication.sAppContext.packageName
    }

    @JvmStatic
    val AUTHORITIES_FILE by lazy {
        AUTHORITIES.plus(".fileprovider")
    }
}