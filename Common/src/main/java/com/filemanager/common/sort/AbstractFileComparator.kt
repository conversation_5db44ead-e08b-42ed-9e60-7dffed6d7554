/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2020/2/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.wrapper.RecycleFileWrapper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import java.util.*

abstract class AbstractFileComparator : FileBeanComparatorGenerator {

    override fun genComparator(): Comparator<BaseFileBean> {
        return Comparator { o1, o2 ->
            val type1 = getType(o1)
            val type2 = getType(o2)
            if (type1 != type2) {
                type1 - type2
            } else {
                compare1(o1, o2, true)
            }
        }
    }

    fun genSearchAllComparator(): Comparator<BaseFileBean> {
        return Comparator { o1, o2 ->
            val type1 = if (o1.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) TYPE_DIRECTORY else TYPE_NOT_DIRECTORY
            val type2 = if (o2.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) TYPE_DIRECTORY else TYPE_NOT_DIRECTORY
            if (type1 != type2) {
                type1 - type2
            } else {
                compare1(o1, o2, true)
            }
        }
    }

    fun genCategoryComparator(isDesc: Boolean = true): Comparator<BaseFileBean> {
        return Comparator { o1, o2 ->
            if (isDesc) {
                compare1(o1, o2, true)
            } else {
                compare1(o2, o1, false)
            }
        }
    }


    open fun genRecycleFileComparator(isDesc: Boolean = true): Comparator<RecycleFileWrapper> {
        return Comparator<RecycleFileWrapper> { o1, o2 ->
            val type1 = if (o1?.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) TYPE_DIRECTORY else TYPE_NOT_DIRECTORY
            val type2 = if (o2?.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) TYPE_DIRECTORY else TYPE_NOT_DIRECTORY
            if (type1 != type2) {
                type1 - type2
            } else {
                if (isDesc) {
                    compareRecycleFileWrapper(o1, o2, true)
                } else {
                    compareRecycleFileWrapper(o2, o1, false)
                }
            }
        }
    }


    override fun genReverseComparator(): Comparator<BaseFileBean> {
        return Comparator { o1, o2 ->
            val type1 = getType(o1)
            val type2 = getType(o2)
            if (type1 != type2) {
                type1 - type2
            } else {
                compare1(o2, o1, false)
            }
        }
    }

    /**
     * 排序规则
     */
    abstract fun compare1(o1: BaseFileBean, o2: BaseFileBean, isDesc: Boolean): Int

    open fun compareRecycleFileWrapper(o1: RecycleFileWrapper, o2: RecycleFileWrapper, isDesc: Boolean): Int {
        return 0
    }

    private fun getType(file: BaseFileBean): Int {
        return if (MimeTypeHelper.DIRECTORY_TYPE == file.mLocalType) {
            TYPE_DIRECTORY
        } else {
            TYPE_NOT_DIRECTORY
        }
    }

    companion object {

        private val TYPE_DIRECTORY = 1
        private val TYPE_NOT_DIRECTORY = 2
    }
}