/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/2/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.net.Uri
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.Companion.UNKNOWN_TYPE
import com.filemanager.common.utils.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout

open class BaseFileBean {
    companion object {
        const val TAG = "BaseFileBean"
        const val TYPE_FILE_LIST_HEADER: Int = 101
        const val TYPE_RELATED_FILE: Int = 102
        const val TYPE_LABEL_FILE: Int = 103
        const val TYPE_FILE_LIST_FOOTER: Int = 104
        const val TYPE_FILE_AD: Int = 105
        const val TYPE_DOC_GROUP_TITLE: Int = 106
        const val TYPE_SEARCH_THIRD_APP_FILE: Int = 107
        const val FILE_TIME_OUT = 1000L
    }

    var mLocalFileUri: Uri? = null
        get() {
            return localUri(field)
        }

    // File absolute path
    var mData: String? = null
        get() {
            return localPath(field)
        }
    var mMimeType: String? = null
    var mIsDirectory = false
        get() {
            return isDir()
        }
    open var mDisplayName: String? = null
    open var mSize: Long = 0
    open var mDateModified: Long = 0
    open var mLocalType: Int = UNKNOWN_TYPE

    var fileWrapperLabel: Int? = null

    /**
     * Add legacy offending naming alias for corrected naming member [fileWrapperLabel].
     * Pls merge [fileWrapperLabel] and [mFileWrapperLabel] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("fileWrapperLabel"))
    var mFileWrapperLabel: Int?
        get() = fileWrapperLabel
        set(value) = ::fileWrapperLabel.set(value)

    var mFileWrapperViewType: Int? = null
    var mFileWrapperTypeNum: Int? = null
    var mHasLabel = false
    var mMediaDuration = 0L
    var isString = false
    var stringData: String? = null
    var virtualFileHashCode: String? = null
    var originPackage: String? = null
    /** 最近打开时间，文档页面显示使用，若无此值，使用最后修改时间 */
    var lastOpenTime: Long = 0L

    var letter: String? = null
    var recallType: Int = -1
    var highlight: HighLightEntity? = null

    suspend fun checkExist(): Boolean {
        return try {
            withTimeout(FILE_TIME_OUT) {
                withContext(Dispatchers.IO) {
                    JavaFileHelper.exists(this@BaseFileBean)
                }
            }
        } catch (ex: TimeoutCancellationException) {
            Log.d(TAG, "checkExist timeout")
            return false
        }
    }

    override fun toString(): String {
        return "BaseFileBean(mLocalFileUri=$mLocalFileUri, mData=$mData, mDisplayName=$mDisplayName," +
                " mSize=$mSize, mDateModified=$mDateModified," + " mMimeType=$mMimeType," +
                " mLocalType=$mLocalType, fileWrapperLabel=$fileWrapperLabel," +
                " mFileWrapperViewType=$mFileWrapperViewType, mFileWrapperTypeNum=$mFileWrapperTypeNum, mHasLabel=$mHasLabel," +
                " originPackage=$originPackage)"
    }

    open fun getOrientation(): Int {
        return 0
    }

    open fun isDir(): Boolean {
        return mLocalType == MimeTypeHelper.DIRECTORY_TYPE
    }

    open fun localUri(field: Uri?): Uri? {
        return field
    }

    open fun localPath(field: String?): String? {
        return field
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BaseFileBean

        if (mData != other.mData) return false
        if (mMimeType != other.mMimeType) return false
        if (mIsDirectory != other.mIsDirectory) return false
        if (mDisplayName != other.mDisplayName) return false
        if (mSize != other.mSize) return false
        return mDateModified == other.mDateModified
    }

    override fun hashCode(): Int {
        var result = mMimeType?.hashCode() ?: 0
        result = 31 * result + (mDisplayName?.hashCode() ?: 0)
        result = 31 * result + mSize.hashCode()
        result = 31 * result + mDateModified.hashCode()
        return result
    }
}

class HighLightEntity {
    var title: ContentEntity? = null
    var content: ContentEntity? = null
    val titleKeyList: List<String>?
        get() {
            val list = mutableListOf<String>()
            val positionList = title?.position?.split(" ")
            val indexSize = (positionList?.size ?: 0) / 2
            Log.d(BaseFileBean.TAG, "HighLightEntity indexSize $indexSize")
            if (positionList == null || indexSize == 0) {
                return list
            }
            for (i in 0 until indexSize) {
                title?.content?.substring(
                    positionList[i * 2].toInt(),
                    positionList[i * 2 + 1].toInt()
                )?.let { key ->
                    list.add(key)
                    Log.d(BaseFileBean.TAG, "HighLightEntity key $key")
                }
            }
            return list
        }
    var contentHighLightStart = 0
        get() {
            return content?.position?.split(" ")?.get(0)?.toInt() ?: 0
        }
    var contentHighLightEnd = 0
        get() {
            return content?.position?.split(" ")?.get(1)?.toInt() ?: 0
        }
}

class ContentEntity {
    var content: String? = null
    var position: String? = null
}