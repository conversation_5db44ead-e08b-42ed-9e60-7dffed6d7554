/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.utils.ShortCutUtils
 * * Description : for shortcut
 * * Version     : 1.0
 * * Date        : 2024/5/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.app.PendingIntent
import android.content.ContentUris
import android.content.Context
import android.content.Intent
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.drawable.Drawable
import android.graphics.drawable.Icon
import android.net.Uri
import android.os.PersistableBundle
import android.provider.ContactsContract
import android.provider.Settings
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import androidx.core.content.FileProvider
import androidx.core.graphics.drawable.toDrawable
import androidx.lifecycle.lifecycleScope
import com.bumptech.glide.Glide
import com.bumptech.glide.load.Transformation
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.signature.ObjectKey
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.MimeType
import com.filemanager.common.imageloader.glide.DrmCover
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_NONE
import com.filemanager.common.imageloader.glide.RoundedCornersBitmapTransformation
import com.filemanager.common.utils.FileImageLoader.Companion.VIDEO_FRAME_VALUE
import com.filemanager.thumbnail.FileThumbnailSignature
import com.filemanager.thumbnail.ThumbnailManager
import com.filemanager.thumbnail.audio.AudioThumbnailNew
import com.filemanager.thumbnail.audio.AudioThumbnailResult
import com.filemanager.thumbnail.audio.AudioThumbnailTransformation
import com.filemanager.thumbnail.doc.DocThumbnail
import com.google.gson.JsonSyntaxException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.lang.NumberFormatException

object ShortCutUtils {

    private const val TAG = "ShortCutUtils"

    // 快捷方式内容宽高
    private const val ICON_WH = 120
    private const val ICON_PADDING = 20

    // 不显示桌面创建快捷方式弹窗
    private const val SKIP_CONFIRM = "SKIP_CONFIRM"

    // target activity action
    private const val ACTION_FILE_MANAGER_SHORTCUT = "oplus.intent.action.filemanager.SHORTCUT"
    private const val FILE_MANAGER_SHORTCUT_ACTIVITY = "com.oplus.filemanager.main.ui.ShortCutActivity"

    // target activity file params
    private const val KEY_FILE_BEAN = "fileBaseBean"
    private const val KEY_FILE_PATH = "filePath"
    private const val KEY_DISPLAY_NAME = "displayName"
    private const val KEY_LOCAL_TYPE = "localType"
    private const val KEY_SIZE = "size"
    private const val KEY_FILE_MODIFIED = "fileModified"

    // 快捷方式是否来自于文管
    @VisibleForTesting
    const val KEY_SHORTCUT_FORM_FILM_MANAGER = "shortcut_from_file_manager"
    private const val IMAGE_FILE_TYPE = "file-type"
    private const val IMAGE_FILE_TYPE_FILE_MANAGER = 2

    /**
     * 打开指定文件夹-手机存储页面
     *  CurrentDir
     * */
    const val START_FILE_BROWSER = "oplus.intent.action.filemanager.BROWSER_FILE"

    /**
     * shortcut id list
     */
    private const val SHORTCUT_ID_LIST = "file_manager_shortcut_id_list"

    /**
     * 跳转相册大图页传参，是否只含一个id
     */
    private const val SINGLE_MEDIA_ID = "single-media-id"

    /**
     *跳转相册大图页，控制缩略图轴不显示
     */
    private const val ENABLE_THUMB_LINE_PREVIEW = "enable_thumbLine_preview"

    /**
     * 查看文管注册的PinnedShortcutsList是否有失效的
     * 文管onResume时IO中执行，防止影响文管启动时的性能
     */
    @JvmStatic
    fun checkShortCuts(context: BaseVMActivity, paths: List<String>? = null) {
        Log.d(TAG, "checkDisableShortCut in thread")
        context.lifecycleScope.launch(Dispatchers.IO) {
            // 2.更新Shortcut
            checkUpdateShortCut(context, paths)
            //3.将shortcut id 写入 SettingProvider
            writeIdsIntoSettingProvider(context)
        }
    }

    /**
     * 检查是否有要更新快捷方式
     */
    @JvmStatic
    private fun checkUpdateShortCut(context: Context, paths: List<String>? = null) {
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        // pinnedShortcutsListNew和checkDisableShortCut里的数量一样，但是enable数量可能不同
        val pinnedShortcutsListNew = shortcutManager.pinnedShortcuts
        val updatePinnedShortcutsList = ArrayList<ShortcutInfo>()
        Log.d(TAG, "Update pinnedShortcutsListNew size ${pinnedShortcutsListNew.size}")
        for (shortcutItem in pinnedShortcutsListNew) {
            val isFromFileManager = shortcutItem.intent?.extras?.getBoolean(KEY_SHORTCUT_FORM_FILM_MANAGER)
            val isExist = checkFileIsExist(shortcutItem.id)
            // 跳过的情况
            if (isFromFileManager == false || !isExist) {
                continue
            }
            // 跳过的情况--没有缩略图的类型不更新
            val localType = shortcutItem.intent?.extras?.getInt(KEY_LOCAL_TYPE) ?: 0
            if (!isHasThumbnailType(localType)) {
                continue
            }
            // 获取源文件信息
            val file = getFile(shortcutItem.id)
            // 判断mDateModified是否相同，不同则需要更新
            val oldDateModified = shortcutItem.intent?.extras?.getLong(KEY_FILE_MODIFIED, 0L)
            val lastModified = file?.lastModified() ?: 0L
            Log.d(TAG, "oldDateModified $oldDateModified")
            Log.d(TAG, "lastModified $lastModified")
            if (oldDateModified != lastModified) {
                Log.d(TAG, "update id： ${shortcutItem.id}")
                val fileBean = BaseFileBean()
                fileBean.mData = shortcutItem.id
                fileBean.mDisplayName = FilenameUtils.getName(shortcutItem.id)
                fileBean.mSize = file?.length() ?: 0
                fileBean.mDateModified = file?.lastModified() ?: 0
                fileBean.mLocalType = localType
                val shortcutInfo = getShortCutInfo(context, fileBean)
                updatePinnedShortcutsList.add(shortcutInfo)
            }
            if (paths?.contains(shortcutItem.id) == true) {
                val fileBean = BaseFileBean()
                fileBean.mData = shortcutItem.id
                fileBean.mDisplayName = FilenameUtils.getName(shortcutItem.id)
                fileBean.mSize = file?.length() ?: 0
                fileBean.mDateModified = file?.lastModified() ?: 0
                fileBean.mLocalType = localType
                val shortcutInfo = getShortCutInfo(context, fileBean)
                updatePinnedShortcutsList.add(shortcutInfo)
            }
        }
        updateShortCuts(context, updatePinnedShortcutsList)
    }

    /**
     * 检查是否有失效的快捷方式
     */
    @JvmStatic
    fun checkDisableShortCut(context: Context) {
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        val pinnedShortcutsList = shortcutManager.pinnedShortcuts
        Log.d(TAG, "pinnedShortcutsList size ${pinnedShortcutsList.size}")
        val disablePinnedShortcutsList = ArrayList<String>()
        for (shortcutItem in pinnedShortcutsList) {
            val isFromFileManager = shortcutItem.intent?.extras?.getBoolean(KEY_SHORTCUT_FORM_FILM_MANAGER)
            val isExist = checkFileIsExist(shortcutItem.id)
            if (isFromFileManager == true && !isExist) {
                disablePinnedShortcutsList.add(shortcutItem.id)
                Log.d(TAG, "disable id： ${shortcutItem.id}")
            }
        }
        disableShortCuts(context, disablePinnedShortcutsList)
    }

    /**
     * 将快捷方式id写入SettingProvider
     */
    @JvmStatic
    fun writeIdsIntoSettingProvider(context: Context) {
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        val pinnedShortcutsList = shortcutManager.pinnedShortcuts
        Log.d(TAG, "pinnedShortcutsList size ${pinnedShortcutsList.size}")
        val shortcutsList = ArrayList<String>()
        for (shortcutItem in pinnedShortcutsList) {
            try {
                val uri = shortcutItem.intent?.data
                uri?.let {
                    val id = ContentUris.parseId(it).toString()
                    shortcutsList.add(id)
                }
            } catch (e: NumberFormatException) {
                Log.d(e.message)
            }
        }
        try {
            Settings.Global.putString(
                context.contentResolver,
                SHORTCUT_ID_LIST,
                shortcutsList.toString()
            )
        } catch (e: SecurityException) {
            Log.d(e.message)
        }
    }

    @JvmStatic
    fun addIdsIntoSettingProvider(context: Context, uri: Uri?) {
        val id = uri?.let {
            try {
                ContentUris.parseId(it)
            } catch (e: NumberFormatException) {
                null
            }
        }
        if (id == null) return
        val ids = Settings.Global.getString(
            context.contentResolver,
            SHORTCUT_ID_LIST
        )
        val array = if (ids.isNullOrEmpty()) {
            arrayOf()
        } else {
            try {
                GsonUtil.fromJson(ids, Array<String>::class.java)
            } catch (e: JsonSyntaxException) {
                Log.d(e.message)
                arrayOf()
            }
        }
        val list = array.toMutableList().also {
            it.add(id.toString())
        }
        try {
            Settings.Global.putString(
                context.contentResolver,
                SHORTCUT_ID_LIST,
                list.toString()
            )
        } catch (e: SecurityException) {
            Log.d(e.message)
        }
    }

    @JvmStatic
    fun getShortCutIdFromMediaId(context: Context?, id: Long): String? {
        if (context == null) return null
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        val pinnedShortcutsList = shortcutManager.pinnedShortcuts
        pinnedShortcutsList.forEach {
            val itemId = try {
                it.intent?.data?.let { it1 ->
                    ContentUris.parseId(it1)
                }
            } catch (e: NumberFormatException) {
                null
            }
            if (id == itemId) {
                return it.id
            }
        }
        return null
    }

    /**
     * 更新快捷方式
     */
    @JvmStatic
    fun updateShortCuts(
        context: Context,
        updatePinnedShortcutsList: List<ShortcutInfo>
    ): Boolean {
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        Log.d(TAG, "updatePinnedShortcutsList.isNotEmpty == ${updatePinnedShortcutsList.isNotEmpty()}")
        if (updatePinnedShortcutsList.isNotEmpty()) {
            runCatching {
                shortcutManager.updateShortcuts(updatePinnedShortcutsList)
                updatePinnedShortcutsList.forEach {
                    addIdsIntoSettingProvider(context, it.intent?.data)
                }
                Log.d(TAG, "updateShortcuts success")
            }.onFailure {
                Log.e(TAG, "updateShortcuts onFailure: ${it.message}")
                return false
            }
        }
        return true
    }

    /**
     * 删除单个桌面快捷方式
     */
    @JvmStatic
    fun disableShortCutItem(context: Context, file: BaseFileBean): Boolean {
        Log.d(TAG, "disableShortCutItem")
        val disablePinnedShortcutsList = ArrayList<String>()
        file.mData?.let {
            Log.d(TAG, "id： $it")
            disablePinnedShortcutsList.add(it)
        }
        return disableShortCuts(context, disablePinnedShortcutsList)
    }

    /**
     * 删除多个桌面快捷方式
     */
    @JvmStatic
    fun disableShortCutItems(context: Context, delList: List<BaseFileBean>): Boolean {
        Log.d(TAG, "disableShortCutItems")
        if (delList.isEmpty()) {
            Log.d(TAG, "delList.isEmpty")
            return false
        }
        val disablePinnedShortcutsList = ArrayList<String>()
        // 不用过滤，直接都加进去，shortcutManager自己会把list里有效的id disable
        for (item in delList) {
            item.mData?.let { disablePinnedShortcutsList.add(it) }
        }
        return disableShortCuts(context, disablePinnedShortcutsList)
    }

    /**
     * 内部调用：执行真正disable shortcut操作的代码
     */
    @VisibleForTesting
    @JvmStatic
    fun disableShortCuts(
        context: Context,
        disablePinnedShortcutsList: ArrayList<String>
    ): Boolean {
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        if (disablePinnedShortcutsList.size > 0) {
            runCatching {
                shortcutManager.disableShortcuts(disablePinnedShortcutsList)
                Log.d(TAG, "disableShortcuts success")
            }.onFailure {
                Log.e(TAG, "disableShortcuts onFailure: ${it.message}")
                return false
            }
        }
        return true
    }

    /**
     * 内部调用：执行真正enable shortcut操作的代码
     */
    @VisibleForTesting
    @JvmStatic
    fun enableShortCuts(
        context: Context,
        disablePinnedShortcutsList: ArrayList<String>
    ): Boolean {
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        Log.d(TAG, "disablePinnedShortcutsList.size == ${disablePinnedShortcutsList.size}")
        if (disablePinnedShortcutsList.size > 0) {
            runCatching {
                shortcutManager.enableShortcuts(disablePinnedShortcutsList)
                Log.d(TAG, "enableShortcuts success")
            }.onFailure {
                Log.e(TAG, "enableShortcuts onFailure: ${it.message}")
                return false
            }
        }
        return true
    }

    /**
     * 创建桌面快捷方式
     * @return 成功 、失败
     */
    @JvmStatic
    fun createShortCut(context: Context, file: BaseFileBean, callback: ShortCutCallback) {
        Log.d(TAG, "createShortCut file $file")
        val shortcutManager = context.getSystemService(ShortcutManager::class.java)
        Log.d(TAG, "isRequestPinShortcutSupported " + shortcutManager.isRequestPinShortcutSupported)
        // 主键id : 路径 + 文件名
        val id = file.mData
        // 对比是否已经创建，若重复创建过，则提示
        if (hasSameId(id, shortcutManager)) {
            Log.d(TAG, "hasSameId show toast")
            callback.result(false)
            return
        }
        // 关键：获取shortcutInfo
        val shortcutInfo = getShortCutInfo(context, file)
        val pinnedShortcutCallbackIntent = shortcutManager.createShortcutResultIntent(shortcutInfo)
        val successCallback = PendingIntent.getBroadcast(context, 0, pinnedShortcutCallbackIntent, PendingIntent.FLAG_IMMUTABLE)
        runCatching {
            shortcutManager.requestPinShortcut(shortcutInfo, successCallback.intentSender)
            addIdsIntoSettingProvider(context, shortcutInfo.intent?.data)
        }.onFailure { ex ->
            // IllegalArgumentException – if a shortcut with the same ID exists and is disabled.
            Log.e(TAG, "requestPinShortcut failed: $ex")
            callback.result(false)
            return
        }
        callback.result(true)
    }

    @VisibleForTesting
    @JvmStatic
    fun getShortCutInfo(context: Context, file: BaseFileBean): ShortcutInfo {
        // 主键id : 路径 + 文件名
        val id = file.mData
        Log.d(TAG, "id:$id")
        val name = file.mDisplayName?.trim() ?: ""
        Log.d(TAG, "name:$name")
        // 获取图片
        val disPlayIcon = runBlocking(Dispatchers.IO) {
            getIcon(context, file)
        }
        // 根据类型创建intent（相当于点击事件）
        val defIntent = getShortCutIntent(context, file)
        // 创建shortcut
        val shortcutInfo = ShortcutInfo.Builder(context, id).apply {
            setShortLabel(name)
            setLongLabel(name)
            setIcon(disPlayIcon)
            setIntent(defIntent)
            setDisabledMessage(MyApplication.appContext.getString(R.string.toast_file_not_exist))
            // 防止桌面弹窗--注意需要release包才行
            val dismissDialogParams = PersistableBundle()
            dismissDialogParams.putBoolean(SKIP_CONFIRM, true)
            setExtras(dismissDialogParams)
        }.build()
        return shortcutInfo
    }

    /**
     * 生成指定id的ShortcutInfo
     */
    @VisibleForTesting
    @JvmStatic
    fun getShortCutInfoById(context: Context, file: BaseFileBean, id: String): ShortcutInfo {
        Log.d(TAG, "id:$id")
        val name = file.mDisplayName?.trim() ?: ""
        Log.d(TAG, "name:$name")
        // 获取图片
        val disPlayIcon = runBlocking(Dispatchers.IO) {
            getIcon(context, file)
        }
        // 根据类型创建intent（相当于点击事件）
        val defIntent = getShortCutIntent(context, file)
        // 创建shortcut
        val shortcutInfo = ShortcutInfo.Builder(context, id).apply {
            setShortLabel(name)
            setLongLabel(name)
            setIcon(disPlayIcon)
            setIntent(defIntent)
            setDisabledMessage(MyApplication.appContext.getString(R.string.toast_file_not_exist))
            // 防止桌面弹窗--注意需要release包才行
            val dismissDialogParams = PersistableBundle()
            dismissDialogParams.putBoolean(SKIP_CONFIRM, true)
            setExtras(dismissDialogParams)
        }.build()
        return shortcutInfo
    }

    /**
     * 获取文件的logo图，用于桌面显示
     * 参考的FileImageLoader displayDefault
     */
    @VisibleForTesting
    @JvmStatic
    fun getIcon(context: Context, baseFileBean: BaseFileBean): Icon {
        var targetDrawable: Drawable? = null
        Log.d(TAG, "getIcon type " + baseFileBean.mLocalType)
        targetDrawable =
            if (baseFileBean.mLocalType == MimeTypeHelper.IMAGE_TYPE || baseFileBean.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
                // 图片 视频
                getIconForHasThumbnailType(context, baseFileBean)
            } else if (baseFileBean.mLocalType == MimeTypeHelper.DRM_TYPE) {
                // DRM
                getIconForDRM(context, baseFileBean)
            } else if (baseFileBean.mLocalType == MimeTypeHelper.COMPRESSED_TYPE) {
                // 压缩类型
                getIconForCompressed(context, baseFileBean)
            } else if (MimeTypeHelper.isAudioType(baseFileBean.mLocalType)) {
                // 音频
                getIconForAudio(context, baseFileBean)
            } else if (MimeTypeHelper.isDocType(baseFileBean.mLocalType)) {
                // office
                getDocTypeIcon(context, baseFileBean)
            } else if (baseFileBean.mLocalType == MimeTypeHelper.ALBUM_SET_TYPE_CARD_CASE) {
                context.resources.getDrawable(R.drawable.grid_album_set_card_case, null)
            } else if (MimeTypeHelper.isOtherDocType(baseFileBean.mLocalType)) {
                getIconForOtherDoc(context, baseFileBean)
            } else {
                null
            }
        Log.d(TAG, "targetDrawable $targetDrawable")
        // 默认图片图片：文件夹使用的默认图片不做处理，其余的需要默认处理
        if (targetDrawable == null) {
            targetDrawable = KtThumbnailHelper.getClassifyDrawable(context, baseFileBean.mLocalType, true)
            // 默认图处理
            if (baseFileBean.mLocalType != MimeTypeHelper.DIRECTORY_TYPE) {
                targetDrawable = dealDefTargetDrawable(context, targetDrawable)
                Log.d(TAG, "show default targetDrawable and dealDefTargetDrawable")
            }
        }
        Log.d(TAG, "display targetDrawable $targetDrawable")
        // 最终转化成Icon类型
        val iconBitmap = targetDrawable?.let { DragUtils.drawableToBitmap(it) }
        val displayIcon = if (iconBitmap != null) {
            Icon.createWithBitmap(iconBitmap)
        } else {
            // 最终兜底图
            Icon.createWithResource(context, R.drawable.ic_file_folder_icon)
        }
        return displayIcon
    }

    @JvmStatic
    private fun getIconForOtherDoc(context: Context, baseFileBean: BaseFileBean): Drawable? {
        var targetDrawable: Drawable? = null
        // other doc
        targetDrawable = MimeTypeHelper.getIconByType(baseFileBean.mLocalType)
        // 默认图处理
        targetDrawable = dealDefTargetDrawable(context, targetDrawable)
        return targetDrawable
    }

    @JvmStatic
    private fun getIconForAudio(context: Context, baseFileBean: BaseFileBean): Drawable? {
        var targetDrawable: Drawable? = null
        val audioThumbnail = AudioThumbnailNew(
            baseFileBean.mData ?: "",
            baseFileBean.mDateModified, baseFileBean.mSize
        )
        val errorRes = R.drawable.ic_file_audio
        val holderResId = R.drawable.ic_file_audio
        targetDrawable = displayThumbnail(context, baseFileBean, audioThumbnail, errorRes, holderResId)
        return targetDrawable
    }

    @JvmStatic
    private fun getIconForCompressed(context: Context, baseFileBean: BaseFileBean): Drawable? {
        var targetDrawable: Drawable? = null
        val compressedType = MimeTypeHelper.getCompressedTypeByPath(
            if (baseFileBean.mDisplayName.isNullOrEmpty()) {
                baseFileBean.mData
            } else {
                baseFileBean.mDisplayName
            }
        )
        targetDrawable = KtThumbnailHelper.getClassifyDrawable(context, compressedType, true)
        // 默认图处理
        targetDrawable = dealDefTargetDrawable(context, targetDrawable)
        return targetDrawable
    }

    @JvmStatic
    private fun getIconForDRM(context: Context, baseFileBean: BaseFileBean): Drawable? {
        var targetDrawable: Drawable? = null
        baseFileBean.mData?.apply {
            val typeString = MimeTypeHelper.getDrmMimeType(context, this)
            if (!TextUtils.isEmpty(typeString) && (typeString!!.startsWith("video/") || typeString.startsWith("image/"))) {
                targetDrawable = getIconForHasThumbnailType(context, baseFileBean)
            } else {
                if (!TextUtils.isEmpty(typeString) && typeString!!.startsWith("audio/")) {
                    targetDrawable = KtThumbnailHelper.getClassifyDrawable(context, MimeTypeHelper.AUDIO_TYPE, true)
                } else {
                    targetDrawable = KtThumbnailHelper.getClassifyDrawable(context, MimeTypeHelper.DRM_TYPE, true)
                }
                // 默认图处理
                targetDrawable = dealDefTargetDrawable(context, targetDrawable)
            }
        }
        return targetDrawable
    }

    @JvmStatic
    private fun getDocTypeIcon(context: Context, baseFileBean: BaseFileBean): Drawable? {
        var targetDrawable: Drawable? = null
        val isDocThumbnailSupported = ThumbnailManager.isDocThumbnailSupported(context)
        Log.d(TAG, "isDocType -> isDocThumbnailSupported=$isDocThumbnailSupported")
        // 支持文档缩略图预览则显示对应图片，否则显示对应类型默认图片
        if (isDocThumbnailSupported) {
            val docThumbnail = DocThumbnail(baseFileBean.mData ?: "", baseFileBean.mDateModified, baseFileBean.mSize)
            val holderResId = KtThumbnailHelper.getClassifyResId(baseFileBean.mLocalType)
            // 注意这里为了适配桌面取正方形,防止长方形缩略图出现和默认图片类似的问题
            if (baseFileBean.mLocalType == MimeTypeHelper.PPT_TYPE || baseFileBean.mLocalType == MimeTypeHelper.PPTX_TYPE) {
                targetDrawable = displayThumbnail(
                    context, baseFileBean, docThumbnail, holderResId, holderResId,
                    wh = FileImageLoader.pptHeight
                )
            } else {
                targetDrawable = displayThumbnail(
                    context, baseFileBean, docThumbnail, holderResId, holderResId,
                    wh = FileImageLoader.otherHeight
                )
            }
        }
        return targetDrawable
    }

    /**
     * 处理targetDrawable,如果使用的是默认图-是长方形的(ClassifyDrawable)，则需要适配
     * 方案：直接加一个更大的空白布局-正方形， bitmap拼接两张图
     */
    @VisibleForTesting
    @JvmStatic
    fun dealDefTargetDrawable(context: Context, targetDrawable: Drawable?): Drawable? {
        var newDrawable: Drawable? = null
        var frontBitmap: Bitmap? = null
        var backBitmap: Bitmap? = null
        frontBitmap = targetDrawable?.let { DragUtils.drawableToBitmap(it) }
        val frontW = frontBitmap?.width ?: 0
        val frontH = frontBitmap?.height ?: 0
        Log.d(TAG, "frontW $frontW front_h $frontH")
        // 输出一个大一点的正方形背景
        backBitmap = Bitmap.createBitmap(frontH + ICON_PADDING, frontH + ICON_PADDING, Bitmap.Config.ARGB_8888)
        backBitmap.eraseColor(context.resources.getColor(R.color.color_white, null))
        if (frontBitmap != null && backBitmap != null) {
            val newBitmap = Bitmap.createBitmap(backBitmap.width, backBitmap.height, backBitmap.config!!)
            Log.d(TAG, "newBitmap width " + newBitmap.width)
            Log.d(TAG, "newBitmap height " + newBitmap.height)
            val canvas = Canvas(newBitmap)
            val matrix = Matrix()
            matrix.postScale(1f, 1f)
            val base = Bitmap.createBitmap(
                frontBitmap,
                0,
                0,
                frontBitmap.width,
                frontBitmap.height,
                matrix,
                false
            )
            val left = (backBitmap.width - frontBitmap.width) / 2.0F
            val top = (backBitmap.height - frontBitmap.height) / 2.0F
            canvas.drawBitmap(base, left, top, null)
            frontBitmap.recycle()
            backBitmap.recycle()
            frontBitmap = null
            backBitmap = null
            newDrawable = newBitmap.toDrawable(context.resources)
        }
        return newDrawable
    }

    /**
     * getIcon 有缩略图的：如image video drm
     * for IMAGE_TYPE VIDEO_TYPE DRM_TYPE
     */
    @JvmStatic
    private fun getIconForHasThumbnailType(
        context: Context,
        baseFileBean: BaseFileBean
    ): Drawable? {
        var targetDrawable: Drawable? = null
        val path = Uri.fromFile(File(baseFileBean.mData))
        var drmPath: String? = null
        if (baseFileBean.mLocalType == MimeTypeHelper.DRM_TYPE) {
            drmPath = baseFileBean.mData
        }
        var options = RequestOptions()
        val stringSignature = baseFileBean.mDateModified.toString() + baseFileBean.mSize + "" + Utils.isRtl() + ROUND_CONNER_NONE
        options = options.diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).signature(ObjectKey(stringSignature))
        //设置缩略图的显示模式
        options = options.transform(CenterCrop()).override(ICON_WH, ICON_WH)
        //加载缩略图
        val errorResId = when (baseFileBean.mLocalType) {
            MimeTypeHelper.IMAGE_TYPE -> R.drawable.ic_file_image
            MimeTypeHelper.VIDEO_TYPE -> R.drawable.ic_file_video
            MimeTypeHelper.AUDIO_TYPE -> R.drawable.ic_file_audio
            else -> null
        }
        val placeholderResId = R.drawable.thumbnail_load_default_place_holder
        options = if (errorResId == null) {
            options.placeholder(placeholderResId)
        } else {
            options.placeholder(errorResId).error(errorResId)
        }
        runCatching {
            val loadBuilder = Glide.with(context).asDrawable()
            if (TextUtils.isEmpty(drmPath)) {
                loadBuilder.load(path)
            } else {
                loadBuilder.load(DrmCover(drmPath))
            }
            options.frame(VIDEO_FRAME_VALUE)
            targetDrawable = loadBuilder.apply(options).submit().get()
        }.onFailure {
            Log.e(TAG, "display: Glide with " + it.message)
        }
        Log.d(TAG, "return targetDrawable $targetDrawable")
        return targetDrawable
    }

    /**
     *  getIcon 有缩略图的：音频、office部分类型
     *  for AudioType、office部分类型
     *  圆角参数取消，默认是0，让系统去裁剪
     */
    @JvmStatic
    private fun displayThumbnail(
        context: Context,
        baseFileBean: BaseFileBean,
        thumbnail: Any,
        errorRes: Int,
        placeHolderRes: Int = -1,
        wh: Int = -1
    ): Drawable? {
        Log.d(TAG, "displayThumbnail wh:$wh")
        var targetDrawable: Drawable? = null
        var requestOptions = RequestOptions.signatureOf(
            FileThumbnailSignature(baseFileBean.mData ?: "", baseFileBean.mDateModified, baseFileBean.mSize)
        )
        val transformation: Transformation<Bitmap> = CenterCrop()
        Log.d(TAG, "transformation CenterCrop")
        requestOptions = requestOptions.transform(transformation)
        when (thumbnail) {
            is AudioThumbnailNew -> {
                runCatching {
                    requestOptions = requestOptions.transform(
                        AudioThumbnailResult::class.java,
                        AudioThumbnailTransformation(transformation)
                    )
                    val targetResult = Glide.with(context)
                        .`as`(AudioThumbnailResult::class.java)
                        .load(thumbnail)
                        .apply(requestOptions)
                        .override(ICON_WH, ICON_WH)
                        .placeholder(placeHolderRes)
                        .error(errorRes).submit().get()
                    Log.d(TAG, "displayThumbnail Audio -> targetResult is $targetResult")
                    targetDrawable = targetResult.mBitmap?.toDrawable(context.resources)
                }.onFailure {
                    Log.e(TAG, "AudioThumbnailNew onFailure: ${it.message}")
                    targetDrawable = null
                }
            }

            is DocThumbnail -> {
                runCatching {
                    val roundedCorner = RoundedCornersBitmapTransformation(0F, Utils.isRtl(), true, true, true, true)
                    thumbnail.isShortcut = true
                    val requestBuilder = Glide.with(context)
                        .asDrawable()
                        .load(thumbnail)
                        .apply(requestOptions)
                        .transform(roundedCorner)
                        .override(wh, wh)
                        .placeholder(placeHolderRes)
                        .error(errorRes)
                    targetDrawable = requestBuilder.submit().get()
                    Log.d(TAG, "displayThumbnail Doc -> targetResult is $targetDrawable")
                }.onFailure {
                    // java.util.concurrent.ExecutionException: com.bumptech.glide.load.engine.GlideException: Failed to load resource
                    Log.e(TAG, "DocThumbnail onFailure: ${it.message}")
                    targetDrawable = null
                }
            }
        }
        return targetDrawable
    }

    /**
     * 获取Intent，相当于点击快捷方式的点击事件
     */
    @JvmStatic
    private fun getShortCutIntent(context: Context, file: BaseFileBean): Intent {
        // 方案：跳透明页(目前想实现点击删除的快捷方式，文管提示，这个方便可行性高一些)
        if (file.isDir() || AndroidDataHelper.isAndroidDataPath(file.mData ?: "")) {
            //Android/data目录文件快捷方式点击后跳转到文件列表页
            val intent = Intent()
            intent.action = START_FILE_BROWSER
            intent.putExtra(KtConstants.CURRENT_DIR, file.mData)
            intent.putExtra(KtConstants.FROM_DETAIL, true)
            // ShortCut Param:is from filemanager
            intent.putExtra(KEY_SHORTCUT_FORM_FILM_MANAGER, true)
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
            return intent
        } else if (isCompressedFile(file)) {
            val intent = Intent("oppo.intent.action.ACTION_COMPRESS_PREVIEW")
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            // ShortCut Param:is from filemanager
            intent.putExtra(KEY_SHORTCUT_FORM_FILM_MANAGER, true)
            val path = file.mData
            val type = setType(path)
            val uri: Uri? = UriHelper.getFileUri(file, intent, fileType = type, isShortCut = true)
            intent.setDataAndType(uri, "application/zip")
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
            return intent
        } else {
            val intent = Intent(Intent.ACTION_VIEW)
            intent.addCategory(Intent.CATEGORY_DEFAULT)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            // ShortCut Param:is from filemanager
            intent.putExtra(KEY_SHORTCUT_FORM_FILM_MANAGER, true)
            //  ShortCut Param:KEY_FILE_MODIFIED
            intent.putExtra(KEY_FILE_MODIFIED, file.mDateModified)
            intent.putExtra(KEY_LOCAL_TYPE, file.mLocalType)
            val path = file.mData
            val type = setType(path)
            val uri: Uri? = UriHelper.getFileUri(file, intent, fileType = type, isShortCut = true)
            when (file.mLocalType) {
                MimeTypeHelper.IMAGE_TYPE -> {
                    intent.putExtra(SINGLE_MEDIA_ID, true)
                    intent.putExtra(ENABLE_THUMB_LINE_PREVIEW, false)
                    intent.putExtra(IMAGE_FILE_TYPE, IMAGE_FILE_TYPE_FILE_MANAGER)
                    intent.setDataAndType(uri, "image/*")
                }

                MimeTypeHelper.VIDEO_TYPE -> intent.setDataAndType(uri, "video/*")
                MimeTypeHelper.AUDIO_TYPE -> intent.setDataAndType(uri, "audio/*")
                MimeTypeHelper.HTML_TYPE -> intent.setDataAndType(uri, "text/html")
                MimeTypeHelper.TXT_TYPE, MimeTypeHelper.LRC_TYPE, MimeTypeHelper.UMD_TYPE -> {
                    intent.action = Intent.ACTION_VIEW
                    intent.setDataAndType(uri, "text/plain")
                }

                MimeTypeHelper.EPUB_TYPE -> intent.setDataAndType(uri, "application/epub+zip")

                MimeTypeHelper.EBK_TYPE -> intent.setDataAndType(uri, "application/x-expandedbook")

                MimeTypeHelper.CHM_TYPE -> intent.setDataAndType(uri, "application/x-chm")

                MimeTypeHelper.CSV_TYPE -> {
                    if (!path.isNullOrEmpty()) {
                        intent.type = ContactsContract.Contacts.CONTENT_VCARD_TYPE
                        intent.data = uri
                        intent.putExtra(Intent.EXTRA_STREAM, path)
                    } else {
                        Log.d(TAG, "CSV_TYPE open error, path is null!")
                    }
                }

                MimeTypeHelper.VCS_TYPE, MimeTypeHelper.ICS_TYPE -> {
                    Log.d(TAG, "VCS_TYPE")
                    intent.setDataAndType(uri, "text/calendar")
                    intent.putExtra(Intent.EXTRA_STREAM, path)
                }

                MimeTypeHelper.VCF_TYPE -> {
                    intent.type = ContactsContract.Contacts.CONTENT_VCARD_TYPE
                    if (SdkUtils.isAtLeastR() && !path.isNullOrEmpty()) {
                        intent.data = uri
                        intent.putExtra(Intent.EXTRA_STREAM, path)
                        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                    } else {
                        intent.putExtra(Intent.EXTRA_STREAM, path)
                    }
                }

                MimeTypeHelper.P12_TYPE -> intent.setDataAndType(uri, "application/x-pkcs12")

                MimeTypeHelper.CER_TYPE -> intent.setDataAndType(uri, "application/pkix-cert")

                MimeTypeHelper.TORRENT_TYPE -> intent.setDataAndType(uri, "application/x-bittorrent")

                MimeTypeHelper.THEME_TYPE -> {
                    if (Utils.isNeededSdk27()) {
                        if (SdkUtils.isAtLeastOS12()) {
                            if (FeatureCompat.sIsExpRom) {
                                intent.action = "com.oplus.themestore.action.PREVIEW_THEME"
                                intent.putExtra("oplus_preview_theme_path", path)
                            } else {
                                intent.action = "oppo.intent.action.OPPO_PREVIEW_THEME"
                                intent.putExtra("oppo_preview_theme_path", path)
                            }
                        } else {
                            intent.action = "oppo.intent.action.OPPO_PREVIEW_THEME"
                            intent.putExtra("oppo_preview_theme_path", path)
                        }
                    } else {
                        intent.action = "android.intent.action.OPPO_PREVIEW_THEME"
                        intent.putExtra("oppo_preview_theme_path", path)
                    }
                }

                MimeTypeHelper.DOC_TYPE -> intent.setDataAndType(uri, "application/msword")

                MimeTypeHelper.DOCX_TYPE -> {
                    intent.setDataAndType(
                        uri,
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    )
                }

                MimeTypeHelper.XLS_TYPE -> intent.setDataAndType(uri, "application/vnd.ms-excel")

                MimeTypeHelper.XLSX_TYPE -> {
                    intent.setDataAndType(
                        uri,
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )
                }

                MimeTypeHelper.PPT_TYPE -> intent.setDataAndType(uri, "application/vnd.ms-powerpoint")

                MimeTypeHelper.PPTX_TYPE -> {
                    intent.setDataAndType(
                        uri,
                        "application/vnd.openxmlformats-officedocument.presentationml.presentation"
                    )
                }

                MimeTypeHelper.PDF_TYPE -> intent.setDataAndType(uri, "application/pdf")

                MimeTypeHelper.OFD_TYPE -> intent.setDataAndType(uri, "application/ofd")

                else -> {
                    val mimeType = MimeTypeHelper.getMimeTypeByFileType(file.mLocalType)
                    if (mimeType == MimeType.MIMETYPE_UNKNOWN) {
                        intent.setData(uri)
                    } else {
                        intent.setDataAndType(uri, mimeType)
                    }
                }
            }
            return intent
        }
    }

    @JvmStatic
    private fun setType(path: String?): Int {
        var type = MimeTypeHelper.getTypeFromPath(path)
        if (type == MimeTypeHelper.UNKNOWN_TYPE) {
            type = MimeTypeHelper.getMediaType(path)
        }
        type = MimeTypeHelper.getTypeFromDrm(MyApplication.appContext, type, path)
        return type
    }

    @Suppress("ComplexCondition")
    @JvmStatic
    private fun isCompressedFile(baseFile: BaseFileBean) =
        (baseFile.mLocalType == MimeTypeHelper.COMPRESSED_TYPE || baseFile.mLocalType == MimeTypeHelper.ZIP_TYPE
                || baseFile.mLocalType == MimeTypeHelper.RAR_TYPE || baseFile.mLocalType == MimeTypeHelper.JAR_TYPE
                || baseFile.mLocalType == MimeTypeHelper.P7ZIP_TYPE)

    /**
     * 判断是否有相同的id
     */
    @VisibleForTesting
    @JvmStatic
    fun hasSameId(id: String?, shortcutManager: ShortcutManager): Boolean {
        val pinnedShortcutsList = shortcutManager.pinnedShortcuts
        Log.d(TAG, "pinnedShortcutsList " + pinnedShortcutsList.size)
        for (shortcutItem in pinnedShortcutsList) {
            if (shortcutItem.id == id) {
                Log.d(TAG, "same id: $id")
                return true
            }
        }
        return false
    }

    /**
     * 判断文件or文件夹是否存在
     */
    @JvmStatic
    fun checkFileIsExist(fileData: String?): Boolean {
        val file = File(fileData)
        return file.exists()
    }

    @JvmStatic
    private fun getFile(filePath: String): File? {
        val file = File(filePath)
        if (file.exists()) {
            return file
        }
        return null
    }

    interface ShortCutCallback {
        fun result(success: Boolean)
    }

    /**
     * 判读文件是否是有缩略图的类型
     */
    @JvmStatic
    private fun isHasThumbnailType(localType: Int): Boolean {
        return localType == MimeTypeHelper.IMAGE_TYPE
                || localType == MimeTypeHelper.VIDEO_TYPE
                || localType == MimeTypeHelper.AUDIO_TYPE
                || localType == MimeTypeHelper.DRM_TYPE
                || MimeTypeHelper.isDocType(localType)
    }
}