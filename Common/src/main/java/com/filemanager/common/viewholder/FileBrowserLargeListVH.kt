/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileBrowserLargeListVH
 * * Description : 大屏的文件列表VH
 * * Version     : 1.0
 * * Date        : 2025/07/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.content.Context
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.setPadding
import androidx.core.view.updateLayoutParams
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.FileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.imageloader.application.ApplicationInfoDetailLoadListener
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.GetMediaDurationUtil
import com.filemanager.common.utils.HighlightUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.KtUtils.STRING_FOUR_SPACES
import com.filemanager.common.utils.LabelVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

open class FileBrowserLargeListVH : BaseFileBrowserVH {

    companion object {

        private const val TAG = "FileBrowserLargeListVH"
        private const val SECOND = 1000L
        const val UNKNOWN_CONTENT = "--"
        private const val LARGE_MAX_LINE = 2
        private const val MEDIUM_MAX_LINE = 1

        @JvmStatic
        fun getLayoutId(): Int {
            return R.layout.file_browser_large_list_item
        }

        @JvmStatic
        fun create(parent: ViewGroup, imgRadius: Int = 0, isLabelFileList: Boolean = false): FileBrowserLargeListVH {
            val context = parent.context
            val itemView = LayoutInflater.from(context).inflate(getLayoutId(), parent, false)
            return FileBrowserLargeListVH(itemView, imgRadius, isLabelFileList)
        }
    }

    private lateinit var rootView: ConstraintLayout
    private lateinit var thumbnailImg: FileThumbView
    private lateinit var apkIcon: ImageView
    private lateinit var durationTv: TextView
    protected lateinit var titleTv: TextViewSnippet
    private lateinit var fileDetailTv: TextView
    private lateinit var anotherNameTv: TextViewSnippet
    private lateinit var typeTv: TextView
    private lateinit var sizeTv: TextView
    private lateinit var timeTv: TextView
    protected var imgRadius: Int = 0
    private val docRadius = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_2dp)
    private val largeWidth = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_120dp)
    private val mediumTypeWidth = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_60dp)
    private val mediumTimeWidth = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_80dp)
    private var isLabelFileList: Boolean = false
    private val cloudConfigApi: ICloudConfigApi? by lazy {
        Injector.injectFactory<ICloudConfigApi>()
    }
    protected var choiceMode = false
    private var windowType = WindowUtils.LARGE // 当前窗口形态

    /**
     * 是否显示apk的详情内容
     */
    var showApkDetail = true

    /**
     * 是否加载doc缩略图
     */
    var loadDocThumbnail = true

    /**
     * 搜索关键字
     */
    var keyword: String = ""

    /**
     * 是否显示最近打开时间，文档页面排序为打开时间时使用
     */
    private var isDisplayLastOpenTime = false


    constructor(convertView: View, imgRadius: Int = 0, isLabelFileList: Boolean = false) : super(convertView) {
        rootView = convertView.findViewById(R.id.file_browser_item_list_root)
        thumbnailImg = convertView.findViewById(R.id.file_list_item_icon)
        apkIcon = convertView.findViewById(R.id.apk_icon)
        durationTv = convertView.findViewById(R.id.file_duration_tv)
        titleTv = convertView.findViewById(R.id.file_list_item_title)
        fileDetailTv = convertView.findViewById(R.id.file_list_item_detail)
        anotherNameTv = convertView.findViewById(R.id.another_name_text)
        typeTv = convertView.findViewById(R.id.file_type_text)
        sizeTv = convertView.findViewById(R.id.file_size_text)
        timeTv = convertView.findViewById(R.id.file_time_text)
        dividerLine = convertView.findViewById(R.id.divider_line)
        mCheckBox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
        this.isLabelFileList = isLabelFileList
        this.imgRadius = imgRadius
    }


    override fun updateViewHolder(
        context: Context,
        key: Int?,
        data: BaseFileBean,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        sizeCache: HashMap<String, String>,
        threadManager: ThreadManager,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        val path = data.mData
        if (path == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        this.choiceMode = choiceMode
        updateWindowType(context)
        setFileThumbnail(context, thumbnailImg, fileDetailTv, data)
        setFileTitle(titleTv, data, keyword)
        setFileDetail(fileDetailTv, anotherNameTv, data)
        setDuration(durationTv, data)
        showApkIconIfNeed(data, apkIcon)
        setFileType(typeTv, data)
        setFileSize(context, sizeTv, data, sizeCache)
        setFileTime(context, timeTv, data)
        updateDividerVisible(adapter.getRealFileItemCount() - 1, position = position)
        HighlightUtil.addItemHighlight(context, this, bindingAdapterPosition)
        setCheckBox(mCheckBox, timeTv, adapter, choiceMode, data.mLocalType)
        setHiddenItemAlpha(data, adapter.mIsDarkModel)
        adjustSize(context)
    }

    private fun updateWindowType(context: Context) {
        windowType = WindowUtils.getCurrentWindowType(context)
        val isPadPortrait = UIConfigMonitor.instance.isDevicePortrait(context) && ModelUtils.isTablet()
        if (windowType == WindowUtils.LARGE && isPadPortrait) {
            windowType = WindowUtils.MIDDLE
        }
    }


    protected open fun setFileThumbnail(context: Context, thumbnailImg: FileThumbView, detailTv: TextView, file: BaseFileBean) {
        val type = file.mLocalType
        FileImageVHUtils.updateFileListImgSize(context, thumbnailImg, file)
        if (type == MimeTypeHelper.IMAGE_TYPE || type == MimeTypeHelper.VIDEO_TYPE) {
            thumbnailImg.setStrokeStyle(FileThumbView.STROKE_2DP)
        } else {
            thumbnailImg.setStrokeStyle(FileThumbView.STROKE_NONE)
        }
        thumbnailImg.setDrmState(type == MimeTypeHelper.DRM_TYPE)
        if (type == MimeTypeHelper.APPLICATION_TYPE) {
            detailTv.tag = file.mData
            thumbnailImg.setBorderStyle(0F, 0F)
            // 显示apk 的名称和版本号
            val appDetailListener = if (showApkDetail) {
                ApplicationInfoDetailLoadListener { appInfo ->
                    val tagDetail = detailTv.tag as? String
                    if (appInfo?.mPath == tagDetail && !TextUtils.isEmpty(appInfo?.mApkVersion)) {
                        detailTv.visibility = View.VISIBLE
                        val apkFile = FileWrapper(appInfo?.mPath)
                        val apkDetail = Utils.formatSize(apkFile)
                        if (!TextUtils.isEmpty(apkDetail)) {
                            detailTv.text = Utils.getApplicationDetailFormat(context, appInfo?.mApkName, "", appInfo?.mApkVersion)
                        }
                    }
                }
            } else {
                null
            }
            FileImageLoader.sInstance.clear(context, thumbnailImg)
            FileImageLoader.sInstance.displayApplicationWithDetail(file, thumbnailImg, appDetailListener)
        } else {
            val padding = when (type) {
                MimeTypeHelper.VIDEO_TYPE -> {
                    MyApplication.sAppContext.resources.getDimension(R.dimen.file_list_image_padding)
                        .toInt()
                }

                else -> 0
            }
            val radius = ViewUtils.getIconRadius(type, docRadius, imgRadius)
            thumbnailImg.setPadding(padding)
            FileImageLoader.sInstance.clear(context, thumbnailImg)
            FileImageLoader.sInstance.displayDefault(
                file,
                thumbnailImg,
                0,
                radius,
                loadDocThumbnail = loadDocThumbnail,
                isCoverError = true,
                isSmallDoc = true
            )
        }
    }

    protected open fun setFileTitle(titleTv: TextViewSnippet, file: BaseFileBean, keyword: String) {
        // 设置显示内容和高亮
        val displayName = file.mDisplayName
        if (keyword.isEmpty()) {
            titleTv.text = displayName
            titleTv.setTextViewStyle(ignorePoint = file.mLocalType == MimeTypeHelper.DIRECTORY_TYPE)
        } else {
            if (Utils.isNeededTargetLanguage(Constants.SNIPPET_LANGUAGE)) {
                titleTv.text = displayName
            } else {
                titleTv.setTextWithPost(displayName, keyword)
            }
        }
        // 显示标签
        LabelVHUtils.displayLabelFlag(file, titleTv.context, titleTv, this.isLabelFileList)
    }

    open fun setFileDetail(detailTv: TextView, anotherNameTv: TextViewSnippet, file: BaseFileBean) {
        if (file.mLocalType == MimeTypeHelper.APPLICATION_TYPE) {
            return
        }
        if (!file.mIsDirectory) {
            detailTv.visibility = View.GONE
            anotherNameTv.visibility = View.GONE
            return
        }
        // 文件个数
        detailTv.visibility = View.VISIBLE
        val fileCount = JavaFileHelper.listFilesCount(file, isNeedShowHiddenFile().not())
        detailTv.text = MyApplication.sAppContext.resources.getQuantityString(R.plurals.text_x_items, fileCount, fileCount)
        // 别名
        anotherNameTv.visibility = View.VISIBLE
        if (!FeatureCompat.sIsExpRom) {
            cloudConfigApi?.updateViewByAlias(anotherNameTv, file.mData ?: "")
        } else {
            anotherNameTv.text = ""
        }
        if (anotherNameTv.text.isEmpty() && file.originPackage.isNullOrEmpty().not()) {
            GlobalScope.launch(Dispatchers.IO) {
                val alias = AndroidDataHelper.installedNameMap?.get(file.originPackage) ?: ""
                withContext(Dispatchers.Main) {
                    if (alias != file.originPackage) {
                        anotherNameTv.text = "$STRING_FOUR_SPACES$alias"
                    }
                    if (anotherNameTv.text.isEmpty()) {
                        anotherNameTv.visibility = View.GONE
                    }
                }
            }
        } else if (anotherNameTv.text.isEmpty()) {
            anotherNameTv.visibility = View.GONE
        }
    }


    open fun setDuration(durationTv: TextView, file: BaseFileBean) {
        if (file.mLocalType == MimeTypeHelper.VIDEO_TYPE && file.mMediaDuration == 0L) {
            GlobalScope.launch(Dispatchers.IO) {
                file.mMediaDuration = GetMediaDurationUtil.getDuration(file)
                withContext(Dispatchers.Main) {
                    showDurationIfNeed(file, durationTv)
                }
            }
        } else {
            showDurationIfNeed(file, durationTv)
        }
    }

    private fun showDurationIfNeed(file: BaseFileBean, durationTv: TextView) {
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType && file.mMediaDuration != 0L) {
            durationTv.visibility = View.VISIBLE
            durationTv.text = KtUtils.formatVideoTime(file.mMediaDuration / SECOND)
        } else {
            durationTv.visibility = View.GONE
        }
    }

    protected open fun setFileType(textView: TextView, file: BaseFileBean) {
        if (file.mIsDirectory) {
            textView.text = textView.context.getString(R.string.folder)
        } else {
            val ext = FileTypeUtils.getExtension(file.mData)
            textView.text = if (TextUtils.isEmpty(ext)) {
                UNKNOWN_CONTENT
            } else {
                ext
            }
        }
    }

    protected open fun setFileSize(context: Context, textView: TextView, file: BaseFileBean, sizeCache: HashMap<String, String>) {
        if (file.mIsDirectory) {
            textView.text = UNKNOWN_CONTENT
            return
        }
        val lastModified = getDisplayTime(file, isDisplayLastOpenTime)
        val path = file.mData
        val size = sizeCache.get(path + file.mSize + lastModified + isNeedShowHiddenFile())
        if (size.isNullOrEmpty()) {
            //ViewHolder reuse causes display exception,holder.mDetail need to set default display text
            textView.text = ""
            textView.tag = path
            (context as? AppCompatActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
                val formatStorageDetail = if (file.mIsDirectory) {
                    val fileCount = JavaFileHelper.listFilesCount(file, isNeedShowHiddenFile().not())
                    MyApplication.appContext.resources.getQuantityString(R.plurals.text_x_items, fileCount, fileCount)
                } else {
                    KtUtils.formatSize(file)
                }
                textView.post {
                    val currentPath = textView.tag as? String
                    val modifyTime = getDisplayTime(file, isDisplayLastOpenTime)
                    if (path == currentPath) {
                        sizeCache.put(path + file.mSize + modifyTime + isNeedShowHiddenFile(), formatStorageDetail)
                        textView.text = formatStorageDetail
                    }
                }
            }
        } else {
            textView.text = size
        }
    }

    protected open fun setFileTime(context: Context, textView: TextView, file: BaseFileBean) {
        val lastModified = getDisplayTime(file, isDisplayLastOpenTime)
        if (lastModified > 0L) {
            val dateAndTime = if (windowType == WindowUtils.LARGE) {
                Utils.getDateFormat(context, lastModified)
            } else {
                Utils.getShortDateFormat(context, lastModified)
            }
            textView.text = dateAndTime
        } else {
            textView.text = UNKNOWN_CONTENT
        }
    }

    fun getDisplayTime(file: BaseFileBean, isDisplayLastOpenTime: Boolean): Long = if (isDisplayLastOpenTime) {
        if (file.lastOpenTime == 0L) file.mDateModified else file.lastOpenTime
    } else {
        file.mDateModified
    }

    private fun setCheckBox(
        checkBox: COUICheckBox?,
        rightView: View,
        adapter: BaseSelectionRecycleAdapter<*, *>,
        choiceMode: Boolean,
        localType: Int
    ) {
        checkBox?.apply {
            adapter.updateCheckBoxState(checkBox, layoutPosition)
        }

        val marginEnd = rightView.context.resources.getDimensionPixelOffset(R.dimen.dimen_8dp)
        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.file_time_text, ConstraintSet.END)
            if (choiceMode) { // 编辑模式
                connect(R.id.file_time_text, ConstraintSet.END, R.id.listview_scrollchoice_checkbox, ConstraintSet.START)
                setMargin(R.id.file_time_text, ConstraintSet.END, marginEnd)
                setVisibility(R.id.listview_scrollchoice_checkbox, View.VISIBLE)
            } else {
                connect(R.id.file_time_text, ConstraintSet.END, ConstraintSet.PARENT_ID, ConstraintSet.END)
                setMargin(R.id.file_time_text, ConstraintSet.END, 0)
                setVisibility(R.id.listview_scrollchoice_checkbox, View.GONE)
            }
            applyTo(rootView)
        }
    }


    /**
     * 根据是否显示隐藏文件，置灰隐藏文件
     */
    protected open fun setHiddenItemAlpha(file: BaseFileBean, isDarkMode: Boolean) {
        val alpha = getHiddenItemAlpha(file, isDarkMode)
        thumbnailImg.alpha = alpha
        titleTv.alpha = alpha
        fileDetailTv.alpha = alpha
        typeTv.alpha = alpha
        sizeTv.alpha = alpha
        timeTv.alpha = alpha
    }

    protected open fun getHiddenItemAlpha(file: BaseFileBean, isDarkMode: Boolean): Float {
        return HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, isDarkMode)
    }

    /**
     * 动态调整中大屏的布局
     * 1. 文件类型，大小，时间 宽度有变化
     * 2. item左右间距有修改
     */
    private fun adjustSize(context: Context) {
        var typeWidth = mediumTypeWidth
        var timeWidth = mediumTimeWidth
        if (windowType == WindowUtils.LARGE) {
            typeWidth = largeWidth
            timeWidth = largeWidth
        }
        updateViewWidth(typeTv, typeWidth)
        updateViewWidth(timeTv, timeWidth)

        val startPadding = FileImageVHUtils.getListLeftMargin()
        val endPadding = FileImageVHUtils.getListRightMargin()
        rootView.setPaddingRelative(startPadding, rootView.paddingTop, endPadding, rootView.paddingBottom)
    }

    private fun updateViewWidth(view: View, width: Int) {
        view.updateLayoutParams<ViewGroup.LayoutParams> {
            this.width = width
        }
    }

    fun setDisplayLastOpenTime(display: Boolean) {
        isDisplayLastOpenTime = display
    }

    fun setSelected(isSelected: Boolean) {
        rootView.isSelected = isSelected
    }
}