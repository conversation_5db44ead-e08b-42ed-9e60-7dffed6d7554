/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/24, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.view.DragEvent
import com.filemanager.common.R
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.IDraggingActionOperate
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil

object DropDispatchAction {

    private const val TAG = "DropDispatchAction"
    const val PROHIBIT_STATE = 2
    const val COPY_STATE = 1
    const val NORMAL_STATE = 0

    private val CLASS_ITEM_TYPE = arrayOf(
        CategoryHelper.CATEGORY_IMAGE, CategoryHelper.CATEGORY_VIDEO, CategoryHelper.CATEGORY_AUDIO, CategoryHelper.CATEGORY_DOC,
        CategoryHelper.CATEGORY_APK, CategoryHelper.CATEGORY_COMPRESS
    )

    /**
     * 处理拖拽结果
     * @param targetCategoryType 拖拽到目标页面类别
     * @param dropSideItem 是否拖拽到侧导，true：为侧导，false：具体页面
     */
    fun handleDragDrop(activity: Activity, targetCategoryType: Int, dragEvent: DragEvent?, dropSideItem: Boolean): Boolean {
        val dragTag = dragEvent?.localState as? String ?: DragUtils.dragTag
        Log.d(TAG, "handleDropAction -> categoryType $targetCategoryType, dropSideItem $dropSideItem , dragTag: $dragTag")
        if (dragEvent == null) return false
        var dropResult = false
        MacDragUtil.MacDragObject.targetCategoryType = targetCategoryType
        runCatching {
            //内部拖拽接收不需要获取uri授权
            if (dragTag == null) {
                activity.requestDragAndDropPermissions(dragEvent)
            }
        }.onFailure {
            Log.e(TAG, "handleDragDrop $it")
        }
        if (isSamePage(activity, targetCategoryType, dragTag)) {
            Log.d(TAG, "getCornerMarkStateCode same page")
            return false
        }
        //禁止从mac拖出到云盘、最近删除
        if (handleMacDragDrop(targetCategoryType)) return false
        when (targetCategoryType) {
            //分类,三方云文档,其他类不支持的
            in CLASS_ITEM_TYPE,
            CategoryHelper.CATEGORY_TENCENT_DOCS,
            CategoryHelper.CATEGORY_K_DOCS,
            CategoryHelper.CATEGORY_ADD_LABEL,
            CategoryHelper.CATEGORY_PAGE_ALBUM_SET,
            CategoryHelper.CATEGORY_ADD_FOLDER -> {
                CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
                dropResult = false
            }
            //最近
            CategoryHelper.CATEGORY_RECENT -> {
                dropResult = DropRecentAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
            }
            //本地存储
            CategoryHelper.CATEGORY_FILE_BROWSER,
            CategoryHelper.CATEGORY_OTG_BROWSER,
            CategoryHelper.CATEGORY_SDCARD_BROWSER -> {
                dropResult = DropStorageAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
            }
            //云盘
            CategoryHelper.CATEGORY_CLOUD -> {
                dropResult = DropCloudDiskAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
            }
            //分布式文管
            CategoryHelper.CATEGORY_DFM -> dropResult = DropDfsAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
            //私密保险箱
            CategoryHelper.CATEGORY_PRIVATE_SAVE -> {
                dropResult = DropPrivacySafeAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
            }
            //最近删除
            CategoryHelper.CATEGORY_RECYCLE_BIN -> {
                dropResult = DropRecycleBinAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
            }
        }
        //来源
        if (CategoryHelper.isSuperAppType(targetCategoryType) || targetCategoryType == CategoryHelper.CATEGORY_SOURCE_GROUP) {
            dropResult = DropSourceAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
        }
        //快捷文件夹
        if (CategoryHelper.isShortcutFolderType(targetCategoryType) || targetCategoryType == CategoryHelper.CATEGORY_FOLDER_GROUP) {
            dropResult = DropShortFolderAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
        }
        //标签
        if (CategoryHelper.isLabelType(targetCategoryType) || targetCategoryType == CategoryHelper.CATEGORY_LABEL_GROUP) {
            dropResult = DropLabelAction.handleDropAction(dragEvent, dragTag, targetCategoryType, activity, dropSideItem)
        }
        runCatching {
            OptimizeStatisticsUtil.dragFilesResult(activity, dragEvent, dragTag ?: "", targetCategoryType.toString(), dropSideItem.toString())
        }.onFailure {
            Log.e(TAG, "dragFilesResult ${it.message}")
        }
        return dropResult
    }

    private fun handleMacDragDrop(targetCategoryType: Int): Boolean {
        MacDragUtil.MacDragObject.targetCategoryType = targetCategoryType
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            when (targetCategoryType) {
                CategoryHelper.CATEGORY_CLOUD, CategoryHelper.CATEGORY_RECYCLE_BIN, CategoryHelper.CATEGORY_FOLDER_GROUP -> {
                    CustomToast.showShort(R.string.drag_not_support_position)
                    return true
                }
            }
        }
        return false
    }

    private fun requestDragPermissions(activity: Activity, dragTag: String?, dragEvent: DragEvent?) {
        runCatching {
            //内部拖拽接收不需要获取uri授权
            if (dragTag == null) {
                activity.requestDragAndDropPermissions(dragEvent)
            }
        }.onFailure {
            Log.e(TAG, "handleDragDrop $it")
        }
    }

    /**
     * 处理拖拽结果
     * @param targetCategoryType 拖拽到目标页面类别
     * @param dropSideItem 是否拖拽到侧导，true：为侧导，false：具体页面
     */
    fun getCornerMarkStateCode(activity: Activity, targetCategoryType: Int, dragEvent: DragEvent?): Int {
        val dragTag = dragEvent?.localState as? String ?: DragUtils.dragTag
        Log.d(TAG, "getCornerMarkStateCode dragTag:$dragTag target:$targetCategoryType")
        if (dragEvent == null) return NORMAL_STATE
        MacDragUtil.MacDragObject.targetCategoryType = targetCategoryType
        requestDragPermissions(activity, dragTag, dragEvent)
        if (isSamePage(activity, targetCategoryType, dragTag)) {
            Log.d(TAG, "getCornerMarkStateCode same page")
            return NORMAL_STATE
        }
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            when (targetCategoryType) {
                CategoryHelper.CATEGORY_CLOUD,
                CategoryHelper.CATEGORY_RECYCLE_BIN,
                CategoryHelper.CATEGORY_FOLDER_GROUP -> {
                    return PROHIBIT_STATE
                }
            }
        }
        when (targetCategoryType) {
            in CLASS_ITEM_TYPE,
            CategoryHelper.CATEGORY_TENCENT_DOCS,
            CategoryHelper.CATEGORY_K_DOCS,
            CategoryHelper.CATEGORY_ADD_LABEL,
            CategoryHelper.CATEGORY_ADD_FOLDER -> {
                val dragCategoryType = DropUtil.getCategoryTypeByDragTag(dragTag)
                if (dragCategoryType == targetCategoryType) {
                    return NORMAL_STATE
                }
                return PROHIBIT_STATE
            }
            CategoryHelper.CATEGORY_PAGE_ALBUM_SET -> return PROHIBIT_STATE
            CategoryHelper.CATEGORY_RECENT -> {
                return DropRecentAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
            }
            CategoryHelper.CATEGORY_FILE_BROWSER,
            CategoryHelper.CATEGORY_OTG_BROWSER,
            CategoryHelper.CATEGORY_SDCARD_BROWSER -> {
                return DropStorageAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
            }
            CategoryHelper.CATEGORY_CLOUD -> {
                return DropCloudDiskAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
            }
            CategoryHelper.CATEGORY_DFM -> return DropDfsAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
            CategoryHelper.CATEGORY_PRIVATE_SAVE -> {
                return DropPrivacySafeAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
            }
            CategoryHelper.CATEGORY_RECYCLE_BIN -> {
                return DropRecycleBinAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
            }
        }
        if (CategoryHelper.isSuperAppType(targetCategoryType) || targetCategoryType == CategoryHelper.CATEGORY_SOURCE_GROUP) {
            return DropSourceAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
        }
        if (CategoryHelper.isShortcutFolderType(targetCategoryType) || targetCategoryType == CategoryHelper.CATEGORY_FOLDER_GROUP) {
            return DropShortFolderAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
        }
        if (CategoryHelper.isLabelType(targetCategoryType) || targetCategoryType == CategoryHelper.CATEGORY_LABEL_GROUP) {
            return DropLabelAction.getCornerMarkStateCode(dragEvent, dragTag, targetCategoryType, activity, false)
        }
        return PROHIBIT_STATE
    }

    private fun isSamePage(activity: Activity, targetCategoryType: Int, dragTag: String?): Boolean {
        if (activity is IDraggingActionOperate) {
            val currentPath = activity.getDragCurrentPath()
            if ((currentPath == null || currentPath == DragUtils.dragStartPath)
                && (CommonConstants.DRAG_FROM_FRAGMENT + targetCategoryType) == dragTag
            ) {
                return true
            }
        }
        return false
    }

    private fun handleMacDragDrop(targetCategoryType: Int, dropResult: Boolean): Boolean {
        var dropResult1 = dropResult
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            when (targetCategoryType) {
                CategoryHelper.CATEGORY_CLOUD, CategoryHelper.CATEGORY_RECYCLE_BIN, CategoryHelper.CATEGORY_FOLDER_GROUP -> {
                    CustomToast.showShort(R.string.drag_not_support_position)
                    dropResult1 = false
                }
            }
        }
        return dropResult1
    }
}