/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ViewUtils.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/6
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  hank.zhou      2022/7/6      1.0        create
 ***********************************************************************/
package com.filemanager.common.utils

import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.thumbnail.ThumbnailManager

object ViewUtils {

    private const val START_INDEX = 3
    private const val QUARTER = 4

    @JvmStatic
    @VisibleForTesting
    fun getStringIndexOverWidth(tempString: String, lineWidth: Float, textPaint: Paint): Int {
        for (index in (START_INDEX..tempString.length)) {
            val segmentTextWidth = textPaint.measureText(tempString.substring(0, index))
            if (segmentTextWidth > lineWidth) {
                return index
            }
        }
        return -1
    }

    @JvmStatic
    fun findStringForTwoLineDisplay(tempString: String, lineWidth: Float, textPaint: Paint): String {
        val index = getStringIndexOverWidth(tempString, lineWidth, textPaint)
        if (index > 0) {
            val otherLineWidth = textPaint.measureText(tempString.substring(index - 1))
            if (otherLineWidth <= lineWidth) {
                return tempString.substring(0, index - 1) + "\n" + tempString.substring(index - 1)
            }
        }
        return ""
    }

    @JvmStatic
    fun getStartIndex(startIndex: Int, enterIndex: Int): Int {
        if (enterIndex > 0 && enterIndex <= startIndex) {
            return startIndex + 1
        }
        return startIndex
    }

    @JvmStatic
    fun getEndIndex(startIndex: Int, enterIndex: Int, searchLength: Int, maxLength: Int): Int {
        var endIndex = startIndex + searchLength
        if (enterIndex in 1..endIndex) {
            endIndex += 1
        }
        if (endIndex > maxLength) {
            endIndex = maxLength
        }
        return endIndex
    }

    /**
     * 可预览文档、图片、视频使用 [docRadius]，其他使用[otherRadius]
     */
    @JvmStatic
    fun getIconRadius(localType: Int, docRadius: Int, otherRadius: Int): Int {
        return if ((MimeTypeHelper.isDocType(localType) && ThumbnailManager.isDocThumbnailSupported(MyApplication.appContext))
            || localType == MimeTypeHelper.IMAGE_TYPE
            || localType == MimeTypeHelper.VIDEO_TYPE
        ) {
            docRadius
        } else {
            otherRadius
        }
    }

    @JvmStatic
    fun isMoreOneLine(textView: TextView, text: String): Boolean {
        val visibleWidth = textView.width - textView.paddingLeft - textView.paddingRight
        val checkWidth = textView.resources.displayMetrics.widthPixels / QUARTER
        val maxWidth = visibleWidth.coerceAtLeast(checkWidth)
        val textWidth = textView.paint.measureText(text)
        return textWidth > maxWidth
    }

    @JvmStatic
    fun isPointInsideView(view: View, x: Float, y: Float): Boolean {
        val rect = Rect()
        view.getHitRect(rect)
        return rect.contains(x.toInt(), y.toInt())
    }
}