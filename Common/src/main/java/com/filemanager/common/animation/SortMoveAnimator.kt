/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SortMoveAnimator
 * Description: com.filemanager.common.animation.SortMoveAnimator
 * Version: 1.0
 * Date: 2025/2/25
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/2/25      1.0         Create this module
 *********************************************************************************/
package com.filemanager.common.animation

import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import androidx.core.animation.doOnCancel
import androidx.core.animation.doOnEnd
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIEaseInterpolator

class SortMoveAnimator : BaseItemAnimator() {

    companion object {
        const val SCALE_DOWN = 0.7f
    }

    override fun animateRemoveImpl(holder: RecyclerView.ViewHolder) {
        val scaleAnimationX = ObjectAnimator.ofFloat(holder.itemView, "scaleX", 1f, SCALE_DOWN)
        val scaleAnimationY = ObjectAnimator.ofFloat(holder.itemView, "scaleY", 1f, SCALE_DOWN)
        val alphaAnimation = ObjectAnimator.ofFloat(holder.itemView, "alpha", 1f, 0f)
        scaleAnimationX.duration = removeDuration
        scaleAnimationY.duration = removeDuration
        alphaAnimation.duration = removeDuration
        val animationSet = AnimatorSet()
        animationSet.apply {
            interpolator = COUIEaseInterpolator()
            doOnCancel {
                clear(holder.itemView)
            }
            doOnEnd {
                clear(holder.itemView)
                dispatchRemoveFinished(holder)
                dispatchFinishedWhenDone()
            }
            play(scaleAnimationX).with(alphaAnimation).with(scaleAnimationY)
        }.start()
    }

    override fun animateAddImpl(holder: RecyclerView.ViewHolder) {
        val scaleAnimationX = ObjectAnimator.ofFloat(holder.itemView, "scaleX", SCALE_DOWN, 1f)
        val scaleAnimationY = ObjectAnimator.ofFloat(holder.itemView, "scaleY", SCALE_DOWN, 1f)
        val alphaAnimation = ObjectAnimator.ofFloat(holder.itemView, "alpha", 0f, 1f)
        scaleAnimationX.duration = addDuration
        scaleAnimationY.duration = addDuration
        alphaAnimation.duration = addDuration
        val animationSet = AnimatorSet()
        animationSet.apply {
            interpolator = COUIEaseInterpolator()
            doOnCancel {
                clear(holder.itemView)
            }
            doOnEnd {
                clear(holder.itemView)
                dispatchAddFinished(holder)
                dispatchFinishedWhenDone()
            }
            play(scaleAnimationX).with(alphaAnimation).with(scaleAnimationY)
        }.start()
    }
}