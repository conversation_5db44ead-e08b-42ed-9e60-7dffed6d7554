/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecycleFileDeleteTimeComparatorGenerator
 ** Description : RecycleBin file sort by recycle_date
 ** Version     : 1.0
 ** Date        : 2022/9/7
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/9/7      1.0        create
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.wrapper.RecycleFileWrapper
import java.util.Comparator

class RecycleFileDeleteTimeComparatorGenerator : NameComparatorGenerator() {
    companion object {
        val comparator = RecycleFileDeleteTimeComparatorGenerator()

        fun getComparator(isDesc: Boolean = true): Comparator<RecycleFileWrapper> {
            return comparator.genRecycleFileComparator(isDesc)
        }
    }

    override fun compareRecycleFileWrapper(file1: RecycleFileWrapper, file2: RecycleFileWrapper, isDesc: Boolean): Int {
        return if (file1.mIsDirectory && file2.mIsDirectory) {
            super.compareRecycleFileWrapper(file1, file2, isDesc)
        } else {
            var data1: Long = 0
            if (file1.mIsDirectory) {
                // do nothing
            } else {
                data1 = file1.mRecycelDate
            }
            var data2: Long = 0
            if (file2.mIsDirectory) {
                // do nothing
            } else {
                data2 = file2.mRecycelDate
            }
            val result = data1 - data2
            when {
                result < 0 -> 1
                result > 0 -> -1
                else -> 0
            }
        }
    }
}