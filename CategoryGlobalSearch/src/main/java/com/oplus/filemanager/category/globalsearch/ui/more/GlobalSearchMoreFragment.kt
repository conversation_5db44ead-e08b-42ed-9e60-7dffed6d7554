/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GlobalSearchMoreFragment
 ** Description : 搜索更多结果的Fragment
 ** Version     : 1.0
 ** Date        : 2024/05/16 15:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/16       1.0      create
 ***********************************************************************/
package com.oplus.filemanager.category.globalsearch.ui.more

import android.annotation.SuppressLint
import android.content.res.Configuration
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.util.ArrayMap
import android.view.MenuItem
import android.view.MotionEvent
import android.view.View
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.activity.ComponentActivity
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.FileGridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.FileManagerDefaultItemAnimator
import com.filemanager.common.base.RecyclerPercentSelectionVMFragment
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.base.SearchDMPFileWrapper
import com.filemanager.common.base.SearchFileWrapper
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.controller.FileEmptyController
import com.filemanager.common.controller.navigation.IMenuEnable
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.OnBackPressed
import com.filemanager.common.interfaces.TransformNextFragmentListener
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.FileEmptyUtils
import com.filemanager.common.utils.FileEmptyUtils.SEARCH_EMPTY_ANIMATION_FILE
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileManagerPercentWidthRecyclerView
import com.filemanager.fileoperate.FileOperatorListenerImpl
import com.filemanager.fileoperate.NormalFileOperateController
import com.filemanager.fileoperate.base.ACTION_DONE
import com.coui.appcompat.material.navigation.NavigationBarView
import com.oplus.dropdrag.recycleview.ItemDetailsLookup
import com.oplus.filemanager.category.globalsearch.R
import com.oplus.filemanager.category.globalsearch.adapter.GlobalSearchAdapter
import com.oplus.filemanager.category.globalsearch.adapter.SearchSpaceItemDecoration
import com.oplus.filemanager.category.globalsearch.bean.CategoryFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchDFMFileWrapper
import com.oplus.filemanager.category.globalsearch.bean.SearchLabelWrapper
import com.oplus.filemanager.category.globalsearch.controller.GlobalSearchFilterController
import com.oplus.filemanager.category.globalsearch.controller.SearchFilterClickListener
import com.oplus.filemanager.category.globalsearch.manager.filter.FilterItem
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchActivity
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchFragmentViewModel
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchNormalViewModel
import com.oplus.filemanager.category.globalsearch.ui.GlobalSearchViewModel
import com.oplus.filemanager.category.globalsearch.ui.navigation.SearchMenuEnableImpl
import com.oplus.filemanager.category.globalsearch.view.refresh.BounceCallBack
import com.oplus.filemanager.category.globalsearch.view.refresh.BounceHandler
import com.oplus.filemanager.category.globalsearch.view.refresh.BounceLayout
import com.oplus.filemanager.category.globalsearch.view.refresh.DefaultFooter
import com.oplus.filemanager.category.globalsearch.view.refresh.EventForwardingHelper
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import com.oplus.filemanager.interfaze.filecloudbrowser.IFileCloudBrowser
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import com.oplus.filemanager.interfaze.touchshare.TouchShareFragmentSupplier
import com.oplus.filemanager.interfaze.touchshare.TouchShareSupplier
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class GlobalSearchMoreFragment : RecyclerPercentSelectionVMFragment<GlobalSearchFragmentViewModel>(), OnBackPressed,
    NavigationBarView.OnItemSelectedListener {

    companion object {
        private const val TAG = "GlobalSearchMoreFragment"
        private const val REFRESH_MIN_TIME = 100L
        private const val DELAY_NOTIFY_TIME = 100L
        private const val ACTION_CLOUD_FILE_DOWNLOAD = 101 // 云文档下载成功的code
        private const val EMPTY_DELAY = 30L

        val downloadPath: String by lazy {
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath
        }

        fun newInstance(category: Int, searchWord: String): GlobalSearchMoreFragment {
            val fragment = GlobalSearchMoreFragment()
            fragment.arguments = Bundle().apply {
                this.putInt(KtConstants.P_CATEGORY_TYPE, category)
                this.putString(KtConstants.SEARCH_WORD, searchWord)
            }
            return fragment
        }
    }

    private var category: Int = CategoryFileWrapper.TYPE_LOCAL_FILE
    private var bounceLayout: BounceLayout? = null
    private var refreshRoot: FrameLayout? = null
    private var mLastListMode: Int = KtConstants.LIST_NORMAL_MODE
    private var adapter: GlobalSearchAdapter? = null
    private var fileOperateController: IFileOperate? = null
    private val filterController by lazy { GlobalSearchFilterController(lifecycle) }
    private val fileEmptyController by lazy { FileEmptyController(lifecycle) }
    private var mHandler: Handler? = Handler(Looper.getMainLooper())
    private var menuEnableImpl: IMenuEnable = SearchMenuEnableImpl {
        mViewModel?.getSelectItems()
    }

    override fun getLayoutResId(): Int {
        return R.layout.fragment_global_search_more
    }

    override fun createViewModel(): GlobalSearchFragmentViewModel {
        category =
            arguments?.getInt(KtConstants.P_CATEGORY_TYPE, CategoryFileWrapper.TYPE_LOCAL_FILE) ?: CategoryFileWrapper.TYPE_LOCAL_FILE
        val viewModel = ViewModelProvider(this).get(TAG, GlobalSearchNormalViewModel::class.java)
        viewModel.init({
            (mActivity as? GlobalSearchMoreActivity)?.reloadData()
        }, GlobalSearchActivity.TAB_ALL, category, true)
        val listener = FileOperatorListenerImpl(viewModel)
        fileOperateController = NormalFileOperateController(lifecycle, CategoryHelper.CATEGORY_SEARCH, viewModel).apply {
            setInterceptor(viewModel)
            setResultListener(listener)
            checkHasDynamicBeans = true
        }
        return viewModel
    }


    override fun initView(view: View) {
        appBarLayout = mActivity?.findViewById(R.id.appbar_layout)
        rootView = view.findViewById(R.id.root_view)
        bounceLayout = view.findViewById(R.id.bounce_layout)
        refreshRoot = view.findViewById(R.id.pull_down_refresh_root)
        mRecyclerView = view.findViewById<FileManagerPercentWidthRecyclerView>(R.id.recycler_view)
        initBounceLayout()
        initRecyclerView()
        adjustRefreshDistance()
        mActivity?.let {
            bounceLayout?.post {
                rootView?.setPadding(0, appBarLayout?.height ?: 0, 0, 0)
                initFilterController()
            }

            adapter = GlobalSearchAdapter(it, <EMAIL>, CategoryHelper.CATEGORY_SEARCH).apply {
                setHasStableIds(true)
                selectRemoteFile = CategoryFileWrapper.TYPE_REMOTE_FILE == category
                mRecyclerView?.adapter = this
                deepSearchClickListener = View.OnClickListener { _ ->
                    (it as? GlobalSearchMoreActivity)?.clickDeepSearch()
                }
            }
        }
    }

    private fun initBounceLayout() {
        bounceLayout?.apply {
            setBounceHandler(BounceHandler(), mRecyclerView)
            setFooterView(
                DefaultFooter(context),
                refreshRoot
            )
            setEventForwardingHelper(object : EventForwardingHelper {
                override fun notForwarding(downX: Float, downY: Float, moveX: Float, moveY: Float): Boolean {
                    return true
                }
            })
            //这里应产品述求，云文档和远程电脑文件2级页面，才做分页加载的UI交互；本地文件+三方应用文件下拉时不显示底部loading
            if (category != CategoryFileWrapper.TYPE_DRIVE_FILE && category != CategoryFileWrapper.TYPE_REMOTE_FILE) {
                setDisableLoadMore(true)
            }
            setBounceCallBack(object : BounceCallBack {
                override fun startRefresh() {
                    Log.d(TAG, "startRefresh")
                    //加载下一页数据
                    val hasMore = (mActivity as? GlobalSearchMoreActivity)?.loadMore()
                    Log.d(TAG, "startRefresh load $hasMore")
                    if (hasMore == false) {
                        setDisableLoadMore(true)
                        mHandler?.postDelayed({
                            stopLoadMore()
                        }, REFRESH_MIN_TIME)
                    }
                }

                override fun startLoadingMore() {
                    Log.d(TAG, "startLoadingMore")
                }
            })
        }
    }

    /**
     * 停止加载更多
     */
    private fun stopLoadMore() {
        bounceLayout?.setRefreshCompleted()
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initRecyclerView() {
        mRecyclerView?.apply {
            isNestedScrollingEnabled = true
            clipToPadding = false
            layoutManager = FileGridLayoutManager(context, ItemDecorationFactory.GRID_ITEM_COUNT_1)
            val animator = FileManagerDefaultItemAnimator()
            itemAnimator = animator
            animator.supportsChangeAnimations = false
            addItemDecoration(SearchSpaceItemDecoration(context = mActivity))
            setHasFixedSize(true)
            setPadding(paddingLeft, 0, paddingRight, resources.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom))
            isForceDarkAllowed = false
            setOnTouchListener { _, event ->
                if (event?.actionMasked == MotionEvent.ACTION_DOWN) {
                    hideKeyboard()
                }
                false
            }
        }
    }

    /**
     * 调整下拉刷新的间距
     */
    private fun adjustRefreshDistance() {
        val lp = refreshRoot?.layoutParams as RelativeLayout.LayoutParams
        val footerViewHeight = resources.getDimensionPixelSize(R.dimen.default_footer_loading_height)
        val footerTopPadding = resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_top_padding)
        val footerBottomPadding = resources.getDimensionPixelOffset(R.dimen.pull_refresh_head_bottom_padding)
        val listMaxTop = resources.getDimensionPixelOffset(R.dimen.pull_refresh_down_fragment_max_drag_distance)

        /**
         * In different layouts, the loading animation (LottieView) is hidden (hidden by other views) by setting
         * the topMargin of the root layout, and is 12dp above the listView or recyclerView, This value is up to
         * the business side according to the needs.
         */
        val footerTopMargin = footerViewHeight + footerTopPadding
        lp.bottomMargin = -footerTopMargin
        /**
         * Drag threshold. When the drag distance is greater than or equal to this value, it means that it will enter
         * the loading state after letting go. The reason for this calculation is to make the loading animation (LottieView)
         * 24dp from the bottom (the View that covered it before), and also 12dp from the listView or recyclerView below,
         * so the specific value is up to the business party according to the needs up to you.
         */
        bounceLayout?.dragDistanceThreshold = footerViewHeight + footerBottomPadding + footerTopPadding
        /* The maximum distance that bounceLayout can be dragged down. */
        bounceLayout?.maxDragDistance = listMaxTop
        Log.d(TAG, "adjustDistance height:$footerViewHeight top:$footerTopPadding bottom:$footerBottomPadding maxTop:$listMaxTop")
    }

    private fun initFilterController() {
        val activity = mActivity ?: return
        filterController.init(activity, rootView, category)?.apply {
            val lp = bounceLayout?.layoutParams as? RelativeLayout.LayoutParams
            lp?.addRule(RelativeLayout.BELOW, this.id)
            //这里加入bindRecyclerView的逻辑，保证上滑时有分割线
            val recyclerView = rootView?.findViewById<RecyclerView>(R.id.recycler_view)
            if (recyclerView != null) {
                filterController.bindRecyclerView(recyclerView)
            }
        }
        filterController.showOrHideFilter()
        filterController.setFilterClickListener(object : SearchFilterClickListener {
            override fun onFilterClick(filterItem: FilterItem) {
                Log.d(TAG, "onFilterClick: ${filterItem.desc}")
                (activity as? GlobalSearchMoreActivity)?.updateSelectFilter(filterItem)
            }

            override fun onChangeFilterPanel() {
                Log.d(TAG, "onChangeFilterPanel")
                hideKeyboard()
                toggleFilterPanel()
            }

            override fun onFileterItemRemoved(itemList: List<FilterItem>) {
                Log.d(TAG, "onFileterItemRemoved: $itemList")
                (activity as? GlobalSearchMoreActivity)?.removeSelectFilter(itemList)
            }
        })
    }

    fun onSelectedFilterChange(select: ArrayMap<Int, FilterItem>, filterData: Boolean = true) {
        filterController.onFilterSelectChanged(select)
        mViewModel?.onFilterSelectUpdate(select, filterData)
    }


    override fun initData(savedInstanceState: Bundle?) {
        TouchShareSupplier.attach(this, TouchShareFragmentSupplier(category, baseVMActivity, mViewModel, fileOperateController))
    }

    override fun startObserve() {
        mRecyclerView?.post {
            if (isAdded) {
                startListSelectModeObserver()
                startUIDataStateObserver()
            }
        }
    }

    private fun startListSelectModeObserver() {
        mViewModel?.apply {
            mModeState.mListModel.observe(this@GlobalSearchMoreFragment, object : Observer<Int> {
                override fun onChanged(value: Int) {
                    if (!mViewModel!!.mModeState.mInitState) {
                        return
                    }
                    Log.d(TAG, "startListSelectModeObserver: mListModel=$value")
                    val selectModel = (value == KtConstants.LIST_SELECTED_MODE)
                    setRecyclerViewSelectModel(selectModel)
                    setBarSelectModel(selectModel)
                    filterController.setFilterTitleEnable(selectModel.not())
                    hideKeyboardByListModel(value)
                }
            })
        }
    }


    /**
     * 设置recyclerView选中模式的表现
     * @param selectModel 是否选中
     */
    private fun setRecyclerViewSelectModel(selectModel: Boolean) {
        adapter?.let {
            it.setSelectEnabled(selectModel)
            it.setChoiceModeAnimFlag(selectModel)
        }
        mRecyclerView?.let {
            val res = MyApplication.sAppContext.resources
            val bottom = if (selectModel) {
                val bottomView = mActivity?.findViewById<View>(R.id.navigation_tool)
                KtViewUtils.getSelectModelPaddingBottom(it, bottomView)
            } else {
                res.getDimensionPixelSize(com.filemanager.common.R.dimen.ftp_text_margin_bottom)
            }
            it.setPadding(it.paddingLeft, it.paddingTop, it.paddingRight, bottom)
            if (selectModel) {
                it.setFadingEdgeLength(res.getDimensionPixelSize(com.filemanager.common.R.dimen.list_fading_edge_height))
            }
        }
    }

    /**
     * 设置顶部toolbar和底部导航栏选中模式下的表现
     * @param selectModel 是否选中
     */
    private fun setBarSelectModel(selectModel: Boolean) {
        val searchMoreActivity = mActivity as? GlobalSearchMoreActivity
        if (selectModel) {
            searchMoreActivity?.showNavigation()
            searchMoreActivity?.processNavigationItemEnable(menuEnableImpl)
        } else {
            searchMoreActivity?.hideNavigation()
        }
        searchMoreActivity?.notifySelectModel(selectModel)
        if (selectModel) {
            searchMoreActivity?.initToolbarSelectedMode(
                true,
                mViewModel!!.getRealFileSize(),
                mViewModel!!.mUiState.value?.mSelectedList?.size ?: 0,
                mViewModel!!.getSelectItems()
            )
        } else {
            searchMoreActivity?.initToolbarNormalMode(needInit = true, empty = false)
        }
    }


    private fun startUIDataStateObserver() {
        mViewModel?.mUiState?.observe(this) { uiModel ->
            Log.d(
                TAG, "startUIDataStateObserver: total=${uiModel.mFileList.size}, real:${mViewModel?.getRealFileSize()} "
                        + "select=${uiModel.mSelectedList.size}, keyword=${uiModel.mKeyWord}"
            )
            val selectModel = (uiModel.mStateModel.mListModel.value == KtConstants.LIST_SELECTED_MODE)
            val searchMoreActivity = mActivity as? GlobalSearchMoreActivity
            if (selectModel) {
                searchMoreActivity?.initToolbarSelectedMode(
                    false,
                    mViewModel!!.getRealFileSize(),
                    mViewModel!!.mUiState.value?.mSelectedList?.size ?: 0,
                    mViewModel!!.getSelectItems()
                )
                searchMoreActivity?.processNavigationItemEnable(menuEnableImpl)
            } else {
                searchMoreActivity?.initToolbarNormalMode(needInit = false, empty = false)
            }
            filterController.setFilterTitleEnable(selectModel.not())
            if (uiModel.mFileList.isEmpty() && selectModel) {
                mViewModel!!.mModeState.mListModel.value = KtConstants.LIST_NORMAL_MODE
            }

            stopLoadMore()
            checkNeedShowEmptyView()
            updateResultCountShow(mViewModel?.getTotalFileSize() ?: 0)
            if (uiModel.mFileList is ArrayList<BaseFileBean>) {
                adapter?.let {
                    it.setKeyWord(uiModel.mKeyWord)
                    it.setData(uiModel.mFileList as ArrayList<BaseFileBean>, uiModel.mSelectedList)
                }
            }
        }
    }

    private fun checkNeedShowEmptyView() {
        mViewModel?.let {
            val activityViewModel = (mActivity as? GlobalSearchMoreActivity)?.mViewModel
            val loading = activityViewModel?.isLoadingData()
                ?: false
            Log.d(TAG, "checkNeedShowEmptyView loading: $loading")
            if (it.mUiState.value?.mFileList.isNullOrEmpty() && !loading) {
                // When switching zoomWindows, a delay is required to ensure that view height can be obtained
                mHandler?.postDelayed({
                    if (it.mUiState.value?.mFileList.isNullOrEmpty()) {
                        val title = if (AndroidDataHelper.openAndroidData == true &&
                            (it.getSearchKey() != activityViewModel?.deepSearchResult?.key)
                        ) {
                            AndroidDataHelper.buildDeepSearchTips(
                                mActivity!!, com.filemanager.common.R.string
                                    .search_empty_deep_search_tips_1, com.filemanager.common.R.string
                                    .deep_search
                            ) {
                                (mActivity as? GlobalSearchMoreActivity)?.clickDeepSearch()
                            }
                        } else {
                            appContext.resources.getString(com.filemanager.common.R.string.no_search_results)
                        }
                        rootView?.apply {
                            var index = -1
                            //避免空布局盖在筛选面板的上面
                            if (childCount > 0 && getChildAt(childCount - 1).id == R.id.search_filter_root_layout) {
                                index = childCount - 1
                            }
                            baseVMActivity?.let { activity ->
                                fileEmptyController.showFileEmptyView(
                                    activity, this,
                                    SEARCH_EMPTY_ANIMATION_FILE, title, index
                                )
                            }
                        }
                    }
                }, EMPTY_DELAY)
            } else {
                fileEmptyController.hideFileEmptyView()
            }
        }
    }

    private fun updateResultCountShow(size: Int) {
        filterController.updateTitle(size)
        if ((mActivity as? GlobalSearchMoreActivity)?.isLoadingData() == false) {
            onShowFilter()
        }
    }

    fun onSearchDataUpdate(result: GlobalSearchViewModel.GlobalSearchResult?, searchKey: String? = result?.searchKey) {
        mViewModel?.onSearchDataUpdate(result, searchKey)
    }

    fun onShowFilter() {
        filterController.showOrHideFilter()
    }

    override fun onResumeLoadData() {
        (mActivity as? GlobalSearchMoreActivity)?.reloadData()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        activity?.let {
            if (filterController.mIsShow) {
                filterController.reLayoutFilterPanel()
            }
        }
        mRecyclerView?.postDelayed({
            adapter?.notifyDataSetChanged()
        }, DELAY_NOTIFY_TIME)
        if (fileOperateController is NormalFileOperateController) {
            (fileOperateController as NormalFileOperateController).onConfigurationChanged(newConfig)
        }
    }

    fun notifySearchStart() {
        fileEmptyController.hideFileEmptyView()
        filterController.showOrHideFilter(false)
        mViewModel?.apply {
            if (mModeState.mListModel.value != KtConstants.LIST_NORMAL_MODE) {
                changeListMode(KtConstants.LIST_NORMAL_MODE)
            }
        }
    }


    private fun hideKeyboard() {
        (activity as? GlobalSearchMoreActivity)?.hideKeyboard()
    }

    fun toggleFilterPanel() {
        Log.d(TAG, "showOrHidePanel: listMode=${mViewModel?.mModeState?.mListModel?.value}")
        mViewModel?.apply {
            if (mModeState.mListModel.value == KtConstants.LIST_NORMAL_MODE) {
                filterController.showOrHideFilterPanel()
            }
        }
    }

    fun hideKeyboardByListModel(mListModel: Int?) {
        mListModel?.let {
            if (mListModel != mLastListMode) {
                mLastListMode = mListModel
                hideKeyboard()
            }
        }
    }

    override fun pressBack(): Boolean {
        if (filterController.mIsShow) {
            filterController.showOrHideFilterPanel()
            return true
        }
        return mViewModel?.pressBack() ?: false
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        mViewModel?.mUiState?.value?.let { uiModel ->
            if (uiModel.mStateModel.mListModel.value != KtConstants.LIST_NORMAL_MODE) {
                return@let
            } else if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@let
            }
            val baseFile = uiModel.mKeyMap[item.selectionKey].apply {
                Log.d(TAG, "onItemClick baseFile=$this")
            } ?: return false
            activity?.let {
                val mediaImgIds: ArrayList<String> = ArrayList()
                // 判断当前点击是否是媒体库中的图片
                lifecycleScope.launch(context = Dispatchers.IO) {
                    if (FileMediaHelper.isImgAndInMedia(baseFile)) {
                        // 获取当前视图中的图片文件，按照排序顺序生成一个media id列表，传给相册
                        mViewModel?.uiState?.value?.fileList?.forEach { file ->
                            file.takeIf { curFile ->
                                !curFile.isDir() && curFile.mLocalType == MimeTypeHelper.IMAGE_TYPE && curFile.mSize > 0
                            }?.takeIf {
                                file !is SearchDFMFileWrapper && file !is SearchDMPFileWrapper
                            }?.let {
                                if (file is SearchFileWrapper) mediaImgIds.add(file.id.toString())
                            }
                        }
                        // 限制数据大小
                        FileMediaHelper.limitNumberOfFileList(baseFile, mediaImgIds)
                    }
                    withContext(Dispatchers.Main) {
                        fileOperateController?.onFileClick(it, baseFile, e, mediaImgIds)
                        if (baseFile.mLocalType != MimeTypeHelper.DIRECTORY_TYPE && baseFile !is SearchLabelWrapper) {
                            OptimizeStatisticsUtil.clickSearchResultFileType(baseFile, category)
                        }
                    }
                }
            }
        }
        return true
    }

    fun onMenuItemSelected(item: MenuItem?): Boolean {
        if (null == item || Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return false
        }
        when (item.itemId) {
            android.R.id.home -> mActivity?.finish()

            com.filemanager.common.R.id.action_select_all -> mViewModel?.clickToolbarSelectAll()

            com.filemanager.common.R.id.action_select_cancel -> {
                if (mViewModel?.mModeState?.mListModel?.value == KtConstants.LIST_SELECTED_MODE) {
                    mViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                }
            }
        }
        return true
    }

    fun fromSelectPathResult(requestCode: Int, path: List<String>?) {
        Log.d(TAG, "fromSelectPathResult requestCode:$requestCode path: $path")
        activity?.let {
            if (requestCode == MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD) {
                realDownloadCloudFile(path?.getOrNull(0))
            } else {
                fileOperateController?.onSelectPathReturn(it, requestCode, path)
            }
        }
        if (requestCode != MessageConstant.MSG_EDITOR_COMPRESS && requestCode != MessageConstant.MSG_EDITOR_DECOMPRESS) {
            mViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
        }
    }

    override fun onNavigationItemSelected(item: MenuItem): Boolean {
        if (!Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
            return activity?.let {
                val selectFiles = mViewModel?.getSelectItems()
                if (handleSelectDriveFile(it, item, selectFiles)) {
                    Log.d(TAG, "onNavigationItemSelected select drive file")
                    return true
                }
                if (handleSelectRemoteFile(it, item, selectFiles)) {
                    Log.d(TAG, "onNavigationItemSelected select remote file")
                    return true
                }
                //这里要求复选多个数据时也需要显示压缩菜单
                fileOperateController?.onNavigationItemSelected(it, item)
            } ?: false
        }
        return false
    }

    /**
     * 选中云文件的事件：重命名和下载
     */
    private fun handleSelectDriveFile(activity: ComponentActivity, item: MenuItem, selectFiles: ArrayList<BaseFileBean>?): Boolean {
        if (selectFiles == null) {
            return false
        }
        if (selectFiles.size != 1) {
            return false
        }
        val file = selectFiles.get(0)
        Log.d(TAG, "handleSelectDriveFile select : $file")
        if (file !is DriveFileWrapper) {
            return false
        }
        when (item.itemId) {
            com.filemanager.common.R.id.navigation_download -> downloadCloudFile(activity, file)
            com.filemanager.common.R.id.navigation_rename -> renameCloudFile(activity, file)
            else -> return false
        }
        return true
    }

    private fun handleSelectRemoteFile(activity: ComponentActivity, item: MenuItem, selectFiles: ArrayList<BaseFileBean>?): Boolean {
        Log.d(TAG, "handleSelectRemoteFile select : ${selectFiles?.size}")
        if (selectFiles == null) {
            return false
        }
        if (selectFiles.size == 0) {
            return false
        }
        val containOtherFile = selectFiles.filterNot {
            it is RemoteFileBean
        }.isNotEmpty()
        if (containOtherFile) {
            return false
        }
        when (item.itemId) {
            com.filemanager.common.R.id.navigation_download -> mViewModel?.downloadRemoteFile(activity, false, selectFiles)
            else -> return false
        }
        return true
    }

    /**
     * 下载云文档
     */
    private fun downloadCloudFile(activity: ComponentActivity, downloadFile: DriveFileWrapper) {
        Log.d(TAG, "downloadCloudFile $downloadFile")
        if (downloadFile.isDir()) {
            Log.d(TAG, "downloadCloudFile -> selected file is folder, not support download.")
            CustomToast.showShort(com.filemanager.common.R.string.download_folder_not_supported)
            return
        }
        if (!downloadFile.isSupportDownload()) {
            Log.d(TAG, "downloadCloudFile -> selected file format not support download.")
            CustomToast.showShort(com.filemanager.common.R.string.download_format_not_supported)
            return
        }
        if (activity is TransformNextFragmentListener) {
            (activity as TransformNextFragmentListener).showSelectPathFragmentDialog(
                MessageConstant.MSG_CLOUD_DRIVE_DOWNLOAD,
                downloadPath
            )
        }
    }

    /**
     * 下载远程电脑文件
     */
    private fun downloadRemoteFile(activity: ComponentActivity, isOpen: Boolean, downloadFiles: List<BaseFileBean>) {
        val downloadPaths = ArrayList<Pair<String, Long>>()
        downloadFiles.forEach {
            if (it is RemoteFileBean && !it.mIsDirectory) {
                downloadPaths.add(Pair(it.originalPath, it.mSize))
            }
        }
        val code = if (isOpen || downloadPaths.size == 1) {
            MessageConstant.MSG_OPEN_REMOTE_FILE
        } else {
            MessageConstant.MSG_DOWNLOAD_REMOTE_FILE
        }
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>()
        val deviceId = remoteDeviceApi?.getCurrentLinkedRemoteDiceInfo()?.deviceId
        val categoryRemoteDeviceApi = Injector.injectFactory<ICategoryRemoteDeviceApi>()
        categoryRemoteDeviceApi?.jumpDownloadActivity(activity, deviceId ?: "", downloadPaths, code)
    }

    /**
     *  真正下载云文档
     */
    private fun realDownloadCloudFile(targetPath: String?) {
        val activity = activity ?: return
        if (targetPath == null) {
            Log.w(TAG, "realDownloadCloudFile targetPath is null")
            return
        }
        val selectFiles = mViewModel?.getSelectItems() ?: return
        val selectLocalFile = mViewModel?.isSelectLocalFile(selectFiles) ?: false
        Log.d(TAG, "realDownloadCloudFile select localFile: $selectLocalFile")
        if (selectLocalFile) {
            Log.w(TAG, "realDownloadCloudFile local file don't need download")
            return
        }
        val list = selectFiles.map { it as DriveFileWrapper }
        val fileCloudBrowser = Injector.injectFactory<IFileCloudBrowser>()
        fileCloudBrowser?.downloadCloudFile(activity, list.get(0), targetPath) { code ->
            if (code == ACTION_DONE || code == ACTION_CLOUD_FILE_DOWNLOAD) { //
                // 下载到本地后，搜索结果需要更新
                onResumeLoadData()
            }
        }
    }

    /**
     * 重命名文件
     */
    private fun renameCloudFile(activity: ComponentActivity, file: DriveFileWrapper) {
        Log.d(TAG, "renameCloudFile $file")
        val fileCloudBrowser = Injector.injectFactory<IFileCloudBrowser>()
        fileCloudBrowser?.renameCloudFile(activity, file) { result ->
            if (result.first == ACTION_DONE) {
                mViewModel?.changeListMode(KtConstants.LIST_NORMAL_MODE)
                adapter?.onRenameFile(file, result.second)
            }
        }
    }

    override fun getFragmentCategoryType(): Int {
        return CategoryHelper.CATEGORY_SEARCH
    }
}