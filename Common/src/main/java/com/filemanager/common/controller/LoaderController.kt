/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller

import androidx.collection.SparseArrayCompat
import com.filemanager.common.base.FileLoader
import com.filemanager.common.utils.Log

class LoaderController {
    companion object {
        private const val TAG = "LoaderController"
    }
    private val mLoaders = SparseArrayCompat<LoaderInfo<*>>()

    fun <D> initLoader(id: Int, onLoaderCompleteListener: OnLoaderListener<D>) {
        var info = mLoaders.get(id)
        if (info != null) {
            mLoaders.remove(id)
        }
        val loader = onLoaderCompleteListener.onCreateLoader() ?: kotlin.run {
            Log.w(TAG, "initLoader loader null, id=$id, listener=$onLoaderCompleteListener")
            return
        }
        info = LoaderInfo(id, loader, onLoaderCompleteListener)
        mLoaders.put(id, info)
    }

    fun onDestroy() {
        val size = mLoaders.size()
        for (index in 0 until size) {
            val info = mLoaders.valueAt(index)
            info.destroy()
        }
        mLoaders.clear()
    }

    internal class LoaderInfo<D> : FileLoader.OnFileLoaderStateListener<D> {
        private val mId: Int
        private val mLoader: FileLoader<D>
        private var mLoaderListener: OnLoaderListener<D>?

        constructor(id: Int, loader: FileLoader<D>, onLoaderCompleteListener: OnLoaderListener<D>) {
            mId = id
            mLoader = loader
            mLoaderListener = onLoaderCompleteListener
            loader.registerListener(id, this)
            loader.startLoading()
        }

        override fun onLoadStart(loader: FileLoader<D>) {
            Log.d(TAG, "onLoadStart: loader=$loader, mLoaderListener=$mLoaderListener")
            mLoaderListener?.onLoadStart()
        }

        override fun onLoadComplete(loader: FileLoader<D>, data: D?) {
            Log.d(TAG, "onLoadComplete: loader=$loader, mLoaderListener=$mLoaderListener")
            mLoaderListener?.onLoadComplete(data)
        }

        override fun onLoadCanceled(loader: FileLoader<D>) {
            Log.d(TAG, "onLoadCanceled: loader=$loader, mLoaderListener=$mLoaderListener")
            mLoaderListener?.onLoadCanceled()
        }

        fun destroy() {
            mLoader.cancelLoad()
            mLoader.abandon()
            mLoader.onDestroy()
            mLoader.unregisterListener()
            mLoaderListener?.onLoadDestroy()
            mLoaderListener = null
        }
    }
}

/**
 * Use to create loader and receive the change of loader state
 */
interface OnLoaderListener<D> {
    companion object {
        const val STATE_START = 0
        const val STATE_DONE = 1
        const val STATE_CANCEL = 2
    }

    fun onCreateLoader(): FileLoader<D>?
    fun onLoadStart() {}
    fun onLoadCanceled() {}
    fun onLoadComplete(result: D?)
    fun onLoadDestroy() {}
}
