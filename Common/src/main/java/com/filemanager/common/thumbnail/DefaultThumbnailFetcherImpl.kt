/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DefaultThumbnailFetcherImpl
 * * Description : 默认的缩略图加载类
 * * Version     : 1.0
 * * Date        : 2025/05/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.util.Size

class DefaultThumbnailFetcherImpl : BaseThumbnailFetcher() {

    override fun generatorThumbnailPath(path: String, modifyTime: Long, fileSize: Long): String {
        val type = subdivideLocalType(baseFile.mLocalType, baseFile.mData)
        return super.generatorThumbnailPath("$type", 0, 0)
    }

    override fun fetchSpecificThumbnail(originPath: String, thumbnailPath: String, thumbnailSize: Size): Boolean {
        super.fetchDefaultThumbnail(originPath, thumbnailSize)
        return true
    }
}