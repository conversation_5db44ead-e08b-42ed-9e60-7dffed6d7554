/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import androidx.core.content.ContextCompat
import com.filemanager.common.R
import com.filemanager.common.compat.OplusChangeTextUtilCompat
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Utils

class GridThumbView : FileThumbView {
    companion object {
        private const val TAG = "GridThumbView"
        private const val ALPHA_MAX_VALUE = 255
    }

    private val mTextPaint by lazy {
        Paint().apply {
            isAntiAlias = true
            color = context.getColor(com.support.appcompat.R.color.coui_color_label_on_color)
            textSize = ViewHelper.setClassificationTextSize(
                context, resources.getDimension(R.dimen.video_duration_text_size), OplusChangeTextUtilCompat.G4)
        }
    }

    // Video duration
    private val mVideoDurationMinWidth = resources.getDimensionPixelSize(R.dimen.grid_item_video_duration_width)
    private var mVideoDuration: String? = null // null mean not need to draw
    private var mVideoDurationBgRect: Rect? = null
    private var mVideoDurationTextRect: RectF? = null
    private var mVideoDurationDrawable: Drawable? = null

    // Check state: null->CheckBox is gone, else->CheckBox is visible and mean the check state
    private var mCheckedState: Boolean? = null
    private var mCheckedDrawable: Drawable? = null
    private var mCheckedRect: Pair<Int, Int>? = null // first:right margin; second:bottom margin
    private val mCheckedStateAttr = intArrayOf(com.support.appcompat.R.attr.coui_state_allSelect)

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle)

    override fun onCreateDrawableState(extraSpace: Int): IntArray {
        return if (mCheckedState == true) {
            mergeDrawableStates(super.onCreateDrawableState(extraSpace + 1), mCheckedStateAttr)
        } else {
            super.onCreateDrawableState(extraSpace)
        }
    }

    override fun drawableStateChanged() {
        super.drawableStateChanged()
        mCheckedDrawable?.state = drawableState
        if (mCheckedState == null) {
            mCheckedDrawable?.jumpToCurrentState()
        }
    }

    override fun jumpDrawablesToCurrentState() {
        super.jumpDrawablesToCurrentState()
        mCheckedDrawable?.jumpToCurrentState()
    }

    fun setVideoState(videoDuration: String? = null) {
        mVideoDuration = if (videoDuration.isNullOrEmpty().not() && videoDuration.isNullOrBlank().not()) {
            if (mVideoDurationDrawable == null) {
                mVideoDurationDrawable = ContextCompat.getDrawable(context, R.drawable.item_video_duration_view_bg)
            }
            if (mVideoDurationTextRect == null) {
                mVideoDurationTextRect = RectF()
            }
            if (mVideoDurationBgRect == null) {
                mVideoDurationBgRect = Rect()
            }
            mTextPaint.getTextBounds(videoDuration, 0, videoDuration!!.length, mVideoDurationBgRect)
            mVideoDurationBgRect!!.let { bgRect ->
                val textWidth = bgRect.right - bgRect.left
                val textHeight = bgRect.bottom - bgRect.top
                val textBgWidth = (resources.getDimensionPixelSize(R.dimen.dimen_3dp) * 2 + textWidth)
                    .coerceAtLeast(mVideoDurationMinWidth)
                bgRect.left = resources.getDimensionPixelSize(R.dimen.dimen_6dp)
                bgRect.right = bgRect.left + textBgWidth
                bgRect.bottom = resources.getDimensionPixelSize(R.dimen.dimen_5dp)
                bgRect.top = bgRect.bottom + (mVideoDurationDrawable?.minimumHeight ?: textHeight)
                mVideoDurationTextRect!!.let { txtRect ->
                    txtRect.left = (bgRect.left + (textBgWidth - textWidth) / 2).toFloat()
                    txtRect.bottom = (bgRect.bottom * 2).toFloat()
                    // Don't add txt height, because draw text need y-coordinate of the baseline of the text
                    txtRect.top = txtRect.bottom
                    txtRect.right = txtRect.left + textWidth
                }
            }
            videoDuration
        } else {
            null
        }
    }

    fun setCheckedState(isChecked: Boolean? = null) {
        mCheckedState = if (isChecked != null) {
            if (mCheckedDrawable == null) {
                mCheckedDrawable = ContextCompat.getDrawable(context, com.support.appcompat.R.drawable.coui_shape_checkbox_state)
            }
            if (mCheckedRect == null) {
                mCheckedRect = Pair(0, 0)
            }
            isChecked
        } else null
        refreshDrawableState()
    }

    fun isChecked() = mCheckedState ?: false

    fun isSelectMode() = (mCheckedState != null)

    override fun onDrawOther(canvas: Canvas) {
        drawVideoDuration(canvas)
        drawCheckBox(canvas)
        super.onDrawOther(canvas)
    }

    fun setDrawableVisible(alpha: Int) {
        mCheckedDrawable?.alpha = alpha * ALPHA_MAX_VALUE
        invalidate()
    }

    private fun drawVideoDuration(canvas: Canvas) {
        if (mVideoDuration.isNullOrEmpty()) {
            return
        }
        mVideoDurationBgRect?.let { bgRect ->
            mVideoDurationDrawable?.let { drawable ->
                drawDrawable(canvas, drawable, bgRect.left, height - bgRect.top, bgRect.right, height - bgRect.bottom)
                mVideoDurationTextRect?.let { txtRect ->
                    var x = txtRect.left
                    val y = height - txtRect.top
                    if (Utils.isRtl()) {
                        x = width - txtRect.right
                    }
                    canvas.drawText(mVideoDuration!!, x, y, mTextPaint)
                }
            }
        }
    }

    private fun drawCheckBox(canvas: Canvas) {
        if (mCheckedState != null) {
            mCheckedDrawable?.let { drawable ->
                mCheckedRect?.let { rect ->
                    drawDrawable(
                        canvas, drawable,
                        width - drawable.intrinsicWidth - rect.first,
                        height - drawable.intrinsicHeight - rect.second,
                        width - rect.first,
                        height - rect.second
                    )
                }
            }
        }
    }
}