/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ModelUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/3/16 21:31
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/3/16       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import com.filemanager.common.compat.AddonSdkCompat
import com.filemanager.common.compat.PropertyCompat

object ModelUtils {

    private const val TAG = "ModelUtils"

    private const val FEATURE_TABLET = "oplus.hardware.type.tablet"

    private const val FEATURE_LIGHT = "oppo.sys.light.func"

    private const val FEATURE_LIGHT_OPLUS = "oplus.software.product.oh_light"

    private const val REGION_EUEX = "EUEX"

    @JvmStatic
    fun isTablet(): Boolean {
        return hasFeature(FEATURE_TABLET)
    }

    @JvmStatic
    fun isEURegion(): Boolean {
        val region = PropertyCompat.sPhoneMarkRegion
        return REGION_EUEX.equals(region, true)
    }

    @JvmStatic
    fun isLight(context: Context): Boolean {
        return try {
            if (KtAppUtils.mIsOnePlusOverSea) {
                hasFeature(FEATURE_LIGHT_OPLUS)
            } else {
                context.packageManager.hasSystemFeature(FEATURE_LIGHT)
            }
        } catch (e: CommonThrowable) {
            Log.e(TAG, "isLight error: ", e)
            false
        }
    }

    @JvmStatic
    private fun hasFeature(key: String): Boolean {
        val result = try {
            AddonSdkCompat.hasFeature(key)
        } catch (e: CommonThrowable) {
            false
        }
        return result
    }
}