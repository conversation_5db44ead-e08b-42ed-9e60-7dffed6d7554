/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FileManagerExpandableRecyclerView
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2021/6/8
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.content.Context
import android.util.AttributeSet
import com.coui.appcompat.expandable.COUIExpandableRecyclerView

class FileManagerExpandableRecyclerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyle: Int = androidx.recyclerview.R.attr.recyclerViewStyle)
    : COUIExpandableRecyclerView(context, attrs, defStyle) {

    companion object {
        private const val TAG = "FileManagerExpandableRecyclerView"
    }

    /**
     * stop ColorRecyclerView over Scroll and restore default location
     * the superclass must be COUIERecyclerView
     */
    private fun stopOverScroll() {
        super.overScrollBy(0, 0, 0, 0, 0,
            0, 0, 0, false)
    }

    override fun stopScroll() {
        stopOverScroll()
        super.stopScroll()
    }

    override fun isPaddingOffsetRequired(): Boolean {
        return true
    }

    override fun getTopFadingEdgeStrength(): Float {
        return 0f
    }

    override fun getBottomPaddingOffset(): Int {
        return paddingBottom
    }
}