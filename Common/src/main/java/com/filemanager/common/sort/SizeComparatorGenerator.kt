/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2020/2/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import java.util.Comparator

class SizeComparatorGenerator : NameComparatorGenerator() {
    init {
        SizeComparatorGenerator.initLastSortMode()
    }

    override fun compare1(file1: BaseFileBean, file2: BaseFileBean, isDesc: Boolean): Int {
        if (file1.mIsDirectory && file2.mIsDirectory) {
            if (sLastSortMode != SortHelper.FILE_TIME_REVERSE_ORDER) {
                return super.compare1(file2, file1, isDesc)
            } else {
                val result = file1.mDateModified - file2.mDateModified
                return if (result < 0) {
                    -1
                } else if (result > 0) {
                    1
                } else {
                    0
                }
            }
        } else {
            var size1: Long = 0
            if (file1.mIsDirectory) {
                // do nothing
            } else {
                size1 = file1.mSize
            }
            var size2: Long = 0
            if (file2.mIsDirectory) {
                // do nothing
            } else {
                size2 = file2.mSize
            }
            val result = size1 - size2
            return if (result < 0) {
                -1
            } else if (result > 0) {
                1
            } else {
                0
            }
        }
    }

    companion object {
        val comparator = SizeComparatorGenerator()
        val REVERSE_COMPARATOR = comparator.genReverseComparator()
        private var sLastSortMode: Int = 0

        private fun initLastSortMode() {
            SizeComparatorGenerator.sLastSortMode = SortHelper.FILE_NAME_ORDER
        }

        fun setLastSortMode(mode: Int) {
            sLastSortMode = mode
        }

        fun getComparator(isDesc: Boolean = true): Comparator<BaseFileBean> {
            return if (isDesc) {
                comparator.genReverseComparator()
            } else {
                comparator.genComparator()
            }
        }
    }
}