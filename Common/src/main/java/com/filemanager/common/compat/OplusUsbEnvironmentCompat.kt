/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OplusUsbEnvironmentCompat
 ** Description : OplusUsbEnvironmentCompat
 ** Version     : 1.0
 ** Date        : 2024/09/12 10:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/12     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

import android.content.Context
import java.io.File

object OplusUsbEnvironmentCompat {

    private const val DEFAULT_EXTERNAL_PATH = "/storage/emulated/0"

        @JvmStatic
        fun getInternalPath(context: Context?): String {
            return context?.let {
                CompatUtils.compactSApi({
                    com.oplus.os.OplusUsbEnvironment.getInternalPath(it)
                }, {
                    com.heytap.addon.os.OplusUsbEnvironment.getInternalPath(it)
                })
            } ?: DEFAULT_EXTERNAL_PATH
        }

        @JvmStatic
        fun getInternalSdDirectory(context: Context?): File? {
            return context?.let {
                return CompatUtils.compactSApi({
                    com.oplus.os.OplusUsbEnvironment.getInternalSdDirectory(it)
                }, {
                    com.heytap.addon.os.OplusUsbEnvironment.getInternalSdDirectory(it)
                })
            }
        }

        @JvmStatic
        fun getExternalPath(context: Context?): String? {
            return context?.let {
                CompatUtils.compactSApi({
                    com.oplus.os.OplusUsbEnvironment.getExternalPath(it)
                }, {
                    com.heytap.addon.os.OplusUsbEnvironment.getExternalPath(it)
                })
            }
        }

        @JvmStatic
        fun getOtgPath(context: Context?): List<String>? {
            return context?.let {
                CompatUtils.compactSApi({
                    com.oplus.os.OplusUsbEnvironment.getOtgPath(context)
                }, {
                    com.heytap.addon.os.OplusUsbEnvironment.getOtgPath(context)
                })
            }
        }
    @JvmStatic
    fun getMultiappSdDirectory(): String? {
        return CompatUtils.compactSApi({
            com.oplus.os.OplusUsbEnvironment.getMultiappSdDirectory()
        }, {
            com.heytap.addon.os.OplusUsbEnvironment.getMultiappSdDirectory()
        })
    }
}