/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - BaseSelectionViewHolder.java
 ** Description: abstract ViewHolder class to selection
 ** Version: 1.0
 ** Date : 2020/05/25
 ** Author: <PERSON>afei.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/05/25    1.0     create
 ****************************************************************/

package com.filemanager.common.base

import android.graphics.Rect
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.dragselection.SlideUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.view.GridThumbView
import com.oplus.dropdrag.base.SelectionViewHolder
import com.oplus.dropdrag.recycleview.ItemDetailsLookup

abstract class BaseSelectionViewHolder(itemView: View, canLongPressOrClick: Boolean = true) :
    SelectionViewHolder<Int>(itemView), COUIRecyclerView.ICOUIDividerDecorationInterface {
    companion object {
        private const val TAG = "BaseSelectionViewHolder"
    }

    private var mKey: Int? = null
    var mCheckBox: COUICheckBox? = null
    var dividerLine: View? = null
    var itemCount: Int = 0

    init {
        mDetails = StorageItemDetails(canLongPressOrClick)
        dividerLine = itemView.findViewById(R.id.divider_line)
    }

    fun updateDividerVisible(lastItemIndex: Int, position: Int) {
        updateDividerVisible(lastItemIndex != position)
    }

    fun updateDividerVisible(visible: Boolean) {
        dividerLine?.let {
            if (visible) {
                it.visibility = View.VISIBLE
            } else {
                it.visibility = View.GONE
            }
        }
    }

    override fun updateKey(key: Int?) {
        mKey = key
    }

    open fun isInDragRegionImpl(event: MotionEvent) = true

    final override fun isInDragRegion(event: MotionEvent): Boolean {
        // When PC Assistant connect, disable drag
        return if (PCConnectAction.isScreenCast()) {
            false
        } else {
            isInDragRegionImpl(event)
        }
    }

    open fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        mCheckBox?.let {
            val checkBoxRect = Rect()
            it.getGlobalVisibleRect(checkBoxRect)
            // add offset to left and right border.consistent with ColorListView
            val leftOffset = SlideUtils.getCheckBoxLeftOffset(MyApplication.sAppContext)
            val rightOffset = SlideUtils.getCheckBoxRightOffset(MyApplication.sAppContext)
            return ((event.rawX > (checkBoxRect.left - leftOffset))
                    && (event.rawX < (checkBoxRect.right + rightOffset)))
        }
        return false
    }

    final override fun isInSelectRegion(event: MotionEvent): Boolean {
        // When PC Assistant connect, disable slide to choose
        if (PCConnectAction.isScreenCast()) {
            return false
        }
        return isInSelectRegionImpl(event)
    }

    open fun isInClickArea(event: MotionEvent): Boolean {
        return true
    }

    private inner class StorageItemDetails(canLongPressOrClick: Boolean = true)
        : ItemDetailsLookup.ItemDetails<Int>() {
        private val mCanLongPressOrClick: Boolean = canLongPressOrClick

        override fun inSelectionHotspot(e: MotionEvent): Boolean {
            return isInSelectRegion(e)
        }

        override fun isClickArea(e: MotionEvent): Boolean {
            return isInClickArea(e)
        }

        override fun canLongPressOrClick(): Boolean {
            // Fix bug 1616522: In zoom window and pc connect, long press to select files is not supported,
            // unless long press to enter edit mode
            return if (mCanLongPressOrClick && PCConnectAction.isScreenCast()) {
                // Checkbox may be displayed off-screen due to animation. In this case, it is not in select mode.
                val normalMode = when {
                    (mCheckBox?.visibility == View.VISIBLE) -> {
                        val checkBoxRect = Rect()
                        mCheckBox!!.getGlobalVisibleRect(checkBoxRect)
                        val parentRect = Rect()
                        itemView.getGlobalVisibleRect(parentRect)
                        Log.d(TAG, "canLongPressOrClick: checkBoxRect=$checkBoxRect, parentRect=$parentRect")
                        (checkBoxRect.right <= parentRect.left) || (checkBoxRect.left >= parentRect.right)
                    }
                    (itemView is GridThumbView) -> {
                        !(itemView as GridThumbView).isSelectMode()
                    }
                    else -> {
                        true
                    }
                }
                PCConnectAction.checkViewCanLongPress(!normalMode)
            } else {
                mCanLongPressOrClick
            }
        }

        override fun inDragRegion(e: MotionEvent): Boolean {
            return isInDragRegion(e)
        }

        override val position: Int
            get() = adapterPosition

        override val selectionKey: Int?
            get() = mKey
    }

    override fun drawDivider(): Boolean {
        return false
    }
}