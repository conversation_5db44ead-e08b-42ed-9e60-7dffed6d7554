/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SwitchChangeEvent
 ** Description : 授权卡片的在RecyclerView中的dataBeanWrapper
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.filemanager.common.bean

data class SwitchChangeEvent(val eventType: Int = EVENT_TYPE_ON) {

    companion object {
        const val TAG = "SwitchChangeEvent"

        const val EVENT_TYPE_ON = 1
        const val EVENT_TYPE_OFF = 2
    }
}
