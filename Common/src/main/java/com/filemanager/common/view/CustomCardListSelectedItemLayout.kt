/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : HoverAwareFrameLayout
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/8/18
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   ********                2025/8/18       1
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.graphics.RectF
import android.util.AttributeSet
import android.view.MotionEvent
import com.coui.appcompat.cardlist.COUICardListSelectedItemLayout
import com.coui.appcompat.state.COUIMaskEffectDrawable

class CustomCardListSelectedItemLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
    defStyleRes: Int = 0
) : COUICardListSelectedItemLayout(context, attrs, defStyleAttr, defStyleRes) {

    private var mMaskDrawable: COUIMaskEffectDrawable? = null
    private var mHoverRadius: Float = 0f
    private var mBackgroundAnimationEnabled = true

    init {
        initMaskEffect()
    }

    private fun initMaskEffect() {
        // 创建遮罩绘制对象
        mMaskDrawable = COUIMaskEffectDrawable(context, 1).apply {
            setMaskRect(RectF(0f, 0f, width.toFloat(), height.toFloat()), mHoverRadius, mHoverRadius)
            setAnimateEnabled(mBackgroundAnimationEnabled)
        }

        foreground = mMaskDrawable
    }
    override fun dispatchHoverEvent(event: MotionEvent): Boolean {
        when (event.actionMasked) {
            MotionEvent.ACTION_HOVER_ENTER -> mMaskDrawable?.setHoverStateLocked(true, true, true)
            MotionEvent.ACTION_HOVER_EXIT -> mMaskDrawable?.setHoverStateLocked(false, false, true)
        }
        return super.dispatchHoverEvent(event)
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mMaskDrawable?.setMaskRect(
            RectF(0f, 0f, w.toFloat(), h.toFloat()), mHoverRadius, mHoverRadius
        )
    }

    override fun setBackgroundAnimationEnabled(enabled: Boolean) {
        mBackgroundAnimationEnabled = enabled
        mMaskDrawable?.setAnimateEnabled(mBackgroundAnimationEnabled)
    }

    override fun drawableStateChanged() {
        super.drawableStateChanged()
        mMaskDrawable?.state = drawableState
    }
}
