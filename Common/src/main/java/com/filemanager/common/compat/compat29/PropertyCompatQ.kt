/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/15
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.compat.compat29

import androidx.annotation.VisibleForTesting
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.compat.PropertyCompat.FEATURE_GDPR
import com.filemanager.common.compat.PropertyCompat.FEATURE_REALME_MACHINE_MODEL
import com.filemanager.common.compat.PropertyCompat.PROPERTY_COLOROS_VERSION
import com.filemanager.common.compat.PropertyCompat.PROPERTY_CPU_INFO
import com.filemanager.common.compat.PropertyCompat.PROPERTY_LOG_ENABLE
import com.filemanager.common.compat.PropertyCompat.PROPERTY_LOG_PANIC
import com.filemanager.common.compat.PropertyCompat.PROPERTY_OTA_VERSION
import com.filemanager.common.compat.PropertyCompat.PROPERTY_PHONE_MARK_REGION
import com.filemanager.common.compat.PropertyCompat.PROPERTY_ROM_SIZE
import com.filemanager.common.compat.PropertyCompat.PROPERTY_SDCARD_SUPPORT
import com.filemanager.common.compat.PropertyCompat.PROPERTY_SYSTEM_REGION
import com.filemanager.common.utils.Log
import com.oplus.compat.os.SystemPropertiesNative

object PropertyCompatQ : PropertyCompat.IPropertyInterface {
    private const val TAG = "OPlusPropertyCompatQ"

    override fun getCPUInfo() = getProperty(PROPERTY_CPU_INFO)

    override fun getModel() = getProperty(PropertyCompat.PROPERTY_MODEL)

    override fun getOTAInfo() = getProperty(PROPERTY_OTA_VERSION)

    override fun getSystemRegion() = getProperty(PROPERTY_SYSTEM_REGION, "CN")

    override fun getPhoneMarkRegion() = getProperty(PROPERTY_PHONE_MARK_REGION, PropertyCompat.sSystemRegion)

    override fun getColorOSVersion(): String {
        val default = "V3.0.0"
        var v = getProperty(PROPERTY_COLOROS_VERSION, default)
        return if (v.isNullOrEmpty()) default else v
    }

    override fun getAndroidVersion() = getProperty(PropertyCompat.PROPERTY_ANDROID_VERSION)

    override fun isLogEnable(): Boolean {
        val qeOff = getProperty(PROPERTY_LOG_PANIC)
        val qeOffMtk = getProperty(PROPERTY_LOG_ENABLE)
        return "true".equals(qeOff, ignoreCase = true) || "true".equals(qeOffMtk, ignoreCase = true)
    }

    override fun getRomSize() = getProperty(PROPERTY_ROM_SIZE)
    override fun getOplusSeries() = getProperty(FEATURE_REALME_MACHINE_MODEL)

    override fun isSupportSDCard() = getProperty(PROPERTY_SDCARD_SUPPORT, false)
    override fun isGDPR() = getProperty(FEATURE_GDPR, false)
    override fun isNeedUpdateByOta(): Boolean = true

    override fun getRSAProperty(): String {
        return getProperty(PropertyCompat.PROPERTY_RSA, "")
    }

    override fun isLightOS(): Boolean {
        return false
    }

    override fun getAnimLevel(): String {
        return ""
    }

    override fun getChannelInfo(): String {
        return getProperty(PropertyCompat.PROPERTY_CHANNEL)
    }

    override fun getChannelCountry(): String {
        return getProperty(PropertyCompat.PROPERTY_CHANNEL_COUNTRY)
    }

    override fun getPipelineCarrier(): String {
        return getProperty(PropertyCompat.PROPERTY_PIPELINE_CARRIER)
    }

    override fun getPipelineRegion(): String {
        return getProperty(PropertyCompat.PROPERTY_PIPELINE_REGION)
    }

    @VisibleForTesting
    fun getProperty(name: String, default: String = ""): String {
        return try {
            SystemPropertiesNative.get(name, default) ?: default
        } catch (e: Throwable) {
            Log.w(TAG, "getProperty[String] failed: $name, ${e.message}")
            default
        }.also {
            Log.d(TAG, "getProperty[String]: n=$name, value=$it, default=$default")
        }
    }

    private fun getProperty(name: String, default: Boolean): Boolean {
        return try {
            SystemPropertiesNative.getBoolean(name, default)
        } catch (e: Throwable) {
            Log.w(TAG, "getProperty[Boolean] failed: $name, ${e.message}")
            default
        }.also {
            Log.d(TAG, "getProperty[Boolean]: n=$name, value=$it, default=$default")
        }
    }
}

