/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/4/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common

import android.app.Activity
import android.app.Application
import android.content.Context
import android.net.Uri
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.Constants
import com.filemanager.common.crashhandler.CrashHandler
import com.filemanager.common.fileutils.UriHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.WpsManager
import com.filemanager.thumbnail.IPathToUriCallback
import com.filemanager.thumbnail.ThumbnailManager
import com.oplus.dropdrag.DragDropSdkManager
import com.oplus.filemanager.interfaze.ad.IAdvertApi
import com.oplus.filemanager.interfaze.push.IPushApi
import com.oplus.filemanager.interfaze.simulateclick.ISimulateClickApi
import org.jetbrains.annotations.VisibleForTesting
import java.util.UUID
import kotlin.properties.Delegates

object MyApplication {
    const val TAG = "MyApplication"

    private const val WPS_THUMB_PRIORITY = 200
    private const val YO_ZO_THUMB_PRIORITY = 100

    /**
     * 应用启动类型：冷启动和热启动
     */
    private const val LAUNCH_TYPE_COLD = 1
    private const val LAUNCH_TYPE_HOT = 2

    private var instance: Context by Delegates.notNull()

    @JvmStatic
    var flavorRegion: String = ""
        @VisibleForTesting set

    @JvmStatic
    var flavorBrand: String = ""
        @VisibleForTesting set

    @JvmStatic
    var buildFlavorBrand: String = ""
        private set

    @JvmStatic
    var buildFlavorRegion: String = ""
        private set

    @JvmStatic
    fun configureBuildInfo(buildBrand: String, buildRegion: String) {
        buildFlavorBrand = buildBrand
        buildFlavorRegion = buildRegion
    }

    @JvmStatic
    fun init(context: Context, region: String? = "") {
        instance = context
        Log.i(TAG, "init context $instance")
        flavorRegion = region ?: ""
        flavorBrand = when {
            Utils.isRealmePhone() -> KtUtils.FLAVOR_REALME
            Utils.isOnePlusPhone() -> KtUtils.FLAVOR_ONEPLUS
            else -> KtUtils.FLAVOR_OPPO
        }
    }

    @JvmStatic
    fun onCreate(application: Application) {
        isColdLaunch = true
        CrashHandler.instance.init()
        initDragDropSelection(application)
        initThumbnail(application)
        Injector.injectFactory<ISimulateClickApi>()?.init(true, Constants.LOG_DEFAULT_TAG, BuildConfig.DEBUG)
        initAdRelate(application)
    }

    /**
     * 初始化广告相关的sdk：例如风险控制sdk，透传消息sdk
     */
    private fun initAdRelate(app: Application) {
        val advertApi = Injector.injectFactory<IAdvertApi>()
        // 保存广告sdk的版本
        val adSdkVersion = advertApi?.getAdSdkVersion()
        StatisticsUtils.setAdSdkVersion(adSdkVersion)
        // 初始化风险控制sdk
        advertApi?.initRiskSdk(app)
        // 初始化透传消息的sdk
        val pushApi = Injector.injectFactory<IPushApi>()
        pushApi?.init(app)
    }

    @JvmStatic
    private fun initDragDropSelection(application: Application) {
        DragDropSdkManager.baseLogTag = Constants.BASE_LOG_TAG
        DragDropSdkManager.enableLog = PropertyCompat.sLogEnable
        DragDropSdkManager.init(application)
    }

    @JvmStatic
    private fun initThumbnail(application: Application) {
        ThumbnailManager.baseLogTag = Constants.BASE_LOG_TAG
        ThumbnailManager.WpsDocConfigs.enableThumbnail = WpsManager.isEnabledWpsThumbnail
        ThumbnailManager.WpsDocConfigs.usagePriority = WPS_THUMB_PRIORITY
        ThumbnailManager.YoZoDocConfigs.enableThumbnail = !FeatureCompat.sIsLightVersion
        ThumbnailManager.YoZoDocConfigs.usagePriority = YO_ZO_THUMB_PRIORITY
        ThumbnailManager.init(
            application,
            PropertyCompat.sLogEnable,
            object : IPathToUriCallback {
                override fun getFileProviderUri(path: String?): Uri? {
                    return UriHelper.getFileProviderUri(path)
                }
            })
    }

    @JvmStatic
    val appContext: Context
        get() = instance

    /**
     * Add legacy offending naming alias for corrected naming member [appContext].
     * Pls merge [appContext] and [sAppContext] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("appContext"))
    @JvmStatic
    val sAppContext: Context
        get() = appContext

    @JvmStatic
    var activities: MutableList<Activity> = mutableListOf()

    @JvmStatic
    @Volatile
    var dmpAndIndexInitSuc: Boolean = false

    @JvmStatic
    @Volatile
    var activityResumedCounter: Int = 0

    /**
     * APP是否处于后台
     */
    @JvmStatic
    @Volatile
    var appInBackground: Boolean? = null

    /**
     * 处于后台原因是否是由于打开了在首页文件
     */
    @JvmStatic
    @Volatile
    var openFileCauseBg: Boolean = false

    /**
     * 判断是否是冷启动
     */
    @JvmStatic
    var isColdLaunch: Boolean = true

    /**
     * 记录用户一次从打开到退出app的唯一ID
     */
    @JvmStatic
    val sessionId: String by lazy {
        UUID.randomUUID().toString()
    }

    /**
     * 1为冷启；2为热启
     * 冷启动定义：进程为重新启动，且创建页面
     * 热启动定义：进程存在的情况打开文管
     */
    @JvmStatic
    fun getAppLaunchType(): Int {
        return if (isColdLaunch) {
            LAUNCH_TYPE_COLD
        } else {
            LAUNCH_TYPE_HOT
        }
    }
}