/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: MacDragUtil.kt
 ** Description: MacDragUtil
 ** Version: 1.0
 ** Date: 2025/1/13
 ** Author: W9086652
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.dragselection

import android.app.Activity
import android.app.PendingIntent
import android.content.ClipDescription
import android.content.Intent
import android.net.Uri
import androidx.activity.ComponentActivity
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.CommonConstants.DEFAULT_DOWNLOAD_PATH
import com.filemanager.common.constants.CommonConstants.DRAG_FROM_MAC_FILES
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.EncryptConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.DEEPLINK_REMOTE_PC
import com.filemanager.common.constants.KtConstants.IS_DFM
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.dragselection.MacDragUtil.MacDragObject.deviceId
import com.filemanager.common.dragselection.MacDragUtil.MacDragObject.targetCategoryType
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.filemanager.common.utils.StatisticsUtils
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.oplus.filemanager.interfaze.categoryremotedevice.ICategoryRemoteDeviceApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.reflect.Type

class MacDragUtil {

    object MacDragObject {
        var deviceId: String = ""
        var targetCategoryType: Int = -1
        var isDraggingFromMac = false // 当前是否正在mac拖拽中
            get() {
                Log.i(TAG, "get isDraggingFromMac : $this")
                return field
            }
            set(value) {
                Log.i(TAG, "set isDraggingFromMac: $value")
                field = value
            }
    }

    companion object {
        const val TAG = "MacDragUtil"
        private const val PACKAGE_NAME_KEY = "package_name_key"
        private const val FROM_EXT_KEY = "from_external_app"
        private const val TO_CATEGORY_KEY = "to_category_key"
        private const val INVALID_CATEGORY = -1
        private const val PHOTOS_VIDEO_CATEGORY = 0
        private const val AUDIO_CATEGORY = 1
        private const val DOCUMENT_CATEGORY = 2
        private const val OTHER_FILE_CATEGORY = 3

        fun downloadMacFile(
            activity: Activity,
            desPath: String,
            onMacDownloadCallBack: ((Boolean, ArrayList<RemoteFileBean>) -> Unit)?,
            list: ArrayList<RemoteFileBean>?
        ) {
            if (list == null) {
                return
            }
            val myConsumer: (Boolean) -> Unit = {
                onMacDownloadCallBack?.invoke(it, list)
            }
            val pairList = arrayListOf<Pair<String, Long>>()
            list.forEach { bean ->
                val pair = Pair(bean.originalPath, bean.mSize)
                pairList.add(pair)
            }
            val code = if (targetCategoryType == CategoryHelper.CATEGORY_PRIVATE_SAVE) {
                MessageConstant.MSG_ENCRYPT
            } else {
                -1
            }

            val pendingIntent = if (targetCategoryType == CategoryHelper.CATEGORY_PRIVATE_SAVE) {
                PendingIntent.getActivity(
                    activity,
                    0,
                    initIntent(pairList),
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else if (targetCategoryType == CategoryHelper.CATEGORY_DFM) {
                PendingIntent.getActivity(
                    activity,
                    0,
                    initPendingIntent(desPath, true),
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else if (targetCategoryType == CategoryHelper.CATEGORY_FOLDER) {
                PendingIntent.getActivity(
                    activity,
                    0,
                    initPendingIntent(desPath, false),
                    PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
                )
            } else {
                null
            }
            Log.w(TAG, "code  $code , pendingIntent  $pendingIntent")
            Injector.injectFactory<ICategoryRemoteDeviceApi>()?.downloadRemoteFile(
                activity as ComponentActivity,
                deviceId,
                pairList,
                code,
                pendingIntent,
                desPath,
                myConsumer
            )
            statisticsDragDropDownload(pairList)
        }

        private fun statisticsDragDropDownload(paths: ArrayList<Pair<String, Long>>) {
            val fileCount = paths.size
            var fileSize = 0L
            paths.forEach {
                fileSize += it.second
            }
            OptimizeStatisticsUtil.remoteFileOperateEvent(
                StatisticsUtils.REMOTE_VALUE_OPERATE_DRAG_DROP_DOWNLOAD, fileCount, fileSize
            )
        }

        private fun initIntent(pairList: List<Pair<String, Long>>): Intent {
            val fileType = determineFileCategory(pairList)
            val value = when (fileType) {
                MimeTypeHelper.IMAGE_TYPE, MimeTypeHelper.VIDEO_TYPE -> PHOTOS_VIDEO_CATEGORY
                MimeTypeHelper.AUDIO_TYPE -> AUDIO_CATEGORY
                MimeTypeHelper.DOC_TYPE -> DOCUMENT_CATEGORY
                MimeTypeHelper.UNKNOWN_TYPE -> OTHER_FILE_CATEGORY
                else -> OTHER_FILE_CATEGORY
            }
            val intent = Intent()
            intent.action = Constants.INTENT_FILE_SAFE_ACTION_OPLUS
            //OTHER_FILE_CATEGORY是跳转到私密保险箱其他文件界面，目前私密保险箱不支持，所以默认跳首页
            if (value != OTHER_FILE_CATEGORY) {
                intent.putExtra(TO_CATEGORY_KEY, value)
            }
            intent.putExtra(PACKAGE_NAME_KEY, AppUtils.getPackageName())
            intent.putExtra(FROM_EXT_KEY, true)
            intent.`package` = EncryptConstants.FILE_ENCRYPTION_PACKGE_OPLUS
            return intent
        }

        private fun initPendingIntent(path: String, isDfm: Boolean): Intent {
            val intent = Intent(Intent.ACTION_VIEW)
            val uri = Uri.parse(DEEPLINK_REMOTE_PC)
            intent.setData(uri)
            intent.putExtra(KtConstants.FILE_PATH, path)
            intent.putExtra(KtConstants.FROM, KtConstants.SELF)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            intent.putExtra(IS_DFM, isDfm)
            return intent
        }

        fun determineFileCategory(pairList: List<Pair<String, Long>>): Int {
            // 存储当前检测到的类型
            val detectedCategories = mutableSetOf<Int>()

            for (pair in pairList) {
                val typeFromPath = MimeTypeHelper.getTypeFromPath(pair.first)
                detectedCategories.add(typeFromPath)
            }

            return when {
                detectedCategories.isEmpty() -> INVALID_CATEGORY // 无效类型
                detectedCategories.size > 1 -> MimeTypeHelper.UNKNOWN_TYPE // 多个类型
                else -> detectedCategories.first() // 返回唯一类型
            }
        }

        /**
         * 解析拖拽过来的文件
         * */
        fun parseFiles(
            activity: Activity,
            path: String = DEFAULT_DOWNLOAD_PATH,
            onMacDownloadCallBack: ((Boolean, ArrayList<RemoteFileBean>) -> Unit)?,
            description: ClipDescription?
        ) {
            // 启动一个新的协程
            GlobalScope.launch(Dispatchers.Main) {
                // 在IO线程上执行耗时操作
                val remoteFileList = withContext(Dispatchers.IO) {
                    val bundle = description?.extras
                    val jsonString = bundle?.getString(DRAG_FROM_MAC_FILES)
                    val listType: Type = object : TypeToken<List<RemoteFileBean>>() {}.type
                    // 解析JSON字符串为ArrayList<RemoteFileBean>
                    Gson().fromJson<ArrayList<RemoteFileBean>>(jsonString, listType)
                }
                Log.w(TAG, "start download")
                // 切换回主线程，调用downloadMacFile
                downloadMacFile(activity, path, onMacDownloadCallBack, remoteFileList)
            }
        }
    }
}