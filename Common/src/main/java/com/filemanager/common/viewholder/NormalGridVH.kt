/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.viewholder
 * * Version     : 1.0
 * * Date        : 2020/9/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.content.Context
import android.graphics.Rect
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.imageloader.glide.GlideLoader
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.GridThumbView
import com.filemanager.common.wrapper.AudioFileWrapper
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.PathFileWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*

class NormalGridVH(val convertView: View) : BaseNormalVH(convertView) {
    companion object {
        fun getLayoutId(): Int {
            return R.layout.normal_scan_grid_item
        }
    }

    private var mImg: GridThumbView? = null
    private var mDuration: TextView = convertView.findViewById(R.id.file_duration_tv)

    init {
        mImg = convertView.findViewById(R.id.file_grid_item_icon)
    }

    fun setItemWidth(width: Int) {
        mImg?.apply {
            if (layoutParams == null) {
                layoutParams = ViewGroup.LayoutParams(width, width)
            } else {
                layoutParams.width = width
                layoutParams.height = width
            }
        }
    }

    fun setBorderRoundCornerType(roundCornerByCornerType: Boolean, roundCornerType: Int = GlideLoader.ROUND_CONNER_NONE) {
        mImg?.setBorderRoundCornerType(roundCornerByCornerType, roundCornerType = roundCornerType)
    }

    override fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        val checkBoxRect = Rect()
        mImg?.getGlobalVisibleRect(checkBoxRect)
        return checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }

    override fun updateViewHolder(
        context: Context,
        key: Int?,
        data: BaseFileBean,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        sizeCache: HashMap<String, String>,
        threadManager: ThreadManager,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        mImg?.apply {
            val isCShot = if (data is ImageFileWrapper) {
                data.getCShot() > 0
            } else false
            setImgCShotState(isCShot)
            showDurationIfNeed(mDuration, data)
            setDrmState(data.mLocalType == MimeTypeHelper.DRM_TYPE)

            if ((data.mLocalType == MimeTypeHelper.VIDEO_TYPE) || (data.mLocalType == MimeTypeHelper.IMAGE_TYPE)) {
                setStrokeStyle(FileThumbView.STROKE_RECT)
            } else {
                setStrokeStyle(FileThumbView.STROKE_NONE)
            }
            FileImageLoader.sInstance.clear(context, this)
            FileImageLoader.sInstance.displayDefault(data, this, data.getOrientation(), thumbnailType = FileImageLoader.THUMBNAIL_TYPE_SQUARE,
                errorImageType = FileImageLoader.ERROR_IMAGE_NORMAL_GRID)

            val checkState = if (choiceMode) {
                selectionArray.contains(key)
            } else null
            setCheckedState(checkState)
            val alpha = HiddenFileHelper.getAlphaWithHidden(data.mDisplayName, adapter.mIsDarkModel)
            this.alpha = alpha
            contentDescription = data.mDisplayName
            setFileLabelFlag(data.mHasLabel)
        }
    }

    private fun showDurationIfNeed(mDuration: TextView, data: BaseFileBean) {
        var duration = if ((data is AudioFileWrapper) && (data.mLocalType == MimeTypeHelper.VIDEO_TYPE) && (data.mDuration != null)) {
            KtUtils.formatVideoTime(data.mDuration!! / 1000L)
        } else null
        mDuration.visibility = View.GONE
        (data as? AudioFileWrapper)?.mDuration?.let {
            if (it > 0) {
                mDuration.visibility = View.VISIBLE
                mDuration.text = duration
            }
        }
        (data as? PathFileWrapper)?.let {
            GlobalScope.launch(Dispatchers.IO) {
                val mediaDuration = it.getMediaDuration()
                if (mediaDuration > 0) {
                    duration = KtUtils.formatVideoTime(mediaDuration / 1000L)
                    withContext(Dispatchers.Main) {
                        mDuration.visibility = View.VISIBLE
                        mDuration.text = duration
                    }
                }
            }
        }
    }
}