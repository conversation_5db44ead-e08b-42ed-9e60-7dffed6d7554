package com.filemanager.common.thread

import java.util.concurrent.FutureTask

/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/2/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
abstract class BaseThreadTask<T> : FutureTask<T>, Comparable<PriorityComparable>, PriorityComparable {
    var mThreadCancelInterface: ThreadCancelInterface? = null

    @Volatile
    var mIsCallCancel = false
    var mRunnable: FileRunnable? = null
    var mRemoveWorkInterface: RemoveWorkInterface? = null

    constructor(runnable: FileRunnable, result: T?, removeWorkInterface: RemoveWorkInterface?) : super(runnable, result) {
        mRunnable = runnable
        mRemoveWorkInterface = removeWorkInterface
    }

    override fun compareTo(priorityComparable: PriorityComparable): Int {
        return getPriority().priorityValue - priorityComparable.getPriority().priorityValue
    }

    override fun equals(obj: Any?): Boolean {
        return null != obj && obj is PriorityComparable && obj.getPriority() === getPriority() && super.equals(obj)
    }

    override fun hashCode(): Int {
        return super<Comparable>.hashCode()
    }

    abstract fun getUniqueFlag(): String
    abstract override fun getPriority(): ThreadPriority
    abstract fun getThreadType(): ThreadType
}

interface ThreadCancelInterface {
    fun onCancel()
}