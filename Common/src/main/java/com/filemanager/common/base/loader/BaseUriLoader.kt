/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.factor
 * * Version     : 1.0
 * * Date        : 2020/2/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base.loader

import android.content.Context
import android.database.Cursor
import android.net.Uri
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.FileTaskLoader
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.Log

data class UriLoadResult<K, T : BaseFileBean>(
    val resultList: MutableList<T>,
    val resultMap: HashMap<K, T>
) {
    /**
     * 搜索Loader的结果列表，里面包含了分类Bean：CategoryFileWrapper，更多Bean:MoreFileWrapper，三方应用授权卡片bean：SearchCardWrapper
     * 具体数据: 本地文档数据SearchDMPFileWrapper, 三方应用数据：ThirdAppFileWrapper， 云文档搜索结果Bean：DriveFileWrapper
     * 标签数据Bean: SearchLabelWrapper  等等,包含所有的数据
     */
    @Deprecated("Legacy offending naming", ReplaceWith("resultList"))
    val mResultList: MutableList<T>
        get() = resultList

    /**
     * 跟列表mResultList对应，
     * key值为各个Loader(GlobalSearchLoader, ThirdAppFileSearchLoader, DriveFileSearchLoader)中的getItemKey值，用于选择模式，
     * value值为上面 mResultList中的各类具体的数据bean
     * 注意：这里针对不可参与选择的Bean没有getItemKey中赋值处理，itemKey相同，比如：CategoryFileWrapper，MoreFileWrapper得到的itemKey
     */
    @Deprecated("Legacy offending naming", ReplaceWith("resultMap"))
    val mResultMap: HashMap<K, T>
        get() = resultMap
}

abstract class BaseUriLoader<K, T : BaseFileBean>(context: Context) :
    FileTaskLoader<UriLoadResult<K, T>>(context) {
    companion object {
        private const val TAG = "BaseUriLoader"
    }

    var mUri: Uri? = null
    var mProjection: Array<String>? = null
    var mSelectionArgs: Array<String>? = null
    var mSelection: String? = null
    var mSortOrder: String? = null
    var mLoadResult: UriLoadResult<K, T>? = null
    var mObserver: ForceLoadContentObserver? = null
    var mObserverUris: Array<Uri>? = null
    var mInterrupt = false

    fun initData() {
        mUri = getUri()
        mObserverUris = getObserverUri()
        mObserverUris?.let { uris ->
            if (uris.isNotEmpty()) {
                mObserver = ForceLoadContentObserver()
                runCatching {
                    for (observerUri in uris) {
                        context.contentResolver?.registerContentObserver(
                            observerUri,
                            true,
                            mObserver!!
                        )
                    }
                }.onFailure {
                    Log.i(TAG, "registerContentObserver failed, ${it.message}")
                }
            }
        }
    }

    private fun isCancelled() = Thread.currentThread().isInterrupted.also {
        if (it) {
            Log.d(TAG, "loadInBackground interrupted")
        }
    }

    override fun loadInBackground(): UriLoadResult<K, T>? {
        val items = ArrayList<T>()
        val keyMap = HashMap<K, T>()
        Log.d(TAG, "loadInBackground() start")
        runCatching {
            val uri = mUri ?: run {
                Log.e(TAG, "mUri is null")
                return UriLoadResult(items, keyMap)
            }
            mProjection = getProjection()
            mSelectionArgs = getSelectionArgs()
            mSelection = addFilterSize(getSelection())
            mSortOrder = getSortOrder()
            preHandleBeforeBackground()
            Log.d(TAG, "loadInBackground() mUri=$uri," +
                    "mProjection=$mProjection," +
                    "mSelection=$mSelection," +
                    "mSelectionArgs=$mSelectionArgs," +
                    "mSortOrder=$mSortOrder")
            context.contentResolver.query(uri, mProjection, mSelection, mSelectionArgs, mSortOrder)?.use {
                if (!isCancelled()) {
                    var key: K?
                    while (it.moveToNext() && !isCancelled()) {
                        val item = createFromCursor(it, mUri)
                        if (item != null) {
                            if (preHandleResultNoNeedUpdateKey()) {
                                key = getItemKey(item) ?: continue
                                keyMap[key] = item
                            }
                            items.add(item)
                        }
                    }
                }
            }
        }.onFailure {
            Log.e(TAG, "loadInBackground ${it.message}")
        }
        Log.d(TAG, "loadInBackground() items.size = ${items.size}")
        val sortItems = preHandleResultBackground(items)
        Log.d(TAG, "loadInBackground() sortItems.size= ${sortItems.size}")
        if (!preHandleResultNoNeedUpdateKey()) {
            for (item in sortItems) {
                val key = getItemKey(item) ?: continue
                keyMap[key] = item
            }
        }
        return UriLoadResult(sortItems, keyMap)
    }

    abstract fun getUri(): Uri?
    abstract fun getSelection(): String?
    abstract fun getSelectionArgs(): Array<String>?
    abstract fun getObserverUri(): Array<Uri>?
    abstract fun getProjection(): Array<String>?
    abstract fun createFromCursor(cursor: Cursor, uri: Uri?): T?
    abstract fun getItemKey(item: T): K?

    /**
     * when after preHandleResultBackground ,it will add some item,so need to update KeyMap
     * @return whether update keyMap
     */
    open fun preHandleResultNoNeedUpdateKey(): Boolean {
        return true
    }

    /**
     * Add size > 0 filter condition to the selection
     * @param selection selection
     * @return selection
     */
    open fun addFilterSize(selection: String?): String {
        return MediaStoreCompat.addFilterSize(selection)
    }

    /**
     * Processing the query result list in background,Subclasses can be replicated
     * @return Processed results
     */
    open fun preHandleResultBackground(list: MutableList<T>): MutableList<T> {
        return list
    }

    /**
     * Preprocessing before querying data in background,Subclasses can be replicated
     */
    open fun preHandleBeforeBackground() {
    }

    /**
     * Collation of the query,Subclasses can be replicated
     */
    open fun getSortOrder(): String? {
        return getOrderWithType(SortModeUtils.getSharedSortMode(appContext, SortModeUtils.CATEGORY_SORT_RECORD))
    }

    private fun getOrderWithType(sort: Int): String {
        val orderBy = when (sort) {
            SortHelper.FILE_TIME_REVERSE_ORDER -> "date_modified DESC"

            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> "_size DESC"

            else -> "date_modified DESC"
        }
        return orderBy
    }

    override fun onStartLoading() {
        if (mInterrupt) {
            return
        }

        val resultSize = mLoadResult?.resultList?.size ?: 0
        if (resultSize > 0) {
            deliverResult(mLoadResult)
        }

        if (takeContentChanged() || (mLoadResult == null) || (resultSize == 0)) {
            forceLoad()
        }
    }

    override fun onStopLoading() {
        cancelLoad()
    }

    override fun forceLoad() {
        runCatching {
            super.forceLoad()
        }.onFailure {
            Log.w(TAG, "forceLoad = ${it.message}")
        }
    }

    override fun deliverResult(data: UriLoadResult<K, T>?) {
        if (mReset) {
            data?.let {
                (it.resultList as? ArrayList)?.clear()
                (it.resultMap as? HashMap)?.clear()
            }
            return
        }
        mLoadResult = data
        if (mStarted) {
            super.deliverResult(data)
        }
    }

    override fun onReset() {
        super.onReset()
        onStopLoading()
        mObserver?.let { observer ->
            runCatching {
                context.contentResolver?.unregisterContentObserver(observer)
            }.onFailure {
                Log.i(TAG, "unregisterContentObserver exception ${it.message}")
            }
        }
    }
}