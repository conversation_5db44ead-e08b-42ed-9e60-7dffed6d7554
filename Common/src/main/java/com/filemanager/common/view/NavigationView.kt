/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:NavigationView
 ** Description:
 ** Version: 1.0
 ** Date: 2023/8/3
 ** Author:wanghonglei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.Gravity
import androidx.core.view.updateLayoutParams
import androidx.core.view.updatePadding
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.filemanager.common.utils.Log

class NavigationView : COUINavigationView {
    companion object {
        private const val TAG = "NavigationView"
    }

    var bottomPadding = 0

    constructor(context: Context) : this(context, null)
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)
    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(
        context,
        attrs,
        defStyle
    ) {
        couiNavigationMenuView?.updateLayoutParams<LayoutParams> {
            gravity = Gravity.TOP.or(Gravity.CENTER_HORIZONTAL)
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        val heightMode = MeasureSpec.getMode(heightMeasureSpec)
        val heightSize = MeasureSpec.getSize(heightMeasureSpec)
        var height = heightMeasureSpec
        val defaultHeightSize =
            resources.getDimensionPixelSize(com.support.bottomnavigation.R.dimen.coui_tool_navigation_item_height)
        if (heightMode == MeasureSpec.AT_MOST) {
            Log.d(TAG, "onMeasure paddingBottom:$bottomPadding")
            //14上的COUINavigationView设置paddingbottom不生效 重写此方法增加paddingbottom计算
            height =
                MeasureSpec.makeMeasureSpec(defaultHeightSize + bottomPadding, MeasureSpec.EXACTLY)
        } else if (heightMode == MeasureSpec.EXACTLY) {
            height = MeasureSpec.makeMeasureSpec(heightSize, MeasureSpec.EXACTLY)
        }
        super.onMeasure(widthMeasureSpec, height)
    }

    fun updatePaddingBottom(systemBarInsetsBottom: Int) {
        bottomPadding = systemBarInsetsBottom
        this.updatePadding(bottom = systemBarInsetsBottom)
    }
}