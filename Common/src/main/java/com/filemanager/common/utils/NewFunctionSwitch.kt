/***********************************************************
 ** Copyright (C), 2008-2030 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File: NewFunctionSwitch.kt
 ** Description: Set file to privacy
 ** Version: 1.0
 ** Date: 2023/8/2
 ** Author: hank.zhou(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <date>    <version >    <desc>
 *    hank.zhou   2023/8/2     1.0         function switch
 ****************************************************************/
package com.filemanager.common.utils

import com.filemanager.common.compat.FeatureCompat

object NewFunctionSwitch {
    /*
    * 安全芯片加密功能开关
    * */
    val SUPPORT_SECURITY_ENCRYPT =
        FeatureCompat.isSupportSecurityChip || (Utils.isRealmePhone() && FeatureCompat.isSupportNFCSecurityChip)
    /*
    * 文件夹显示应用logo功能开关
    * */
    const val SUPPORT_FOLDER_LOGO = true
    /*
    * 过滤系统log功能开关
    * */
    const val FILTER_SYSTEM_APP_LOG = true
    /*
    * 私有目录入口提示功能开关
    * */
    const val SUPPORT_PRIVATE_DIRECTORY_ENTRY_PROMPT = true

    /**
     * 跨端文件内容搜索功能隔离开关
     */
    @JvmStatic
    val isSupportDfmSearch = true
}