/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/8/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.animation.Animator
import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.app.Activity
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.view.MenuItem
import android.view.animation.AlphaAnimation
import android.view.animation.PathInterpolator
import android.view.animation.TranslateAnimation
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import com.filemanager.common.MyApplication


object KtAnimationUtil {
    private const val TOOLBAR_ANIMATE_DURATION = 100L
    private const val GRID_SLIDE_IN_DURATION = 460L
    private const val GRID_FADE_IN_DURATION = 100L
    private const val GRID_FADE_OUT_DURATION = 300L
    private const val GRID_BUTTON_FADE_DURATION = 200L
    private const val ALPHA_TRANSPARENT = 0
    private const val ALPHA_NOT_TRANSPARENT = 255

    fun showUpdateToolbarTitleWithAnimate(toolbar: Toolbar?, title: String?) {
        val hideTitleAnimator = ValueAnimator.ofInt(ALPHA_NOT_TRANSPARENT, ALPHA_TRANSPARENT)
        hideTitleAnimator.addUpdateListener { animation ->
            toolbar?.setTitleTextColor(Color.argb(animation.animatedValue as Int, 0, 0, 0))
        }
        hideTitleAnimator.addListener(object : Animator.AnimatorListener {
            override fun onAnimationStart(animator: Animator) {}
            override fun onAnimationEnd(animator: Animator) {
                toolbar?.title = title
                val showTitleAnimator = ValueAnimator.ofInt(ALPHA_TRANSPARENT, ALPHA_NOT_TRANSPARENT)
                showTitleAnimator.addUpdateListener { animation ->
                    toolbar?.setTitleTextColor(Color.argb(animation.animatedValue as Int, 0, 0, 0))
                }
                showTitleAnimator.duration = TOOLBAR_ANIMATE_DURATION
                showTitleAnimator.start()
            }

            override fun onAnimationCancel(animator: Animator) {
                toolbar?.setTitleTextColor(Color.argb(0, 0, 0, 0))
            }

            override fun onAnimationRepeat(animator: Animator) {}
        })
        hideTitleAnimator.duration = TOOLBAR_ANIMATE_DURATION
        hideTitleAnimator.start()
    }

    @JvmStatic
    fun updateMenuItemWithFadeAnimate(menuItem: MenuItem?, targetIconResId: Int, activity: Activity? = null) {
        var drawable: Drawable? = menuItem?.icon
        var oldDrawable: Drawable? = null

        val fadeOutAnimator = ValueAnimator.ofInt(ALPHA_NOT_TRANSPARENT, ALPHA_TRANSPARENT).apply {
            addUpdateListener { animation ->
                drawable?.alpha = animation.animatedValue as Int
            }
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animator: Animator) {}
                override fun onAnimationEnd(animator: Animator) {
                    oldDrawable = drawable
                    val context = activity ?: MyApplication.appContext
                    drawable = ContextCompat.getDrawable(context, targetIconResId)
                    drawable?.alpha = ALPHA_TRANSPARENT
                    menuItem?.icon = drawable
                    oldDrawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationCancel(animator: Animator) {
                    drawable?.alpha = ALPHA_NOT_TRANSPARENT
                    oldDrawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationRepeat(animator: Animator) {}
            })
            duration = GRID_BUTTON_FADE_DURATION
            interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
        }

        val fadeInAnimator = ValueAnimator.ofInt(ALPHA_TRANSPARENT, ALPHA_NOT_TRANSPARENT).apply {
            addUpdateListener { animation ->
                drawable?.alpha = animation.animatedValue as Int
            }
            addListener(object : Animator.AnimatorListener {
                override fun onAnimationStart(animator: Animator) {}
                override fun onAnimationEnd(animator: Animator) {
                    drawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationCancel(animator: Animator) {
                    drawable?.alpha = ALPHA_NOT_TRANSPARENT
                }

                override fun onAnimationRepeat(animator: Animator) {}
            })
            duration = GRID_BUTTON_FADE_DURATION
            interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
        }

        AnimatorSet().apply {
            playSequentially(fadeOutAnimator, fadeInAnimator)
            start()
        }
    }

    fun getFadeInAlphaAnimation(animDuration: Long = GRID_FADE_IN_DURATION): AlphaAnimation {
        return AlphaAnimation(0f, 1f).apply {
            interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
            duration = animDuration
        }
    }

    fun getFadeOutAlphaAnimation(animDuration: Long = GRID_FADE_OUT_DURATION): AlphaAnimation {
        return AlphaAnimation(1f, 0f).apply {
            interpolator = PathInterpolator(0.33f, 0f, 0.67f, 1f)
            duration = animDuration
        }
    }

    fun getSlideInAnimation() = TranslateAnimation(0f, 0f, 288f, 0f).apply {
        interpolator = PathInterpolator(0.22f, 0f, 0f, 1f)
        duration = GRID_SLIDE_IN_DURATION
    }
}