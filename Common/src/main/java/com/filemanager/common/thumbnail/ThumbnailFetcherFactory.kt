/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ThumbnailFetcherFactory
 * * Description : 缩略图文件的工厂类
 * * Version     : 1.0
 * * Date        : 2025/05/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thumbnail

import android.content.Context
import android.util.Size
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.BitmapUtils
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import java.io.File

object ThumbnailFetcherFactory {

    private const val TAG = "ThumbnailFetcherFactory"

    /**
     * 获取缩略图
     * @param file 原始文件
     * @return 缩略图地址
     */
    fun fetch(file: BaseFileBean): String {
        val type = file.mLocalType
        val size = getThumbnailSize(type)
        return fetch(file, size)
    }

    /**
     * 获取缩略图
     * @param file 原始文件
     * @param size 缩略图大小
     * @return 缩略图地址
     */
    fun fetch(file: BaseFileBean, size: Size): String {
        val type = file.mLocalType
        val fetcher = getFetcherImpl(type)
        return fetcher.fetchThumbnail(file, size)
    }

    /**
     * 获取应用的icon,并且将icon保存到文件中
     * @param context 上下文
     * @param pkgName 应用包名
     * @return icon保存的路径
     */
    fun fetch(context: Context, pkgName: String): String {
        // 生成缩略图地址
        val thumbnailPath = KtThumbnailHelper.generatorThumbnailPath(BaseThumbnailFetcher.THUMBNAIL_DIR, pkgName, 0, 0)
        if (File(thumbnailPath).exists()) {
            Log.w(TAG, "fetch pkg:$pkgName exist thumbnail:$thumbnailPath")
            return thumbnailPath
        }
        val startTime = System.currentTimeMillis()
        // 获取icon
        val icon = AppUtils.getAppIcon(context, pkgName)
        // 转出bitmap
        val bitmap = BitmapUtils.createBitmap(icon)
        // 保存到文件中
        BitmapUtils.save(bitmap, thumbnailPath)
        Log.d(TAG, "fetch pkg:$pkgName thumbnail:$thumbnailPath cost:${System.currentTimeMillis() - startTime}ms")
        return thumbnailPath
    }

    private fun getFetcherImpl(type: Int): IThumbnailFetcher {
        if (MimeTypeHelper.isDocType(type)) {
            return DocThumbnailFetcherImpl()
        }
        if (MimeTypeHelper.isImageType(type)) {
            return ImageThumbnailFetcherImpl()
        }
        if (MimeTypeHelper.isAudioType(type)) {
            return AudioThumbnailFetcherImpl()
        }
        if (MimeTypeHelper.isVideoType(type)) {
            return VideoThumbnailFetcherImpl()
        }
        if (MimeTypeHelper.APPLICATION_TYPE == type) {
            return ApkThumbnailFetcherImpl()
        }
        return DefaultThumbnailFetcherImpl()
    }

    private fun getThumbnailSize(type: Int): Size {
        if (MimeTypeHelper.isDocType(type)) {
            return DocThumbnailFetcherImpl.getThumbnailSize(MyApplication.appContext)
        }
        if (MimeTypeHelper.isImageType(type)) {
            return ImageThumbnailFetcherImpl.getThumbnailSize()
        }
        if (MimeTypeHelper.isAudioType(type)) {
            return AudioThumbnailFetcherImpl.getThumbnailSize()
        }
        if (MimeTypeHelper.isVideoType(type)) {
            return VideoThumbnailFetcherImpl.getThumbnailSize()
        }
        if (MimeTypeHelper.APPLICATION_TYPE == type) {
            return ImageThumbnailFetcherImpl.getThumbnailSize()
        }
        return ImageThumbnailFetcherImpl.getThumbnailSize()
    }

    /**
     * 清理所有的缩略图及缩略图文件夹
     */
    fun clearThumbnail() {
        val thumbnailPath = KtThumbnailHelper.generatorThumbnailPath(BaseThumbnailFetcher.THUMBNAIL_DIR, "", 0, 0)
        val dir = File(thumbnailPath).parent ?: return
        val result = JavaFileHelper.delete(PathFileWrapper(dir))
        Log.w(TAG, "clearThumbnail delete $dir result:$result")
    }

    /**
     * 判断是否是默认的缩略图
     */
    fun isDefaultThumbnail(file: BaseFileBean, thumbnailPath: String): Boolean {
        val type = BaseThumbnailFetcher.subdivideLocalType(file.mLocalType, file.mData)
        val target = KtThumbnailHelper.generatorThumbnailPath(BaseThumbnailFetcher.THUMBNAIL_DIR, "$type", 0, 0)
        return thumbnailPath == target
    }
}