/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : OpenAnyConfig
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/1/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/1/16       1      create
 ***********************************************************************/
package com.filemanager.common.bean

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class OpenAnyConfig(
    val version: String,
    @SerializedName("config")
    val configs: List<Config>
) {
    @Keep
    data class Config(val romVersion: String, val model: String)
}




