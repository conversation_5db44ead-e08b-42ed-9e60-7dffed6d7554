/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/25, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.os.Environment
import android.view.DragEvent
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.CommonConstants.DEFAULT_DOWNLOAD_PATH
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.dfm.DFMManager
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import java.io.File

object DropDfsAction : DropListener {

    private const val TAG = "DropDfsAction"
    /**
     * 分布式文管，所有拖拽到分布式文管的都采用复制策略
     */
    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        val dfsRootPath = DFMManager.getDFSMountPath()
        Log.d(TAG, "handleDropAction -> dfsRootPath $dfsRootPath dragTag $dragTag dropSideItem $dropSideItem")
        if (dfsRootPath == null) {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }
        val destPath = dfsRootPath + File.separator + Environment.DIRECTORY_DOWNLOADS
        Log.d(TAG, "handleDropAction -> destPath $destPath")

        val parseData = DropUtil.parseDragFile(dragEvent)
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            parseData.isMacDragData = true
            dragEvent.clipData?.apply {
                val onMacDownloadCallBack: (Boolean, ArrayList<RemoteFileBean>) -> Unit =
                    { result, files ->
                        if (result) {
                            parseData.noneMediaPathList.clear()
                            //下载成功后复制到目标路径
                            files.forEach {
                                parseData.noneMediaPathList.add(DEFAULT_DOWNLOAD_PATH + File.separator + it.mDisplayName)
                            }
                            Injector.injectFactory<IFileOperateApi>()?.cutMediaFile(activity, parseData, destPath, targetCategoryType, false)
                            COUISnackBarUtils.show(
                                activity, com.filemanager.common.R.string.drag_in_success
                            ) {
                                val fileBrowser = Injector.injectFactory<IFileBrowser>()
                                fileBrowser?.toFileBrowserActivity(activity, destPath)
                            }
                            Log.d(TAG, "onDragMacResult true")
                        } else {
                            //处理下载失败
                            Log.d(TAG, "onDragMacResult false")
                        }
                    }
                MacDragUtil.parseFiles(
                    activity = activity,
                    path = DEFAULT_DOWNLOAD_PATH, //因为跨端文件是虚拟目录，所以先下载到本地默认目录
                    onMacDownloadCallBack = onMacDownloadCallBack,
                    description = description
                )
            }
            return true
        }
        //内部文件拖拽，一律是复制逻辑
        if (dragTag != null) {
            Injector.injectFactory<IFileOperateApi>()?.copyMediaFile(activity, parseData, destPath)
            return true
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
                Log.d(TAG, "drag out media and private files, return!")
                return false
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
                return true
            }
            //公有媒体库文件
            if (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty()) {
                Injector.injectFactory<IFileOperateApi>()?.copyMediaFile(activity, data, destPath)
                return true
            }
            return false
        }

        //外部拖拽文本
        if (parseData.isExternalTextFiles()) {
            Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
            return true
        }

        //外部私有文件
        val privacyFileCallback: () -> Boolean = {
            Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
            true
        }
        //外部公共媒体库文件
        val mediaFileCallback = {
            Injector.injectFactory<IFileOperateApi>()?.copyMediaFile(activity, parseData, destPath)
            true
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        DFMManager.getDFSMountPath() ?: return DropDispatchAction.PROHIBIT_STATE

        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            return DropDispatchAction.COPY_STATE
        }
        //内部文件拖拽，一律是复制逻辑
        if (dragTag != null) {
            return DropDispatchAction.COPY_STATE
        }
        val parseData = DropUtil.parseDragFile(dragEvent)
        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                return DropDispatchAction.COPY_STATE
            }
            //公有媒体库文件
            if (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty()) {
                return DropDispatchAction.COPY_STATE
            }
            return DropDispatchAction.PROHIBIT_STATE
        }

        //外部拖拽文本
        if (parseData.isExternalTextFiles()) {
            return DropDispatchAction.COPY_STATE
        }

        return DropDispatchAction.COPY_STATE
    }
}