/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/2/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thread

import com.filemanager.common.utils.Log
import java.util.concurrent.*


abstract class BaseExecutor(params: ThreadPoolParams, mThreadFactory: ThreadFactory, mBlockingQueue: BlockingQueue<Runnable>)
    : ThreadPoolExecutor(params.corePoolSize, params.maxPoolSize, params.keepAliveTimeSec, TimeUnit.SECONDS, mBlockingQueue, mThreadFactory, DiscardOldestPolicy()) {
    companion object {
        const val TAG = "BaseExecutor"
    }

    override fun submit(task: Runnable?): Future<*>? {
        if (task == null) return null

        if (task is BaseThreadTask<*>) {
            try {
                execute(task)
                return task
            } catch (e: Exception) {
                Log.w(TAG, e.message)
            }
        } else {
            try {
                val newTask = ThreadTask<Void>(task as FileRunnable, null, null, null)
                execute(newTask)
                return newTask
            } catch (e: Exception) {
                Log.w(TAG, e.message)
            }
        }
        return null
    }

    override fun beforeExecute(thread: Thread, runnable: Runnable?) {
        super.beforeExecute(thread, runnable)
        runnable?.let {
            var p: ThreadPriority? = null
            if (it is BaseThreadTask<*>) {
                p = it.getPriority()
            }
            if (null != p) {
                when (p) {
                    ThreadPriority.HIGH -> android.os.Process.setThreadPriority(android.os.Process.myTid(), android.os.Process.THREAD_PRIORITY_DEFAULT)
                    ThreadPriority.BACKGROUND -> android.os.Process.setThreadPriority(android.os.Process.myTid(), android.os.Process.THREAD_PRIORITY_BACKGROUND)
                    ThreadPriority.LOW -> android.os.Process.setThreadPriority(android.os.Process.myTid(), android.os.Process.THREAD_PRIORITY_LOWEST)
                }
            }
        }

    }

    override fun afterExecute(runnable: Runnable?, t: Throwable?) {
        super.afterExecute(runnable, t)
        runnable?.let {
            var key: String? = null
            if (it is BaseThreadTask<*>) {
                key = it.getUniqueFlag()
                if (it.isCancelled) {
                    it.mThreadCancelInterface?.onCancel()
                }
                it.mRunnable?.mRecycler?.invoke(key)
                if (it.mIsCallCancel) {
                    return
                }
                if (null != key) {
                    it.mRemoveWorkInterface?.removeWork(key)
                }
            }
        }
    }


}