/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.compat.compat30

import android.app.Activity
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import android.widget.LinearLayout
import androidx.lifecycle.Lifecycle
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.controller.PermissionController
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.R
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.interfaces.InstalledPermissionCallback
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isInvalid

class PermissionControllerR(lifecycle: Lifecycle, listener: OnRequestPermissionListener) : PermissionController(lifecycle, listener) {
    companion object {
        private const val TAG = "PermissionControllerR"
    }

    private var mPermissionInstalledAppsController: PermissionInstalledAppsControllerR? = null

    override fun internalCheckPermission(activity: Activity) {
        val shouldShow = PermissionUtils.hasStoragePermission().not()
        if (shouldShow) {
            //需要显无权限页面
            Log.d(TAG, "internalCheckPermission shouldShowStorage")
            (activity as? BaseVMActivity.PermissonCallBack)?.handleNoStoragePermission()
        } else {
            //有存储权限时
            Log.d(TAG, "internalCheckPermission hasStoragePermission")
            mPermissionListener?.onPermissionSuccess()
        }
    }

    override fun showSettingGuildDialog(activity: Activity) {
        if (activity.isInvalid()) {
            Log.d(TAG, "showSettingGuildDialog: activity invalid, not show mStatementDialog")
            return
        }
        if (mPermissionGuildDialog == null) {
            Log.d(TAG, "createGuildDialogAfterR()")
            val builder = COUIAlertDialogBuilder(activity)
                    .setTitle(R.string.manage_files_permission_title)
                    .setMessage(R.string.manage_files_permission_desc_normal)
                    .setPositiveButton(R.string.set_button_text) { _, _ ->
                        isDialogShow = false
                        // Start to check permission after showing guild dialog
                        if (PermissionUtils.hasStoragePermission()) {
                            checkGetInstalledAppsPermission(activity, true)
                        } else {
                            val result = PermissionUtils.requestStoragePermission(activity)
                            setWaitPermissionGrantResult(result)
                        }
                    }
                    .setNegativeButton(R.string.dialog_cancel) { _, _ ->
                        isDialogShow = false
                        mPermissionListener?.onPermissionReject(true)
                    }
                    .setCancelable(false)
            mPermissionGuildDialog = builder.create()
            mPermissionGuildDialog!!.setCanceledOnTouchOutside(false)
        } else if (mPermissionGuildDialog?.isShowing == true) {
            return
        }
        mPermissionGuildDialog?.show()
        isDialogShow = true
    }

    override fun createPermissionEmptyView(activity: Activity): View {
        val viewRoot = LayoutInflater.from(activity).inflate(R.layout.permission_common_view_layout, null)
        val topBackIcon = viewRoot.findViewById<ImageButton>(R.id.top_back_icon)
        val toolbarLayout = viewRoot.findViewById<LinearLayout>(R.id.empty_toolbar_layout)
        val visible = if (WindowUtils.isMiddleAndLargeScreen(activity)) View.GONE else View.VISIBLE
        toolbarLayout.visibility = visible
        topBackIcon.setOnClickListener {
            activity.finish()
        }
        return viewRoot
    }

    override fun onPermissionsResultReturn(activity: Activity, requestCode: Int,
                                           permissions: Array<out String>?, grantResults: IntArray?) {
        mPermissionInstalledAppsController?.onPermissionsResultReturn(activity, requestCode, permissions, grantResults)
    }

    override fun setWaitPermissionGrantResult(waiting: Boolean) {
        super.setWaitPermissionGrantResult(waiting)
        if (waiting.not()) {
            if (PermissionUtils.hasStoragePermission()) {
                val waitingInstalledPermissionState =
                    mPermissionInstalledAppsController?.getIsOnPermissionResult() ?: false
                if (waitingInstalledPermissionState.not()) {
                    mPermissionListener?.onPermissionSuccess()
                }
            } else {
                mPermissionListener?.onPermissionReject(true)
            }
        }
    }

    override fun checkGetInstalledAppsPermission(activity: Activity, isMainShow: Boolean) {
        if(activity is InstalledPermissionCallback) {
            if(mPermissionInstalledAppsController == null) {
                mPermissionInstalledAppsController =
                    PermissionInstalledAppsControllerR(mLifecycle,this, mPermissionListener)
            }
            mPermissionInstalledAppsController?.checkGetInstalledAppsPermission(activity, isMainShow)
        } else {
            mPermissionListener?.onPermissionSuccess()
        }
    }
}