/***********************************************************
 ** Copyright (C), 2008-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: ThemeColorUtils.kt
 ** Description: The whole color utils
 ** Version: 1.0
 ** Date: 2020/7/16
 ** Author: lijin(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 *     lijin    2020/7/16   1.0         The whole color utils
 ****************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.graphics.drawable.Drawable
import android.widget.TextView
import androidx.annotation.AttrRes
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.theme.COUIThemeOverlay
import com.coui.appcompat.tintimageview.COUITintUtil
import com.filemanager.common.R

object ThemeColorUtils {
    private const val TAG = "ThemeColorUtils"

    @JvmStatic
    fun getThemeColor(context: Context, @AttrRes attrColor: Int = com.support.appcompat.R.attr.couiColorContainerTheme): Int {
        return COUIContextUtil.getAttrColor(context, attrColor, 0)
    }

    fun tintDrawableWithTheme(context: Context, drawable: Drawable?): Boolean {
        drawable?.let {
            if (isWholeColor(context)) {
                COUITintUtil.tintDrawable(drawable, COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorContainerTheme))
                return true
            }
        } ?: kotlin.run {
            Log.e(TAG, "setWholeColorDrawable  drawable = null")
        }
        return false
    }

    @JvmStatic
    fun tintDrawable(
        context: Context,
        drawable: Drawable?,
        @AttrRes attrColor: Int = com.support.appcompat.R.attr.couiColorContainerTheme
    ): Drawable? {
        drawable?.let {
            COUITintUtil.tintDrawable(drawable, COUIContextUtil.getAttrColor(context, attrColor, 0))
        }
        return null
    }

    fun tintTextView(view: TextView?, @AttrRes defThemeAttrRes: Int = -1) {
        view?.let {
            val context = view.context
            if (isWholeColor(context) || (defThemeAttrRes == -1)) {
                it.setTextColor(getThemeColor(context, com.support.appcompat.R.attr.couiColorLabelTheme))
            } else {
                it.setTextColor(getThemeColor(context, defThemeAttrRes))
            }
        }
    }

    /**
     * this context can not use application
     */
    private fun isWholeColor(context: Context): Boolean {
        return COUIThemeOverlay.getInstance().isCOUITheme(context)
    }
}