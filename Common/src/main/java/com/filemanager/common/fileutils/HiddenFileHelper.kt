/***********************************************************
 * * Copyright (C), 2020 - 2020, Oplus. All rights reserved.
 * * File: HiddenFileHelper.kt
 * * Description: public methods related with hidden files
 * * Version: 1.0
 * * Date : 2020.09.29
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020.09.29       1.0         public methods related with hidden files
 ****************************************************************/
package com.filemanager.common.fileutils

import android.view.View
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.constants.CommonConstants.NEED_SHOW_HIDDEN_FILES
import com.filemanager.common.constants.CommonConstants.NEED_SHOW_RECENT_CAMERA

object HiddenFileHelper {

    private const val HIDDEN_FILES_TRANSPARENCY = 0.3f
    private const val HIDDEN_FILES_TRANSPARENCY_DARK_MODEL = 0.5f
    private const val HIDDEN_FILES_TRANSPARENCY_NONE = 1.0f
    val DEFAULT_CAMERA_SCREENSHOT_SAVE_PATH = arrayOf("DCIM/Camera/", "Pictures/Screenshots/", "DCIM/Screenshots/")

    /**
     * @attention set [mIsNeedShowHiddenFile] value when change the preference NEED_SHOW_HIDDEN_FILE
     */
    @JvmStatic
    var mIsNeedShowHiddenFile: Boolean? = null
        get() {
            if (field == null) {
                field = PreferencesUtils.getBoolean(key = NEED_SHOW_HIDDEN_FILES)
            }
            return field
        }

    @JvmStatic
    var mIsHideCameraScreenshotInRecentOpen: Boolean? = null
        get() {
            if (field == null) {
                field = PreferencesUtils.getBoolean(key = NEED_SHOW_RECENT_CAMERA)
            }
            return field
        }

    /**
     * 在设置页打开或关闭最近页显示相机与截屏文件开关后，再回到最近页面刷新时需要重新获取最近列表的数据
     */
    @JvmStatic
    var forceReloadRecentDataOnce: Boolean = false
        get() {
            if (field) {
                field = false
                return true
            } else {
                return field
            }
        }

    fun isNeedShowHiddenFile(): Boolean {
        return mIsNeedShowHiddenFile!!
    }

    fun isHiddenFile(displayName: String?): Boolean {
        return (displayName?.startsWith(".") ?: false)
    }

    /**
     * @param pathOrName
     * @return If there is a hidden folder in the file path, will return true
     */
    fun isHiddenFileByPath(pathOrName: String?): Boolean {
        return isHiddenFile(pathOrName) || (pathOrName?.contains("/.") ?: false)
    }

    /**
     * 根据“显示隐藏文件”开关，来决定文件是否显示
     * 正常文件，无论开关是否开启或关闭，都会显示
     * 隐藏文件，只有在开关打开时，才会显示；开关关闭时，隐藏文件不显示
     * 如果文件路径中包含隐藏文件夹，开关关闭时，隐藏文件夹下面的正常文件和隐藏文件都不显示
     * @param pathOrName 文件路径或者名称
     * @return ture 显示文件，false 不显示文件
     */
    fun isDisplayFile(pathOrName: String): Boolean {
        return isNeedShowHiddenFile() || isHiddenFileByPath(pathOrName).not()
    }

    fun getAlphaWithHidden(hiddenFile: Boolean, isDarkMode: Boolean): Float {
        return if (hiddenFile) {
            if (isDarkMode) {
                HIDDEN_FILES_TRANSPARENCY_DARK_MODEL
            } else {
                HIDDEN_FILES_TRANSPARENCY
            }
        } else {
            HIDDEN_FILES_TRANSPARENCY_NONE
        }
    }

    fun getAlphaWithHidden(displayName: String?, isDarkMode: Boolean): Float {
        return getAlphaWithHidden(isHiddenFile(displayName), isDarkMode)
    }

    fun getViewAlphaWithHidden(view: View?, isDarkMode: Boolean): Float {
        return getAlphaWithHidden(view?.alpha != HIDDEN_FILES_TRANSPARENCY_NONE, isDarkMode)
    }
}