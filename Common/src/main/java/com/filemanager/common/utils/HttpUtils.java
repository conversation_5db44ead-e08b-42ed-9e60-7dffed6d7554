/***********************************************************
 * * Copyright (C), 2008-2019 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:HttpUtils.java
 * * Description:
 * * Version:1.0
 * * Date :2019/5/22
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.common.utils;

import android.accounts.NetworkErrorException;

import com.filemanager.common.thread.FileRunnable;
import com.filemanager.common.thread.ThreadManager;
import com.filemanager.common.thread.ThreadPriority;
import com.filemanager.common.thread.ThreadType;

import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class HttpUtils {
    private static final String TAG = "HttpUtils";
    private static final int DEFAULT_GZIP_SIZE = 1024;
    private static final int CONNECT_TIME_OUT = 5000;
    private static final int READ_TIME_OUT = 8000;
    private static final int HTTP_RESPNESE_OK = 200;
    private static final int BYTE_LENGTH = 1024;

    /**
     * HttpUrlConnection Post Request
     * POST Method , response to Byte[]
     *
     * @param urlString request Url
     * @param listener  callback
     * @param pbbytes   prams, protobuf byte[]
     */
    public static void doPostByPB(final String urlString, final HttpCallbackBytesListener listener,
                                  final byte[] pbbytes) {
        //Network requests are time-consuming operations, put into child thread processing
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                URL url = null;
                HttpURLConnection httpURLConnection = null;
                try {
                    byte[] data = pbbytes.clone();
                    url = new URL(urlString);
                    httpURLConnection = (HttpURLConnection) url.openConnection();
                    httpURLConnection.setRequestProperty("Content-Type", "application/x-protobuf");
                    httpURLConnection.setRequestProperty("Accept-Encoding", "gzip");
                    httpURLConnection.setRequestProperty("Accept", "application/x-protobuf");
                    if (data.length >= DEFAULT_GZIP_SIZE) {
                        Log.d(TAG, "data.length >= 1024 ,need gzip compress.");
                        data = gzipCompress(data);
                        httpURLConnection.setRequestProperty("Content-Encoding", "gzip");
                    }
                    httpURLConnection.setRequestMethod("POST");
                    httpURLConnection.setConnectTimeout(CONNECT_TIME_OUT);
                    httpURLConnection.setReadTimeout(READ_TIME_OUT);
                    httpURLConnection.setDoInput(true);
                    httpURLConnection.setDoOutput(true);

                    try (OutputStream os = httpURLConnection.getOutputStream()) {
                        os.write(data);
                        os.flush();
                    }

                    if (httpURLConnection.getResponseCode() == HTTP_RESPNESE_OK) {
                        //get inputStream
                        try (InputStream is = httpURLConnection.getInputStream()) {
                            try (BufferedInputStream bis = new BufferedInputStream(is)) {
                                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                                byte[] bytes = new byte[BYTE_LENGTH];
                                int len = -1;
                                while ((len = bis.read(bytes)) != -1) {
                                    baos.write(bytes, 0, len);
                                }
                                // callback success method
                                if (listener != null) {
                                    new ResponseCall(listener).doSuccess(baos.toByteArray());
                                }
                            }
                        }
                    } else {
                        if (listener != null) {
                            new ResponseCall(listener).doFail(
                                    new NetworkErrorException("response err code:"
                                            + httpURLConnection.getResponseCode()));
                        }
                    }
                } catch (Exception e) {
                    Log.e(TAG, "doPostByPB exception");
                    if (listener != null) {
                        // callback onError() method
                        new ResponseCall(listener).doFail(e);
                    }
                } finally {
                    if (httpURLConnection != null) {
                        // remember to close the connection
                        httpURLConnection.disconnect();
                    }
                }
            }
        };

        ThreadManager.getSThreadManager().execute(new FileRunnable(runnable, TAG + "_doPostByPB", null),
                ThreadType.NORMAL_THREAD, ThreadPriority.HIGH
        );
    }

    /**
     * @param data
     * @return
     */
    private static byte[] gzipCompress(byte[] data) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzipOutputtStream = new GZIPOutputStream(out);
        try {
            gzipOutputtStream.write(data);
        } finally {
            closeQuietly(gzipOutputtStream);
        }
        return out.toByteArray();
    }

    /**
     * @param data
     * @return
     */
    private static byte[] gzipDecompress(byte[] data) throws IOException {
        ByteArrayOutputStream buffer = null;
        GZIPInputStream gizpInputStream = null;
        try {
            buffer = new ByteArrayOutputStream();
            gizpInputStream = new GZIPInputStream(new ByteArrayInputStream(data));
            int n = -1;
            byte[] tempbuffer = new byte[1024 * 12];
            while (-1 != (n = gizpInputStream.read(tempbuffer))) {
                buffer.write(tempbuffer, 0, n);
            }
            return buffer.toByteArray();
        } finally {
            closeQuietly(gizpInputStream);
            closeQuietly(buffer);
        }
    }

    private static void closeQuietly(Closeable closeable) {
        try {
            if (closeable != null) {
                closeable.close();
            }
        } catch (IOException ioe) {
            // ignore
        }
    }

    public interface MyHttpCallback {
        void onRequestSuccess();

        void onRequestError();

        void onRequestFailed();

        void onRequestFinished();

    }

    /**
     * HttpURLConnection
     */
    public interface HttpCallbackBytesListener {
        void onFinish(byte[] response);

        void onError(Exception e);
    }

}