/***********************************************************
 * * Copyright (C), 2020 - 2020, Oplus. All rights reserved.
 * * File: ImageLoaderInterface
 * * Description: the interface for image loader
 * * Version: 1.0
 * * Date : 2020/11/13
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/11/13       1.0         the interface for image loader
 ****************************************************************/
package com.filemanager.common.imageloader

import android.content.Context
import android.net.Uri
import android.widget.ImageView
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_ALL
import com.filemanager.common.utils.FileImageLoader

interface ImageLoaderInterface {
    /**
     * display image on imageView in params.
     */
    @Suppress("LongParameterList")
    fun display(
        mContext: Context?,
        baseFileBean: BaseFileBean?,
        radiusSize: Int,
        orientation: Int,
        imageView: ImageView?,
        isCoverError: Boolean = false,
        roundConnerType: Int = ROUND_CONNER_ALL,
        errorImageType: Int = FileImageLoader.ERROR_IMAGE_DEFAULT
    )
    /**
     * display image on imageView in params.
     */
    fun display(mContext: Context?, resourceId: Int?, radiusSize: Int, imageView: ImageView?)

    fun display(context: Context?, uri: Uri?, imageView: ImageView?)
}