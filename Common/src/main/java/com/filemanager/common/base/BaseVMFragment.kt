/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.BaseVMFragment
 * * Version     : 1.0
 * * Date        : 2020/1/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.helper.CategoryHelper.CATEGORY_FILE_REMOTE_MAC
import com.filemanager.common.helper.uiconfig.UIConfigMonitor.OnUIConfigChangeListener
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.ToolbarUtil
import com.google.android.material.appbar.AppBarLayout

abstract class BaseVMFragment<VM : BaseViewModel> : androidx.fragment.app.Fragment(), OnUIConfigChangeListener {
    companion object {
        private const val TAG = "BaseVMFragment"
    }

    protected var baseVMActivity: BaseVMActivity? = null
    protected var permissionEmptyView: ConstraintLayout? = null
    protected var isShowPermissionEmptyView = false
    protected var rootView: ViewGroup? = null
    protected var appBarLayout: AppBarLayout? = null
    protected var toolbar: COUIToolbar? = null

    protected val mainHandler = Handler(Looper.getMainLooper())

    @Deprecated("Legacy offending naming", ReplaceWith("baseVMActivity"))
    protected var mActivity: BaseVMActivity?
        get() = baseVMActivity
        set(value) {
            baseVMActivity = value
        }

    protected inline fun withBaseVMActivity(action: (BaseVMActivity) -> Unit) {
        baseVMActivity?.let(action)
    }

    protected inline fun runOnBaseVMActivity(action: BaseVMActivity.() -> Unit) {
        baseVMActivity?.run(action)
    }

    @SuppressLint("ResourceType")
    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        kotlin.runCatching {
            return inflater.inflate(getLayoutResId(), container, false)
        }.onFailure {
            Log.e(TAG, "onCreateView ${it.message}", it)
        }
        return null
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        initView(view)
        initData(savedInstanceState)
        startObserve()
        Log.d(TAG, "onViewCreated ${this.javaClass}")
        checkShowPermissionEmpty()
        super.onViewCreated(view, savedInstanceState)
        PCConnectAction.attachToLifecycle(this)
        setFragmentViewDragTag(view)
        //每个页面初始化时为返回按钮设置TAG
        baseVMActivity?.let { ToolbarUtil.setToolbarChildViewTag(toolbar, it) }
    }

    open fun setFragmentViewDragTag(view: View) {
        val categoryType = getFragmentCategoryType()
        val activity = baseVMActivity
        if (categoryType != -1 && activity != null) {
            view.tag = if (categoryType == CATEGORY_FILE_REMOTE_MAC) {
                DropTag(
                    categoryType, DropTag.Type.MAC_FRAGMENT_VIEW
                )
            } else {
                DropTag(categoryType, DropTag.Type.FRAGMENT_VIEW)
            }
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        baseVMActivity = activity as? BaseVMActivity
        Log.d(TAG, "onAttach baseVMActivity $baseVMActivity")
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        PCConnectAction.onFragmentHiddenChanged(this, hidden)
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
    }

    abstract fun getLayoutResId(): Int
    abstract fun initView(view: View)
    abstract fun initData(savedInstanceState: Bundle?)
    abstract fun startObserve()
    abstract fun onResumeLoadData()
    open fun checkPermission() {}

    /**
     * 获取每个Fragment页面对应的类别
     */
    open fun getFragmentCategoryType(): Int {
        return -1
    }

    /**
     * needGetInstall 是否需要检查读取应用列表权限
     */
    open fun checkShowPermissionEmpty(needGetInstall: Boolean = true): Boolean {
        if (needGetInstall) {
            getInstallPerMission()
        }
        if (PermissionUtils.hasStoragePermission().not()) {
            setPermissionEmptyVisible(View.VISIBLE)
            return true
        }
        setPermissionEmptyVisible(View.GONE)
        return false
    }

    open fun setPermissionEmptyVisible(visible: Int) {
        Log.d(TAG, "setPermissionEmptyVisible $visible")
        if (View.VISIBLE == visible) {
            isShowPermissionEmptyView = true
            createPermissionEmptyView(rootView)
        } else {
            isShowPermissionEmptyView = false
        }
        bringToFront(visible)
    }

    open fun bringToFront(visible: Int) {
        permissionEmptyView?.let { layout ->
            layout.visibility = visible
            if (View.VISIBLE == visible) {
                layout.bringToFront()
            }
        }
    }

    open fun createPermissionEmptyView(rootView: ViewGroup?) {
        if (permissionEmptyView != null || rootView == null) {
            return
        }
        val id = getPermissionEmptyViewStubId()
        if (id == 0) return
        val viewStub = rootView.findViewById<ViewStub>(id)
        val viewRoot = viewStub?.inflate()
        permissionEmptyView = viewRoot?.findViewById(R.id.permission_root_layout)
        viewRoot?.findViewById<TextView>(R.id.button)?.setOnClickListener {
            withBaseVMActivity { PermissionUtils.openPermissionSetting(it) }
        }
    }

    open fun getPermissionEmptyViewStubId(): Int {
        return 0
    }

    open fun updatePermissionEmptyMarginTop() {
        if (isShowPermissionEmptyView.not()) {
            return
        }
        appBarLayout?.post {
            permissionEmptyView?.let { emptyLayout ->
                val emptyMarginTop = getEmptyMarginTop()
                val layoutParams = emptyLayout.layoutParams
                if (layoutParams is ViewGroup.MarginLayoutParams) {
                    layoutParams.topMargin = emptyMarginTop
                    emptyLayout.layoutParams = layoutParams
                }
            }
        }
    }

    open fun updatePermissionEmptyViewPaddingForChild() {
        (toolbar?.parent as? AppBarLayout)?.let {
            it.post {
                if (isAdded) {
                    permissionEmptyView?.let { permissionEmptyView ->
                        permissionEmptyView.setPadding(
                            permissionEmptyView.paddingLeft,
                            KtViewUtils.getRecyclerViewTopPadding(it, 0),
                            permissionEmptyView.paddingRight,
                            permissionEmptyView.paddingRight
                        )
                        permissionEmptyView.visibility = View.VISIBLE
                        permissionEmptyView.bringToFront()
                    }
                }
            }
        }
    }

    private fun getEmptyMarginTop(): Int {
        return appBarLayout?.let { bar -> (bar.y + bar.measuredHeight).toInt() - StatusBarUtil.getStatusBarHeight() } ?: 0
    }

    open fun getInstallPerMission() {}

    open fun updatePermissionEmptyHeight() {}

    protected fun actionCheckPermission(): Boolean {
        return PermissionUtils.hasStoragePermission()
    }

    fun updateToolbarHeight(toolbar: COUIToolbar?) {
        toolbar?.updateLayoutParams<ViewGroup.LayoutParams> {
            height = appContext.resources.getDimensionPixelOffset(com.support.toolbar.R.dimen.toolbar_min_height)
        }
    }

    /**
     * 适配中大屏侧导展开收起时toolbar到边距的距离
     * @param right 侧导分割线到屏幕的距离
     * @param drawerWidth 侧导面板宽度
     */
    open fun adjustToolBarMarginStart(right: Int, drawerWidth: Int) {
        var collapsedMarginStart = 0
        val activity = requireActivity()
        val screenWidth = activity.resources.configuration.screenWidthDp
        if (COUIResponsiveUtils.isSmallScreen(activity, screenWidth)) {
            collapsedMarginStart = activity.resources.getDimensionPixelOffset(R.dimen.sidebar_toolbar_collapsed_margin_start_compat)
        } else if (COUIResponsiveUtils.isMediumScreen(requireActivity(), screenWidth)) {
            collapsedMarginStart = activity.resources.getDimensionPixelOffset(R.dimen.sidebar_toolbar_collapsed_margin_start_medium)
        } else if (COUIResponsiveUtils.isLargeScreen(requireActivity(), screenWidth)) {
            collapsedMarginStart = activity.resources.getDimensionPixelOffset(R.dimen.sidebar_toolbar_collapsed_margin_start_expanded)
        }
        toolbar?.apply {
            val lp = layoutParams as ViewGroup.MarginLayoutParams
            val toolbarMarginStart = ((1 - right.toFloat() / drawerWidth.toFloat()) * collapsedMarginStart).toInt()
            lp.marginStart = toolbarMarginStart
            layoutParams = lp
        }
    }

    /**
     * 延时执行方法
     * @param delayMillis 延迟时间（单位：毫秒）
     * @param action 延迟后要执行的函数
     */
    fun delay(delayMillis: Long = 200, action: () -> Unit) {
        mainHandler.postDelayed(action, delayMillis)
    }
}