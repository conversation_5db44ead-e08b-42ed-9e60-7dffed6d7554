/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RecallTypeUtil.kt
 ** Description : 用于从dmp的recallType的字节中提取recallType的int值工具类
 ** Version     : 1.0
 ** Date        : 2024/04/28
 ** Author      : W9059186
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9059186      2024/04/28       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.util.Log
import java.nio.ByteBuffer

object RecallTypeUtil {

    private const val TAG = "RecallTypeUtil"
    private const val INT_BYTE_SIZE = 4
    @JvmStatic
    private fun toIntArray(byteArray: ByteArray?): IntArray {
        if (byteArray == null) {
            return IntArray(0)
        }
        val buffer = ByteBuffer.wrap(byteArray)
        val intArray = IntArray(byteArray.size / INT_BYTE_SIZE)
        for (i in intArray.indices) {
            intArray[i] = buffer.int
        }
        return intArray
    }

    /**
     * 将recallType的blob类型的数据转换recallType的Int值
     */
    @JvmStatic
    fun getRecallType(byteArray: ByteArray?): Int {
        val recallTypes: IntArray = toIntArray(byteArray)
        val recallTypesStr = StringBuffer()
        var result: Int = -1
        recallTypes.let {
            for (recallType in it) {
                recallTypesStr.append("$recallType ")
            }
            if (it.isNotEmpty()) {
                result = it[0]
            }
            Log.i(TAG, "getRecallType string $recallTypesStr, result $result")
        }
        return result
    }
}