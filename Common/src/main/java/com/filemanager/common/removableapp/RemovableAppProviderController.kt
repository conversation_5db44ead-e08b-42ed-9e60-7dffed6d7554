/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemovableAppProviderController
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/29 18:59
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/12/29       1.0      create
 ***********************************************************************/
package com.filemanager.common.removableapp

import android.content.Context
import androidx.core.database.getBlobOrNull
import androidx.core.database.getStringOrNull
import com.filemanager.common.utils.Log

class RemovableAppProviderController(
    private val context: Context
) {

    fun queryAppInfos(): List<RemovableAppInfo> {
        Log.d(TAG, "queryAppInfos")
        val appInfos = mutableListOf<RemovableAppInfo>()
        val query = context.contentResolver.query(RemovableAppCompat.obtainQueryAppInfoUri(), null, null, null)
        query?.use { cursor ->
            val packageNameColumn = cursor.getColumnIndexOrThrow(KEY_PACKAGE_NAME)
            val labelColumn = cursor.getColumnIndexOrThrow(KEY_LABEL)
            val iconColumn = cursor.getColumnIndexOrThrow(KEY_ICON)
            while (cursor.moveToNext()) {
                val packageName = cursor.getString(packageNameColumn)
                val label = cursor.getString(labelColumn)
                val icon = cursor.getBlobOrNull(iconColumn)
                appInfos += RemovableAppInfo(packageName, label, icon)
            }
        }
        return appInfos
    }

    fun queryAppInfo(packageName: String): RemovableAppInfo? {
        Log.d(TAG, "queryAppInfo -> packageName = $packageName")
        val query = context.contentResolver.query(RemovableAppCompat.obtainQueryAppInfoUri(), null, null, null)
        query?.use { cursor ->
            val removableAppInfo = RemovableAppInfo(packageName)
            val packageNameColumn = cursor.getColumnIndexOrThrow(KEY_PACKAGE_NAME)
            val labelColumn = cursor.getColumnIndexOrThrow(KEY_LABEL)
            val iconColumn = cursor.getColumnIndexOrThrow(KEY_ICON)
            while (cursor.moveToNext()) {
                cursor.getStringOrNull(packageNameColumn)?.let { name ->
                    if (name == packageName) {
                        removableAppInfo.label = cursor.getString(labelColumn)
                        removableAppInfo.icon = cursor.getBlobOrNull(iconColumn)
                        return removableAppInfo
                    }
                }
            }
        }
        return null
    }

    companion object {
        private const val TAG = "RemovableAppProviderController"

        private const val KEY_PACKAGE_NAME = "package_name"
        private const val KEY_VERSION_CODE = "version_code"
        private const val KEY_VERSION_NAME = "version_name"
        private const val KEY_CODE_PATH = "code_path"
        private const val KEY_BASE_CODE_PATH = "base_code_path"
        private const val KEY_LOCALE = "locale"
        private const val KEY_LABEL = "label"
        private const val KEY_ICON = "icon"
        private const val KEY_FILE_SIZE = "file_size"
    }
}