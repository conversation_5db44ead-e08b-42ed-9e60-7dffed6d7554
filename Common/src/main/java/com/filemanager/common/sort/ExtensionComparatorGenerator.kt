package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.FileTypeUtils
import org.apache.commons.io.FilenameUtils
import java.util.*
import kotlin.Comparator


/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2020/2/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
class ExtensionComparatorGenerator : NameComparatorGenerator() {
    init {
        ExtensionComparatorGenerator.initLastSortMode()
    }

    override fun compare1(file1: BaseFileBean, file2: BaseFileBean, isDesc: Boolean): Int {
        if (sCategoryType == CategoryHelper.CATEGORY_APK) {
            return super.compare1(file1, file2, isDesc)
        }
        if (sCategoryType > 0) {
            val sort = compareFileExtension(file1, file2)
            return if (sort == 0) {
                super.compare1(file1, file2, isDesc)
            } else {
                sort
            }
        }
        if (file1.mIsDirectory && file2.mIsDirectory) {
            if (sLastSortMode != SortHelper.FILE_TIME_REVERSE_ORDER) {
                return super.compare1(file1, file2, isDesc)
            } else {
                val result = file2.mDateModified - file1.mDateModified
                return if (result < 0) {
                    -1
                } else if (result > 0) {
                    1
                } else {
                    0
                }
            }
        } else {
            val sort = compareFileExtension(file1, file2)
            return if (sort == 0) {
                super.compare1(file1, file2, isDesc)
            } else {
                sort
            }
        }
    }

    private fun compareFileExtension(file1: BaseFileBean, file2: BaseFileBean): Int {
        val locale = Locale.getDefault()
        val ext1 = FileTypeUtils.getExtension(file1.mDisplayName)?.toLowerCase(locale) ?: ""
        val ext2 = FileTypeUtils.getExtension(file2.mDisplayName)?.toLowerCase(locale) ?: ""
        return ext1.compareTo(ext2)
    }

    companion object {

        val comparator = ExtensionComparatorGenerator()
        val COMPARATOR = comparator.genComparator()
        private var sLastSortMode: Int = 0
        private var sCategoryType: Int = 0

        private fun initLastSortMode() {
            ExtensionComparatorGenerator.sLastSortMode = SortHelper.FILE_NAME_ORDER
        }

        fun setLastSortMode(mode: Int) {
            sLastSortMode = mode
        }

        fun setCategoryType(type: Int) {
            sCategoryType = type
        }

        fun getComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return if (isDesc) {
                comparator.genComparator()
            } else {
                comparator.genReverseComparator()
            }
        }
    }

}