/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/25, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.view.DragEvent
import com.filemanager.common.R
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.Companion.parseFiles
import com.filemanager.common.dragselection.action.DropDispatchAction.PROHIBIT_STATE
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import com.oplus.filemanager.interfaze.main.IMain

object DropStorageAction : DropListener {

    private const val TAG = "DropStorageAction"

    /**
     * 拖入情况
     * 移动处理：本地文件（包括OTG、SDCARD）、最近文件taskbar（本地媒体库文件）
     * 复制处理：外部应用：私有目录文件（从documentUI)、跨端文件（分布式文件）、中转站、PC文件
     */
    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        Log.d(TAG, "handleDropAction categoryType $targetCategoryType dragTag $dragTag dropSideItem $dropSideItem")

        val parseData = DropUtil.parseDragFile(dragEvent)
        if (DragUtils.hasAndroidDataFile) {
            CustomToast.showShort(R.string.drag_cloud_not_support_position)
            Log.w(TAG, "handleDropAction hasAndroidDataFile")
            return false
        }
        val fileOperateApi = Injector.injectFactory<IFileOperateApi>()
        val destPath = if (dropSideItem) {
            if (targetCategoryType == CategoryHelper.CATEGORY_SDCARD_BROWSER) {
                Injector.injectFactory<IMain>()?.getSdCardPath(activity)
            } else if (targetCategoryType == CategoryHelper.CATEGORY_OTG_BROWSER) {
                Injector.injectFactory<IMain>()?.getOtgPath(activity)
            } else {
                DropUtil.STORAGE_ROOT_PATH
            }
        } else {
            Injector.injectFactory<IMain>()?.getMainActivityCurrentPath(activity)
        }
        Log.d(TAG, "destPath $destPath")

        if (destPath.isNullOrEmpty() || AndroidDataHelper.isAndroidDataPath(destPath)) {
            CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
            return false
        }
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            dragEvent.clipData?.apply {
                val onMacDownloadCallBack: (Boolean, ArrayList<RemoteFileBean>) -> Unit =
                    { result, _ ->
                        if (result) {
                            COUISnackBarUtils.show(
                                activity,
                                com.filemanager.common.R.string.drag_in_success
                            ) {
                                val fileBrowser = Injector.injectFactory<IFileBrowser>()
                                fileBrowser?.toFileBrowserActivity(activity, destPath)
                            }
                            Log.d(TAG, "onDragMacResult true")
                        } else {
                            //处理下载失败
                            Log.d(TAG, "onDragMacResult false")
                        }
                    }
                parseFiles(activity, destPath, onMacDownloadCallBack, description)
            }
            return true
        }
        val delayReloadData = dropSideItem && DropUtil.checkIsMediaFragmentTag(dragTag)
        val fragmentCategoryType = DropUtil.getCategoryTypeByDragTag(dragTag)
        //dragTag不为空或者不是分布式对端文件，内部文件，实行移动操作
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dragTag != null && dragTag != dfmFragmentTag && dropSideItem) {
            fileOperateApi?.cutMediaFile(activity, parseData, destPath, fragmentCategoryType, true, delayReloadData)
            return true
        }

        //分布式文件，复制操作
        if (dragTag == dfmFragmentTag) {
            fileOperateApi?.copyMediaFile(activity, parseData, destPath)
            return true
        }

        if (parseData.isExternalTextFiles()) {
            fileOperateApi?.saveFile(activity, parseData, destPath)
            return true
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (dragTag == null && uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
                Log.d(TAG, "drag out media and private files, return!")
                return false
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
                return true
            }
            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                Injector.injectFactory<IFileOperateApi>()?.cutMediaFile(activity, data, destPath, fragmentCategoryType, false)
                return true
            }
            return false
        }

        val privacyFileCallback = {
            fileOperateApi?.saveFile(activity, parseData, destPath)
            true
        }
        val mediaFileCallback = {
            fileOperateApi?.cutMediaFile(activity, parseData, destPath, fragmentCategoryType, false)
            true
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            return DropDispatchAction.COPY_STATE
        }

        //dragTag不为空或者不是分布式对端文件，内部文件，实行移动操作
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dragTag != null && dragTag != dfmFragmentTag && dropSideItem) {
            return DropDispatchAction.NORMAL_STATE
        }

        //分布式文件，复制操作
        if (dragTag == dfmFragmentTag) {
            return DropDispatchAction.COPY_STATE
        }

        val parseData = DropUtil.parseDragFile(dragEvent)
        if (DragUtils.hasAndroidDataFile) {
            Log.w(TAG, "getCornerMarkStateCode hasAndroidDataFile")
            return PROHIBIT_STATE
        }
        if (parseData.isExternalTextFiles()) {
            return DropDispatchAction.COPY_STATE
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (dragTag == null && uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                return DropDispatchAction.COPY_STATE
            }
            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.COPY_STATE
            }
            return DropDispatchAction.PROHIBIT_STATE
        }

        return DropDispatchAction.NORMAL_STATE
    }
}