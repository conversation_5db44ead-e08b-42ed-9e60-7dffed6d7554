/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: PersonalizedServiceController.kt
 ** Description: Check personalized service
 ** Version: 1.0
 ** Date: 2024/11/11
 ** Author: W9086652
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.controller

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.view.KeyEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleObserver
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.KtConstants.ACTION_AGREEMENT_PAGE
import com.filemanager.common.constants.KtConstants.ACTION_PERSONAL_INFO
import com.filemanager.common.controller.PrivacyPolicyController.Companion.activityPrivacyPolicyListener
import com.filemanager.common.controller.PrivacyPolicyController.Companion.privacyPolicyListener
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.oplus.filemanager.interfaze.ad.IAdvertApi

class PersonalizedServiceController : LifecycleObserver {

    companion object {
        private const val TAG = "PersonalizedServiceController"
        const val SP_NAME = "personal_service"
        private const val SP_KEY_SHOULD_SHOW_PERSONAL_SERVICE = "should show personal service"
    }

    private val advertSwitchHelp = Injector.injectFactory<IAdvertApi>()
    private val originRegion = advertSwitchHelp?.getRegion(appContext)

    var agreedToPersonalService: Boolean
        get() = PreferencesUtils.getBoolean(
            SP_NAME,
            key = SP_KEY_SHOULD_SHOW_PERSONAL_SERVICE,
            default = false
        )
        set(value) = PreferencesUtils.put(
            SP_NAME,
            key = SP_KEY_SHOULD_SHOW_PERSONAL_SERVICE,
            value = value
        )

    private var mPersonalServiceDialog: Dialog? = null

    fun isNoAdRegionFromCloud(): Boolean {
        val regionNoAdCloud = advertSwitchHelp?.getRegionFromCloud(appContext)//云控配置的地区码
        Log.d(TAG, "regionNoAdCloud : $regionNoAdCloud")
        Log.d(TAG, "originRegion : $originRegion")
        regionNoAdCloud?.let {
            val list = regionNoAdCloud.split(",")
            for (region in list) {
                if (region.equals(originRegion, ignoreCase = true)) {
                    return true
                }
            }
        }
        return false
    }

    fun carriersSupported(context: Context): Boolean {
        val iAdvertApi = Injector.injectFactory<IAdvertApi>()
        val carrierResult: Boolean? = iAdvertApi?.isCarrierSupport(context)//判断运营商是否支持
        if (carrierResult == null || !carrierResult) {
            Log.d(TAG, "The carrier does not support ads")
            return false
        }
        return true
    }

    fun checkIsSupportAd(context: Context): Boolean {
        //运营商
        if (!carriersSupported(context)) {
            return false
        }
        //美国
        if (isNoAdRegionFromCloud()) {
            Log.d(TAG, "The cloud-controlled area is not an advertising area")
            return false
        }

        return true
    }
    /**
     * 只有外销其他区域才弹窗
     * */
    fun checkShouldShowDialog(activity: Activity, listener: OnPersonalServiceListener): Boolean {
        if (agreedToPersonalService) {
            Log.d(TAG, "it has already been agreed")
            return false
        }
        if (!checkIsSupportAd(activity)) {
            return false
        }
        if (!isActivityValid(activity)) {
            Log.e(TAG, "isActivityValid = false")
            onDestroy()
            return false
        }
        if (mPersonalServiceDialog?.isShowing == true) {
            Log.d(TAG, "mPslServiceDialog is showing and return")
            return false
        }
        showPersonalServiceDialog(activity, listener)
        return true
    }

    fun isNeedAgreeAdPersonalService(context: Context): Boolean {
        if (agreedToPersonalService) {
            Log.d(TAG, "isNeedAgreeAdPersonalService, it has already been agreed")
            return false
        }
        if (!checkIsSupportAd(context)) {
            Log.d(TAG, "isNeedAgreeAdPersonalService, it is not support Ad")
            return false
        }
        return true
    }

    private fun showPersonalServiceDialog(activity: Activity, listener: OnPersonalServiceListener) {
        mPersonalServiceDialog = createPslServiceDialog(activity, listener)
        mPersonalServiceDialog?.show()
        Log.d(TAG, "Display pop-ups")
    }

    private fun isActivityValid(activity: Activity?): Boolean {
        return (activity != null) && !(activity.isFinishing || activity.isDestroyed)
    }

    fun onDestroy() {
        mPersonalServiceDialog?.setOnDismissListener(null)
        mPersonalServiceDialog?.dismiss()
        mPersonalServiceDialog = null
        privacyPolicyListener = null
        activityPrivacyPolicyListener = null
    }

    private fun createPslServiceDialog(
        activity: Activity,
        listener: OnPersonalServiceListener
    ): Dialog {
        val dialog = object : COUIUserStatementDialog(activity) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                super.onBackPressed()
                activity.finish()
            }
        }
        dialog.apply {
            //设置固定非半屏标记
            setCanceledOnTouchOutside(false)
            logoDrawable = ContextCompat.getDrawable(activity, R.drawable.ic_launcher_filemanager)
            appName = activity.getString(R.string.app_name)
            setTitleText(R.string.welcome)
            if (PropertyCompat.sIsGDPR) {
                addGDPRPrivacyPolicyLinkSpan(this, activity)
            } else if (FeatureCompat.sIsExpRom) {
                addExpStatementContent(this, activity)
                addExpPrivacyPolicyLinkSpan(this, activity)
            }
            bottomButtonText = activity.getString(R.string.color_runtime_dialog_ok_r)
            exitButtonText = activity.getString(R.string.btn_runtime_dialog_cancel)
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onBottomButtonClick() {
                    agreedToPersonalService = true
                    dialog.dismiss()
                    Log.d(
                        TAG,
                        "onBottomButtonClick , agreedToPersonalService -> $agreedToPersonalService"
                    )
                }

                override fun onExitButtonClick() {
                    agreedToPersonalService = false
                    activity.finish()
                    dialog.dismiss()
                    Log.d(
                        TAG,
                        "onExitButtonClick , agreedToPersonalService -> $agreedToPersonalService"
                    )
                }
            }
            setOnDismissListener {
                Log.d(TAG, "OnDismissListener")
                listener.onAgreeResult(agreedToPersonalService)
                onDestroy()
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onBackPressed()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }

        return dialog
    }

    private fun addExpStatementContent(dialog: COUIUserStatementDialog, activity: Activity) {
        val appName = activity.getString(R.string.app_name)
        val statementString = activity.getString(R.string.personal_service_content_v1, appName)

        val spannableString = SpannableStringBuilder(statementString)
        dialog.statement = spannableString
    }

    private fun addExpPrivacyPolicyLinkSpan(dialog: COUIUserStatementDialog, activity: Activity) {
        val resources = activity.resources
        val userAgreementLinkString = resources.getString(R.string.user_agreement_title_top)//用户协议
        val theListOfPersonalInformationLinkString =
            resources.getString(R.string.collect_personal_infomation_title)//个人信息明示清单
        val statementString = activity.getString(
            R.string.personal_service_content_v2,
            userAgreementLinkString,
            theListOfPersonalInformationLinkString
        )
        val spannableString = SpannableStringBuilder(statementString)

        val clickToUserAgreement = object : COUIStatementClickableSpan(activity) {
            override fun onClick(widget: View) {
                widget.clearFocus()
                widget.isPressed = false
                widget.isSelected = false
                val intent = Intent(ACTION_AGREEMENT_PAGE)
                activity.startActivity(intent)
            }
        }
        val clickToTheListOfPersonalInformation = object : COUIStatementClickableSpan(activity) {
            override fun onClick(widget: View) {
                widget.clearFocus()
                widget.isPressed = false
                widget.isSelected = false
                val intent = Intent(ACTION_PERSONAL_INFO)
                activity.startActivity(intent)
            }
        }
        setLinkString(
            spannableString,
            statementString,
            userAgreementLinkString,
            clickToUserAgreement
        )
        setLinkString(
            spannableString,
            statementString,
            theListOfPersonalInformationLinkString,
            clickToTheListOfPersonalInformation
        )
        dialog.protocolText = spannableString
    }

    private fun addGDPRPrivacyPolicyLinkSpan(dialog: COUIUserStatementDialog, activity: Activity) {
        val resources = activity.resources
        val appName = activity.getString(R.string.app_name)
        val userAgreementLinkString = resources.getString(R.string.user_agreement_title_top)//用户协议

        val statementString = activity.getString(
            R.string.user_statement_content_exp,
            appName,
            userAgreementLinkString)
        val spannableString = SpannableStringBuilder(statementString)

        val clickToUserAgreement = object : COUIStatementClickableSpan(activity) {
            override fun onClick(widget: View) {
                widget.clearFocus()
                widget.isPressed = false
                widget.isSelected = false
                val intent = Intent(ACTION_AGREEMENT_PAGE)
                activity.startActivity(intent)
            }
        }
        setLinkString(
            spannableString,
            statementString,
            userAgreementLinkString,
            clickToUserAgreement
        )
        dialog.protocolText = spannableString
    }

    private fun setLinkString(
        spannableString: SpannableStringBuilder,
        statementString: String,
        linkString: String,
        obj: COUIStatementClickableSpan
    ) {
        val startIndex = statementString.indexOf(linkString)
        if (startIndex > 0) {
            val endIndex = startIndex + linkString.length
            spannableString.setSpan(obj, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
        }
    }

    interface OnPersonalServiceListener {
        fun onAgreeResult(agree: Boolean)
    }
}