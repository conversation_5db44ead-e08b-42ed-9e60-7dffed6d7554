/***********************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** File: - SPManagerUtil
 ** Description:
 ** Version: 1.0
 ** Date : 2024/9/23
 ** Author: <EMAIL>
 **
 ** --------------------- Revision History: -------------------------------
 ** <W9085374>	<NA> 	  <version >	   <desc>
 ****************************************************************/
package com.filemanager.common.managers

import android.content.Context

object SPManagerUtil {

    /**
     * 获取参数.
     *
     * @param key 待获取参数的 key 值
     * @param defValue (可选) 如果没有获取到该参数的值, 则默认使用的值, 如果不传或传空, 则按类型返回默认值.
     */
    @JvmStatic
    inline fun <reified T> Context.getValue(key: String, defValue: T? = null): T =
        SharedPreferenceManager.getValue(this, key, defValue)

    /**
     * 存储参数.
     *
     * @param key 保存参数的 key 值
     * @param value 保存参数的 value 值
     */
    @JvmStatic
    fun Context.putValue(key: String, value: Any) {
        SharedPreferenceManager.putValue(this, key, value)
    }
}