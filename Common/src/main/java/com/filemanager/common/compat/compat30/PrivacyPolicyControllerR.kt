/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: PrivacyPolicyController.kt
 ** Description: Check privacy policy
 ** Version: 1.0
 ** Date: 2020/4/17
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.compat.compat30

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Build
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.KeyEvent
import android.view.View
import android.view.WindowManager
import androidx.annotation.VisibleForTesting
import androidx.core.content.ContextCompat
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.statement.COUIStatementClickableSpan
import com.coui.appcompat.statement.COUIUserStatementDialog
import com.coui.appcompat.statusbar.COUIStatusbarTintUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.KtConstants.ACTION_USER_INFO
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.stringResource
import com.filemanager.common.view.statement.StatementHelper
import java.util.Locale

class PrivacyPolicyControllerR internal constructor() : PrivacyPolicyController() {


    companion object {
        private const val TAG = "PrivacyPolicyControllerR"
        @VisibleForTesting
        const val SP_NAME = "privacy_policy_alert"
        @VisibleForTesting
        const val SP_KEY_SHOULD_SHOW_PRIVACY_POLICY = "privacy_policy_alert_should_show"
        private const val SP_KEY_ACCEPT_FILE_PREVIEW = "accept_file_preview"
        private const val SP_KEY_SHOW_FUNCTION_DIALOG = "show_function_dialog"
        private const val SP_KEY_REQUEST_ADDITIONAL_FUNCTIONS = "request_additional_functions"
        //同意三方云文档功能授权开关
        private const val SP_KEY_AGREE_ADDITIONAL_FUNCTIONS = "agree_additional_functions"
        private const val SP_KEY_AGREE_USE_NET = "agree_use_net"

        /**
         * Check whether the user agrees to the terms of use
         * FEATURE 2725463: only domestic need to show at first time launch, export no need this
         */
        fun hasAgreePrivacy(): Boolean {
            return hasAgreePrivacy(MyApplication.sAppContext)
        }

        /**
         * Check whether user agree privacy policy.
         *
         * Because distinguish basic features and additional features, determine whether to request additional features.
         */
        fun hasAgreePrivacy(context: Context, determineAdditionalFunctions: Boolean = true): Boolean {
            val additionalFunctions = if (determineAdditionalFunctions) {
                hasRequestAdditionalFunctions()
            } else {
                true
            }
            Log.d(TAG, "hasAgreePrivacy -> determine = $determineAdditionalFunctions ; result = $additionalFunctions")
            return PreferencesUtils.getBoolean(
                context,
                SP_NAME,
                SP_KEY_SHOULD_SHOW_PRIVACY_POLICY,
                !FeatureCompat.sIsExpRom
            ).not() && additionalFunctions
        }

        fun saveAgreePrivacy(shouldShowPolicy: Boolean = false) {
            PreferencesUtils.put(SP_NAME, SP_KEY_SHOULD_SHOW_PRIVACY_POLICY, shouldShowPolicy)
        }

        /**
         * Returns whether additional features have been requested. If is exp rom, return true by default.
         */
        @VisibleForTesting
        fun hasRequestAdditionalFunctions(): Boolean {
            Log.d(TAG, "hasRequestAdditionalFunctions")
            return PreferencesUtils.getBoolean(SP_NAME, SP_KEY_REQUEST_ADDITIONAL_FUNCTIONS, FeatureCompat.sIsExpRom)
        }

        fun saveRequestAdditionalFunctions(hadRequest: Boolean = true) {
            Log.d(TAG, "saveRequestAdditionalFunctions -> hadRequest = $hadRequest")
            PreferencesUtils.put(SP_NAME, SP_KEY_REQUEST_ADDITIONAL_FUNCTIONS, hadRequest)
        }

        fun hasAgreeAdditionalFunctions(): Boolean {
            return PreferencesUtils.getBoolean(SP_NAME, SP_KEY_AGREE_ADDITIONAL_FUNCTIONS, false)
        }

        fun saveAgreeAdditionalFunctions(agree: Boolean) {
            PreferencesUtils.put(SP_NAME, SP_KEY_AGREE_ADDITIONAL_FUNCTIONS, agree)
        }

        fun hasAgreeUseNet(): Boolean {
            return PreferencesUtils.getBoolean(SP_NAME, SP_KEY_AGREE_USE_NET, false)
        }

        fun saveAgreeAgreeUseNet(agree: Boolean) {
            PreferencesUtils.put(SP_NAME, SP_KEY_AGREE_USE_NET, agree)
        }

        fun hasShowFunction(): Boolean {
            return PreferencesUtils.getBoolean(SP_NAME, SP_KEY_SHOW_FUNCTION_DIALOG, false)
        }

        fun saveShowFunction(isShow: Boolean) {
            PreferencesUtils.put(SP_NAME, SP_KEY_SHOW_FUNCTION_DIALOG, isShow)
        }

        /**
         * preload PrivacyPolicy SharePreference
         */
        fun preloadPrivacyPolicySharePreference(context: Context) {
            context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE)
        }

        fun hasAcceptFilePreview(): Boolean {
            return PreferencesUtils.getBoolean(SP_NAME, SP_KEY_ACCEPT_FILE_PREVIEW, false)
        }

        fun saveAcceptFilePreview(status: Boolean) {
            PreferencesUtils.put(SP_NAME, SP_KEY_ACCEPT_FILE_PREVIEW, status)
        }
    }
    private var statementDialog: Dialog? = null
    private var basicFunctionDialog: COUIUserStatementDialog? = null
    private var restrictedDialog: COUIUserStatementDialog? = null

    override fun createPolicyDialog(activity: Activity, listener: OnPrivacyPolicyListener): Dialog {
        Log.d(TAG, "createPolicyDialog")
        if (!FeatureCompat.sIsExpRom) {
            return createPolicyDialog2(activity, listener)
        }
        val dialog = object : COUIUserStatementDialog(activity) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                super.onBackPressed()
                Log.d(TAG, "onBackPressed")
                activity.finish()
            }
        }
        var agree = false
        dialog.apply {
            setCanceledOnTouchOutside(false)
            hideDragView()
            dialog.window?.decorView?.setBackgroundColor(Color.WHITE)
            logoDrawable = ContextCompat.getDrawable(activity, R.drawable.ic_launcher_filemanager)
            setTitleText(R.string.full_page_statement_statement_title)
            addExpStatementContent(this, activity)
            bottomButtonText = activity.getString(R.string.color_runtime_dialog_ok_r)
            exitButtonText = activity.getString(R.string.btn_runtime_dialog_cancel)
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onExitButtonClick() {
                    Log.d(TAG, "onExitButtonClick")
                    activity.window.decorView.visibility = View.INVISIBLE
                    dialog.dismiss()
                    activity.finish()
                }

                override fun onBottomButtonClick() {
                    Log.d(TAG, "onBottomButtonClick")
                    agree = true
                    saveAgreeAdditionalFunctions(true)
                    saveAgreeUseNet(true)
                    dialog.dismiss()
                }
            }
            setOnDismissListener {
                Log.d(TAG, "OnDismissListener")
                listener.onAgreeResult(agree)
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onBackPressed()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        setDialogStatusBarTransparentAndBlackFont(activity, dialog, false)
        return dialog
    }

    private fun addExpStatementContent(dialog: COUIUserStatementDialog,  activity: Activity) {
        Log.d(TAG, "addExpStatementContent")
        val resources = activity.resources
        val statementString: String
        val linkString = resources.getString(R.string.privacy_policy_declare_submit_more)
        statementString = String.format(
            Locale.getDefault(),
            resources.getString(R.string.privacy_policy_declare_content_r_external),
            linkString
        )

        val spannableString = SpannableStringBuilder(statementString)
        val startIndex = statementString.indexOf(linkString)
        if (startIndex > 0) {
            val endIndex = startIndex + linkString.length
            spannableString.setSpan(object : COUIStatementClickableSpan(activity) {
                override fun onClick(widget: View) {
                    PermissionUtils.openPrivacyPolicy(activity)
                    widget.clearFocus()
                    widget.isPressed = false
                    widget.isSelected = false
                }

            }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            spannableString.setSpan(
                ForegroundColorSpan(activity.getColor(R.color.text_color_black_alpha_60)),
                0, startIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        dialog.statement = spannableString
    }

    private fun setDialogStatusBarTransparentAndBlackFont(context: Context, dialog: Dialog, isFullScreen: Boolean) {
        if (dialog.window != null) {
            val window = dialog.window
            val decorView = window?.decorView
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                if (isFullScreen) {
                    decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                }
            }
            var flag = decorView?.systemUiVisibility
            val versionCode = ViewHelper.getRomVersionCode()
            val white = false//context.resources.getBoolean(R.bool.is_status_white)
            if (versionCode >= ViewHelper.COLOR_OS_3_0 || versionCode == 0) {
                window?.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
                if (COUIDarkModeUtil.isNightMode(dialog.context)) {
                    flag = flag?.and(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv())
                    flag = flag?.and(View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv())
                } else {
                    flag = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                        if (!white) {
                            flag?.or(View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR)
                        } else {
                            flag?.or(View.SYSTEM_UI_FLAG_LAYOUT_STABLE)
                        }
                    } else {
                        flag?.or(COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT)
                    }
                }
                flag?.let {
                    decorView?.systemUiVisibility = it
                }
            }
        }
    }

    /**
     * 内销的用户须知弹窗
     */
    private fun createPolicyDialog2(activity: Activity, listener: OnPrivacyPolicyListener): Dialog {
        Log.d(TAG, "createPolicyDialog2")
        var agree: Boolean
        val dialog = object : COUIUserStatementDialog(activity) {
            @Deprecated("Deprecated in Java")
            override fun onBackPressed() {
                super.onBackPressed()
                Log.d(TAG, "onBackPressed")
                activity.finish()
            }
        }
        dialog.apply {
            setCanceledOnTouchOutside(false)
            hideDragView()
            logoDrawable = ContextCompat.getDrawable(activity, R.drawable.ic_launcher_filemanager)
            appName = activity.getString(R.string.app_name)
            setTitleText(R.string.welcome)
            addStatementContent(this, activity, listener)
            addPrivacyPolicyLinkSpan(this, activity)
            bottomButtonText = activity.getString(R.string.color_runtime_dialog_ok_r)
            exitButtonText = activity.getString(R.string.dont_agree)
            onButtonClickListener =  object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onExitButtonClick() {
                    Log.d(TAG, "onExitButtonClick")
                    dialog.dismiss()
                    if (restrictedDialog == null) {
                        initRestrictedDialog(activity, listener)
                    }
                    restrictedDialog?.show()
                }

                override fun onBottomButtonClick() {
                    Log.d(TAG, "createPolicyDialog2, onBottomButtonClick")
                    agree = true
                    saveAgreeAdditionalFunctions(true)
                    saveAgreeUseNet(true)
                    saveDriveCloudAgree(true)
                    saveFeedBackAgree(true)
                    dialog.dismiss()
                    listener.onAgreeResult(agree)
                }
            }

            setOnCancelListener { activity.finish() }
            setOnDismissListener {
                Log.d(TAG, "createPolicyDialog2 OnDismissListener")
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onBackPressed()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        setDialogStatusBarTransparentAndBlackFont(activity, dialog, false)
        statementDialog = dialog
        return dialog
    }

    private fun addStatementContent(
        dialog: COUIUserStatementDialog,
        activity: Activity,
        listener: OnPrivacyPolicyListener
    ) {
        val spannableString: SpannableStringBuilder
        val linkString = activity.getString(R.string.basic_function)
        val appName = activity.getString(R.string.app_name)
        val statementString = activity.getString(R.string.user_statement_content_new, appName, linkString)
        spannableString = SpannableStringBuilder(statementString)
        val startIndex = statementString.indexOf(linkString)
        if (startIndex > 0) {
            val endIndex = startIndex + linkString.length
            spannableString.setSpan(object : COUIStatementClickableSpan(activity) {
                override fun onClick(widget: View) {
                    if (basicFunctionDialog == null) {
                        initBasicFunctionDialog(activity, listener)
                    }
                    statementDialog?.dismiss()
                    basicFunctionDialog?.show()
                    widget.clearFocus()
                    widget.isPressed = false
                    widget.isSelected = false
                }
            }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            spannableString.setSpan(
                ForegroundColorSpan(activity.getColor(R.color.text_color_black_alpha_60)),
                0, startIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        dialog.statement = spannableString
    }

    private fun initRestrictedDialog(activity: Activity, listener: OnPrivacyPolicyListener) {
        Log.d(TAG, "initRestrictedDialog")
        val restrictedTextList = ArrayList<String>()
        restrictedTextList.add(stringResource(R.string.personal_information_protection_policy))
        val restrictedListenerList = ArrayList<StatementHelper.OnSpanClickListener>()
        restrictedListenerList.add(object : StatementHelper.OnSpanClickListener {
            override fun onSpanClick() {
                startPrivacyPolicy(activity)
            }
        })

        restrictedDialog = COUIUserStatementDialog(activity).apply {
            setIsShowInMaxHeight(false)
            titleText = stringResource(R.string.basic_function_continue_title)
            bottomButtonText = stringResource(R.string.basic_function_ok)
            exitButtonText = stringResource(R.string.btn_runtime_dialog_cancel)
            statement = stringResource(R.string.user_statement_limit_content)
            protocolText = StatementHelper.createSpannableString(
                activity,
                stringResource(
                    R.string.check_for_details,
                    stringResource(R.string.personal_information_protection_policy)
                ),
                restrictedTextList,
                restrictedListenerList
            )
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onBottomButtonClick() {
                    Log.w(TAG, "restrictedDialog -> onBottomButtonClick")
                    listener.onAgreeResult(true)
                    restrictedDialog?.dismiss()
                }

                override fun onExitButtonClick() {
                    Log.w(TAG, "restrictedDialog -> onExitButtonClick")
                    activity.window.decorView.visibility = View.INVISIBLE
                    restrictedDialog?.dismiss()
                    activity.finish()
                }
            }
            setOnCancelListener {
                Log.w(TAG, "onCancel")
                statementDialog?.show()
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onButtonClickListener?.onExitButtonClick()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        restrictedDialog?.dragableLinearLayout?.dragView?.visibility = View.INVISIBLE
        restrictedDialog?.window?.decorView?.setBackgroundColor(Color.WHITE)
    }

    /**
     * 使用基本功能弹窗
     */
    private fun initBasicFunctionDialog(activity: Activity, listener: OnPrivacyPolicyListener) {
        val basicFunctionTextList = ArrayList<String>()
        basicFunctionTextList.add(stringResource(R.string.personal_information_protection_policy))
        val basicFunctionListenerList = ArrayList<StatementHelper.OnSpanClickListener>()
        basicFunctionListenerList.add(object : StatementHelper.OnSpanClickListener {
            override fun onSpanClick() {
                startPrivacyPolicy(activity)
            }
        })
        basicFunctionDialog = COUIUserStatementDialog(activity).apply {
            setIsShowInMaxHeight(false)
            titleText = stringResource(R.string.basic_function_title)
            bottomButtonText = stringResource(R.string.basic_function_ok)
            exitButtonText = stringResource(R.string.basic_function_cancel)
            statement = stringResource(R.string.user_statement_basic_content)
            protocolText = StatementHelper.createSpannableString(
                activity,
                stringResource(
                    R.string.check_for_details,
                    stringResource(R.string.personal_information_protection_policy)
                ),
                basicFunctionTextList,
                basicFunctionListenerList
            )
            onButtonClickListener = object : COUIUserStatementDialog.OnButtonClickListener {
                override fun onBottomButtonClick() {
                    Log.w(TAG, "basicFuncDialog -> onBottomButtonClick")
                    listener.onAgreeResult(true)
                    basicFunctionDialog?.dismiss()
                }

                override fun onExitButtonClick() {
                    Log.w(TAG, "basicFuncDialog -> onExitButtonClick")
                    basicFunctionDialog?.dismiss()
                    statementDialog?.show()
                }
            }
            setOnCancelListener {
                statementDialog?.show()
            }
            setOnKeyListener { _, keyCode, event ->
                Log.d(TAG, "onKey keyCode:$keyCode event:$event")
                if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                    onButtonClickListener?.onExitButtonClick()
                    return@setOnKeyListener true
                }
                false
            }
            PredictiveBackUtils.registerOnBackInvokedCallback(this)
        }
        basicFunctionDialog?.hideDragView()
        basicFunctionDialog?.window?.decorView?.setBackgroundColor(Color.WHITE)
    }

    private fun addPrivacyPolicyLinkSpan(
        dialog: COUIUserStatementDialog,
        activity: Activity
    ) {
        Log.d(TAG, "addPrivacyPolicyLinkSpan")
        val statementString: String
        val spannableString: SpannableStringBuilder
        val resources = activity.resources
        if (FeatureCompat.sIsExpRom) {
            val linkString = resources.getString(R.string.privacy_policy_declare_submit_more)
            statementString = String.format(
                Locale.getDefault(),
                resources.getString(R.string.privacy_policy_declare_content_r_external),
                linkString)

            spannableString = SpannableStringBuilder(statementString)
            val startIndex = statementString.indexOf(linkString)
            if (startIndex > 0) {
                val endIndex = startIndex + linkString.length
                spannableString.setSpan(object : COUIStatementClickableSpan(activity) {
                    override fun onClick(widget: View) {
                        PermissionUtils.openPrivacyPolicy(activity)
                        widget.clearFocus()
                        widget.isPressed = false
                        widget.isSelected = false
                    }
                }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
                spannableString.setSpan(
                    ForegroundColorSpan(activity.getColor(R.color.text_color_black_alpha_60)),
                    0, startIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            }
        } else {
            val linkString2 = resources.getString(R.string.personal_information_protection_policy)
            statementString = String.format(
                Locale.getDefault(),
                resources.getString(R.string.check_for_details),
                linkString2)
            spannableString = SpannableStringBuilder(statementString)
            val startIndex2 = statementString.indexOf(linkString2)
            if (startIndex2 > 0) {
                val endIndex = startIndex2 + linkString2.length
                spannableString.setSpan(object : COUIStatementClickableSpan(activity) {
                    override fun onClick(p0: View) {
                        startPrivacyPolicy(activity)
                        p0.clearFocus()
                        p0.isPressed = false
                        p0.isSelected = false
                    }
                }, startIndex2, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            }
        }
        dialog.protocolText = spannableString
    }

    private fun startPrivacyPolicy(context: Context) {
        val intent = Intent(ACTION_USER_INFO)
        intent.setPackage(context.packageName)
        runCatching {
            context.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "startPrivacyPolicy -> error cause ${it.message}")
        }
    }
}