/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.helper;

import com.filemanager.common.utils.Log;
import org.apache.commons.io.FilenameUtils;
import java.io.File;
import java.io.FileFilter;
import java.io.FilenameFilter;
import java.io.IOException;
import java.util.ArrayList;

public class FileWrapper extends File {
    public static final byte TYPE_FILE_LIST_HEADER = 101;
    public static final byte TYPE_RELATED_FILE = 102;
    public static final byte TYPE_LABLE_FILE = 103;
    // #ifdef REALMEOS_EDIT
    // <EMAIL>,2020/3/13 , Add for ad
    public static final byte TYPE_AD_VIEW = 104;
    // #endif /* REALMEOS_EDIT */
    private int mType;
    private byte mViewType;
    private int mViewTypeNum;
    private int mLable;
    private boolean mRootStoragePath = false;
    private int mOrientation;

    public FileWrapper(String pathname) {
        super(pathname);
        innerGetType();
    }

    public FileWrapper(String pathname, int type) {
        super(pathname);
        innerGetType(type);
    }

    public FileWrapper(int orientation, String pathname) {
        super(pathname);
        mOrientation = orientation;
        innerGetType();
    }

    public FileWrapper(String pathname, byte viewType, int typeNum) {
        super(pathname);
        mViewType = viewType;
        mViewTypeNum = typeNum;
        innerGetType();
    }

    public FileWrapper(String pathname, int lable, byte viewType, int typeNum) {
        super(pathname);
        mLable = lable;
        mViewType = viewType;
        mViewTypeNum = typeNum;
        innerGetType();
    }

    public FileWrapper(File parent, String child) {
        super(parent, child);
        innerGetType();
    }

    public FileWrapper(String parent, String child) {
        super(parent, child);
        innerGetType();
    }

    public FileWrapper(File file) {
        this(file.getAbsolutePath());
    }

    @Override
    public FileWrapper[] listFiles() {
        String[] ss = list();
        if (ss == null) {
            return null;
        }
        int n = ss.length;
        FileWrapper[] fs = new FileWrapper[n];
        for (int i = 0; i < n; i++) {
            fs[i] = new FileWrapper(this, ss[i]);
        }
        return fs;
    }

    /**
     * To list files where in the specified directory.
     *
     * @param flag whether hide the system file, for example ".android"
     * @return
     */
    public ArrayList<FileWrapper> listArrayFiles(boolean flag) {
        String[] ss = list();
        if (ss == null) {
            Log.v("FileWrapper", "file system can not fetch data");
            return null;
        }
        int n = ss.length;
        ArrayList<FileWrapper> v = new ArrayList<FileWrapper>(n);
        for (int i = 0; i < n; i++) {
            if (flag) {
                if (ss[i].startsWith(".")) {
                    continue;
                }
            }
            v.add(new FileWrapper(this, ss[i]));
        }
        return v;
    }

    public ArrayList<FileWrapper> listArrayFolder(boolean flag) {
        String[] ss = list();
        if (ss == null) {
            Log.v("FileWrapper", "file system can not fetch data");
            return null;
        }
        int n = ss.length;
        ArrayList<FileWrapper> v = new ArrayList<FileWrapper>(n);
        FileWrapper file = null;
        for (int i = 0; i < n; i++) {
            if (flag) {
                file = new FileWrapper(this, ss[i]);
                if (ss[i].startsWith(".") || file.isFile()) {
                    continue;
                }
            }
            v.add(new FileWrapper(this, ss[i]));
        }
        return v;
    }

    @Override
    public FileWrapper[] listFiles(FileFilter filter) {
        String ss[] = list();
        if (ss == null) {
            return null;
        }
        ArrayList<FileWrapper> v = new ArrayList<FileWrapper>();
        for (int i = 0; i < ss.length; i++) {
            FileWrapper f = new FileWrapper(this, ss[i]);
            if ((filter == null) || filter.accept(f)) {
                v.add(f);
            }
        }
        return v.toArray(new FileWrapper[v.size()]);
    }

    @Override
    public FileWrapper[] listFiles(FilenameFilter filter) {
        String ss[] = list();
        if (ss == null) {
            return null;
        }
        ArrayList<FileWrapper> v = new ArrayList<FileWrapper>();
        for (int i = 0; i < ss.length; i++) {
            if ((filter == null) || filter.accept(this, ss[i])) {
                v.add(new FileWrapper(this, ss[i]));
            }
        }
        return (FileWrapper[]) (v.toArray(new FileWrapper[v.size()]));
    }

    @Override
    public FileWrapper getParentFile() {
        String p = this.getParent();
        if (p == null) {
            return null;
        }
        return new FileWrapper(p);
    }

    @Override
    public FileWrapper getAbsoluteFile() {
        return new FileWrapper(this.getAbsolutePath());
    }

    @Override
    public FileWrapper getCanonicalFile() throws IOException {
        return new FileWrapper(getCanonicalPath());
    }

    public String getBaseNameWithExt() {
        return FilenameUtils.getName(this.getAbsolutePath());
    }

    public int getType() {
        return mType;
    }

    public long getCShot() {
        return 0;
    }

    public int getId() {
        return 0;
    }

    public long getDateTaken() {
        return 0;
    }

    public byte getViewType() {
        return mViewType;
    }

    public int getLable() {
        return mLable;
    }

    public int getViewTypeNum() {
        return mViewTypeNum;
    }

    private void innerGetType() {
        mType = isDirectory() ? MimeTypeHelper.DIRECTORY_TYPE : MimeTypeHelper.Companion.getTypeFromPath(getAbsolutePath());
    }

    private void innerGetType(int type) {
        switch (type) {
            case CategoryHelper.CATEGORY_IMAGE:
            case CategoryHelper.CATEGORY_AUDIO:
            case CategoryHelper.CATEGORY_VIDEO:
            case CategoryHelper.CATEGORY_DOC:
                mType = MimeTypeHelper.Companion.getTypeFromPath(getAbsolutePath());
                break;
            case CategoryHelper.CATEGORY_APK:
                mType = MimeTypeHelper.APPLICATION_TYPE;
                break;
            default:
                mType = isDirectory() ? MimeTypeHelper.DIRECTORY_TYPE : MimeTypeHelper.Companion.getTypeFromPath(getAbsolutePath());
                break;
        }
    }

    public void setRootStorageFlag(boolean flag) {
        mRootStoragePath = flag;
    }

    public boolean getRootStorageFlag() {
        return mRootStoragePath;
    }

    public int getOrientation() {
        return mOrientation;
    }

    public void setmViewType(byte mViewType) {
        this.mViewType = mViewType;
    }

    public void setmViewTypeNum(int mViewTypeNum) {
        this.mViewTypeNum = mViewTypeNum;
    }

    public void setmLable(int mLable) {
        this.mLable = mLable;
    }
}
