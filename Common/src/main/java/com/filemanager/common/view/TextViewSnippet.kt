/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : TextViewSnippet.kt
 * * Description : add snippet for TextView
 * * Version     : 1.0
 * * Date        : 2020/12/4
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.app.Activity
import android.content.Context
import android.graphics.Point
import android.text.Layout
import android.text.SpannableString
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.StaticLayout
import android.text.TextDirectionHeuristics
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.text.style.IconMarginSpan
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.annotation.MainThread
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.text.getSpans
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.utils.WindowUtils
import org.apache.commons.io.FilenameUtils
import java.util.Locale
import kotlin.math.max
import kotlin.math.min

class TextViewSnippet @JvmOverloads constructor(
        context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : AppCompatTextView(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "TextViewSnippet"
        private const val ELLIPSIS = '\u2026'
        private const val HALF = 2
        private const val QUARTER = 4
        private const val RETENRTION = 4
        private const val NEWLINE = "\n"
        private const val POINT = "."
    }

    private var lastWidth: Int = 0
    private var mFullText: String? = null
    private var mSearchText: String? = null
    private var searchTextDMPTitleList: List<String>? = null
    private var mLayoutListener: OnLayoutChangeListener? = null
    private var mLayoutListenerForTwoLine: OnLayoutChangeListener? = null
    private var mOriginText: CharSequence = ""
    private var isContent: Boolean = false
    private fun getFolderTitleMaxSize(context: Context): Int = if (context is Activity) {
        if (WindowUtils.isSmallScreen(context)) {
            KtViewUtils.getWindowSize(context).x - context.getResources()
                .getDimensionPixelOffset(R.dimen.dimen_188dp)
        } else {
            MyApplication.appContext.resources.getDimensionPixelOffset(R.dimen.dimen_320dp)
        }
    } else {
        MyApplication.appContext.resources.getDimensionPixelOffset(R.dimen.file_list_item_info_selected_width_new)
    }
    private var mHighlightColor = 0

    init {
        mHighlightColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTheme, 0)
    }

    fun setHighLightColor(@ColorInt color: Int) {
        mHighlightColor = color
    }

    private fun updateText(needRetention: Boolean = true) {
        if ((mFullText.isNullOrEmpty()) || (mSearchText.isNullOrEmpty())) {
            Log.w(TAG, "updateText: mFullText or mSearchString is null")
            return
        }

        val startPos = mFullText!!.toLowerCase(Locale.getDefault())
                .indexOf(mSearchText!!.toLowerCase(Locale.getDefault()))

        if (-1 == startPos) {
            Log.w(TAG, "updateText: startPos is -1")
            val snippetString = measureSnippetText(mFullText!!, 0, 1, needRetention)
            val visibleWidth = getWidthButDrawableStart() - paddingLeft - paddingRight
            val checkWidth = resources.displayMetrics.widthPixels / QUARTER
            val maxWidth = visibleWidth.coerceAtLeast(checkWidth)
            val spannable = buildSpannable(
                maxWidth = maxWidth,
                snippetString = snippetString,
                visibleWidth = visibleWidth
            )
            text = spannable
            return
        }

        val searchTextLength = mSearchText!!.length
        val searchTextWidth = paint.measureText(mSearchText)
        val textFieldWidth = width.toFloat()

        var snippetString = if (searchTextWidth > textFieldWidth) {
            val endIndex = min(startPos + searchTextLength, mFullText!!.length)
            mFullText!!.substring(startPos, endIndex)
        } else {
            // both ends
            ellipsize = null
            measureSnippetText(mFullText!!, startPos, searchTextLength, needRetention)
        }

        val enterIndexOfSnippetString = snippetString.indexOf("\n")
        if (enterIndexOfSnippetString > 0) {
            snippetString = snippetString.replace("\n", "")
        }
        val visibleWidth = getWidthButDrawableStart() - paddingLeft - paddingRight
        val checkWidth = resources.displayMetrics.widthPixels / QUARTER
        val maxWidth = visibleWidth.coerceAtLeast(checkWidth)
        val spannable = buildSpannable(
            maxWidth = maxWidth,
            snippetString = snippetString,
            visibleWidth = visibleWidth
        )
        Log.d(TAG, "searchTextDMPTitleList: $searchTextDMPTitleList")
        if (searchTextDMPTitleList != null) {
            searchTextDMPTitleList?.let {
                for (i in it.indices) {
                    Log.d(TAG, "searchTextDMPTitleList spannable ${it[i]}")
                    val word = it[i].trim()
                    setSpan(spannable, word, word.length)
                }
            }
        } else {
            setSpan(spannable, mSearchText, searchTextLength)
        }
        text = spannable
        mSearchText = ""
    }

    fun buildSpannable(snippetString: String, visibleWidth: Int, maxWidth: Int): SpannableString {
        val spannable: SpannableString
        var isEllipsisFirstChar = false
        if (paint.measureText(snippetString) > maxWidth) {  //判断整个字符串的宽度是否超出了textView的宽度
            var lastIndex = 0
            for (index in (1..snippetString.length)) {
                if (paint.measureText(snippetString.substring(0, index)) > visibleWidth) {  //找出第一行能显示出多少个字符
                    lastIndex = index - 1
                    break
                }
            }
            val pointLastIndex =  snippetString.lastIndexOf(POINT) // 获取最后一个 "." 的index
            if (pointLastIndex in 1 until lastIndex) {
                lastIndex = pointLastIndex // 如果最后一个"."在第一行的最后一个字符前，则提前换行
            }
            val secondText = snippetString.substring(lastIndex)  // 截取第二行的字符串内容
            val sequence = if (isContent) {
                secondText
            } else {
                TextUtils.ellipsize(secondText, paint, visibleWidth.toFloat(), TextUtils.TruncateAt.MIDDLE)   // 把第二行的字符串内容做缩略显示处理
            }
            val firstLineString = if (snippetString[0] == ELLIPSIS) {
                isEllipsisFirstChar = true
                snippetString.substring(1, lastIndex)
            } else {
                snippetString.substring(0, lastIndex)
            }
            val newSnippetString =
                if (isEllipsisFirstChar) { ELLIPSIS.toString() } else { "" } +
                        Utils.formatFileNameWithRTL(firstLineString) +
                        NEWLINE +
                        Utils.formatFileNameWithRTL(sequence.toString())//拼接第一行和第二行的内容
            spannable = SpannableString(newSnippetString)
        } else {
            spannable = SpannableString(Utils.formatFileNameWithRTL(snippetString))
        }
        return spannable
    }

    private fun setSpan(
        spannable: SpannableString,
        mSearchText: String?,
        searchTextLength: Int
    ) {
        Log.d(TAG, "setSpan spannable: $spannable, mSearchText: $mSearchText, searchTextLength: $searchTextLength")
        var snippetStringLower = spannable.toString().lowercase(Locale.getDefault())
        var start = 0
        val enterIndex = snippetStringLower.indexOf("\n")
        if (enterIndex > 0) {
            snippetStringLower = snippetStringLower.replace("\n", "")
        }
        while (true) {
            if (mSearchText.isNullOrEmpty()) {
                break
            }
            if (spannable.isEmpty()) {
                break
            }
            var index = snippetStringLower.indexOf(mSearchText.toLowerCase(Locale.getDefault()), start)
            if (index == -1) {
                break
            }
            val endIndex =
                ViewUtils.getEndIndex(index, enterIndex, searchTextLength, spannable.length)
            index = ViewUtils.getStartIndex(index, enterIndex)
            Log.d(TAG, "start: $index, end: $endIndex")
            spannable.setSpan(
                ForegroundColorSpan(mHighlightColor),
                index,
                endIndex,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            start = if (enterIndex < spannable.length && enterIndex != -1 && enterIndex <= endIndex) {
                endIndex - 1
            } else {
                endIndex
            }
        }
    }

    private fun measureSnippetText(
        fullText: String,
        startPos: Int,
        searchTextLength: Int,
        needRetention: Boolean = true
    ): String {
        val lineTextWidth = width.toFloat() - paddingLeft - paddingRight
        val fullTextLength = fullText.length
        var snippetString = ""
        var snippetStringBuff = ""
        var offset = -1
        var start = -1
        var end = -1
        var traversalString = fullText
        var retentionString = ""
        var traversalStringLength = -1
        val ext = FilenameUtils.getExtension(fullText)
        var extWithPoint = ""
        var index = -1
        index.takeIf {
            needRetention
        }?.let {
            index = it.takeIf { ext.isNullOrEmpty().not() }?.let {
                fullText.length - (ext.length + 1) - RETENRTION
            } ?: (fullText.length - RETENRTION)
        }
        index.takeIf { it > 0 }?.let {
            extWithPoint = if (ext.isNotBlank()) ".$ext" else ""
            traversalString = fullText.substring(0, index)
            retentionString = fullText.substring(index, fullTextLength - extWithPoint.length)
        }
        traversalStringLength = traversalString.length
        if (startPos > traversalStringLength || startPos + searchTextLength > traversalStringLength) {
            traversalString = fullText
            extWithPoint = ""
            retentionString = ""
            traversalStringLength = traversalString.length
        }
        while (true) {
            offset++
            if (offset > traversalStringLength) {
                break
            }
            val newStart = max(0, startPos - offset)
            val newEnd = min(traversalStringLength, startPos + searchTextLength + offset)
            if ((newStart == start) && (newEnd == end)) {
                break
            }
            start = newStart
            end = newEnd
            val candidate = traversalString.substring(start, end)
            val startPart = if (start == 0) "" else ELLIPSIS
            val endPart = if (end == traversalStringLength) "" else ELLIPSIS
            snippetString = "$startPart$candidate$endPart$retentionString$extWithPoint"
            if (overMaxLinesFun(snippetString, lineTextWidth) && snippetStringBuff.isNotEmpty()) {
                snippetString = snippetStringBuff
                Log.i(TAG, "over the maxLines")
                break
            } else {
                snippetStringBuff = snippetString
            }
        }
        return snippetString
    }

    private fun overMaxLinesFun(snippetString: String, lineTextWidth: Float): Boolean {
        var nowLine = 1
        var currentString = snippetString
        var currentStringWidth = paint.measureText(snippetString)
        while (currentStringWidth > lineTextWidth) {
            nowLine++
            if (nowLine > maxLines) {
                return true
            } else {
                val lineEndIndex = getOverOneLineIndex(currentString, lineTextWidth)
                currentString = currentString.substring(lineEndIndex)
                currentStringWidth = paint.measureText(currentString)
            }
        }
        return false
    }

    private fun getOverOneLineIndex(text: String, lineWidth: Float): Int {
        for (i in 1..text.length) {
            if (paint.measureText(text.substring(0, i)) > lineWidth) {
                return i - 1
            }
        }
        return -1
    }

    /**
     * needRetention 是否需要保留末尾显示
     */
    @MainThread
    fun setTextWithPost(
        fullText: String?,
        searchText: String?,
        needRetention: Boolean = true,
        isContent: Boolean = false,
        hasAnotherName: Boolean = false
    ) {
        lastWidth = width
        this.isContent = isContent
        setText(fullText, searchText, needRetention, hasAnotherName)
        post {
            if (lastWidth != 0 && lastWidth != width) {
                setText(fullText, searchText, needRetention, hasAnotherName)
            }
            return@post
        }
    }

    /**
     * Highlight search text will not have space to display alias
     */
    @MainThread
    fun setText(fullText: String?, searchText: String?, needRetention: Boolean = true, hasAnotherName: Boolean = false) {
        Log.d(TAG, "setText searchText  $searchText")
        if (fullText.isNullOrEmpty()) {
            return
        }
        if (searchText.isNullOrEmpty()) {
            text = fullText
            return
        }

        mFullText = fullText
        mSearchText = searchText
        if (hasAnotherName) {
            layoutParams.width = ViewGroup.LayoutParams.WRAP_CONTENT
            width = getFolderTitleMaxSize(context)
        } else {
            layoutParams.width = ViewGroup.LayoutParams.MATCH_PARENT
        }
        text = mFullText
        if (width == 0) {
            if (mLayoutListener == null) {
                mLayoutListener = object : OnLayoutChangeListener {
                    override fun onLayoutChange(v: View?, left: Int, top: Int, right: Int, bottom: Int, oldLeft: Int, oldTop: Int, oldRight: Int, oldBottom: Int) {
                        if (width != 0) {
                            updateText(needRetention)
                        }
                        removeOnLayoutChangeListener(this)
                        mLayoutListener = null
                    }
                }
            } else {
                removeOnLayoutChangeListener(mLayoutListener)
            }
            addOnLayoutChangeListener(mLayoutListener)
        } else {
            updateText(needRetention)
        }
    }

    override fun setText(textTemp: CharSequence?, type: BufferType?) {
        if (mSearchText.isNullOrEmpty()) {
            if (maxWidth == 0) {
                maxWidth = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_list_item_info_selected_width_new_new)
            }
        }
        super.setText(textTemp, type)
    }

    override fun onDetachedFromWindow() {
        mLayoutListener?.let {
            removeOnLayoutChangeListener(it)
        }
        mLayoutListenerForTwoLine?.let {
            removeOnLayoutChangeListener(it)
            mLayoutListenerForTwoLine = null
        }
        super.onDetachedFromWindow()
    }

    fun setTextViewStyle(ignorePoint: Boolean = false) {
        saveOrigintextAndAddListener(ignorePoint)
    }

    fun setTextViewStyleForFixedWidth() {
        saveOrigintextAndAddListener()
        if (width > 0) {
            modifyText()
            requestLayout()
        }
    }

    private fun saveOrigintextAndAddListener(ignorePoint: Boolean = false) {
        mOriginText = text
        if (mLayoutListenerForTwoLine == null) {
            mLayoutListenerForTwoLine = object : OnLayoutChangeListener {
                override fun onLayoutChange(
                    v: View?,
                    left: Int,
                    top: Int,
                    right: Int,
                    bottom: Int,
                    oldLeft: Int,
                    oldTop: Int,
                    oldRight: Int,
                    oldBottom: Int
                ) {
                    handleStartDrawable()
                    if (width != 0) {
                        text = mOriginText
                        modifyText(ignorePoint)
                    }
                }
            }
            addOnLayoutChangeListener(mLayoutListenerForTwoLine)
        }
    }

    private fun modifyText(ignorePoint: Boolean = false) {
        if (isSingleLine || maxLines == 1) {
            return
        }
        val firstLineWidth = getLineMaxWidth(0)
        val firstLinePoint = getLineTextPoint(text, firstLineWidth.toInt(), 0)
        val paintWidth = paint.measureText(text.toString())
        var lastIndex = firstLinePoint.y
        if (firstLineWidth < paintWidth) {  //判断整个字符串的宽度是否超出了textView的宽度
            val pointLastIndex = text.lastIndexOf(POINT) // 获取最后一个 "." 的index
            if (!ignorePoint && pointLastIndex in 1 until lastIndex) {
                lastIndex = pointLastIndex // 如果最后一个"."在第一行的最后一个字符前，则提前换行
            }
            val secondText = text.substring(lastIndex)  // 截取第二行的字符串内容
            val secondLineWidth = getLineMaxWidth(1)
            val sequence = TextUtils.ellipsize(secondText, paint, secondLineWidth, TextUtils.TruncateAt.MIDDLE)   // 把第二行的字符串内容做缩略显示处理
            val newText = text.substring(0, lastIndex) + NEWLINE + sequence.toString() //拼接第一行和第二行的内容
            text = createSpan(text, newText)
        }
    }

    /**
     * 获取第几行的最大宽度
     * @param line 第几行
     */
    private fun getLineMaxWidth(line: Int): Float {
        val visibleWidth = getWidthButDrawableStart() - paddingLeft - paddingRight
        val checkWidth = resources.displayMetrics.widthPixels / QUARTER
        val span = getIconMarginSpan(text)
        val margin = span?.getLeadingMargin(line == 0) ?: 0
        Log.d(TAG, "getLineMaxWidth $line -> visible:$visibleWidth margin:$margin spans:$span checkWidth:$checkWidth")
        return if (visibleWidth > 0) (visibleWidth.toFloat() - margin) else (checkWidth.toFloat() - margin)
    }

    /**
     * 给定文字，返回静态布局
     * 即当前文字，会显示的布局，用于获取当前显示的文字的行数，有几行，每一行的文字
     */
    private fun getStaticLayout(text: CharSequence, width: Int): StaticLayout {
        return StaticLayout.Builder.obtain(text, 0, text.length, paint, width)
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setTextDirection(TextDirectionHeuristics.FIRSTSTRONG_LTR)
            .setLineSpacing(lineSpacingExtra, lineSpacingMultiplier)
            .setIncludePad(includeFontPadding).setBreakStrategy(breakStrategy)
            .setHyphenationFrequency(hyphenationFrequency)
            .setMaxLines(if (maxLines == -1) Integer.MAX_VALUE else maxLines)
            .setJustificationMode(justificationMode).build()
    }

    /**
     * 获取一行文字显示的位置
     */
    private fun getLineTextPoint(text: CharSequence, lineWidth: Int, line: Int = 0): Point {
        val layout = getStaticLayout(text, lineWidth)
        return Point(layout.getLineStart(line), layout.getLineEnd(line))
    }


    /**
     * 创建一个span
     * @param originText 原始数据
     * @param newText 新的文字
     */
    private fun createSpan(originText: CharSequence, newText: CharSequence): Spanned {
        val span = SpannableStringBuilder(newText)
        val iconSpan = getIconMarginSpan(originText)
        if (iconSpan != null) {
            span.setSpan(iconSpan, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
        }
        return span
    }

    /**
     * 查找IconMarginSpan
     */
    private fun getIconMarginSpan(text: CharSequence): IconMarginSpan? {
        (text as? Spanned)?.getSpans<IconMarginSpan>(0, 1)?.let {
            if (it.isNotEmpty()) {
                return it[0]
            }
        }
        return null
    }

    fun isMoreThanOneLine(text: String): Boolean {
        val visibleWidth = getWidthButDrawableStart() - paddingLeft - paddingRight
        val checkWidth = resources.displayMetrics.widthPixels / QUARTER
        val maxWidth = visibleWidth.coerceAtLeast(checkWidth)
        val textWidth = paint.measureText(text)
        return textWidth > maxWidth
    }

    /**
     * 有标签Icon的，宽度减去Icon的宽度
     */
    private fun getWidthButDrawableStart(): Int {
        val drawableStart = getDrawableStart()
        val drawableWidth = drawableStart?.let {
            drawableStart.intrinsicWidth.plus(compoundDrawablePadding)
        } ?: 0
        return width - drawableWidth
    }

    private fun getDrawableStart() = if (Utils.isRtl()) compoundDrawables[2] else compoundDrawables[0]

    /** 左边Label icon 第一行居中，与文字增加间距 */
    private fun handleStartDrawable() {
        val drawableStart = getDrawableStart()
        drawableStart ?: return
        val lineCount = min(lineCount, maxLines)
        val vSpace = bottom - top - compoundPaddingBottom - compoundPaddingTop
        val verticalOffset = (-1 * (vSpace * (1 - 1.0f / lineCount)) / 2).toInt()
        drawableStart.setBounds(
            0,
            verticalOffset,
            drawableStart.intrinsicWidth,
            drawableStart.intrinsicHeight + verticalOffset
        )
    }

    /**
     * 设置中子搜索时 标题的命中词组
     */
    fun setDMPTitleList(titleKeyList: List<String>?) {
        searchTextDMPTitleList = titleKeyList
        Log.i(TAG, "setDMPTitleList $searchTextDMPTitleList")
    }
}