/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DFMMediaFile
 ** Description : DFM Media File
 ** Version     : 1.0
 ** Date        : 2024/03/13 19:18
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/03/13       1.0      create
 ***********************************************************************/
package com.filemanager.common.base

import android.net.Uri
import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.MimeTypeHelper
import com.oplus.filemanager.dfm.DFMManager
import org.apache.commons.io.FilenameUtils
import java.io.File

class DFMMediaFile : BaseFileBean {

    var id: Int = 0

    /**
     * cursor查询出来的路径:相对路径
     */
    var cursorPath: String? = ""

    /**
     * 真实路径
     */
    private var realMountPath: String = ""

    /**
     * DFMManager查询出来的URI
     */
    private var mountUri: Uri? = null
    override var mDateModified: Long = 0

    constructor()

    constructor(id: Int, cursorPath: String?, displayName: String?, mimeType: String?, size: Long, dateModified: Long) : super() {
        this.id = id
        this.cursorPath = cursorPath
        mDisplayName = displayName
        mSize = size
        mDateModified = dateModified
        mMimeType = mimeType
        mLocalType = MimeTypeHelper.getTypeFromExtension(FilenameUtils.getExtension(mDisplayName))
            ?: MimeTypeHelper.UNKNOWN_TYPE
    }

    override fun toString(): String {
        return "DFMMediaFile(id=$id, remotePath=$cursorPath)"
    }


    /**
     * mData路径为真实路径：/mnt/dfs/xxx
     */
    override fun localPath(field: String?): String? {
        if (TextUtils.isEmpty(realMountPath)) {
            realMountPath = getRealPath(cursorPath ?: "")
        }
        return realMountPath
    }

    override fun localUri(field: Uri?): Uri? {
        if (mountUri == null) {
            mountUri = DFMManager.getRemoteUri(mData ?: "", MyApplication.sAppContext)
        }
        return mountUri
    }

    /**
     * 获取挂载的真实路径
     */
    private fun getRealPath(path: String): String {
        val remoteRootPath = DFMManager.getRemoteDeviceMountPath() ?: return path
        return getMountRealPath(path, remoteRootPath)
    }

    /**
     * 获取挂载的真实路径
     */
    private fun getMountRealPath(path: String, remotePath: String): String {
        val builder = StringBuilder(remotePath)
        if (!path.startsWith(File.separator)) {
            builder.append(File.separator)
        }
        builder.append(path)
        return builder.toString()
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false
        if (!super.equals(other)) return false

        other as DFMMediaFile

        return id == other.id
    }

    override fun hashCode(): Int {
        var result = super.hashCode()
        result = 31 * result + id
        return result
    }
}