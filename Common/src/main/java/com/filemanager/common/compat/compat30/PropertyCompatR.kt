/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/15
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.compat.compat30

import android.os.Bundle
import android.os.OplusSystemProperties
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.CompatUtils
import com.filemanager.common.compat.OplusBuildCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.compat.PropertyCompat.FEATURE_GDPR
import com.filemanager.common.compat.PropertyCompat.FEATURE_REALME_MACHINE_MODEL
import com.filemanager.common.compat.PropertyCompat.PROPERTY_ANIM_LEVEL
import com.filemanager.common.compat.PropertyCompat.PROPERTY_CPU_INFO
import com.filemanager.common.compat.PropertyCompat.PROPERTY_LIGHT_OS
import com.filemanager.common.compat.PropertyCompat.PROPERTY_LOG_ENABLE
import com.filemanager.common.compat.PropertyCompat.PROPERTY_LOG_PANIC
import com.filemanager.common.compat.PropertyCompat.PROPERTY_MODEL
import com.filemanager.common.compat.PropertyCompat.PROPERTY_OTA_VERSION
import com.filemanager.common.compat.PropertyCompat.PROPERTY_PHONE_MARK_REGION
import com.filemanager.common.compat.PropertyCompat.PROPERTY_ROM_SIZE
import com.filemanager.common.compat.PropertyCompat.PROPERTY_RSA
import com.filemanager.common.compat.PropertyCompat.PROPERTY_SYSTEM_REGION
import com.filemanager.common.compat.PropertyCompat.sColorOSVersionCode
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.Log
import com.oplus.compat.os.OplusSystemPropertiesNative
import com.oplus.compat.os.SystemPropertiesNative
import com.oplus.wrapper.os.SystemProperties
import org.json.JSONObject
import java.util.Objects

object PropertyCompatR : PropertyCompat.IPropertyInterface {
    private const val TAG = "OPlusPropertyCompatR"
    private const val METHOD_CALL_SUPPORT_LOCAL_UPDATE = "callSupportLocalUpdate"
    private const val SUPPORT_LOCAL_UPDATE = "supportLocalUpdate"
    private const val IS_AB_UPDATE_DEVICE = "isABUpdateDevice"
    private const val OTA_BUNDLE_DATA = "data"

    // This region is changed with the system Settings
    private const val PROPERTY_OPLUS_SYSTEM_REGION = "persist.sys.oplus.region"

    // This region is the preset region, and not changed with the system settings
    private const val PROPERTY_OPLUS_PHONE_MARK_REGION = "ro.vendor.oplus.regionmark"

    override fun getCPUInfo() = getProperty(PROPERTY_CPU_INFO)

    override fun getModel() = getProperty(PROPERTY_MODEL)

    override fun getOTAInfo() = getProperty(PROPERTY_OTA_VERSION)

    override fun getSystemRegion(): String {
        val default = "CN"
        var firstUseFeature = PROPERTY_OPLUS_SYSTEM_REGION
        var secondUseFeature = PROPERTY_SYSTEM_REGION
        if (sColorOSVersionCode < OplusBuildCompat.OS_11_3) {
            firstUseFeature = PROPERTY_SYSTEM_REGION
            secondUseFeature = PROPERTY_OPLUS_SYSTEM_REGION
        }
        var result = ""
        try {
            result = getOplusProperty(firstUseFeature, result, true)
        } catch (e: Throwable) {
            Log.w(TAG, "getSystemRegion failed: ${e.message}")
        }
        if (result.isEmpty()) {
            try {
                Log.i(TAG, "getOplusProperty empty, try getProperty $firstUseFeature")
                result = getProperty(firstUseFeature, result, false)
            } catch (e: Throwable) {
                Log.w(TAG, "getSystemRegion getProperty failed: ${e.message}")
            }
        }
        if (result.isEmpty()) {
            try {
                Log.i(TAG, "getSystemRegion : $firstUseFeature is empty, try $secondUseFeature")
                result = getOplusProperty(secondUseFeature, result, true)
            } catch (e: Throwable) {
                Log.w(TAG, "getSystemRegion getOplusProperty failed: ${e.message}")
            }
        }
        if (result.isEmpty()) {
            Log.i(TAG, "getOplusProperty is empty, try getProperty $secondUseFeature")
            result = getProperty(secondUseFeature, default, false)
        }
        return result
    }

    override fun getPhoneMarkRegion(): String {
        var result = try {
            getProperty(PROPERTY_PHONE_MARK_REGION, throwExp = true)
        } catch (e: Throwable) {
            Log.w(TAG, "getPhoneMarkRegion failed: ${e.message}")
            ""
        }
        if (result.isEmpty()) {
            Log.i(TAG,
                    "getPhoneMarkRegion : $PROPERTY_PHONE_MARK_REGION is empty, try $PROPERTY_OPLUS_PHONE_MARK_REGION")
            result = getProperty(PROPERTY_OPLUS_PHONE_MARK_REGION, throwExp = false)
        }
        if (result.isEmpty()) {
            result = PropertyCompat.sSystemRegion
            Log.d(TAG, "getPhoneMarkRegion empty, use system region=$result")
        }
        return result
    }

    override fun getColorOSVersion() = OplusBuildCompat.getVersionRelease() ?: "V3.0.0"

    override fun getAndroidVersion() = getProperty(PropertyCompat.PROPERTY_ANDROID_VERSION)

    override fun isLogEnable(): Boolean {
        val qeOff = getProperty(PROPERTY_LOG_PANIC)
        val qeOffMtk = getProperty(PROPERTY_LOG_ENABLE)
        return "true".equals(qeOff, ignoreCase = true) || "true".equals(qeOffMtk, ignoreCase = true)
    }

    override fun getRomSize() = getProperty(PROPERTY_ROM_SIZE)

    override fun getOplusSeries() = getProperty(FEATURE_REALME_MACHINE_MODEL)

    // Not used in R
    override fun isSupportSDCard() = true
    override fun isGDPR(): Boolean {
        val property = getProperty(FEATURE_GDPR)
        Log.d(TAG, "property: $property")
        return "GDPR".equals(property, ignoreCase = true)
    }

    override fun isNeedUpdateByOta(): Boolean {
        var resultBundle: Bundle? = null
        return try {
            MyApplication.sAppContext.contentResolver
                    .acquireContentProviderClient(KtConstants.OTA_URI)?.use {
                        resultBundle = it.call(METHOD_CALL_SUPPORT_LOCAL_UPDATE, null, null)
                    } ?: MyApplication.sAppContext.contentResolver
                    .acquireContentProviderClient(KtConstants.OTA_OPLUS_URI)?.use {
                        resultBundle = it.call(METHOD_CALL_SUPPORT_LOCAL_UPDATE, null, null)
                    }
            val data = JSONObject(resultBundle?.getString(OTA_BUNDLE_DATA) ?: "")
            val isSupportLocalUpdate = data.getBoolean(SUPPORT_LOCAL_UPDATE)
            val isABUpdateDevice = data.getBoolean(IS_AB_UPDATE_DEVICE)
            Log.d(TAG, "isNeedUpdateByOta: $isSupportLocalUpdate, $isABUpdateDevice")
            isSupportLocalUpdate && !isABUpdateDevice
        } catch (e: Exception) {
            Log.e(TAG, "isNeedUpdateByOta: exception $e")
            false
        }
    }

    override fun getRSAProperty(): String {
        var property = getProperty(PROPERTY_RSA, "")
        if (Objects.equals(property, "")) {
            property = getOplusProperty(PROPERTY_RSA, "")
        }
        return property
    }

    override fun isLightOS(): Boolean {
        val property = getProperty(PROPERTY_LIGHT_OS)
        return "true".equals(property, ignoreCase = true)
    }

    override fun getAnimLevel(): String {
        return getProperty(PROPERTY_ANIM_LEVEL)
    }

    override fun getChannelInfo(): String {
        return getProperty(PropertyCompat.PROPERTY_CHANNEL)
    }

    override fun getChannelCountry(): String {
        return getProperty(PropertyCompat.PROPERTY_CHANNEL_COUNTRY)
    }

    override fun getPipelineCarrier(): String {
        return getProperty(PropertyCompat.PROPERTY_PIPELINE_CARRIER)
    }

    override fun getPipelineRegion(): String {
        return getProperty(PropertyCompat.PROPERTY_PIPELINE_REGION)
    }

    @VisibleForTesting
    fun getOplusProperty(name: String, default: String = "", throwExp: Boolean = false): String {
        return commonGetProperty(name, default, throwExp) { key, def ->
            CompatUtils.compactApi(CompatUtils.OS_14, CompatUtils.OS_14_SUB_V, {
                OplusSystemProperties.get(key, def)
            }, {
                OplusSystemPropertiesNative.get(key, def) ?: def
            })
        }
    }

    @VisibleForTesting
    fun getProperty(name: String, default: String = "", throwExp: Boolean = false): String {
        return commonGetProperty(name, default, throwExp) { key, def ->
            CompatUtils.compactApi(CompatUtils.OS_14, CompatUtils.OS_14_SUB_V, {
                SystemProperties.get(key, def)
            }, {
                SystemPropertiesNative.get(key, def) ?: def
            })
        }
    }

    private fun <T> commonGetProperty(
            name: String, default: T, throwExp: Boolean = false,
            method: (key: String, defValue: T) -> T
    ): T {
        return if (throwExp) {
            method.invoke(name, default)
        } else {
            try {
                method.invoke(name, default)
            } catch (e: Throwable) {
                Log.w(TAG, "getProperty failed: $name, ${e.message}")
                default
            }
        }.also {
            Log.d(TAG, "getProperty done: n=$name, v=$it, default=$default")
        }
    }
}