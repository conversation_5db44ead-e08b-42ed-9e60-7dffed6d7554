/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2025/1/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller

import android.app.Activity
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.Lifecycle
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortPopup
import com.filemanager.common.utils.KtViewUtils

class AlbumSortPopupController : BaseLifeController {
    companion object {
        private const val TAG = "SortPopupController"
    }

    @VisibleForTesting
    var mSortPopUp: SortPopup? = null

    constructor(lifecycle: Lifecycle) {
        lifecycle.addObserver(this)
    }

    fun showSortPopUp(
        mActivity: Activity,
        categoryType: String,
        selectItemListener: SelectItemListener
    ) {
        val width = KtViewUtils.getWindowSize(mActivity).x
        val bundle = Bundle()
        bundle.putString(SortModeUtils.RECORD_CATEGORY_MODE, categoryType)
        bundle.putInt(SortModeUtils.RECORD_DEFAULT_OFFSET, width)
        if (mSortPopUp == null) {
            mSortPopUp = SortPopup(mActivity)
        }
        mSortPopUp?.initDate(bundle)
        mSortPopUp?.setOnPathClickListener(selectItemListener)
    }

    fun clickItemHandle(position: Int) {
        mSortPopUp?.onItemClickHandle(position)
    }

    fun getDefaultItem(): Int {
        return mSortPopUp?.defaultItem ?: 0
    }

    fun ismIsDesc(): Boolean {
        return mSortPopUp?.isDesc ?: true
    }

    override fun onDestroy() {
        mSortPopUp = null
    }
}
