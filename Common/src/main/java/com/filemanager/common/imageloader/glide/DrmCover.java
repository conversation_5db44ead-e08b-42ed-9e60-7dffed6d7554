package com.filemanager.common.imageloader.glide;

/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.module.imageloader.glide
 * * Version     : 1.0
 * * Date        : 2020/5/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
public class DrmCover {
    private String mPath;

    public DrmCover(String path) {
        mPath = path;
    }

    public String getPath() {
        return mPath;
    }

    @Override
    public boolean equals(Object o) {
        if (o instanceof DrmCover) {
            DrmCover drmCover = (DrmCover) o;
            return drmCover.getPath().equals(mPath);
        }
        return false;
    }

    @Override
    public int hashCode() {
        return mPath.hashCode();
    }
}
