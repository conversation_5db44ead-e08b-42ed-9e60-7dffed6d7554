/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : OpenAnySwitchManager
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/1/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/1/17       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.OplusPackageManager
import android.content.pm.PackageManager
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants

object OpenAnyManager {

    private const val TAG = "OpenAnyManager"

    private val isRemovableApp by lazy {
        if (!SdkUtils.isAtLeastS()) {
            Log.w(TAG, "removable app is under Android S")
            return@lazy false
        }
        val removableAppInfo = OplusPackageManager.getOplusPackageManager(MyApplication.sAppContext)
            .getRemovableAppInfo(Constants.OPEN_ANY_PKG_NAME)?.apply {
                Log.i(TAG, "removable app: $packageName $versionName")
            }
        removableAppInfo != null
    }

    /**
     * 当前设备是否包含文件随心开独立apk
     *   -若随心开apk已安装，需禁用文管随心开组件
     *   -若随心开apk是可卸载找回的，需禁用文管随心开组件
     */
    fun hasIntegrateQuickPreview(): Boolean {
        var result = false
        if (AppUtils.isAppInstalledByPkgName(MyApplication.sAppContext, Constants.OPEN_ANY_PKG_NAME)) {
            Log.i(TAG, "Independent app has installed")
            result = true
        }
        if (isRemovableApp) {
            Log.i(TAG, "Independent app is removable and return.")
            result = true
        }
        Log.d(TAG, "hasIndependentApk result:$result")
        return result
    }

    /**
     * 判断随心开是否安装 && 是否有永中创建文件功能
     */
    fun hasCreateFileFunctionInOpenAnyApp(): Boolean {
        // 1.先判断是否安装
        val installed =
            AppUtils.isAppInstalledByPkgName(MyApplication.sAppContext, Constants.OPEN_ANY_PKG_NAME)
        Log.d(TAG, "hasCreateFileFunctionInOpenAnyApp -> installed $installed")
        if (!installed) {
            return false
        }
        // 2.判断是否有对应action
        val newIntent: Intent = Intent()
        newIntent.`package` = Constants.OPEN_ANY_PKG_NAME
        // 专门功能的action
        newIntent.setAction(Constants.START_FILE_CREATE_BY_YOZO_SOFT)
        try {
            val resultList = MyApplication.sAppContext.packageManager.queryIntentActivities(
                newIntent,
                PackageManager.MATCH_DEFAULT_ONLY
            )
            if (resultList.isEmpty()) {
                Log.d(TAG, "hasCreateFileFunctionInOpenAnyApp -> resultList isEmpty")
                return false
            }
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, e.message)
            return false
        }
        return true
    }
}