/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File: FeatureCompat.kt
 * * Description:Query and save the value of common features and query whether the feature exists
 * * Version:1.0
 * * Date :2020/8/17
 * * Author:W9060445
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * *  W9060445     ,  2024/8/23,        v1.0,           Create
 ****************************************************************/
package com.filemanager.common.compat

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.UserManagerUtils

object PrivacyPasswordSettingCompat {
    private const val TAG = "PrivacyPasswordSettingCompat"
    private const val ACTION_SETTINGS_CONFIRM = "oplus.intent.action.settings.PRIVACY_PWD_CONFIRM"
    private const val ACTION_SETTINGS_CHOOSE = "oplus.intent.action.settings.PRIVACY_PWD_CHOOSE"
    private const val ACTION_SETTINGS_PRIVACY = "oplus.intent.action.settings.PRIVACY_PWD_SETTINGS"
    private const val PACKAGE_NAME_SETTINGS = "com.android.settings"
    private const val EXTRA_CONFIRM_PASSWORD_TYPE = "confirm_password_type"
    private const val EXTRA_CALLING_APP_PKG = "calling_app_pkg"
    private const val SETTINGS_TYPE = 3
    private const val EXTRA_PASSWORD_TYPE_SELECTED = "password_type_selected"
    private const val EXTRA_EVENT_CODE = "extra_event_code"
    private const val EVENT_CODE_PASSWORD_CLOSE = 30
    private const val EXTRA_CODE_PASSWORD_OPEN = 31
    private const val RECENT_DELETE_KEY = "file_manager_recently_deleted_privacy_verify"
    private const val RECENT_DELETE_ENABLED = 1
    private const val RECENT_DELETE_DISABLED = 0
    const val SETTINGS_CHOICE_NUM = 0x2
    const val SETTINGS_CHOICE_PIC = 0x5
    const val SETTINGS_CHOICE_MIX = 0x4

    /**
     * 最近删除是否支持隐私密码
     */
    val isSupportPrivacyPWD by lazy {
        isSettingPrivacySupport && UserManagerUtils.checkIsSystemUser(appContext)
    }

    /**
     * 判断设置app 是否支持最近删除隐私密码开关
     */
    private val isSettingPrivacySupport: Boolean by lazy {
        var privacyPasswordProtectionV2 = false
        kotlin.runCatching {
            val applicationInfo = appContext.packageManager.getApplicationInfo(
                KtConstants.SETTING_PACKAGE,
                PackageManager.GET_META_DATA
            )
            privacyPasswordProtectionV2 =
                applicationInfo.metaData.getBoolean("privacy_password_protection_v2")
            Log.d(TAG, "privacy_password_protection_v2 = $privacyPasswordProtectionV2")
        }.onFailure {
            Log.e(TAG, "isSettingPrivacySupport it = ${it.message}")
        }
        privacyPasswordProtectionV2
    }

    @VisibleForTesting
    @JvmStatic
    fun jumpPrivacySettingPasswordChoose(activity: Activity, type: Int) {
        Log.d(TAG, "jumpPrivacySettingPasswordChoose type $type")
        val intent = Intent(ACTION_SETTINGS_CHOOSE)
        intent.putExtra(EXTRA_PASSWORD_TYPE_SELECTED, type)
        intent.putExtra(EXTRA_CALLING_APP_PKG, activity.packageName)
        intent.putExtra(EXTRA_EVENT_CODE, EXTRA_CODE_PASSWORD_OPEN)
        intent.setPackage(PACKAGE_NAME_SETTINGS)
        runCatching {
            activity.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpPrivacySettingPasswordChoose -> error cause ${it.message}")
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun jumpPrivacySettingPassword(activity: Activity) {
        Log.d(TAG, "jumpPrivacySettingPassword")
        val intent = Intent(ACTION_SETTINGS_CONFIRM)
        intent.putExtra(EXTRA_CONFIRM_PASSWORD_TYPE, SETTINGS_TYPE)
        intent.putExtra(EXTRA_CALLING_APP_PKG, activity.packageName)
        intent.setPackage(PACKAGE_NAME_SETTINGS)
        runCatching {
            activity.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpPrivacySettingPassword -> error cause ${it.message}")
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun jumpPrivacySettingPrivacy(activity: Activity) {
        Log.d(TAG, "jumpPrivacySettingPrivacy")
        val intent = Intent(ACTION_SETTINGS_PRIVACY)
        intent.setPackage(PACKAGE_NAME_SETTINGS)
        intent.putExtra(EXTRA_EVENT_CODE, EVENT_CODE_PASSWORD_CLOSE)
        runCatching {
            activity.startActivity(intent)
        }.onFailure {
            Log.e(TAG, "jumpPrivacySettingPrivacy -> error cause ${it.message}")
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun privacySettingPasswordIntent(activity: Activity): Intent {
        Log.d(TAG, "privacySettingPasswordIntent")
        val intent = Intent(ACTION_SETTINGS_CONFIRM)
        intent.putExtra(EXTRA_CONFIRM_PASSWORD_TYPE, SETTINGS_TYPE)
        intent.putExtra(EXTRA_CALLING_APP_PKG, activity.packageName)
        intent.setPackage(PACKAGE_NAME_SETTINGS)
        return intent
    }

    @JvmStatic
    fun checkPrivacySettingPassword(context: Context?): Boolean {
        if (!isSupportPrivacyPWD) {
            Log.w(TAG, "checkPrivacySettingPassword os not support!!!")
            return false
        }
        var isSecure = false
        if (context != null) {
            val cr = context.contentResolver
            val uri = Uri.withAppendedPath(
                Uri.parse("content://oplus.provider.settings.PrivacyStateProvider"),
                "state_result"
            )
            runCatching {
                cr.query(uri, null, null, null, null).use { cursor ->
                    if (cursor != null && cursor.moveToNext()) {
                        isSecure = cursor.getInt(0) > 0
                        cursor.close()
                    }
                }
            }.onFailure {
                Log.e(TAG, "Error when check pp." + it.message)
            }
            Log.d(TAG, "Is the pric enable: $isSecure")
            if (isSecure) {
                return true
            }

            // below os11.3
            val legacyUri = Uri.withAppendedPath(
                Uri.parse("content://com.oppo.settings.privacy.PrivacyStateProvider"),
                "state_result"
            )
            runCatching {
                cr.query(legacyUri, null, null, null, null).use { cursor ->
                    if (cursor != null && cursor.moveToNext()) {
                        isSecure = cursor.getInt(0) > 0
                        cursor.close()
                    }
                }
            }.onFailure {
                Log.e(TAG, "Error when check pp." + it.message)
            }
            Log.d(TAG, "Is the legacy pric enable: $isSecure")
        }
        return isSecure
    }

    /**
     * 设置 -> 安全和隐私 -> 更多安全和隐私设置 -> 隐私密码 -> 最近删除验证-文件管理
     * */
    fun isRecentlyDeletedSwitchOpen(context: Context): Boolean {
        return (Settings.Secure.getInt(context.contentResolver, RECENT_DELETE_KEY, RECENT_DELETE_DISABLED)) == RECENT_DELETE_ENABLED
    }
}