/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: SideNavigationWithGridLayoutAnimationController
 ** Description: Controll the grid layout animation when side navigation open or close
 ** Version: 1.0
 ** Date : 2024/11/14
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.animation

import android.os.Looper
import android.view.View
import android.view.ViewGroup
import androidx.core.view.ViewCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.sidenavigation.COUISideNavigationBar
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseItemDecoration
import com.filemanager.common.base.BaseRecentItemDecoration
import com.filemanager.common.constants.Constants
import com.filemanager.common.decoration.ItemDecorationFactory
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.view.FileManagerRecyclerView

class SideNavigationWithGridLayoutAnimationController {

    companion object {
        private const val TAG = "SideNavigationWithGridLayoutAnimationController"
    }

    private var animator: SideNavigationWithGridLayoutAnimator? = null
    private var recyclerView: FileManagerRecyclerView? = null
    private var oldAnimator: RecyclerView.ItemAnimator? = null
    private var isOpenSideNavigation = false
    private var isCloseSideNavigation = false
    private var sideNavigationContainer: COUISideNavigationBar? = null
    private var sideNavigationBarListener: COUISideNavigationBar.COUISideNavigationBarListener? = null
    private var currentRecyclerViewPaddingLeft: Int = 0
    private var currentRecyclerViewPaddingRight: Int = 0
    private var isDoingAnimation = false

    constructor(recyclerView: FileManagerRecyclerView, sideNavigationContainer: COUISideNavigationBar) {
        this.recyclerView = recyclerView
        this.sideNavigationContainer = sideNavigationContainer
        init()
    }

    private fun init() {
        sideNavigationBarListener = object : COUISideNavigationBar.COUISideNavigationBarListener {
            override fun onDrawerSlide(p0: View?, p1: Float, p2: Int, p3: Int) {}

            override fun onDrawerOpened(p0: View?) {
                resetRecyclerViewWidthAndItemAnimator(true)
            }

            override fun onDrawerClosed(p0: View?) {
                resetRecyclerViewWidthAndItemAnimator(false)
            }

            override fun onDrawerStateChanged(p0: Int) {}

            override fun onDrawerWidthChanged(p0: Int, p1: Int, p2: Int) {}

            override fun onDrawerModeChanged(p0: Int) {}
        }
        sideNavigationContainer?.let {
            it.addSideNavigationBarListener(sideNavigationBarListener)
        }
        animator = SideNavigationWithGridLayoutAnimator().apply {
            this.setItemAnimMoveListener(object : SideNavigationWithGridLayoutAnimator.ItemAnimMoveListener {
                override fun onStartMoveDelay(delayMillis: Long) {
                    Log.d(TAG, "onStartMove isOpenStart $isOpenSideNavigation isCloseStart $isCloseSideNavigation delayMillis $delayMillis")
                    if (isOpenSideNavigation || isCloseSideNavigation) {
                        openOrCloseSideNavigation(isOpenSideNavigation, delayMillis)
                    }
                }
            })
        }
    }

    private fun openOrCloseSideNavigation(isOpen: Boolean, delayMillis: Long) {
        recyclerView?.postDelayed({
            Log.d(TAG, "openOrCloseSideNavigation isOpen=$isOpenSideNavigation isClose=$isCloseSideNavigation")
            if (isOpen) {
                sideNavigationContainer?.open()
                isOpenSideNavigation = false
            } else {
                sideNavigationContainer?.close()
                isCloseSideNavigation = false
            }
        }, delayMillis)
    }

    private fun resetRecyclerViewWidthAndItemAnimator(isOpen: Boolean) {
        recyclerView?.let {
            it.isOpenOrCloseSideNavigation = false
            it.layoutParams?.width = ViewGroup.LayoutParams.MATCH_PARENT
            if (isOpen) {
                it.setPadding(currentRecyclerViewPaddingLeft, it.paddingTop, currentRecyclerViewPaddingRight, it.paddingBottom)
            }
            it.requestLayout()
            recyclerView?.itemAnimator = oldAnimator
            isDoingAnimation = false
            Log.d(TAG, "reset recyclerView width and itemAnimator")
        }
    }

    fun isDoingAnimation(): Boolean {
        Log.d(TAG, "isDoingAnimation $isDoingAnimation")
        return isDoingAnimation
    }

    fun doOpenOrCloseAnim(
        isOpen: Boolean,
        windowWidth: Int,
        sideNavigationWidth: Int,
        category: Int,
        tabPosition: Int = 0
    ) {
        if (recyclerView?.isVisible == false) {
            Log.d(TAG, "recyclerView visible is false, isOpen: $isOpen")
            if (isOpen) {
                sideNavigationContainer?.open()
            } else {
                sideNavigationContainer?.close()
            }
            return
        }
        if (isDoingAnimation()) {
            Log.d(TAG, "has was doing animation")
            return
        }
        isDoingAnimation = true

        val spanCount = getGridItemCount(isOpen, windowWidth, sideNavigationWidth, category, tabPosition)
        (recyclerView?.layoutManager as? GridLayoutManager)?.spanCount = spanCount
        (recyclerView?.getItemDecorationAt(0) as? BaseItemDecoration)?.mSpanCount = spanCount
        (recyclerView?.getItemDecorationAt(0) as? BaseRecentItemDecoration)?.spanCount = spanCount
        Log.d(TAG, "doOpenOrCloseAnim spanCount $spanCount")

        this.oldAnimator = recyclerView?.itemAnimator
        recyclerView?.windowWidth = windowWidth
        recyclerView?.itemAnimator = animator
        recyclerView?.adapter?.apply {
            checkComputingAndExecute {
                recyclerView?.hasCheckAnim = false
                currentRecyclerViewPaddingLeft = recyclerView?.paddingLeft ?: 0
                currentRecyclerViewPaddingRight = recyclerView?.paddingRight ?: 0
                notifyDataSetChanged()
                if (isOpen) {
                    isOpenSideNavigation = true
                    isCloseSideNavigation = false
                    recyclerView?.let {
                        val isRTL = ViewCompat.getLayoutDirection(it) == ViewCompat.LAYOUT_DIRECTION_RTL
                        if (isRTL) {
                            it.setPadding(sideNavigationWidth, it.paddingTop, it.paddingRight, it.paddingBottom)
                        } else {
                            it.setPadding(it.paddingLeft, it.paddingTop, sideNavigationWidth, it.paddingBottom)
                        }
                    }
                } else {
                    isCloseSideNavigation = true
                    isOpenSideNavigation = false
                }
                recyclerView?.layoutParams?.width = windowWidth
                recyclerView?.isOpenOrCloseSideNavigation = true
            }
        }
    }

    private fun getGridItemCount(
        isOpen: Boolean,
        windowWidth: Int,
        sideNavigationWidth: Int,
        category: Int,
        tabPosition: Int = 0
    ): Int {
        val gridWidthDp = if (isOpen) {
            ViewHelper.px2dip(MyApplication.appContext, windowWidth - sideNavigationWidth)
        } else {
            ViewHelper.px2dip(MyApplication.appContext, windowWidth)
        }
        return when (category) {
            ItemDecorationFactory.GRID_ITEM_DECORATION_RECENT -> return ItemDecorationFactory.getRecentGridCount(gridWidthDp)
            ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM_SET -> ItemDecorationFactory.getAlumSetColumnByScreenWidth(gridWidthDp)
            ItemDecorationFactory.GRID_ITEM_DECORATION_ALBUM -> ItemDecorationFactory.getAlumOrVideoColumnByScreenWidth(gridWidthDp)
            ItemDecorationFactory.GRID_ITEM_DECORATION_AUDIO  -> {
                return when (tabPosition) {
                    CategoryHelper.CATEGORY_VIDEO -> ItemDecorationFactory.getAlumOrVideoColumnByScreenWidth(gridWidthDp)
                    else -> ItemDecorationFactory.getDefaultColumnByScreenWidth(gridWidthDp)
                }
            }
            ItemDecorationFactory.GRID_ITEM_DECORATION_SUPER -> {
                return when (tabPosition) {
                    Constants.TAB_IMAGE, Constants.TAB_VIDEO -> ItemDecorationFactory.getAlumOrVideoColumnByScreenWidth(gridWidthDp)
                    else -> ItemDecorationFactory.getDefaultColumnByScreenWidth(gridWidthDp)
                }
            }
            else -> ItemDecorationFactory.getDefaultColumnByScreenWidth(gridWidthDp)
        }
    }

    private fun checkComputingAndExecute(method: () -> Unit) {
        if (recyclerView?.isComputingLayout == true || Looper.myLooper() != Looper.getMainLooper()) {
            recyclerView?.post {
                try {
                    method.invoke()
                } catch (e: IllegalStateException) {
                    Log.e(TAG, "checkComputingAndExecute exception: ${e.message}")
                }
            }
        } else {
            try {
                method.invoke()
            } catch (e: IllegalStateException) {
                Log.e(TAG, "checkComputingAndExecute UI-Thread exception: ${e.message}")
            }
        }
    }

    fun destroy() {
        Log.d(TAG, "destroy")
        animator?.setItemAnimMoveListener(null)
        recyclerView = null
        oldAnimator = null
        sideNavigationContainer?.removeSideNavigationBarListener(sideNavigationBarListener)
        sideNavigationContainer = null
        sideNavigationBarListener = null
    }
}