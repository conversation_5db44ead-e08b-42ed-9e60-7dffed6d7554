/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.content.res.AssetManager;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.preference.PreferenceManager;
import android.text.TextUtils;

import com.filemanager.common.R;
import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.helper.VolumeEnvironment;

import org.apache.commons.io.FilenameUtils;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class BlacklistParser {
    public static final String TAG = "BlacklistParser";
    public static final int FILTER_NO_SUFFIX_STATE = 1;
    public static int sFilterNoSuffixFile;
    private static final String DATABASE_DIR = "databases";
    private static final String IGNORED_ID = "_id";
    private static final String IGNORED_PATH = "ignored_path";
    private static final String IGNORED_CATEGORY_TYPE = "category_type";
    private static final String IGNORED_PATH_TABLE_NAME = "ignored_category_path";
    private static final String BLACKLIST_VERSION = "blacklist_version";
    private static final String BLACKLIST_NAME = "filemanager-blacklist";
    private static final String BLACKLIST_NEARME_URI = "content://com.nearme.romupdate.provider.db/update_list";
    private static final String DEFAULT_MUSIC_BLACKLIST = "default_music_blacklist";
    private static final String DEFAULT_VIDEOS_BLACKLIST = "default_videos_blacklist";
    private static final String DEFAULT_PHOTO_BLACKLIST = "default_photo_blacklist";
    private static final String DEFAULT_DOC_BLACKLIST = "default_doc_blacklist";
    private static final String DEFAULT_APKS_BLACKLIST = "default_apks_blacklist";
    private static final String DEFAULT_ZIP_BLACKLIST = "default_zip_blacklist";
    private static final String DEFAULT_SEVEN_LIST = "default_seven_list";
    private static final String DEFAULT_EIGHT_LIST = "default_eight_list";
    private static final String DEFAULT_NINE_LIST = "default_nine_list";
    private static final String DEFAULT_TEN_LIST = "default_ten_list";
    private static final String DEFAULT_ELEVEN_LIST = "default_eleven_list";
    private static final String DEFAULT_UNKNOWN_FILE_LIST = "default_unknown_file";
    private static final String DEFAULT_UNKNOWN_FILE_EXP_LIST = "default_unknown_file_exp";
    private static final String EXTENSION_EIGHT_LIST = "extension_eight_list";
    private static final String EXTENSION_SEVEN_LIST = "extension_seven_list";
    private static final String FILTER_UNKNOWN_NO_SUFFIX = "filter_unknown_no_suffix";
    private static final String FILTER_RECENT_FILES = "filter_recent_files";
    private static final String NONE = "none";

    private static final String DEFAULT_FILTER_RECENT_FILES = "db;dat;txt;log;xml;ind;prop";

    private static final int FILTER_LENTH = 3;

    private static String[] sMusicList;
    private static String[] sVideosList;
    private static String[] sPhotoList;
    private static String[] sDocList;
    private static String[] sApksList;
    private static String[] sZipList;
    private static String[] sDocFilterTypes;

    private static String[] sUnknownFileList;
    private static String[] sExtensionEightList;
    private static String[] sExtensionSevenList;
    private static String[] sRecentFilterFilelist;

    private static int sMusicFilterSize;
    private static int sVideosFilterSize;
    private static int sPhotoFilterSize;
    private static int sDocFilterSize;
    private static int sApksFilterSize;
    private static int sZipFilterSize;

    public static void initializeBlacklist(Context context) {
        String preference = PreferenceManager.getDefaultSharedPreferencesName(context);
        final int version = PreferencesUtils.getInt(preference, BLACKLIST_VERSION, 0);
        int configVersion = version;
        String[] projection = new String[]{"version", "xml"};
        String whitelistStr = null;
        ContentResolver resolver = context.getContentResolver();
        Cursor cursor = null;

        try {
            cursor = resolver.query(Uri.parse(BLACKLIST_NEARME_URI), projection,
                    "filtername=" + "\"" + BLACKLIST_NAME + "\"", null, null);
            if ((cursor != null) && cursor.moveToFirst()) {
                whitelistStr = cursor.getString(1);
                configVersion = cursor.getInt(0);
            }
        } catch (Exception e) {
            // do nothing
        } finally {
            if (null != cursor) {
                cursor.close();
                cursor = null;
            }
        }
        Log.d(TAG, "configVer = " + configVersion + ", ver = " + version);

        final int newVersion = 2017082820;
        if (version < configVersion) {
            if (saveBlackListToDB(context, whitelistStr)) {
                PreferencesUtils.put(preference, BLACKLIST_VERSION, configVersion);
            }
            saveCategoryPathsToPerf(context);
        } else if (version < newVersion) {
            saveBlackListToDB(context, null);
            saveCategoryPathsToPerf(context);
        }
    }

    private static boolean saveBlackListToDB(final Context context, String xml) {
        try {
            if (null != xml) {
                Log.d(TAG, "saveBlackListToDB xml != null");
                parserBlackListXml(xml, null);
            } else {
                Log.d(TAG, "null == xml");
                AssetManager assetManager = context.getAssets();
                InputStream in = null;
                in = assetManager.open("filemanager-blacklist.xml");
                parserBlackListXml(null, in);
            }
        } catch (Exception e) {
            Log.e(TAG, "parser BlackList Xml fail e = " + e);
        }
        operateIgnoredDatabase(context);
        return true;
    }


    private static void saveCategoryPathsToPerf(Context context) {
        Set<String> sevenSet = new HashSet<String>();
        Set<String> eightSet = new HashSet<String>();
        Set<String> nineSet = new HashSet<String>();
        Set<String> tenSet = new HashSet<String>();
        Set<String> elevenSet = new HashSet<String>();
        Set<String> unknownFileSet = new HashSet<String>();
        Set<String> extensionEightSet = new HashSet<String>();
        Set<String> extensionSevenSet = new HashSet<String>();
        Set<String> filterRecentFileSet = new HashSet<>();

        int len = 0;

        if ((sUnknownFileList != null) && ((len = sUnknownFileList.length) > 0)) {
            for (int i = 0; i < len; i++) {
                unknownFileSet.add(sUnknownFileList[i]);
            }
        }
        if ((sExtensionEightList != null) && ((len = sExtensionEightList.length) > 0)) {
            for (int i = 0; i < len; i++) {
                extensionEightSet.add(sExtensionEightList[i]);
            }
        }
        if ((sExtensionSevenList != null) && ((len = sExtensionSevenList.length) > 0)) {
            for (int i = 0; i < len; i++) {
                extensionSevenSet.add(sExtensionSevenList[i]);
            }
        }
        if ((sRecentFilterFilelist != null) && ((len = sRecentFilterFilelist.length) > 0)) {
            for (int i = 0; i < len; i++) {
                filterRecentFileSet.add(sRecentFilterFilelist[i]);
            }
        }

        String preference = PreferenceManager.getDefaultSharedPreferencesName(context);
        Map<String, Set<String>> map = new HashMap<>();
        map.put(DEFAULT_SEVEN_LIST, sevenSet);
        map.put(DEFAULT_EIGHT_LIST, eightSet);
        map.put(DEFAULT_NINE_LIST, nineSet);
        map.put(DEFAULT_TEN_LIST, tenSet);
        map.put(DEFAULT_ELEVEN_LIST, elevenSet);
        map.put(DEFAULT_UNKNOWN_FILE_LIST, unknownFileSet);
        map.put(EXTENSION_EIGHT_LIST, extensionEightSet);
        map.put(EXTENSION_SEVEN_LIST, extensionSevenSet);
        map.put(FILTER_RECENT_FILES, filterRecentFileSet);
        PreferencesUtils.putAll(preference, map);
    }

    public static ArrayList<String> getOneAppPaths(String[] paths) {
        ArrayList<String> appPaths = new ArrayList();
        if (paths != null) {
            Collections.addAll(appPaths, paths);
        }
        return appPaths;
    }

    public static String[] getsRecentFilterFileType(final Context context) {
        if ((sRecentFilterFilelist == null) || (sRecentFilterFilelist.length == 0)) {
            if (context != null) {
                final String prefs = PreferenceManager.getDefaultSharedPreferencesName(context);
                Set<String> unknownFileSet = PreferencesUtils.getStringSet(prefs, FILTER_RECENT_FILES, null);
                if ((unknownFileSet != null) && (unknownFileSet.size() > 0)) {
                    sRecentFilterFilelist = unknownFileSet.toArray(new String[]{});
                }
            } else {
                Log.e(TAG, "context is null when getRecentFilterFileType");
            }
        }
        String[] list = null;
        if (sRecentFilterFilelist != null) {
            list = sRecentFilterFilelist.clone();
        }
        return list;
    }

    public static String[] getUnknownFiles(final Context context) {
        if ((sUnknownFileList == null) || (sUnknownFileList.length == 0)) {
            if (context != null) {
                final String prefs = PreferenceManager.getDefaultSharedPreferencesName(context);
                Set<String> unknownFileSet = PreferencesUtils.getStringSet(prefs, DEFAULT_UNKNOWN_FILE_LIST, null);
                if ((unknownFileSet != null) && (unknownFileSet.size() > 0)) {
                    sUnknownFileList = unknownFileSet.toArray(new String[]{});
                }
            } else {
                Log.e(TAG, "context is null when getEightPaths");
            }
        }
        String[] list = null;
        if (sUnknownFileList != null) {
            list = sUnknownFileList.clone();
        }
        return list;
    }

    private static void operateIgnoredDatabase(Context context) {
        SQLiteDatabase orignalDB = null;
        String dbName = context.getResources().getString(R.string.resourceFile);
        String dbPath = VolumeEnvironment.getDataDirPath(context, DATABASE_DIR, dbName);
        try {
            if (new File(dbPath).length() <= 0) {
                copyAssetsDbFileToDataDb(context, dbPath, dbName);
            }
            if (new File(dbPath).length() <= 0) {
                Log.e(TAG, "original db not exist!");
                return;
            }
            orignalDB = SQLiteDatabase.openDatabase(dbPath, null, SQLiteDatabase.OPEN_READWRITE);
            if ((sMusicList != null) && (sMusicList.length > 0) || (sVideosList != null) && (sVideosList.length > 0)
                    || (sPhotoList != null) && (sPhotoList.length > 0) || (sDocList != null) && (sDocList.length > 0)
                    || (sApksList != null) && (sApksList.length > 0) || (sZipList != null) && (sZipList.length > 0)) {
                deleteIgnoredDatabase(orignalDB);
                if ((null != sMusicList) && (sMusicList.length > 0)) {
                    insertIgnoredDatabase(sMusicList, CategoryHelper.CATEGORY_AUDIO, orignalDB);
                }
                if ((null != sVideosList) && (sVideosList.length > 0)) {
                    insertIgnoredDatabase(sVideosList, CategoryHelper.CATEGORY_VIDEO, orignalDB);
                }
                if ((null != sPhotoList) && (sPhotoList.length > 0)) {
                    insertIgnoredDatabase(sPhotoList, CategoryHelper.CATEGORY_IMAGE, orignalDB);
                }
                if ((null != sDocList) && (sDocList.length > 0)) {
                    insertIgnoredDatabase(sDocList, CategoryHelper.CATEGORY_DOC, orignalDB);
                }
                if ((null != sApksList) && (sApksList.length > 0)) {
                    insertIgnoredDatabase(sApksList, CategoryHelper.CATEGORY_APK, orignalDB);
                }
                if ((null != sZipList) && (sZipList.length > 0)) {
                    insertIgnoredDatabase(sZipList, CategoryHelper.CATEGORY_COMPRESS, orignalDB);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "operateIgnoredDatabase e = " + e);
        } finally {
            if (null != orignalDB) {
                orignalDB.close();
            }
        }
    }

    private static void copyAssetsDbFileToDataDb(Context context, String path, String dbName) {
        Log.d(TAG, "copyAssetsDbFileToDataDb " + dbName + " to " + path);
        InputStream in = null;
        FileOutputStream out = null;
        path = path.replace(File.separator + dbName, "");
        File file = new File(path);
        if (!file.exists()) {
            if (!file.mkdirs()) {
                Log.e(TAG, "copyAssetsDbFileToDataDb: cannot create directory.");
                return;
            }
        }
        File outfile = new File(file, dbName);
        try {
            in = context.getAssets().open(dbName);
            out = new FileOutputStream(outfile);
            // Transfer bytes from inputStream to fileOutputStream
            byte[] buffer = new byte[1024];
            int byteRead = 0;
            while (-1 != (byteRead = in.read(buffer))) {
                out.write(buffer, 0, byteRead);
            }
            out.flush();
        } catch (Exception e) {
            Log.w(TAG, e.getMessage());
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e1) {
                    // TODO Auto-generated catch block
                    Log.w(TAG, e1.getMessage());
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e1) {
                    // TODO Auto-generated catch block
                    Log.w(TAG, e1.getMessage());
                }
            }
        }
    }

    private static void deleteIgnoredDatabase(SQLiteDatabase originalDb) {
        try {
            originalDb.delete(IGNORED_PATH_TABLE_NAME, null, null);
        } catch (Exception e) {
            Log.e(TAG, "deleteIgnoredDatabase e = " + e);
        }
    }

    private static void insertIgnoredDatabase(String[] paths, int type, SQLiteDatabase originalDb) {
        int length = paths.length;

        try {
            for (int i = 0; i < length; i++) {
                ContentValues values = new ContentValues();
                values.put(IGNORED_ID, i + 1);
                values.put(IGNORED_PATH, paths[i]);
                values.put(IGNORED_CATEGORY_TYPE, type);
                originalDb.insert(IGNORED_PATH_TABLE_NAME, null, values);
            }
        } catch (Exception e) {
            Log.e(TAG, "insertIgnoredDatabase e = " + e);
        }
    }

    private static void parserBlackListXml(String xml, InputStream in) throws XmlPullParserException, IOException {
        String typeString = null;
        ArrayList<String> musicList = new ArrayList<String>();
        ArrayList<String> videosList = new ArrayList<String>();
        ArrayList<String> photoList = new ArrayList<String>();
        ArrayList<String> docList = new ArrayList<String>();
        ArrayList<String> apksList = new ArrayList<String>();
        ArrayList<String> zipList = new ArrayList<String>();
        ArrayList<String> unknownFilelist = new ArrayList<String>();
        ArrayList<String> extensionEightList = new ArrayList<String>();
        ArrayList<String> extensionSevenList = new ArrayList<String>();

        XmlPullParser parser = XmlPullParserFactory.newInstance().newPullParser();
        final String unknownFileListTag = FeatureCompat.getSIsExpRom() ? DEFAULT_UNKNOWN_FILE_EXP_LIST : DEFAULT_UNKNOWN_FILE_LIST;

        if (!TextUtils.isEmpty(xml)) {
            parser.setInput(new StringReader(xml));
        } else if (null != in) {
            parser.setInput(in, "UTF-8");
        }
        parser.nextTag();
        int evenType = parser.getEventType();
        String addName = "more_doc_blacklist";
        while (evenType != XmlPullParser.END_DOCUMENT) {
            if (XmlPullParser.START_TAG == evenType) {
                final String tagName = parser.getName();
                if ("string-array".equals(tagName)) {
                    typeString = parser.getAttributeValue(0);
                }
                if ("integer".equals(tagName)) {
                    typeString = parser.getAttributeValue(0);
                }
                if (null != typeString) {
                    if (DEFAULT_MUSIC_BLACKLIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            if ((values != null) && values.contains(DEFAULT_MUSIC_BLACKLIST)) {
                                String[] list = values.split(":");
                                int len = list.length;
                                if (len == FILTER_LENTH) {
                                    sMusicFilterSize = Integer.parseInt(list[1]);
                                }
                            } else {
                                musicList.add(values);
                            }
                        }
                    } else if (DEFAULT_VIDEOS_BLACKLIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            if ((values != null) && values.contains(DEFAULT_VIDEOS_BLACKLIST)) {
                                String[] list = values.split(":");
                                int len = list.length;
                                if (len == FILTER_LENTH) {
                                    sVideosFilterSize = Integer.parseInt(list[1]);
                                }
                            } else {
                                videosList.add(values);
                            }
                        }
                    } else if (DEFAULT_PHOTO_BLACKLIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            if ((values != null) && values.contains(DEFAULT_PHOTO_BLACKLIST)) {
                                String[] list = values.split(":");
                                int len = list.length;
                                if (len == FILTER_LENTH) {
                                    sPhotoFilterSize = Integer.parseInt(list[1]);
                                }
                            } else {
                                photoList.add(values);
                            }
                        }
                    } else if (DEFAULT_DOC_BLACKLIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            if ((values != null) && values.contains(DEFAULT_DOC_BLACKLIST)) {
                                String[] list = values.split(":");
                                int len = list.length;
                                if (len == FILTER_LENTH) {
                                    sDocFilterSize = Integer.parseInt(list[1]);
                                    sDocFilterTypes = list[2].split(";");
                                }
                            } else {
                                docList.add(values);
                            }
                        }
                    } else if (DEFAULT_APKS_BLACKLIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            if ((values != null) && values.contains(DEFAULT_APKS_BLACKLIST)) {
                                String[] list = values.split(":");
                                int len = list.length;
                                if (len == FILTER_LENTH) {
                                    sApksFilterSize = Integer.parseInt(list[1]);
                                }
                            } else {
                                apksList.add(values);
                            }
                        }
                    } else if (DEFAULT_ZIP_BLACKLIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            if ((values != null) && values.contains(DEFAULT_ZIP_BLACKLIST)) {
                                String[] list = values.split(":");
                                int len = list.length;
                                if (len == FILTER_LENTH) {
                                    sZipFilterSize = Integer.parseInt(list[1]);
                                }
                            } else {
                                zipList.add(values);
                            }
                        }
                    } else if (unknownFileListTag.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            unknownFilelist.add(values);
                        }
                    } else if (EXTENSION_EIGHT_LIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            extensionEightList.add(values);
                        }
                    } else if (EXTENSION_SEVEN_LIST.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            extensionSevenList.add(values);
                        }
                    } else if (FILTER_UNKNOWN_NO_SUFFIX.equals(typeString)) {
                        String values = parser.nextText();
                        sFilterNoSuffixFile = Integer.parseInt(values);
                    } else if (FILTER_RECENT_FILES.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            if (values != null) {
                                String[] list = values.split(";");
                                if (list.length > 0) {
                                    sRecentFilterFilelist = list;
                                }
                            }
                        }
                    } else if (NewFunctionSwitch.FILTER_SYSTEM_APP_LOG && addName.equals(typeString)) {
                        if ("item".equals(tagName)) {
                            String values = parser.nextText();
                            docList.add(values);
                        }
                    }
                }
            }
            evenType = parser.next();
        }
        if (musicList.size() > 0) {
            sMusicList = musicList.toArray(new String[]{});
        }
        if (videosList.size() > 0) {
            sVideosList = videosList.toArray(new String[]{});
        }
        if (photoList.size() > 0) {
            sPhotoList = photoList.toArray(new String[]{});
        }
        if (docList.size() > 0) {
            sDocList = docList.toArray(new String[]{});
        }
        if (apksList.size() > 0) {
            sApksList = apksList.toArray(new String[]{});
        }
        if (zipList.size() > 0) {
            sZipList = zipList.toArray(new String[]{});
        }
        if (unknownFilelist.size() > 0) {
            sUnknownFileList = unknownFilelist.toArray(new String[]{});
        }
        if (extensionEightList.size() > 0) {
            sExtensionEightList = extensionEightList.toArray(new String[]{});
        }
        if (extensionSevenList.size() > 0) {
            sExtensionSevenList = extensionSevenList.toArray(new String[]{});
        }
        if ((sRecentFilterFilelist == null) || (sRecentFilterFilelist.length == 0)) {
            sRecentFilterFilelist = DEFAULT_FILTER_RECENT_FILES.split(";");
        }
    }

    public static int getFilterSizeByType(int type) {
        switch (type) {
            case CategoryHelper.CATEGORY_VIDEO:
                return sVideosFilterSize;
            case CategoryHelper.CATEGORY_AUDIO:
                return sMusicFilterSize;
            case CategoryHelper.CATEGORY_IMAGE:
                return sPhotoFilterSize;
            case CategoryHelper.CATEGORY_DOC:
                return sDocFilterSize;
            case CategoryHelper.CATEGORY_APK:
                return sApksFilterSize;
            case CategoryHelper.CATEGORY_COMPRESS:
                return sZipFilterSize;
            default:
                break;
        }
        return 0;
    }

    public static boolean isFilterTypes(int type, String path) {
        boolean state = false;
        switch (type) {
            case CategoryHelper.CATEGORY_VIDEO:
                break;
            case CategoryHelper.CATEGORY_AUDIO:
                break;
            case CategoryHelper.CATEGORY_IMAGE:
                break;
            case CategoryHelper.CATEGORY_DOC:
                state = isDocFilterTypes(path);
                break;
            case CategoryHelper.CATEGORY_APK:
                state = isApkFilterType(path);
                break;
            case CategoryHelper.CATEGORY_COMPRESS:
                break;
            case CategoryHelper.CATEGORY_QQ:
                break;
            case CategoryHelper.CATEGORY_MICROMSG:
                break;
            default:
                break;
        }
        return state;
    }

    public static boolean isApkFilterType(String path) {
        return !FileTypeUtils.isApkType(path);
    }

    public static boolean isDocFilterTypes(String path) {
        if (sDocFilterTypes == null) {
            return false;
        }

        int len = sDocFilterTypes.length;
        if ((len == 1) && (!TextUtils.isEmpty(sDocFilterTypes[0])) && sDocFilterTypes[0].equals(NONE)) {
            return false;
        }

        if (TextUtils.isEmpty(path)) {
            return true;
        }

        if (len > 0) {
            String ext = FilenameUtils.getExtension(path);
            for (String type : sDocFilterTypes) {
                if (!TextUtils.isEmpty(type) && type.equalsIgnoreCase(ext)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isFilterNoSuffixFile(String path) {
        String ext = FilenameUtils.getExtension(path);
        if (TextUtils.isEmpty(ext)) {
            return true;
        }
        return false;
    }
}
