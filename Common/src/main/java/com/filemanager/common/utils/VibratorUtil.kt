/***********************************************************
 ** Copyright (C), 2010-2023 Oplus. All rights reserved..
 ** File:  - VibratorUtil.java
 ** Description: Vibrator Utils
 ** Version: 1.0
 ** Date : 2023/5/9
 ** Author: W9001702
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>        <version >    <desc>
 **  W9001702      2023/05/9      1.0          create
 ****************************************************************/
package com.filemanager.common.utils

import android.annotation.SuppressLint
import android.provider.Settings
import com.coui.appcompat.vibrateutil.VibrateUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.compat.AddonSdkCompat
import com.filemanager.common.compat.CompatUtils
import com.filemanager.common.compat.FeatureCompat
import com.oplus.os.LinearmotorVibrator
import com.oplus.os.WaveformEffect

object VibratorUtil {
    private const val TAG = "VibratorUtil"
    private const val HAPTIC_FEEDBACK_KEY = "haptic_feedback_enabled"
    private const val HAPTIC_FEEDBACK_OPEN = 1
    private const val HAPTIC_FEEDBACK_CLOSE = 0
    private const val TRACKER_MAX_VELOCITY = 8000
    private const val VIBRATE_LEVEL = 0
    private const val VIBRATE_INTENSITY = 1f
    private const val TRACKER_VELOCITY = 80
    private var linearMotorVibrator: Any? = null


    @SuppressLint("WrongConstant")
    @JvmStatic
    fun vibrateByLinearMotor(effectType: Int) {
        if (linearMotorVibrator == null) {
            linearMotorVibrator = AddonSdkCompat.getLinearmotorVibrator()
        }
        linearMotorVibrator?.let {
            val we = AddonSdkCompat.getWaveformEffect(effectType)
            AddonSdkCompat.vibrator(it, we)
            Log.d(TAG, "vibrateByLinearMotor")
        }
    }

    @SuppressLint("WrongConstant")
    @JvmStatic
    fun vibrateByLinearmotor() {
        val linearmotorVibrator = AddonSdkCompat.getLinearmotorVibrator()
        if (linearmotorVibrator != null) {
            val effectType =
                CompatUtils.compactSApi({ WaveformEffect.EFFECT_CUSTOMIZED_SPREAD_OUT },
                    { com.heytap.addon.os.WaveformEffect.EFFECT_CUSTOMIZED_THREE_DIMENSION_TOUCH })
            val we = AddonSdkCompat.getWaveformEffect(effectType)
            AddonSdkCompat.vibrator(linearmotorVibrator, we)
            Log.d(TAG, "vibrateByLinearmotor")
        }
    }

    @JvmStatic
    fun vibrate() {
        if (FeatureCompat.isLargerThanOS14() && FeatureCompat.sIsSupportLuxunVibrator && isHapticFeedbackOpen()) {
            Log.d(TAG, "is support linear motor vibrator")
            vibrateByLinearmotor()
        }
    }

    @JvmStatic
    fun isHapticFeedbackOpen(): Boolean {
        val switchStatus =
            Settings.System.getInt(appContext.contentResolver, HAPTIC_FEEDBACK_KEY, HAPTIC_FEEDBACK_CLOSE)
        return switchStatus == HAPTIC_FEEDBACK_OPEN
    }

    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun vibrateLinearForScrollBar() {
        if (FeatureCompat.isLargerThanOS14() && FeatureCompat.sIsSupportLuxunVibrator) {
            try {
                if (linearMotorVibrator == null) {
                    linearMotorVibrator =
                        AddonSdkCompat.getLinearmotorVibrator()
                }
                (linearMotorVibrator as? LinearmotorVibrator)?.let {
                    VibrateUtils.setLinearMotorVibratorStrength(
                        it,
                        VibrateUtils.TYPE_GRANULAR_SHORT_MODERATE,
                        TRACKER_VELOCITY,
                        TRACKER_MAX_VELOCITY,
                        VibrateUtils.STRENGTH_MIN_GRANULAR,
                        VibrateUtils.STRENGTH_MAX_GRANULAR,
                        VIBRATE_LEVEL,
                        VIBRATE_INTENSITY
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "get haptic player failed. error = " + e.message)
            }
        } else {
            vibrateByLinearMotor(
                CompatUtils.compactSApi({ WaveformEffect.EFFECT_WEAKEST_SHORT_VIBRATE_ONCE },
                    { com.heytap.addon.os.WaveformEffect.EFFECT_WEAKEST_SHORT_VIBRATE_ONCE })
            )
        }
    }
}