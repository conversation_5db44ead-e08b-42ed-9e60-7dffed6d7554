/***********************************************************
 * Copyright (C), 2008-2017 Oplus. All rights reserved.
 * VENDOR_EDIT
 * File: _
 * Description: com.filemanager.common.interfaces
 * Version: 1.0
 * Date : 2021/6/25
 * Author: w9010681
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.filemanager.common.interfaces

interface InstalledPermissionCallback {
    companion object {
        const val DEFAULT_TYPE = 0
        const val APK_ACTIVITY = 1
        const val FILE_BROWSER_ACTIVITY = 2
        const val FILE_OPEN_MODE_ACTIVITY = 3
        const val SUPER_FILE_PREVIEW_ACTIVITY = 4
        const val LAST_INSTALLED_APPS_PERMISSION ="last_installed_apps_PERMISSION"
    }
    fun getActivityType(): Int {
        return DEFAULT_TYPE
    }
}