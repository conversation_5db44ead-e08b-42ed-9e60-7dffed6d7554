/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : GetMediaDurationUtil.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/9/10
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/9/10      1.0        create
 ***********************************************************************/
package com.filemanager.common.utils

import android.media.MediaMetadataRetriever
import androidx.annotation.VisibleForTesting
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper

object GetMediaDurationUtil {

    private const val TAG = "GetMediaDurationUtil"

    @VisibleForTesting
    val mCache = mutableMapOf<String, Long>()

    @JvmStatic
    @Suppress("TooGenericExceptionCaught")
    fun getDuration(file: BaseFileBean): Long {
        var duration = 0L
        file.mData?.let { path ->
            if (file.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
                val cacheValue = mCache[path]
                Log.d(TAG, "getDuration -> cacheValue = $cacheValue")
                if (cacheValue == null) {
                    val retriever = createMediaMetadataRetriever()
                    try {
                        Log.d(TAG, "getRetriever = $retriever")
                        retriever.setDataSource(path)
                        Log.d(TAG, "getDuration -> setDataSource")
                        duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
                            ?.toLongOrNull() ?: 0L
                        Log.d(TAG, "getRetriever -> duration = $duration")
                        mCache[path] = duration
                    } catch (e: Exception) {
                        Log.e(TAG, "getDuration -> retriever error: $e")
                    } finally {
                        retriever.release()
                    }
                } else {
                    duration = cacheValue
                }
            }
        }
        return duration
    }

    @JvmStatic
    @VisibleForTesting
    fun createMediaMetadataRetriever(): MediaMetadataRetriever = MediaMetadataRetriever()
}