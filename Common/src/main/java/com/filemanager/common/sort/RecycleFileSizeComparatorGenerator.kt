/***********************************************************
 * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File:
 * Description:
 * Version:
 * Date :
 * Author:
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
</desc></version></data></author> */
package com.filemanager.common.sort

import com.filemanager.common.wrapper.RecycleFileWrapper

class RecycleFileSizeComparatorGenerator : NameComparatorGenerator() {
    companion object {
        val comparator = RecycleFileSizeComparatorGenerator()

        fun getComparator(isDesc: Boolean = true): Comparator<RecycleFileWrapper> {
            return comparator.genRecycleFileComparator(isDesc)
        }
    }

    override fun compareRecycleFileWrapper(file1: RecycleFileWrapper, file2: RecycleFileWrapper, isDesc: Boolean): Int {
        return if (file1.mIsDirectory && file2.mIsDirectory) {
            super.compareRecycleFileWrapper(file1, file2, isDesc)
        } else {
            var size1: Long = 0
            if (file1.mIsDirectory) {
                // do nothing
            } else {
                size1 = file1.mSize
            }
            var size2: Long = 0
            if (file2.mIsDirectory) {
                // do nothing
            } else {
                size2 = file2.mSize
            }
            val result = size1 - size2
            when {
                result < 0 -> {
                    1
                }
                result > 0 -> {
                    -1
                }
                else -> {
                    0
                }
            }
        }
    }

}