/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : UriFileWrapper
 * * Version     : 1.0
 * * Date        : 2020/1/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.database.Cursor
import android.net.Uri
import com.filemanager.common.base.BaseFileBean

abstract class UriFileWrapper : BaseFileBean {
    var id: Int? = null

    /**
     * Add legacy offending naming alias for corrected naming member [id].
     * Pls merge [id] and [mId] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("id"))
    var mId: Int?
        get() = id
        set(value) = ::id.set(value)

    constructor(cursor: Cursor, uri: Uri?) {
        createData(cursor, uri)
    }

    constructor()

    /**
     * Implement the more data initialize actions in subclass
     */
    abstract fun createData(cursor: Cursor, uri: Uri?)
}