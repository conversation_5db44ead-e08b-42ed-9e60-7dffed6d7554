/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/25, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.KtConstants

class DropViewModel(private val fileBeanList: ArrayList<BaseFileBean>) : SelectionViewModel<BaseFileBean, BaseUiModel<BaseFileBean>>() {

    override fun getRealFileSize(): Int {
        return 0
    }

    override fun loadData() {
        val modeState = BaseStateModel(MutableLiveData<Int>(KtConstants.LIST_SELECTED_MODE))
        val selectedList = ArrayList<Int>()
        val keyMap = HashMap<Int, BaseFileBean>()
        uiState.value = BaseUiModel(fileBeanList, modeState, selectedList, keyMap)
        for (fileBean in fileBeanList) {
            val key = fileBean.mData?.hashCode()
            key?.let {
                uiState.value?.keyMap?.put(key, fileBean)
                uiState.value?.selectedList?.add(key)
            }
        }
    }
}