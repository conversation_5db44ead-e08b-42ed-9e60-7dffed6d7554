package com.filemanager.common.view.path;

import com.filemanager.common.R;
import com.filemanager.common.utils.Utils;

public class PathBarConfig {
    public static final int DEFAULT_VALUE = -1;

    //default value
    private final int DEFAULT_BTN_PADDING = R.dimen.dimen_0dp;
    private final int DEFAULT_NORMAL_TEXT_COLOR = R.color.black;
    private final int DEFAULT_CHILD_BACKGROUND_COLOR = R.color.color_white;
    private final int DEFAULT_CHILD_TEXT_SIZE = R.dimen.TD05;
    private final int DEFAULT_ROOT_TEXT_SIZE = R.dimen.TD05;
    private final int DEFAULT_PATH_BAR_MIN_WIDTH = R.dimen.M10;
    private final int DEFAULT_MIDDLE_IMG = R.drawable.pathbar_middle_img;
    private final int DEFAULT_POP_NIGHT_MODE_LEFT_GRADIENT = R.drawable.path_bar_left_gradient_pop_night;
    private final int DEFAULT_POP_NIGHT_MODE_RIGHT_GRADIENT = R.drawable.path_bar_right_gradient_pop_night;
    private final int DEFAULT_NIGHT_MODE_LEFT_GRADIENT = R.drawable.path_bar_left_gradient;
    private final int DEFAULT_NIGHT_MODE_RIGHT_GRADIENT = R.drawable.path_bar_right_gradient;

    public int getMiddleImg() {
        int mMiddleImg = DEFAULT_VALUE;
        if (isNeverSetUp(mMiddleImg)) {
            return DEFAULT_MIDDLE_IMG;
        } else {
            return mMiddleImg;
        }
    }

    public boolean isSupportGradient() {
        return true;
    }

    public int getPathBarMinWidth() {
        int mPathBarMinWidth = DEFAULT_VALUE;
        if (isNeverSetUp(mPathBarMinWidth)) {
            return DEFAULT_PATH_BAR_MIN_WIDTH;
        } else {
            return mPathBarMinWidth;
        }
    }

    public int getChildBackgroundColor() {
        int mChildBackgroundColor = DEFAULT_VALUE;
        if (isNeverSetUp(mChildBackgroundColor)) {
            return DEFAULT_CHILD_BACKGROUND_COLOR;
        } else {
            return mChildBackgroundColor;
        }
    }

    public int getTextNormalColor() {
        int mTextNormalColor = DEFAULT_VALUE;
        if (isNeverSetUp(mTextNormalColor)) {
            return DEFAULT_NORMAL_TEXT_COLOR;
        }
        return mTextNormalColor;
    }

    public int getChildPadding() {
        int mChildPadding = DEFAULT_VALUE;
        if (isNeverSetUp(mChildPadding)) {
            return DEFAULT_BTN_PADDING;
        }
        return mChildPadding;
    }

    public int getPopNightModeLeftGradient() {
        int mPopNightModeLeftGradient = DEFAULT_VALUE;
        if (isNeverSetUp(mPopNightModeLeftGradient)) {
            if (Utils.isRtl()) {
                return DEFAULT_POP_NIGHT_MODE_RIGHT_GRADIENT;
            } else {
                return DEFAULT_POP_NIGHT_MODE_LEFT_GRADIENT;
            }
        }
        return mPopNightModeLeftGradient;
    }

    public int getPopNightModeRightGradient() {
        int mPopNightModeRightGradient = DEFAULT_VALUE;
        if (isNeverSetUp(mPopNightModeRightGradient)) {
            if (Utils.isRtl()) {
                return DEFAULT_POP_NIGHT_MODE_LEFT_GRADIENT;
            } else {
                return DEFAULT_POP_NIGHT_MODE_RIGHT_GRADIENT;
            }
        }
        return mPopNightModeRightGradient;
    }


    public int getNightModeLeftGradient() {
        int mNightModeLeftGradient = DEFAULT_VALUE;
        if (isNeverSetUp(mNightModeLeftGradient)) {
            if (Utils.isRtl()) {
                return DEFAULT_NIGHT_MODE_RIGHT_GRADIENT;
            } else {
                return DEFAULT_NIGHT_MODE_LEFT_GRADIENT;
            }
        }
        return mNightModeLeftGradient;
    }

    public int getNightModeRightGradient() {
        int mNightModeRightGradient = DEFAULT_VALUE;
        if (isNeverSetUp(mNightModeRightGradient)) {
            if (Utils.isRtl()) {
                return DEFAULT_NIGHT_MODE_LEFT_GRADIENT;
            } else {
                return DEFAULT_NIGHT_MODE_RIGHT_GRADIENT;
            }
        }
        return mNightModeRightGradient;
    }


    private boolean isNeverSetUp(int value) {
        return value == DEFAULT_VALUE;
    }

    public int getRootTextSize() {
        //never been set up value
        int mRootTextSize = DEFAULT_VALUE;
        if (isNeverSetUp(mRootTextSize)) {
            return DEFAULT_ROOT_TEXT_SIZE;
        } else {
            return mRootTextSize;
        }
    }

    public int getChildTextSize() {
        int mChildTextSize = DEFAULT_VALUE;
        if (isNeverSetUp(mChildTextSize)) {
            return DEFAULT_CHILD_TEXT_SIZE;
        } else {
            return mChildTextSize;
        }
    }

    private static PathBarConfig mSettings;

    public static PathBarConfig init() {
        if (null == mSettings) {
            mSettings = new PathBarConfig();
        }
        return mSettings;
    }

    private PathBarConfig() {
    }
}