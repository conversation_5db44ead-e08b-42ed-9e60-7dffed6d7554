/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.view
 * * Version     : 1.0
 * * Date        : 2020/4/30
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.base.BaseItemDecoration
import com.filemanager.common.utils.Utils

class SpacesItemDecoration(spanCount: Int, spacing: Int, includeEdge: Boolean, edgeSpace: Int = 0, vertical: Int = spacing, verticalFirst: Int = vertical) : BaseItemDecoration() {
    private var mSpace = spacing
    private var mIncludeEdge = includeEdge
    private var mEdgeSpace: Int = edgeSpace
    private var mVertical: Int = vertical
    private var mVerticalFirst: Int = verticalFirst
    private val mIsRtl by lazy { Utils.isRtl() }
    private val mStickyFooterDelegation: StickyFooterDelegation by lazy {
        StickyFooterDelegation()
    }

    init {
        mSpanCount = spanCount
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        if (mStickyFooterDelegation.isFootView(parent, view)) {
            mStickyFooterDelegation.getItemOffsets(mSpace, outRect, view, parent, state)
        } else if (mSpanCount > 1) {
            val position = parent.getChildAdapterPosition(view) // item position
            val column = position % mSpanCount // item column
            val row = position / mSpanCount
            if (mIncludeEdge) {
                outRect.left = mSpace - column * mSpace / mSpanCount // spacing - column * ((1f / spanCount) * spacing)
                outRect.right = (column + 1) * mSpace / mSpanCount // (column + 1) * ((1f / spanCount) * spacing)
                if (position < mSpanCount) { // top edge
                    outRect.top = mVertical
                }
                outRect.bottom = mVertical // item bottom
            } else {
                val mEachSpace = (mEdgeSpace * 2 + (mSpanCount - 1) * mSpace) / mSpanCount
                val diff = ((mEachSpace - mEdgeSpace) - mEdgeSpace) / (mSpanCount - 1)
                val left = (column + 1 - 1) * diff + mEdgeSpace
                val right = mEachSpace - left
                if (mIsRtl) {
                    when (row) {
                        0 -> outRect.set(right, mVerticalFirst, left, mVertical)
                        else -> outRect.set(right, 0, left, mVertical)
                    }
                } else {
                    when (row) {
                        0 -> outRect.set(left, mVerticalFirst, right, mVertical)
                        else -> outRect.set(left, 0, right, mVertical)
                    }
                }
            }
        }
    }
}
