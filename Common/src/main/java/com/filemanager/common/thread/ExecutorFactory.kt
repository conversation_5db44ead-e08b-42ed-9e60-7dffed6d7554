/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/2/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thread

import com.filemanager.common.utils.Log
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.PriorityBlockingQueue


class ExecutorFactory {
    companion object {
        private const val MIN_POOL_SIZE = 2
        private const val LOADER_POOL_SIZE = 5
        private val AVAILABLE_CORE = Runtime.getRuntime().availableProcessors() / 2
        private val CPU_COUNT = if (AVAILABLE_CORE < MIN_POOL_SIZE) MIN_POOL_SIZE else AVAILABLE_CORE
        private val IO_CORE_POOL_SIZE = CPU_COUNT + 1
        private val IO_MAXIMUM_POOL_SIZE = CPU_COUNT * 2 + 1
        private val mNormalExecutor = NormalExecutor(ThreadPoolParams(IO_CORE_POOL_SIZE, IO_MAXIMUM_POOL_SIZE, 10)
                , BaseThreadFactory(), PriorityBlockingQueue<Runnable>(16))

        @JvmStatic
        private val mLoaderExecutor by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            NormalExecutor(ThreadPoolParams(LOADER_POOL_SIZE, 128, 1)
                    , BaseThreadFactory(), LinkedBlockingQueue<Runnable>(16))
        }

        @JvmStatic
        fun getExecutor(type: ThreadType): BaseExecutor {
            return when (type) {
                ThreadType.NORMAL_THREAD -> {
                    mNormalExecutor
                }
                ThreadType.LOADER_THREAD -> {
                    mLoaderExecutor.allowCoreThreadTimeOut(true)
                    return mLoaderExecutor
                }
            }
        }

        @JvmStatic
        fun shutdownAll() {
            try {
                mNormalExecutor.shutdown()
                mLoaderExecutor.shutdown()
            } catch (e: Throwable) {
                Log.e("ExecutorFactory", e.message)
            }

        }
    }

}
