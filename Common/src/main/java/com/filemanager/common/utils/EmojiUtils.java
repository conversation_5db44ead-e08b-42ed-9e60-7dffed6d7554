/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description: tool for emoji v11.0
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils;

import android.text.TextUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class EmojiUtils {
    private static final String TAG = "EmojiUtils";

    private static StringBuilder sEmojiPatternBuilder = new StringBuilder("⭕|⭐|[☀-⟿]|[\ud83c\udc00-\ud83c\udfff]|[\ud83d\udc00-\ud83d\udfff]|[\uD83E\uDD00-\uD83E\uDFFF]|[\u2600-\u27ff]|[\ue000-\uf8ff]");
    private static Pattern sEmojiPattern;
    private static Pattern sFileNameIllegalPattern;


    private EmojiUtils() {
    }

    public static boolean isContainEmoji(CharSequence s) {
        if (TextUtils.isEmpty(s)) {
            return false;
        } else {
            if (sEmojiPattern == null) {
                sEmojiPattern = Pattern.compile(sEmojiPatternBuilder.toString());
            }

            Matcher matcher = sEmojiPattern.matcher(s);
            return matcher.find();
        }
    }

    public static boolean containsIllegalCharFileName(CharSequence source) {
        if (TextUtils.isEmpty(source)) {
            return false;
        } else {
            if (sFileNameIllegalPattern == null) {
                sFileNameIllegalPattern = Pattern.compile(".*[\\\\/*:?<>|\"]+?.*");
            }

            Matcher matcher = sFileNameIllegalPattern.matcher(source);
            return matcher.find();
        }
    }

    private static void addUnicodeToSet(StringBuilder builder, String code) {
        if (builder == null) {
            return;
        }
        builder.append("|[").append(code).append("]");
    }

    public static String getBuilder() {
        return sEmojiPatternBuilder.toString();
    }

    static {
        addUnicodeToSet(sEmojiPatternBuilder, "\u00ae");
        addUnicodeToSet(sEmojiPatternBuilder, "\u00a9");
        addUnicodeToSet(sEmojiPatternBuilder, "\u20e3");
        addUnicodeToSet(sEmojiPatternBuilder, "\u2122");
        addUnicodeToSet(sEmojiPatternBuilder, "\u2196-\u2199");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23e9");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23ea");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23eb");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23ec");
        addUnicodeToSet(sEmojiPatternBuilder, "\u231b");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23f3");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23f9");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23f8");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23fa");
        addUnicodeToSet(sEmojiPatternBuilder, "\u231a");
        addUnicodeToSet(sEmojiPatternBuilder, "\u23f0");
        addUnicodeToSet(sEmojiPatternBuilder, "\u2328");
        addUnicodeToSet(sEmojiPatternBuilder, "\u25aa");
        addUnicodeToSet(sEmojiPatternBuilder, "\u25b6");
        addUnicodeToSet(sEmojiPatternBuilder, "\u25c0");
        addUnicodeToSet(sEmojiPatternBuilder, "\u25fd");
        addUnicodeToSet(sEmojiPatternBuilder, "\u25fe");
        addUnicodeToSet(sEmojiPatternBuilder, "\u2b1b");
        addUnicodeToSet(sEmojiPatternBuilder, "\u2b1c");
        addUnicodeToSet(sEmojiPatternBuilder, "\u2b05-\u2b07");
        addUnicodeToSet(sEmojiPatternBuilder, "\u303d");
        addUnicodeToSet(sEmojiPatternBuilder, "\u3297");
        addUnicodeToSet(sEmojiPatternBuilder, "\u3299");
        addUnicodeToSet(sEmojiPatternBuilder, "\ufe0f");
        addUnicodeToSet(sEmojiPatternBuilder, "\u002a\ufe0f\u20e3");
    }
}
