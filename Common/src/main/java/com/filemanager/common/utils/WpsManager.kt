/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: -
 ** Description: com.filemanager.common.utils
 ** Version: 1.0
 ** Date: 2021/7/1
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.Constants
import com.filemanager.thumbnail.ThumbnailManager

object WpsManager {

    private const val TAG = "Utils"

    @JvmStatic
    var closeWpsPreview = false
        set(value) {
            Log.d(TAG, "setCloseWpsPreview $value")
            field = value
        }

    /**
     * basic switcher for whether wps is support
     */
    val isSupportWps by lazy {
        !FeatureCompat.sIsExpRom
    }

    /**
     * switcher for whether wps preview is support
     * this is designed for opening doc file by wps
     */
    val isSupportWpsPreview by lazy {
        isSupportWps && !closeWpsPreview
    }

    val isEnabledWpsThumbnail: Boolean by lazy {
        !FeatureCompat.sIsLightVersion && isSupportWps
    }

    fun setCloseWpsThumbnail(closeWpsThumbnail: Boolean) {
        ThumbnailManager.WpsDocConfigs.enableThumbnail = isEnabledWpsThumbnail && !closeWpsThumbnail
    }

    /**
     * if the version of wps personal app is above 13.12.0, then jump to wps personal app
     */
    @JvmStatic
    fun needOpenByWpsPersonal(): Boolean =
        isVersionNameAbove(MyApplication.appContext, "13.12.0", Constants.WPS_PERSONAL_PACKAGE_NAME)

    @JvmStatic
    private fun isVersionNameAbove(
        context: Context,
        version: String,
        packageName: String
    ): Boolean {
        val versionName = getVersionName(context, packageName)
        Log.d(TAG, "isVersionNameAbove: version is $versionName, package is $packageName")
        return if (versionName.isNullOrEmpty()) {
            false
        } else {
            versionName >= version
        }
    }

    @JvmStatic
    private fun getVersionName(context: Context, packageName: String): String? = runCatching {
        val packageInfo = context.packageManager.getPackageInfo(packageName, 0)
        packageInfo.versionName
    }.onFailure {
        Log.e(TAG, "getVersionName: ERROR! $it")
    }.getOrNull()
}