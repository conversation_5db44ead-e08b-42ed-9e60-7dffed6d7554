/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : OnceAction
 * * Description : 只执行一次的操作
 * * Version     : 1.0
 * * Date        : 2025/01/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

class OnceAction(private val action: Runnable) : Runnable {

    private var hasExecuted: Boolean = false


    override fun run() {
        if (!hasExecuted) {
            hasExecuted = true
            action.run()
        }
    }
}