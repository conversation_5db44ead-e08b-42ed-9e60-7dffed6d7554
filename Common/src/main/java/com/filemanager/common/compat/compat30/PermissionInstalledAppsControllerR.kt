/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2021/7/1
 * * Author      : w9010681
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.filemanager.common.compat.compat30

import android.app.Activity
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.OnLifecycleEvent
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.controller.PermissionController
import com.filemanager.common.interfaces.InstalledPermissionCallback
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.isInvalid
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils

class PermissionInstalledAppsControllerR(
    lifecycle: Lifecycle?,
    permissionController: PermissionController?,
    listener: PermissionController.OnRequestPermissionListener?
) : LifecycleObserver {
    companion object {
        private const val TAG = "PermissionInstalledAppsControllerR"
        private const val REJECT_TIME_INSTALLED_PERMISSION = "reject_time_installed_permission"
        private const val BROWSER_HAVE_SHOW_INSTALLED_HINT = "browser_have_show_installed_hint"
        private const val APK_HAVE_SHOW_INSTALLED_HINT = "apk_have_show_installed_hint"
        private const val FILE_OPEN_HAVE_SHOW_INSTALLED_HINT = "file_open_have_show_installed_hint"
        private const val SUPER_FILE_PREVIEW_OPEN_HAVE_SHOW_INSTALLED_HINT = "super_file_preview_open_have_show_installed_hint"
        private const val PACKAGE_NAME_SECURITY_PERMISSION = "com.oplus.securitypermission"
        private const val META_DATA_NAVIGATE_TO_APP_PERMISSION_KEY = "navigateToAppPermissions"
        private const val MAIN_ACTIVITY_SHOW_ONLY_ONCE = "main_activity_show_only_once"
        var hasInitCanNavigateToAppPermissions = false
        var canNavigateToAppPermissions = false
    }

    private var mLifecycle: Lifecycle? = null
    private var mPermissionListener: PermissionController.OnRequestPermissionListener? = null
    private var mPermissionController: PermissionController? = null
    private var mInstalledDialog: AlertDialog? = null
    private var mIsOnPermissionResult = false
    private var isDialogShow = false

    init {
        lifecycle?.addObserver(this)
        mLifecycle = lifecycle
        mPermissionListener = listener
        mPermissionController = permissionController
    }

    fun onPermissionsResultReturn(
        activity: Activity, requestCode: Int,
        permissions: Array<out String>?, grantResults: IntArray?
    ) {
        Log.d(TAG, "onPermissionsResultReturn(): start")
        if (PermissionUtils.REQUEST_GET_INSTALLED_APPS_PERMISSIONS != requestCode) {
            return
        }
        if (permissions.isNullOrEmpty() || (grantResults?.isNotEmpty() != true)) {
            Log.d(TAG, "Failed requestPermissionsResult: grantResults is null or empty")
            return
        }
        var reject = false
        for (result in grantResults) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                reject = true
                val alwaysRejected =
                    PreferencesUtils.getInt(key = REJECT_TIME_INSTALLED_PERMISSION)
                Log.d(TAG, "alwaysRejectedTimes =$alwaysRejected ")
                PreferencesUtils.put(
                    key = REJECT_TIME_INSTALLED_PERMISSION,
                    value = (alwaysRejected + 1)
                )
                break
            }
        }
        if (reject.not()) {
            setIsHaveShowRejectDialog()
        }
        Log.d(TAG, "onPermissionsResultReturn(): reject=$reject")
        mIsOnPermissionResult = true
        mPermissionListener?.onPermissionSuccess()
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        Log.d(TAG, "onResume")
        mInstalledDialog?.let {
            if (PermissionUtils.hasGetInstalledAppsPermission()) {
                it.dismiss()
            }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_DESTROY)
    fun onDestroy() {
        Log.d(TAG, "onDestroy()")
        mInstalledDialog?.dismiss()
        mInstalledDialog = null
        mLifecycle?.removeObserver(this)
        mLifecycle = null
        mPermissionListener = null
    }

    fun checkGetInstalledAppsPermission(activity: Activity, isMainShow: Boolean) {
        if (activity.isInvalid()) {
            Log.d(TAG, "activity invalid, not checked GetInstalledApps Permission")
            return
        }
        if (isDialogShow) {
            return
        }
        Log.d(TAG, "checkGetInstalledAppsPermission isMainShow $isMainShow")
        if (PermissionUtils.hasGetInstalledAppsPermission()) {
            setIsHaveShowRejectDialog()
            mPermissionListener?.onPermissionSuccess(true)
        } else {
            if (interceptRequestInstalledAppsPermission(activity, isMainShow).not()) {
                /*如果是首页进来只能弹一次安装包权限的请求（包括系统和受阻）*/
                if (isMainShow) {
                    val hasShow = PreferencesUtils.getBoolean(PreferencesUtils.SHARED_PREFS_NAME, MAIN_ACTIVITY_SHOW_ONLY_ONCE, false)
                    if (!hasShow) {
                        val result = PermissionUtils.requestGetInstalledAppsPermission(activity)
                        mPermissionController?.setWaitPermissionGrantResult(result)
                        PreferencesUtils.put(PreferencesUtils.SHARED_PREFS_NAME, MAIN_ACTIVITY_SHOW_ONLY_ONCE, true)
                    }
                } else {
                    val result = PermissionUtils.requestGetInstalledAppsPermission(activity)
                    mPermissionController?.setWaitPermissionGrantResult(result)
                }
            }
        }
    }

    private fun interceptRequestInstalledAppsPermission(activity: Activity, isMainShow: Boolean): Boolean {
        return if (PreferencesUtils.getInt(key = REJECT_TIME_INSTALLED_PERMISSION) >= 2) {
            showSettingGuildDialog(activity, isMainShow)
            true
        } else {
            false
        }
    }

    private fun setIsHaveShowRejectDialog(isShow: Boolean = false) {
        PreferencesUtils.put(
            key = APK_HAVE_SHOW_INSTALLED_HINT,
            value = isShow
        )
        PreferencesUtils.put(
            key = BROWSER_HAVE_SHOW_INSTALLED_HINT,
            value = isShow
        )
    }

    private fun showSettingGuildDialog(activity: Activity, isMainShow: Boolean) {
        if (isMainShow) {
            val hasShow = PreferencesUtils.getBoolean(PreferencesUtils.SHARED_PREFS_NAME, MAIN_ACTIVITY_SHOW_ONLY_ONCE, false)
            if (!hasShow) {
                showRejectGetInstalledCOUIAlertDialog(
                    activity,
                    MAIN_ACTIVITY_SHOW_ONLY_ONCE
                )
            }
        } else {
            val activityType = (activity as? InstalledPermissionCallback)?.getActivityType()
                ?: InstalledPermissionCallback.DEFAULT_TYPE

            mInstalledDialog = when (activityType) {
                InstalledPermissionCallback.APK_ACTIVITY -> showRejectGetInstalledCOUIAlertDialog(
                    activity,
                    APK_HAVE_SHOW_INSTALLED_HINT
                )

                InstalledPermissionCallback.FILE_BROWSER_ACTIVITY -> showRejectGetInstalledCOUIAlertDialog(
                    activity,
                    BROWSER_HAVE_SHOW_INSTALLED_HINT
                )

                InstalledPermissionCallback.FILE_OPEN_MODE_ACTIVITY -> showRejectGetInstalledCOUIAlertDialog(
                    activity,
                    FILE_OPEN_HAVE_SHOW_INSTALLED_HINT,
                    false
                )

                InstalledPermissionCallback.SUPER_FILE_PREVIEW_ACTIVITY -> showRejectGetInstalledCOUIAlertDialog(
                    activity,
                    SUPER_FILE_PREVIEW_OPEN_HAVE_SHOW_INSTALLED_HINT,
                    false
                )

                else -> {
                    showRejectGetInstalledCOUIAlertDialog(
                        activity,
                        APK_HAVE_SHOW_INSTALLED_HINT
                    )
                }
            }
        }
    }

    private fun showRejectGetInstalledCOUIAlertDialog(
        activity: Activity,
        isShowValue: String,
        isSavePreference: Boolean = true
    ): AlertDialog? {
        val message = R.string.get_installed_permission_des_new
        val builder: COUIAlertDialogBuilder = COUIAlertDialogBuilder(activity).apply {
            setTitle(R.string.get_installed_permission_title)
            setMessage(message)
            setCancelable(false)
            setNegativeButton(R.string.dialog_cancel) { dialog, _ ->
                dialog.dismiss()
            }
            setPositiveButton(R.string.set_button_text) { _, _ ->
                try {
                    jumpToPermissionSetting(activity)
                } catch (e: Exception) {
                    Log.d(TAG, "start setting failed")
                }
            }
        }
        val couiAlertDialog = builder.create()
        couiAlertDialog.show()
        couiAlertDialog.setOnDismissListener {
            isDialogShow = false
        }
        isDialogShow = true
        if (isSavePreference) {
            PreferencesUtils.put(PreferencesUtils.SHARED_PREFS_NAME, isShowValue, true)
        }
        return couiAlertDialog
    }

    @Suppress("TooGenericExceptionCaught")
    @VisibleForTesting
    fun jumpToPermissionSetting(activity: Activity) {
        Log.d(TAG, "jumpToPermissionSetting")
        if (getCanNavigateToAppPermissions(activity)) {
            Log.d(TAG, "NavigateToAppPermissions")
            //指定Action
            val actionIntent = Intent(PermissionUtils.ACTION_SAFE_CENTER_PERMISSION)
            val pkgName = MyApplication.sAppContext.packageName
            val bundle = Bundle()
            // 传入受阻的权限列表（原生权限名）
            val arrayList = ArrayList<String>()
            arrayList.add(PermissionUtils.GET_INSTALLED_APPS)
            bundle.putStringArrayList("permissionList", arrayList)
            // 传入待修改权限的包名
            bundle.putString("packageName", pkgName)
            actionIntent.putExtras(bundle)
            try {
                activity.startActivity(actionIntent)
            } catch (e: Exception) {
                Log.e(TAG, "Exception ${e.message}")
                jumpToAppDetailSetting(activity)
            }
        } else {
            jumpToAppDetailSetting(activity)
        }
    }

    @VisibleForTesting
    fun getCanNavigateToAppPermissions(activity: Activity): Boolean {
        Log.e(TAG, "hasInitCanNavigateToAppPermissions $hasInitCanNavigateToAppPermissions")
        if (hasInitCanNavigateToAppPermissions) {
            return canNavigateToAppPermissions
        } else {
            var applicationInfo: ApplicationInfo? = null
            try {
                applicationInfo = activity.packageManager
                    .getApplicationInfo(
                        PACKAGE_NAME_SECURITY_PERMISSION,
                        PackageManager.GET_META_DATA
                    )
            } catch (e: PackageManager.NameNotFoundException) {
                Log.e(TAG, "securitypermission not found")
            }
            applicationInfo?.let {
                canNavigateToAppPermissions =
                    it.metaData.getBoolean(META_DATA_NAVIGATE_TO_APP_PERMISSION_KEY, false)
            }
            hasInitCanNavigateToAppPermissions = true
            Log.e(TAG, "canNavigateToAppPermissions $canNavigateToAppPermissions")
            return canNavigateToAppPermissions
        }
    }

    @VisibleForTesting
    fun jumpToAppDetailSetting(activity: Activity) {
        Log.d(TAG, "jumpToAppDetailSetting")
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        intent.data = Uri.parse("package:" + activity.packageName)
        intent.setPackage(PermissionUtils.SETTING_PACKAGE_NAME)
        CollectPrivacyUtils.collectInstalledAppList(PermissionUtils.SETTING_PACKAGE_NAME)
        activity.startActivity(intent)
    }

    fun getIsOnPermissionResult():Boolean {
        return mIsOnPermissionResult
    }
}