/***********************************************************
 * * Copyright (C), 2008-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:ResourceUtils.java
 * * Description:
 * * Version:1.0
 * * Date :2024/04/25
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *  keweiwei              1.0
 ****************************************************************/
package com.filemanager.common.utils

import android.content.Context

object ResourceUtils {
    private const val TAG = "ResourceUtils"

    @JvmStatic
    fun getString(context: Context, resId: Int): String {
        kotlin.runCatching {
            return context.getString(resId)
        }.onFailure {
            Log.e(TAG, "getResString : it = ${it.message}")
        }
        return ""
    }
}