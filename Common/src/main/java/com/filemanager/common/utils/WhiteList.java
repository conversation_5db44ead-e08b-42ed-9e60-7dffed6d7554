/***********************************************************
 * * Copyright (C), 2008-2019 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:WhiteList.java
 * * Description:
 * * Version:1.0
 * * Date :2019/10/03
 * * Author:W9000843
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.common.utils;


import android.content.Context;
import android.content.res.AssetManager;
import android.text.TextUtils;

import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.MyApplication;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;

public class WhiteList {
    private static final String TAG = "WhiteList";

    public static final String FILEMANAGER_WHITE_LIST_VERSION = "default_white_bucket_lists_config";
    public static final String FILEMANAGER_WHITE_LIST = "default_white_bucket_lists_config";
    public static final String FILEMANAGER_WHITE_LIST_DIR = "shared_prefs";
    private static final String FILEMANAGER_WHITE_LIST_FILE_NAME = FeatureCompat.getSIsExpRom() ? "default_white_bucket_lists_config_exp.xml"
            : "default_white_bucket_lists_config.xml";
    private static final String WHITE_BUCKET_LIST_NAME = "white_bucket_lists_config";
    private static final String SET_ITEM = "whiteset";
    private static final String SET_ATTR_ORDER = "order";
    private static final String SET_URL_NAME = "seturl";
    private static final int INVALID_VALUE = -99;
    private static final int DEFAULT_ORDER = -1;
    private static WhiteList sInstance;
    private static HashMap<Integer, WhiteListEntry> sWhiteListMap;
    private static WhiteListEntry[] sPaths;
    private Context mContext;

    private static class WhiteListHolder {
        private static WhiteList sInstance = new WhiteList();
    }

    private WhiteList() {
        mContext = MyApplication.getSAppContext();
        sPaths = initialize(mContext);
    }

    public static WhiteList getInstance() {
        return WhiteListHolder.sInstance;
    }

    public static WhiteListEntry[] getWhiteListPaths() {
        return sPaths;
    }

    public HashMap<Integer, WhiteListEntry> getWhiteListMap() {
        return sWhiteListMap;
    }

    public static WhiteListEntry[] initialize(Context context) {
        WhiteListEntry[] paths = null;
        AssetManager assetManager = context.getAssets();
        InputStream in = null;
        try {
            in = assetManager.open(FILEMANAGER_WHITE_LIST_FILE_NAME);
            if (in != null) {
                paths = parserXml(in);
            }
            return paths;
        } catch (XmlPullParserException e) {
            Log.e(TAG, e.getMessage());
        } catch (IOException e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != in) {
                try {
                    in.close();
                } catch (IOException e) {
                    Log.e(TAG, e.getMessage());
                }
                in = null;
            }
        }
        return null;
    }


    private static WhiteListEntry[] parserXml(InputStream in)
            throws XmlPullParserException, IOException {
        XmlPullParser parser = XmlPullParserFactory.newInstance().newPullParser();
        parser.setInput(in, "UTF-8");
        parser.nextTag();
        ArrayList<WhiteListEntry> list = new ArrayList<>();
        HashMap<Integer, WhiteListEntry> whiteListMap = new HashMap<>();

        int evenType = parser.getEventType();
        while (evenType != XmlPullParser.END_DOCUMENT) {
            final String tagName = parser.getName();
            if (tagName != null) {

                if (SET_ITEM.equals(tagName.toLowerCase())) {
                    int order = parserInt(parser.getAttributeValue(null, SET_ATTR_ORDER));
                    order = (order != INVALID_VALUE) ? order : DEFAULT_ORDER;
                    evenType = parser.next();
                    while (evenType != XmlPullParser.END_DOCUMENT) {
                        if (XmlPullParser.START_TAG == evenType) {
                            final String setUrl = parser.getName();
                            if (SET_URL_NAME.equals(setUrl)) {
                                String path = parser.nextText();
                                if (TextUtils.isEmpty(path)) {
                                    continue;
                                }

                                WhiteListEntry entry = new WhiteListEntry(path, order);
                                whiteListMap.put(path.toLowerCase().hashCode(), entry);
                                list.add(entry);
                            } else if (SET_ITEM.equals(setUrl)) {
                                break;
                            }
                        } else if (XmlPullParser.END_TAG == evenType) {
                            final String setUrl = parser.getName();
                            if (SET_ITEM.equals(setUrl)) {
                                break;
                            }
                        }
                        evenType = parser.next();
                    }
                    if (evenType == XmlPullParser.END_DOCUMENT) {
                        break;
                    }
                }

            }
            evenType = parser.next();
        }

        if (sWhiteListMap != null) {
            sWhiteListMap.clear();
        } else {
            sWhiteListMap = new HashMap<>();
        }
        sWhiteListMap.putAll(whiteListMap);

        if (list.size() > 0) {
            return list.toArray(new WhiteListEntry[]{});
        } else {
            Log.d(TAG, "[parserWhiteFilterBlock]parse xml, but get 0 item!");
        }
        return null;
    }


    private static int parserInt(String str) {
        int result = INVALID_VALUE;
        try {
            if (str != null) {
                result = Integer.parseInt(str);
            }
        } catch (NumberFormatException e) {
            Log.v(TAG, "[parserInt]xml wrong!");
        }
        return result;
    }

}
