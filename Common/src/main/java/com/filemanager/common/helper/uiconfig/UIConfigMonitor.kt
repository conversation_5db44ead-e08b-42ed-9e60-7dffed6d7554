/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.helper.uiconfig.UIConfigMonitor
 * * Description : update screen
 * * Version     : 1.0
 * * Date        : 2021/5/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.helper.uiconfig

import android.content.Context
import android.content.res.Configuration
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.ArraySet
import android.util.Size
import androidx.activity.ComponentActivity
import androidx.annotation.IntDef
import androidx.annotation.MainThread
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.*
import androidx.lifecycle.Observer
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.coui.responsiveui.config.UIConfig.WindowType
import com.coui.responsiveui.config.UIScreenSize
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.AddonSdkCompat
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.controller.BaseLifeController
import com.filemanager.common.helper.uiconfig.type.*
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.ColorUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ModelUtils
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.WindowUtils
import com.oplus.flexiblewindow.FlexibleWindowManager
import com.heytap.addon.zoomwindow.IOplusZoomWindowObserver
import com.heytap.addon.zoomwindow.OplusZoomWindowInfo
import com.heytap.addon.zoomwindow.OplusZoomWindowManager
import java.util.*

/**
 * The detail of this class: https://odocs.myoas.com/docs/NJkbElrN5ehZWzqR/
 */
class UIConfigMonitor private constructor() {
    companion object {
        private const val TAG = "UIConfigMonitor"
        const val SYSTEM_FOLDING_MODE = "oplus_system_folding_mode"

        // 屏幕初始状态
        const val SCREEN_DEFAULT = 0x00
        // 从小屏启动
        const val SCREEN_LAUNCH_FROM_SMALL = 0x01
        // 从大屏启动
        const val SCREEN_LAUNCH_FROM_LARGE = 0x02
        // 小屏切换到大屏
        const val SCREEN_SMALL_TO_LARGE = 0x03
        // 大屏切换到小屏
        const val SCREEN_LARGE_TO_SMALL = 0x04

        //横屏
        const val ROTATION_ONE = 1
        //旋转后横屏
        const val ROTATION_THREE = 3

        /**
         * 屏幕变化的状态
         */
        @IntDef(SCREEN_DEFAULT, SCREEN_LAUNCH_FROM_SMALL, SCREEN_LAUNCH_FROM_LARGE, SCREEN_SMALL_TO_LARGE, SCREEN_LARGE_TO_SMALL)
        @Retention(AnnotationRetention.SOURCE)
        annotation class ScreenChangeState

        @VisibleForTesting
        const val ZOOM_WINDOW_PORTRAIT = 0
        const val NOT_FOLDABLE = -1
        const val FOLD_CLOSE = 0
        const val FOLD_OPEN = 1

        private const val DELAY_NOTIFY_TIME = 100L
        val instance by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            UIConfigMonitor()
        }
        private val mPackageName by lazy {
            AppUtils.getPackageName()
        }

        /**
         * Check which config should bring out the ui change
         */
        @Suppress("ComplexCondition")
        fun shouldUpdateUIWhenConfigChange(config: MutableCollection<IUIConfig>): Boolean {
            config.forEach {
                if ((it is ScreenFoldConfig) || (it is ZoomWindowConfig)
                    || (it is ScreenOrientationConfig) || (it is ScreenSizeConfig)
                    || (it is DarkThemeLevelConfig)) {
                    return true
                }
            }
            return false
        }

        // if FileManager is in zoom window state
        private fun getZoomWindowState(): OplusZoomWindowInfo? {
            return try {
                OplusZoomWindowManager.getInstance()?.currentZoomWindowState?.let {
                    //The first time you enter window mode, zoomWindowInfo.zoomPkg will be null.
                    val isFileManagerPkg = it.zoomPkg.isNotEmpty() && (it.zoomPkg == AppUtils.getPackageName())
                    if (isFileManagerPkg) it else null
                }
            } catch (ex: Exception) {
                Log.d(TAG, "getZoomWindowState error: $ex")
                null
            }
        }

        @JvmStatic
        fun isZoomWindowPortrait(): Boolean {
            return getZoomWindowState()?.let {
                it.windowShown && (it.rotation == ZOOM_WINDOW_PORTRAIT)
            } ?: false
        }

        @JvmStatic
        fun isZoomWindowShow(): Boolean {
            return (instance.getZoomWindowState()?.zoomPkg == mPackageName) && (instance.getZoomWindowState()?.windowShown ?: false)
        }

        /**
         * 实时获取浮窗窗口来判断显示
         */
        fun isCurrentZoomWindowShow(): Boolean {
            val zoomWindow = getZoomWindowState()
            return zoomWindow?.windowShown ?: false
        }

        @JvmStatic
        fun isMultiWindow(): Boolean {
            return instance.isMultiWindow
        }

        @JvmStatic
        fun isAnyAppZoomWindowShow(): Boolean {
            return (instance.getZoomWindowState()?.windowShown ?: false)
        }

        fun screenChangeState2String(@ScreenChangeState state: Int): String {
            return when (state) {
                SCREEN_DEFAULT -> "default"
                SCREEN_LAUNCH_FROM_SMALL -> "launch from small"
                SCREEN_LAUNCH_FROM_LARGE -> "launch from large"
                SCREEN_SMALL_TO_LARGE -> "small screen to large"
                SCREEN_LARGE_TO_SMALL -> "large screen to small"
                else -> "$state"
            }
        }

        @VisibleForTesting
        @ScreenChangeState
        fun changeScreenState(@WindowUtils.WindowType screenMode: Int): Int {
            val currentState = instance.screenChangeState
            when(currentState) {
                SCREEN_DEFAULT -> { // 判断启动时大屏，还是小屏启动
                    val screenState = if (screenMode == WindowUtils.SMALL) {
                        SCREEN_LAUNCH_FROM_SMALL
                    } else {
                        SCREEN_LAUNCH_FROM_LARGE
                    }
                    instance.screenChangeState = screenState
                    instance.recordScreenState = screenState
                }
                SCREEN_LAUNCH_FROM_SMALL, SCREEN_LARGE_TO_SMALL -> { // 从小屏切换大屏
                    if (screenMode != WindowUtils.SMALL) {
                        instance.screenChangeState = SCREEN_SMALL_TO_LARGE
                    }
                }
                SCREEN_LAUNCH_FROM_LARGE,SCREEN_SMALL_TO_LARGE -> {// 从大屏切换小屏
                    if (screenMode == WindowUtils.SMALL) {
                        instance.screenChangeState = SCREEN_LARGE_TO_SMALL
                    }
                }
            }
            val changeState = instance.screenChangeState
            Log.e(TAG,"changeScreenState ${screenChangeState2String(currentState)} -> ${screenChangeState2String(changeState)}")
            return changeState
        }

        @ScreenChangeState
        fun getCurrentScreenState(): Int {
            return instance.screenChangeState
        }

        fun isCurrentSmallScreen(): Boolean {
            val state = getCurrentScreenState()
            return (SCREEN_LARGE_TO_SMALL == state) || (SCREEN_LAUNCH_FROM_SMALL == state)
        }

        /**
         * 根据屏幕宽高来确定是否显示底部导航栏
         */
        fun isShowBottomTabByScreenSize(context: Context): Boolean {
            return WindowUtils.isSmallScreen(context)
        }

        /**
         * 获取屏幕的宽度（单位是dp）
         */
        @WindowUtils.Dp
        fun getScreenWidth(): Int {
            return instance.mResponsiveUIConfig?.uiScreenSize?.value?.widthDp ?: 0
        }

        /**
         * 获取屏幕大小类型
         */
        fun getWindowType(): WindowType {
            return instance.mResponsiveUIConfig?.uiConfig?.value?.windowType ?: WindowType.SMALL
        }
    }

    @VisibleForTesting
    var mChangeListenerSet: ArraySet<OnUIConfigChangeListener>? = null
    private var mZoomWindowObserver: IOplusZoomWindowObserver? = null
    @VisibleForTesting
    var mAttachActivityCount = 0
    private val mChangeConfigMap = hashMapOf<Class<IUIConfig>, IUIConfig>()
    @VisibleForTesting
    var mResponsiveUIConfig: ResponsiveUIConfig? = null
    private var mCurrentLayoutDirection = TextUtils.getLayoutDirectionFromLocale(Locale.getDefault())
    private var mCurrentDarkModeLevel = ColorUtil.getDarkModeLevel()
    private val mHandler by lazy { Handler(Looper.getMainLooper()) }
    private var mNotifyChangeRunnable: Runnable? = null
    private var isMultiWindow: Boolean = false

    @VisibleForTesting
    @ScreenChangeState
    var screenChangeState: Int = SCREEN_DEFAULT
    var recordScreenState = SCREEN_DEFAULT

    @VisibleForTesting
    private fun monitorResponsiveUIConfig(activity: ComponentActivity, monitor: Boolean = true) {
        mResponsiveUIConfig?.apply {
            if (monitor) {
                uiStatus.observe(activity, object : UIConfigObserver<UIConfig.Status>() {
                    override fun onConfigChanged(value: UIConfig.Status) {
                        Log.d(TAG, "uiConfig observe: $value")
                        notifyConfigChanged(ScreenFoldConfig(value))
                    }
                })

                uiOrientation.observe(activity, object : UIConfigObserver<Int>() {
                    override fun onConfigChanged(value: Int) {
                        Log.d(TAG, "orientation observe: $value")
                        notifyConfigChanged(ScreenOrientationConfig(value))
                    }
                })
                uiScreenSize.observe(activity, object : UIConfigObserver<UIScreenSize>() {
                    override fun onConfigChanged(value: UIScreenSize) {
                        val isFlexibleActivity = isFlexibleActivitySuitable(activity)
                        Log.d(TAG, "activity = $activity, isFlexibleActivity = $isFlexibleActivity")
                        if (isFlexibleActivity) {
                            Log.i(TAG, "isFlexibleActivity, no need update ScreenSizeConfig")
                            return
                        }
                        val uiScreenSize = Size(value.widthDp, value.heightDp)
                        val activityW = activity.resources.configuration.screenWidthDp
                        val activityH = activity.resources.configuration.screenHeightDp
                        if (activityW > value.widthDp) {
                            value.widthDp = activityW
                        }
                        if (activityH > value.heightDp) {
                            value.heightDp = activityH
                        }
                        if (value.widthDp > activityW && !ModelUtils.isTablet()) { // 小屏时首页不支持横屏，保持文件弹框支持横屏，此时传入的宽度实际为高度需要处理
                            value.widthDp = activityW
                        }
                        val screenMode = WindowUtils.getScreenWindowType(value.widthDp, value.heightDp)
                        changeScreenState(screenMode)
                        Log.d(TAG, "uiScreenSize observe: $value mode:$screenMode UIScreenSize:$uiScreenSize} $activity")
                        notifyConfigChanged(ScreenSizeConfig(value))
                    }
                })
            } else {
                uiStatus.removeObservers(activity)
                uiOrientation.removeObservers(activity)
                uiScreenSize.removeObservers(activity)
            }
        }
    }

    private fun isFlexibleActivitySuitable(activity: ComponentActivity): Boolean {
        if (FeatureCompat.isApplicableForFlexibleWindow) {
            return FlexibleWindowManager.isFlexibleActivitySuitable(activity.resources.configuration)
        }
        return false
    }

    private fun monitorZoomWindow(monitor: Boolean = true) {
        if (AddonSdkCompat.isColorZoomWindowInfoSupported()) {
            try {
                OplusZoomWindowManager.getInstance()?.apply {
                    if (monitor) {
                        if (mZoomWindowObserver == null) {
                            mZoomWindowObserver = ZoomWindowObserver {
                                notifyConfigChanged(it)
                            }
                            registerZoomWindowObserver(mZoomWindowObserver!!)
                            (mZoomWindowObserver as ZoomWindowObserver).initZoomWindowInfo()
                        }
                    } else {
                        mZoomWindowObserver?.let { unregisterZoomWindowObserver(it) }
                    }
                }
            } catch (ex: Throwable) {
                Log.d(TAG, "monitorZoomWindow error: $ex")
            }
        }
    }

    @MainThread
    fun attachActivity(activity: ComponentActivity) {
        Log.d(TAG, "attachActivity, $activity")
        if (mResponsiveUIConfig == null) {
            mResponsiveUIConfig = ResponsiveUIConfig.getDefault(activity)
        }
        if (mAttachActivityCount == 0) {
            monitorZoomWindow()
            val screenWidth = mResponsiveUIConfig?.uiScreenSize?.value?.widthDp ?: 0
            val screenHeight = mResponsiveUIConfig?.uiScreenSize?.value?.heightDp ?: 0
            val screenMode = if (WindowUtils.isMiddleAndLargeScreen(activity)) WindowUtils.LARGE else WindowUtils.SMALL
            Log.d(TAG,"attachActivity width:$screenWidth mode:$screenMode")
            changeScreenState(screenMode)
        }
        mAttachActivityCount++
        activity.lifecycle.addObserver(object : BaseLifeController {

            @OnLifecycleEvent(Lifecycle.Event.ON_CREATE)
            fun onCreate() {
                monitorResponsiveUIConfig(activity)
            }

            @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
            fun onResume() {
                parseConfigChange(activity.resources.configuration)
            }

            override fun onDestroy() {
                monitorResponsiveUIConfig(activity, false)
                if (activity is OnUIConfigChangeListener) {
                    removeOnUIConfigChangeListener(activity)
                }
                mAttachActivityCount--
                Log.d(TAG, "attachActivity, attachCount=$mAttachActivityCount")
                if (mAttachActivityCount == 0) {
                    monitorZoomWindow(false)
                    recycle()
                }
            }
        })
    }

    private fun notifyConfigChanged(config: IUIConfig) {
        Log.d(TAG, "notifyConfigChanged, $config")
        synchronized(mChangeConfigMap) {
            Log.d(TAG, "notifyConfigChanged, put in: $config")
            mChangeConfigMap.put(config.javaClass, config)
        }

        mChangeConfigMap.values.find { it is ScreenSizeConfig }?.let {
            if ((getCurrentScreenState() != recordScreenState)) {
                callback.invoke()
                recordScreenState = getCurrentScreenState()
            }
        }

        if (mNotifyChangeRunnable == null) {
            mNotifyChangeRunnable = Runnable {
                synchronized(mChangeConfigMap) {
                    Log.d(TAG, "notifyConfigChanged, start notify, configSize=${mChangeConfigMap.size}")
                    if (mChangeConfigMap.isNotEmpty()) {
                        val configArray = mChangeConfigMap.values
                        mChangeListenerSet?.forEach {
                            it.onUIConfigChanged(configArray)
                        }
                        mChangeConfigMap.clear()
                    }
                }
            }
        } else if (mHandler.hasCallbacks(mNotifyChangeRunnable!!)) {
            Log.d(TAG, "notifyConfigChanged, ignore by callback exist")
            return
        }
        Log.d(TAG, "notifyConfigChanged, post runnable: $config")
        mHandler.postDelayed(mNotifyChangeRunnable!!, DELAY_NOTIFY_TIME)
    }
    var callback: () -> Unit = { }

    @MainThread
    fun addOnUIConfigChangeListener(listener: OnUIConfigChangeListener) {
        if (mChangeListenerSet == null) {
            mChangeListenerSet = ArraySet()
        }
        mChangeListenerSet!!.add(listener)
    }

    @MainThread
    fun removeOnUIConfigChangeListener(listener: OnUIConfigChangeListener) {
        mChangeListenerSet?.remove(listener)
    }

    fun onActivityConfigChanged(config: Configuration) {
        MyApplication.sAppContext.resources.configuration.screenWidthDp = config.screenWidthDp
        mResponsiveUIConfig?.onActivityConfigChanged(config)
        parseConfigChange(config)
    }

    fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean) {
        isMultiWindow = isInMultiWindowMode
    }

    private fun parseConfigChange(config: Configuration) {
        // Parse layout direction
        val layoutDirection = config.layoutDirection
        if (mCurrentLayoutDirection != layoutDirection) {
            mCurrentLayoutDirection = layoutDirection
            Log.d(TAG, "parseConfigChange: layout direction changed=$layoutDirection")
            notifyConfigChanged(LayoutDirectionConfig(layoutDirection))
        }
        // Parse Theme
        val darkThemeLevel = ColorUtil.getDarkModeLevel()
        if (mCurrentDarkModeLevel != darkThemeLevel) {
            mCurrentDarkModeLevel = darkThemeLevel
            Log.d(TAG, "parseConfigChange: dark theme level changed=$darkThemeLevel")
            notifyConfigChanged(DarkThemeLevelConfig(darkThemeLevel))
        }
    }

    fun isScreenFold(): Boolean {
        val value = mResponsiveUIConfig?.uiStatus?.value
        return (value == null) || (value == UIConfig.Status.FOLD)
    }

    fun isFoldable(context: Context): Boolean {
        //判断是否是折叠屏设备
        return NOT_FOLDABLE != Settings.Global.getInt(context.contentResolver, SYSTEM_FOLDING_MODE, NOT_FOLDABLE)
    }

    fun getFoldState(context: Context): Int {
        //判断是否是折叠屏设备
        return Settings.Global.getInt(context.contentResolver, SYSTEM_FOLDING_MODE, NOT_FOLDABLE)
    }

    @VisibleForTesting
    fun recycle() {
        screenChangeState = SCREEN_DEFAULT
        mResponsiveUIConfig = null
        mChangeListenerSet?.clear()
        mChangeListenerSet = null
        mZoomWindowObserver = null
        mHandler.removeCallbacksAndMessages(null)
        mChangeConfigMap.clear()
    }

    fun getZoomWindowState(): OplusZoomWindowInfo? {
        return if (AddonSdkCompat.isColorZoomWindowInfoSupported()) {
            (mZoomWindowObserver as? ZoomWindowObserver)?.getZoomWindowState()
        } else {
            null
        }
    }

    private abstract class UIConfigObserver<T> : Observer<T> {
        private var mInit = true

        final override fun onChanged(value: T) {
            // We not care the change in the first time, because it is the init state
            if (mInit) {
                mInit = false
            } else if (value != null) {
                onConfigChanged(value)
            }
        }

        abstract fun onConfigChanged(value: T)
    }

    /**
     * An anonymous inner class cannot be used, otherwise the obfuscated code will throw the AbstractMethodError exception
     */
    private class ZoomWindowObserver(private val mNotifyMethod: (config: IUIConfig) -> Unit) : IOplusZoomWindowObserver.Stub() {
        // Because Zoom window has a bug: when the zoom window hide, onZoomWindowShow() will be
        // invoked before onZoomWindowHide(), so use this parameter to avoid this bug. It will be
        // fixed in OS12 by zoom window.
        private var mPreZoomWindowState: Boolean? = null
        private var mZoomWindowInfo: OplusZoomWindowInfo? = null

        override fun onZoomWindowShow(info: OplusZoomWindowInfo?) {
            Log.d(TAG, "onZoomWindowShow, $info")
            mZoomWindowInfo = info
            if (mPreZoomWindowState != true) {
                mNotifyMethod.invoke(
                    ZoomWindowConfig(
                        true,
                        (info == null) || (info.rotation == ZOOM_WINDOW_PORTRAIT)
                    )
                )
            }
            mPreZoomWindowState = true
        }

        override fun onZoomWindowHide(info: OplusZoomWindowInfo?) {
            Log.d(TAG, "onZoomWindowHide, $info")
            mZoomWindowInfo = OplusZoomWindowManager.getInstance()?.currentZoomWindowState
            if (mPreZoomWindowState != false) {
                mNotifyMethod.invoke(
                    ZoomWindowConfig(
                        false,
                        (info == null) || (info.rotation == ZOOM_WINDOW_PORTRAIT)
                    )
                )
            }
            mPreZoomWindowState = false
        }

        override fun onZoomWindowDied(appName: String?) {
            Log.d(TAG, "onZoomWindowDied, $appName")
            mZoomWindowInfo = null
            mPreZoomWindowState = false
        }

        override fun onInputMethodChanged(isShown: Boolean) {
            // Input method changes, no need to deal with
            Log.d(TAG, "onInputMethodChanged $isShown")
        }

        fun getZoomWindowState(): OplusZoomWindowInfo? {
            return mZoomWindowInfo
        }

        @Suppress("TooGenericExceptionCaught")
        fun initZoomWindowInfo() {
            mZoomWindowInfo = try {
                OplusZoomWindowManager.getInstance()?.currentZoomWindowState?.let {
                    //The first time you enter window mode, zoomWindowInfo.zoomPkg will be null.
                    val isAnyAppZoomWindow = it.zoomPkg.isNotEmpty()
                    if (isAnyAppZoomWindow) it else null
                }
            } catch (ex: Exception) {
                Log.d(TAG, "ZoomWindowObserver init error: $ex")
                null
            }
            Log.d(TAG, "ZoomWindowObserver initZoomWindowInfo is $mZoomWindowInfo")
        }
    }

    interface OnUIConfigChangeListener {
        fun onUIConfigChanged(configList: MutableCollection<IUIConfig>)
    }

    fun isDevicePortrait(context: Context): Boolean {
        val rotation = if (SdkUtils.isAtLeastR()) {
            context.display?.rotation ?: 0
        } else {
            0
        }
        // 0, 2 device is portrait, 1, 3 device is landscape
        return rotation % 2 == 0
    }

    fun getDeviceRotation(context: Context): Int {
        // 0, 2 device is portrait, 1, 3 device is landscape
        return if (SdkUtils.isAtLeastR()) {
            context.display?.rotation ?: 0
        } else {
            0
        }
    }
}