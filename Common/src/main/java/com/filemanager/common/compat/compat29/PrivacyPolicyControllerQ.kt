/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: PrivacyPolicyController.kt
 ** Description: Check privacy policy
 ** Version: 1.0
 ** Date: 2020/4/17
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.compat.compat29

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.DialogInterface
import android.text.SpannableStringBuilder
import android.text.Spanned
import android.text.method.LinkMovementMethod
import android.view.KeyEvent
import android.view.View
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.COUISecurityAlertDialogBuilder
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.R
import com.filemanager.common.compat.compat30.PrivacyPolicyControllerR
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils

class PrivacyPolicyControllerQ internal constructor() : PrivacyPolicyController() {
    companion object {
        private const val TAG = "PrivacyPolicyControllerQ"
        @VisibleForTesting
        const val SP_KEY_CTA_DIALOG = "cta_dialog_should_show"
        @VisibleForTesting
        const val SP_NAME = "local_config"

        /**
         * Check whether the user agrees to the terms of use
         * FEATURE 2725463: only domestic need to show at first time launch, export no need this
         */
        fun hasAgreePrivacy(): Boolean {
            return hasAgreePrivacy(MyApplication.sAppContext)
        }

        fun hasAgreePrivacy(context: Context): Boolean {
            return if (FeatureCompat.sIsExpRom) {
                if (FeatureCompat.sIsSupportRuntimePermissionAlert) {
                    PreferencesUtils.getBoolean(context, SP_NAME, SP_KEY_CTA_DIALOG, false).not()
                } else {
                    true
                }
            } else {
                PrivacyPolicyControllerR.hasAgreePrivacy(context, false)
            }
        }

        fun saveAgreePrivacy() {
            Log.d(TAG, "saveStatementDialogState()")
            if (FeatureCompat.sIsExpRom) {
                PreferencesUtils.put(SP_NAME, SP_KEY_CTA_DIALOG, false)
            } else {
                PrivacyPolicyControllerR.saveAgreePrivacy()
            }
        }

        /**
         * preload PrivacyPolicy SharePreference
         */
        fun preloadPrivacyPolicySharePreference(context: Context) {
            if (FeatureCompat.sIsExpRom) {
                if (FeatureCompat.sIsSupportRuntimePermissionAlert) {
                    context.getSharedPreferences(SP_NAME, Context.MODE_PRIVATE)
                }
            } else {
                PrivacyPolicyControllerR.preloadPrivacyPolicySharePreference(context)
            }
        }
    }

    override fun createPolicyDialog(activity: Activity, listener: OnPrivacyPolicyListener): Dialog {
        return if (FeatureCompat.sIsExpRom) {
            createExpPolicyDialog(activity, listener)
        } else {
            createInternalPolicyDialog(activity, listener)
        }
    }

    private fun createExpPolicyDialog(activity: Activity, listener: OnPrivacyPolicyListener): Dialog {
        fun shouldShowStatementLink(): Boolean {
            val region = PropertyCompat.sPhoneMarkRegion
            Log.d(TAG, "shouldShowStatementLink: region=$region")
            return "EUEX".equals(region, true) || "JP".equals(region, true)
        }

        Log.d(TAG, "createStatementDialog()")
        val msg = activity.getString(R.string.text_runtime_dialog_disc, activity.getString(R.string.app_name),
                activity.getString(R.string.text_runtime_write_external_storage))
        var agree = false
        var noLongerRemind = true
        val builder = COUISecurityAlertDialogBuilder(activity).apply {
            setTitle(R.string.title_runtime_dialog_title)
            setMessage(msg)
            setChecked(true)
            setHasCheckBox(true)
            setPositiveString(R.string.btn_runtime_dialog_ok)
            setNegativeString(R.string.btn_runtime_dialog_cancel)
            setOnSelectedListener { whichButton, isCheck ->
                if (whichButton == DialogInterface.BUTTON_POSITIVE) {
                    agree = true
                    noLongerRemind = isCheck
                } else if (whichButton == DialogInterface.BUTTON_NEGATIVE) {
                    activity.finish()
                }
            }
            setStatementLinkString(R.string.text_security_alertdialog_statement, R.string.title_security_alertdialog_privacy)
            setShowStatementText(shouldShowStatementLink())
            setOnLinkTextClickListener {
                PermissionUtils.openPrivacyPolicy(activity)
            }
        }
        val dialog = builder.create().apply {
            setCanceledOnTouchOutside(false)
            setOnDismissListener {
                listener.onAgreeResult(agree, noLongerRemind)
            }
        }
        return dialog
    }

    private fun createInternalPolicyDialog(activity: Activity, listener: OnPrivacyPolicyListener): Dialog {
        fun getStatementSequence(activity: Activity, privacyLink: TextView): CharSequence? {
            val linkString: String = activity.getString(R.string.file_manager_permission_privacy)
            var statementString: String = activity.getString(R.string.permission_privacy_declare_content,
                    activity.getString(R.string.color_runtime_internet),
                    activity.getString(R.string.color_runtime_external_storage),
                    linkString)
            if (Utils.isRtl()) {
                statementString = RTL_SYMBOL + statementString + RTL_SYMBOL
            }
            val termsIndex = statementString.indexOf(linkString)
            val termsLength = linkString.length
            val span = COUIClickableSpan(activity)
            span.setStatusBarClickListener {
                PermissionUtils.openPrivacyPolicy(activity)
            }
            val spannableStringBuilder = SpannableStringBuilder(statementString)
            spannableStringBuilder.setSpan(span, termsIndex, termsIndex + termsLength,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            return spannableStringBuilder
        }

        // ------------------------------------------
        val view: View = activity.layoutInflater.inflate(R.layout.privacy_content, null)
        val privacyLink = view.findViewById<View>(R.id.privacy_link) as TextView
        var title: String = activity.getString(R.string.privacy_policy_declare_title)
        var privacyPolicyLink: String = activity.getString(R.string.privacy_policy_link)
        if (Utils.isRtl()) {
            title = RTL_SYMBOL + title
            privacyPolicyLink += RTL_SYMBOL
        }
        privacyLink.text = privacyPolicyLink
        val privacyContent = view.findViewById<View>(R.id.privacy_policy_text) as TextView
        privacyContent.text = getStatementSequence(activity, privacyContent)
        privacyContent.highlightColor = activity.resources.getColor(android.R.color.transparent)
        privacyContent.movementMethod = LinkMovementMethod.getInstance()
        var agree = false
        val builder: COUIAlertDialogBuilder = COUIAlertDialogBuilder(activity).apply {
            setTitle(title)
            setView(view)
            setPositiveButton(R.string.privacy_policy_ok) { dialog, which ->
                agree = true
            }
            setNegativeButton(R.string.btn_runtime_dialog_cancel) { dialog, which ->
                activity.finish()
            }
            setCancelable(false)
            setOnKeyListener { dialog, keyCode, event ->
                if (keyCode == KeyEvent.KEYCODE_BACK
                    && event.action == KeyEvent.ACTION_DOWN) {
                    dialog.dismiss()
                    activity.finish()
                }
                false
            }
            setOnDismissListener {
                if (agree) {
                    processKdocAndTencentSwitchPreference()
                }
                listener.onAgreeResult(agree)
            }
        }
        return builder.create()
    }
}