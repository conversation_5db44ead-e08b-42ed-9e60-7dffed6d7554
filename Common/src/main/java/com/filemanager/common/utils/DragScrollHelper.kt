/***********************************************************
 ** Copyright (C), 2020-2025 Oplus. All rights reserved.
 ** File:  - DragScrollHelper.kt
 ** Description: Scroll the page while dragging
 ** Version: 1.0
 ** Date : 2025/04/10
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  zhangyitong  2025/04/10    1.0        create
 ****************************************************************/
package com.filemanager.common.utils

import android.view.DragEvent
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication

class DragScrollHelper(private var recyclerView: RecyclerView?) {

    private var dragY = -1f
    private var limitBottomArea: Int = 0
    private val pixelSize = resources().getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_40dp)
    private val bottomSize = resources().getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_72dp)

    private val breenoOffset = resources().getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_92dp)
    private val screenHeight = WindowUtils.getScreenHeight(MyApplication.appContext)
    private val navigationBarHeight = resources().getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_16dp)

    private val scrollRunnable = object : Runnable {
        override fun run() {
            if (dragY != -1f && scrollIfNecessary()) {
                recyclerView?.removeCallbacks(this)
                recyclerView?.postOnAnimation(this)
            }
        }
    }

    private var scrollOffsetBefore = recyclerView?.computeVerticalScrollOffset()
    private var scrollOffsetAfter = recyclerView?.computeVerticalScrollOffset()

    private fun scrollIfNecessary(): Boolean {
        recyclerView?.let {
            if (dragY == -1f) {
                return false
            }
            val recyclerViewLocation = IntArray(2)
            it.getLocationOnScreen(recyclerViewLocation)
            if (dragY < recyclerViewLocation[1] + pixelSize) {
                scrollOffsetBefore = it.computeVerticalScrollOffset()
                it.scrollBy(0, -STEP_SIZE_Y)
                scrollOffsetAfter = it.computeVerticalScrollOffset()
                return true
            }
            val topViewLimitArea = it.y + it.height
            if (limitBottomArea > 0) {
                val isLimitBottomTop = dragY > (limitBottomArea - breenoOffset - pixelSize) && dragY < limitBottomArea
                val isLimitBottomBottom = dragY > (screenHeight - navigationBarHeight)
                if (isLimitBottomTop || isLimitBottomBottom) {
                    scrollOffsetBefore = it.computeVerticalScrollOffset()
                    it.scrollBy(0, STEP_SIZE_Y)
                    scrollOffsetAfter = it.computeVerticalScrollOffset()
                    return true
                }
            } else {
                if (dragY > (topViewLimitArea - bottomSize)) {
                    scrollOffsetBefore = it.computeVerticalScrollOffset()
                    recyclerView?.scrollBy(0, STEP_SIZE_Y)
                    scrollOffsetAfter = it.computeVerticalScrollOffset()
                    return true
                }
            }
            scrollOffsetBefore = 0
            scrollOffsetAfter = 0
        }
        return false
    }

    fun setBottomView(position: Int) {
        limitBottomArea = position
    }

    fun handleDragScroll(event: DragEvent?) {
        Log.d(TAG, "handleDragScroll")
        when (event?.action) {
            DragEvent.ACTION_DRAG_LOCATION -> handlePageScroll(event)

            DragEvent.ACTION_DRAG_ENDED, DragEvent.ACTION_DRAG_EXITED, DragEvent.ACTION_DROP -> resetDragStatus()
        }
    }

    private fun handlePageScroll(event: DragEvent) {
        recyclerView?.let {
            dragY = event.y
            it.removeCallbacks(scrollRunnable)
            scrollRunnable.run()
            it.invalidate()
        }
    }

    fun getRecyclerViewScrollState(): Boolean {
        return scrollOffsetBefore != scrollOffsetAfter
    }

    fun resetDragStatus() {
        dragY = -1f
    }

    companion object {
        private const val TAG = "DragScrollHelper"
        private const val STEP_SIZE_Y = 5
    }
}