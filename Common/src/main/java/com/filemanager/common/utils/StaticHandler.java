/***********************************************************
** Copyright (C), 2008-2017 Oplus. All rights reserved.
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.filemanager.common.utils;

import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import java.lang.ref.WeakReference;

public class StaticHandler<T> extends <PERSON><PERSON> {
    private static final String TAG = "StaticHandler";
    protected WeakReference<T> mRef;

    public StaticHandler(T t) {
        super();
        mRef = new WeakReference<T>(t);
    }

    public StaticHandler(T t, Looper looper) {
        super(looper);
        mRef = new WeakReference<T>(t);
    }

    @Override
    public void handleMessage(Message msg) {
        final T t = mRef.get();
        if (t == null) {
            Log.w(TAG, "ref.get is null.");
            return;
        }
        handleMessage(msg, t);
        super.handleMessage(msg);
    }

    protected void handleMessage(Message msg, T t) {
    }

    public void recycle() {
        removeCallbacksAndMessages(null);
        if (mRef != null) {
            mRef.clear();
        }
        mRef = null;
    }
}
