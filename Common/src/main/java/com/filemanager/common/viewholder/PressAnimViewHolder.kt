/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.filemanager.common.viewholder
 * * Version     : 1.0
 * * Date        : 2021/8/4
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.view.MotionEvent
import android.view.View
import android.view.animation.Animation
import android.view.animation.PathInterpolator
import android.view.animation.ScaleAnimation
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIAnimationListenerAdapter

open class PressAnimViewHolder(contentView: View) : RecyclerView.ViewHolder(contentView) {
    companion object {
        private const val DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION = 200L
        private const val DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION = 355L
        private const val DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE = 0.95f
        private const val DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE = 1f
        private const val DEFAULT_TARGET_GUARANTEED_VALUE_THRESHOLD_HEIGHT = 600
        private const val BIG_CARD_GUARANTEE_VALUE_THRESHOLD_PERCENTAGE = 0.07f
        private const val SMALL_CARD_GUARANTEE_VALUE_THRESHOLD_PERCENTAGE = 0.35f
        private const val DEFAULT_GUARANTEE_VALUE_THRESHOLD_PERCENTAGE = 0.10f
        private const val DEFAULT_FLOATING_BUTTON_HEIGHT = 156
        private val PRESS_FEEDBACK_INTERPOLATOR = PathInterpolator(0.4f, 0f, 0.2f, 1f)

        fun generatePressAnimationRecord(): ValueAnimator {
            val pressAnimationRecord = ValueAnimator.ofFloat(
                DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE,
                DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE
            )
            pressAnimationRecord.duration = DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION
            pressAnimationRecord.interpolator = PRESS_FEEDBACK_INTERPOLATOR
            return pressAnimationRecord
        }

        fun generatePressAnimation(target: View): ScaleAnimation {
            val pressFeedbackAnimation = ScaleAnimation(
                DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE,
                DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE,
                DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE,
                DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE,
                target.width / 2f,
                target.height / 2f
            )
            pressFeedbackAnimation.duration = DEFAULT_PRESS_FEEDBACK_ANIMATION_DURATION
            pressFeedbackAnimation.fillAfter = true
            pressFeedbackAnimation.interpolator = PRESS_FEEDBACK_INTERPOLATOR
            return pressFeedbackAnimation
        }

        /**
         * The recovery animation of the pressing feedback animation, the starting value of the current
         * animation is determined according to the degree of execution of the pressing animation
         *
         * @param target View that require additional press feedback animation effects
         * @throws IllegalArgumentException The given view is empty. Please provide a valid view
         * @return Resume animation for press feedback
         */
        fun generateResumeAnimation(target: View, animationStartValue: Float): ScaleAnimation {
            val pressFeedbackToNormalAnimation = ScaleAnimation(
                animationStartValue,
                DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE,
                animationStartValue,
                DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE,
                target.width / 2f,
                target.height / 2f
            )
            pressFeedbackToNormalAnimation.duration = DEFAULT_RELEASE_FEEDBACK_ANIMATION_DURATION
            pressFeedbackToNormalAnimation.fillAfter = true
            pressFeedbackToNormalAnimation.interpolator = PRESS_FEEDBACK_INTERPOLATOR
            return pressFeedbackToNormalAnimation
        }

        /**
         * Determine the degree of bottom-preservation animation according to the size of the View that
         * needs to perform the animation of pressing feedback
         *
         * @param target View that require additional press feedback animation effects
         * @throws IllegalArgumentException The given view is empty. Please provide a valid view
         * @return Resume animation for press feedback
         */
        fun getGuaranteedAnimationValue(target: View): Float {
            return when {
                target.height >= DEFAULT_TARGET_GUARANTEED_VALUE_THRESHOLD_HEIGHT -> {
                    DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE -
                            (DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE - DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE) * BIG_CARD_GUARANTEE_VALUE_THRESHOLD_PERCENTAGE
                }
                target.height >= DEFAULT_FLOATING_BUTTON_HEIGHT -> {
                    DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE -
                            (DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE - DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE) * SMALL_CARD_GUARANTEE_VALUE_THRESHOLD_PERCENTAGE
                }
                else -> {
                    DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE -
                            (DEFAULT_PRESS_FEEDBACK_ANIMATION_START_VALUE - DEFAULT_PRESS_FEEDBACK_ANIMATION_END_VALUE) * DEFAULT_GUARANTEE_VALUE_THRESHOLD_PERCENTAGE
                }
            }
        }
    }

    var isEdit = false
    var mIcon: ImageView? = null
    var pressAnimRecorder: ValueAnimator = generatePressAnimationRecord()
    var pressAnimValue = 0f
    var animationListener = ValueAnimator.AnimatorUpdateListener { animation ->
        mIcon?.apply {
            val guaranteedAnimationValue = getGuaranteedAnimationValue(this)
            pressAnimValue = animation?.animatedValue as Float
            if (pressAnimValue >= guaranteedAnimationValue) {
                pressAnimValue = guaranteedAnimationValue
            }
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    fun setImageViewTouchListener() {
        mIcon?.setOnTouchListener { _: View, motionEvent: MotionEvent ->
            if (isEdit) {
                return@setOnTouchListener false
            }
            when (motionEvent.action) {
                MotionEvent.ACTION_DOWN -> {
                    pressAnimRecorder.takeIf { it.isRunning }?.cancel()
                    animatePress(pressAnimRecorder)
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    pressAnimRecorder.takeIf { it.isRunning }?.cancel()
                    animateNormal(pressAnimValue)
                }
            }
            return@setOnTouchListener false
        }
    }

    fun setImageViewAttachStateListener() {
        pressAnimRecorder.addUpdateListener(animationListener)
        mIcon?.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
            override fun onViewAttachedToWindow(view: View) {
            }

            override fun onViewDetachedFromWindow(view: View) {
                pressAnimRecorder.removeAllUpdateListeners()
                view?.clearAnimation()
                view?.removeOnAttachStateChangeListener(this)
            }
        })
    }

    private fun animatePress(valueAnimator: ValueAnimator) {
        mIcon?.apply {
            clearAnimation()
            startAnimation(generatePressAnimation(this).apply {
                setAnimationListener(object : COUIAnimationListenerAdapter() {
                    override fun onAnimationStart(animation: Animation) {
                        valueAnimator.start()
                    }
                })
            })
        }
    }

    private fun animateNormal(pressValue: Float) {
        mIcon?.apply {
            clearAnimation()
            startAnimation(generateResumeAnimation(this, pressValue))
        }
    }
}