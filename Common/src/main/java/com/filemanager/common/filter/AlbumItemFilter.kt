/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AlbumItemFilter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/9/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/9/11       1      create
 ***********************************************************************/
package com.filemanager.common.filter

import android.provider.MediaStore
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.AlbumItem
import com.filemanager.common.utils.Log
import org.apache.commons.io.FilenameUtils
import java.io.File

class AlbumItemFilter : IFilter<AlbumItem> {

    companion object {
        private const val TAG = "AlbumItemFilter"
    }

    /**
     * 如果 AlbumItem 的 CoverPath 为 psd，需要将其过滤掉
     * 1.将AlbumItem 的 CoverPath 改为其他图片的 CoverPath
     * 2.将AlbumItem 的 type 改为 [MimeTypeHelper.IMAGE_TYPE]
     */
    override fun filterTarget(target: String, list: List<AlbumItem>): List<AlbumItem> {
        val filterList = list
            .filter { it.getCoverPath().isNullOrEmpty().not() }
            .map { albumItem ->
                val coverPath = albumItem.getCoverPath()
                val ext = FilenameUtils.getExtension(coverPath)
                if (IFilter.isTargetType(target, ext)) {
                    val name = FilenameUtils.getName(coverPath)
                    val dirPath = coverPath.split(name)[0]
                    val newCoverPath = queryImagesByPath(target, dirPath)
                    albumItem.apply {
                        setCoverPath(newCoverPath)
                        type = MimeTypeHelper.IMAGE_TYPE
                    }
                }
                albumItem
            }.filter {
                it.getCoverPath().isNullOrEmpty().not()
            }
        Log.d(TAG, "filterTarget origin size:${list.size} filter size:${filterList.size}")
        return filterList as ArrayList<AlbumItem>
    }

    @Suppress("TooGenericExceptionCaught")
    private fun queryImagesByPath(target: String, path: String): String {
        val projection = arrayOf(MediaStore.Images.Media.DATA)
        val selection = StringBuilder().append(MediaStore.Images.Media.DATA)
            .append(" LIKE ?")
            .toString()
        val selectionArg = arrayOf("$path%")
        val order = "${MediaStore.Images.Media.DATE_MODIFIED} DESC"
        try {
            MyApplication.sAppContext.contentResolver.query(
                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                projection,
                selection,
                selectionArg,
                order
            )?.use { cursor ->
                while (cursor.moveToNext()) {
                    val data = cursor.getString(0)
                    val ext = FilenameUtils.getExtension(data)
                    if (IFilter.isTargetType(target, ext).not()) {
                        if (path.equals(data.substring(0, data.lastIndexOf(File.separator) + 1), true)) {
                            return data
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "queryImagesByPath e: ${e.message}")
        }
        return ""
    }
}