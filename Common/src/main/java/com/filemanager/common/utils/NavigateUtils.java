/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils;


import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager.NameNotFoundException;
import android.media.RingtoneManager;
import android.text.TextUtils;

import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;


public class NavigateUtils {
    private static final String TAG = "NavigateUtils";
    public static final String NAVIGATE_UP_TITLE_ID = "navigate_title_id";
    public static final String NAVIGATE_UP_TITLE_TEXT = "navigate_title_text";
    public static final String NAVIGATE_UP_PACKAGE = "navigate_parent_package";

    public static void setNavigateTitle(AppCompatActivity activity, Intent intent) {
        ActionBar actionBar = activity.getSupportActionBar();
        actionBar.setDisplayHomeAsUpEnabled(true);
        setNavigateTitle(activity, actionBar, intent);
    }


    public static void setTitle(Context context, ActionBar actionBar, Intent intent) {
        if ((intent == null) || (actionBar == null)) {
            Log.i(TAG, "intent or action bar is null");
            return;
        }
        String contentDescribe = IntentUtils.getString(intent, RingtoneManager.EXTRA_RINGTONE_TITLE);

        if (TextUtils.isEmpty(contentDescribe)) {
            contentDescribe = getTitleById(context, intent);
        }

        Log.i(TAG, "contentDescribe " + contentDescribe);
        if (!TextUtils.isEmpty(contentDescribe)) {
            actionBar.setTitle(contentDescribe);
        }
    }

    private static String getTitleById(Context context, Intent intent) {
        if ((null == context) || (null == intent)) {
            return null;
        }
        int id = IntentUtils.getInt(intent, RingtoneManager.EXTRA_RINGTONE_TITLE, 0);
        Log.d(TAG, "getTitleById: id = " + id);
        if (0 != id) {
            String packageName = IntentUtils.getString(intent, NAVIGATE_UP_PACKAGE);
            if (!TextUtils.isEmpty(packageName) && !packageName.equals(context.getPackageName())) {
                Context parentContext = null;

                try {
                    parentContext = context.createPackageContext(packageName,
                            Context.CONTEXT_INCLUDE_CODE | Context.CONTEXT_IGNORE_SECURITY);
                } catch (NameNotFoundException e) {
                    // Ignore.
                }

                if (null != parentContext) {
                    return parentContext.getResources().getString(id);
                }
            } else {
                return context.getResources().getString(id);
            }
        }

        return null;
    }

    public static void setNavigateTitle(Context context, ActionBar actionBar, Intent intent) {
        if ((intent == null) || (actionBar == null)) {
            Log.i(TAG, "intent or action bar is null");
            return;
        }
        String contentDescribe = IntentUtils.getString(intent, NAVIGATE_UP_TITLE_TEXT);

        if (TextUtils.isEmpty(contentDescribe)) {
            contentDescribe = getContentDescribeById(context, intent);
        }

        Log.i(TAG, "contentDescribe " + contentDescribe);
        if (!TextUtils.isEmpty(contentDescribe)) {
            // ColorActionBarUtil.setBackTitle(actionBar, contentDescribe);
        } else {
            actionBar.setDisplayHomeAsUpEnabled(false);
        }
    }

    private static String getContentDescribeById(Context context, Intent intent) {
        if ((null == context) || (null == intent)) {
            return null;
        }
        int id = IntentUtils.getInt(intent, NAVIGATE_UP_TITLE_ID, 0);
        Log.d(TAG, "getContentDescribeById: id = " + id);
        if (0 != id) {
            String packageName = IntentUtils.getString(intent, NAVIGATE_UP_PACKAGE);
            if (!TextUtils.isEmpty(packageName) && !packageName.equals(context.getPackageName())) {
                Context parentContext = null;

                try {
                    parentContext = context.createPackageContext(packageName,
                            Context.CONTEXT_INCLUDE_CODE | Context.CONTEXT_IGNORE_SECURITY);
                } catch (NameNotFoundException e) {
                    // Ignore.
                }

                if (null != parentContext) {
                    return parentContext.getResources().getString(id);
                }
            } else {
                return context.getResources().getString(id);
            }
        }

        return null;
    }
}
