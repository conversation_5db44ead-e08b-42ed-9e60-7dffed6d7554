/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - $file$
 ** Description:
 ** Version: 1.0
 ** Date : 2023/7/5
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  wanghognlei      2023/7/5      1.0     create file
 ****************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.graphics.Color
import android.view.View
import android.view.Window
import androidx.annotation.VisibleForTesting
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.filemanager.common.R

class NavigationBarHelper {

    @VisibleForTesting
    internal var taskbarColor: Int? = null
    @VisibleForTesting
    internal var taskbarLimit: Int? = null

    fun addInsetsCallback(view: View, window: Window, block: ((View, WindowInsetsCompat, Boolean) -> Unit)?) {
        ViewCompat.setOnApplyWindowInsetsListener(view, object : DeDuplicateInsetsCallback() {
            override fun onApplyInsets(v: View, insets: WindowInsetsCompat) {
                val navigationBars = insets.getInsetsIgnoringVisibility(WindowInsetsCompat.Type.navigationBars())
                updateNavigationBarColor(v.context, window, navigationBars.bottom)
                block?.invoke(v, insets, navigationBars.bottom > getNavigationBarLimit(v.context))
            }
        })
    }

    /**
     * 更新NavigationBar的颜色,Taskbar是NavigationBar内部的元素，高度相同
     * 系统默认的NavigationBar高度为48dp，设置提供的技术方案使用30dp进行NavigationBar显示隐藏的判断
     */
    fun updateNavigationBarColor(context: Context, window: Window, height: Int) {
        val limit = getNavigationBarLimit(context)
        val color = if ((height > limit)) {
            window.isNavigationBarContrastEnforced = true
            getNavigationBarColor(context)
        } else {
            window.isNavigationBarContrastEnforced = false
            Color.TRANSPARENT
        }
        window.navigationBarColor = color
    }

    @VisibleForTesting
    internal fun getNavigationBarColor(context: Context): Int {
        val color = taskbarColor ?: StatusBarUtil.getNavBarColor(context)
        taskbarColor = color
        return color
    }

    @VisibleForTesting
    internal fun getNavigationBarLimit(context: Context): Int {
        val limit = taskbarLimit ?: context.resources.getDimensionPixelSize(R.dimen.navigation_gesture_taskbar_height)
        taskbarLimit = limit
        return limit
    }
}