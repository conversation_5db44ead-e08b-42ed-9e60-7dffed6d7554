/*********************************************************************
 * Copyright (C), 2022-2029 Oplus. All rights reserved..
 * VENDOR_EDIT
 * File        : RoundedCornersBitmapTransformation
 * Description :
 * Version     : 1.0
 * Date        : 2022/4/15
 * Author      : <EMAIL>
 *
 * ---------------------Revision History: ----------------------------
 * <author>        <date>       <version>  <desc>
 * dustin.shu      2022/4/15      1.0        create
</desc></version></date></author> */
package com.filemanager.common.imageloader.glide

import android.graphics.Bitmap
import android.graphics.BitmapShader
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Shader
import com.bumptech.glide.load.engine.bitmap_recycle.BitmapPool
import com.bumptech.glide.load.resource.bitmap.BitmapTransformation
import com.bumptech.glide.load.resource.bitmap.TransformationUtils
import java.security.MessageDigest

class RoundedCornersBitmapTransformation(
    private var radius: Float,
    private var isRtl: Boolean,
    private var tl: Boolean,
    private var tr: Boolean,
    private var bl: Boolean,
    private var br: Boolean,
    private var strokeWidth: Float = 0f,
    private var borderColor: Int = 0
) : BitmapTransformation() {

    private val id = javaClass.name
    private val idBytes = id.toByteArray(Charsets.UTF_8)

    override fun transform(
        pool: BitmapPool,
        toTransform: Bitmap,
        outWidth: Int,
        outHeight: Int
    ): Bitmap? {
        val bitmap = TransformationUtils.centerCrop(pool, toTransform, outWidth, outHeight)
        return roundCrop(pool, bitmap)
    }

    override fun equals(other: Any?): Boolean {
        if (other is RoundedCornersBitmapTransformation) {
            return radius == other.radius && isRtl == other.isRtl && tl == other.tl && tr == other.tr && bl == other.bl && br == other.br
        }
        return false
    }

    override fun updateDiskCacheKey(messageDigest: MessageDigest) {
        messageDigest.update(idBytes)
    }

    private fun roundCrop(pool: BitmapPool, source: Bitmap?): Bitmap? {
        if (source == null) return null
        var result: Bitmap? = pool[source.width, source.height, Bitmap.Config.ARGB_8888]
        if (result == null) {
            result = Bitmap.createBitmap(source.width, source.height, Bitmap.Config.ARGB_8888)
        }
        val canvas = Canvas(result!!)
        val paint = Paint()
        paint.shader = BitmapShader(source, Shader.TileMode.CLAMP, Shader.TileMode.CLAMP)
        paint.isAntiAlias = true
        drawSmoothRoundCorners(canvas, source, paint)

        if (strokeWidth > 0) {
            val borderPaint = Paint()
            borderPaint.isAntiAlias = true
            borderPaint.color = borderColor
            borderPaint.strokeWidth = strokeWidth
            borderPaint.style = Paint.Style.STROKE
            drawRoundCornersBorder(canvas, source, borderPaint.strokeWidth / 2, borderPaint)
        }
        return result
    }

    private fun drawSmoothRoundCorners(
        canvas: Canvas,
        source: Bitmap,
        paint: Paint
    ) {
        val save = canvas.save()
        val path = RoundRectUtil.getPath(
            0f,
            0f,
            source.width.toFloat(),
            source.height.toFloat(),
            radius,
            if (isRtl) tr else tl,
            if (isRtl) tl else tr,
            if (isRtl) br else bl,
            if (isRtl) bl else br
        )
        canvas.drawPath(path, paint)
        canvas.restoreToCount(save)
    }

    private fun drawRoundCornersBorder(
        canvas: Canvas,
        source: Bitmap,
        strokeWith: Float,
        paint: Paint
    ) {
        val save = canvas.save()
        val path = RoundRectUtil.getPath(
            strokeWith,
            strokeWith,
            source.width.toFloat() - strokeWith,
            source.height.toFloat() - strokeWith,
            radius.toFloat(),
            tl = true,
            tr = true,
            bl = true,
            br = true
        )
        canvas.drawPath(path, paint)
        canvas.restoreToCount(save)
    }

    override fun hashCode(): Int {
        var result = radius.hashCode()
        result = 31 * result + isRtl.hashCode()
        result = 31 * result + tl.hashCode()
        result = 31 * result + tr.hashCode()
        result = 31 * result + bl.hashCode()
        result = 31 * result + br.hashCode()
        return result
    }
}