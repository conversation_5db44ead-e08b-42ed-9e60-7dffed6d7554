/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved..
 * * File:LoadingController.kt
 * * Description:Loading View Helper
 * * By default, the display will be delayed by 100ms.
 * * Once displayed, it will be displayed for at least 500ms, and then it will be turned off to avoid flickering.
 * * Version:1.0
 * * Date :2020/6/3
 * * Author:********
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 * * ZeJiang.Duan,  2020/6/3,        v1.0,           Create
 ****************************************************************/
package com.filemanager.common.controller

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.os.Handler
import android.os.Message
import android.os.SystemClock
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.PathInterpolator
import android.widget.FrameLayout
import android.widget.RelativeLayout
import androidx.activity.ComponentActivity
import androidx.annotation.ColorInt
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.core.view.contains
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.Observer
import androidx.lifecycle.OnLifecycleEvent
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.coui.appcompat.progressbar.COUICompProgressIndicator
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StaticHandler
import com.filemanager.common.utils.dp2px
import com.filemanager.common.utils.stringResource
import java.lang.ref.WeakReference


class LoadingController(
    activity: ComponentActivity,
    lifecycleOwner: LifecycleOwner,
    private val lockFrameLayoutVertical: Boolean = false
) : View.OnClickListener, BaseLifeController, FolderTransformAnimator.INeedSkipAnimator {
    private val mLoadingHandler: Handler = LoadingHandler(this)
    private var mWeakRefAct: WeakReference<ComponentActivity>? = null
    private var mLoadingDialog: AlertDialog? = null
    private var mParentLayout: ViewGroup? = null
    private var mLoadingLayout: View? = null
    private var mLoadingView: COUICompProgressIndicator? = null
    private var mLoadingShowStartTime = 0L
    private var mAutoCloseDelay = SHOW_LOADING_AUTO_CLOSE_TIME
    private var mIsShowing = false
    private var mAutoClose = false
    private var mCancelButton = false
    private var mIsDialog = false
    private var mCancelableOut: Boolean? = null
    private var mTipMsg: String? = null
    private var mTipMsgId: Int? = null
    private var mLifecycleOwner: LifecycleOwner? = null
    private var backgroundColor: Int? = null
    private var delayShowTime: Long = -1L
    private var showAnimate: Boolean = false
    private var endAnimation: Float = 0f
    private var disappearAnimate: Boolean = false
    private var loadingShowTips: Boolean = true

    private var showAinimatorRunning = false
    private var dismissAnimatorRunning = false

    companion object {
        private const val TAG = "LoadingController"
        const val SHOW_LOADING_DELAY_TIME = 100L
        const val SHOW_LOADING_AUTO_CLOSE_TIME = 3 * 60 * 1000L
        const val LOADING_SHOW_MIN_TIME = 500L

        const val MSG_CLOSE_LOADING = 80
        const val MSG_SHOW_LOADING_DELAY = 81
        const val MSG_SHOW_LOADING_NOW = 82
        const val MSG_AUTO_CLOSE_LOADING = 83
        const val ALPHA_ANIMATE_DURATION = 200L

        const val LOAD_SHOW_ANIMATOR_X1 = 0.33f
        const val LOAD_SHOW_ANIMATOR_Y1 = 0f
        const val LOAD_SHOW_ANIMATOR_X2 = 0.72f
        const val LOAD_SHOW_ANIMATOR_Y2 = 1f
    }

    init {
        mWeakRefAct = WeakReference(activity)
        mLifecycleOwner = lifecycleOwner.apply {
            lifecycle.addObserver(this@LoadingController)
        }
    }

    /**
     * Start observe the loading state.
     * @param loadState the loading live data
     * @param parent the loading view's parent view
     * @param showInterceptor the method used to intercept the show work when load is start, return
     *                        true mean the loading view not need to be shown
     */
    fun observe(loadState: MutableLiveData<Int>?, parent: ViewGroup? = null,
                showInterceptor: (() -> Boolean)? = null) {
        if ((mLifecycleOwner != null) && (loadState != null)) {
            loadState.observe(mLifecycleOwner!!, Observer<Int> {
                //Log.d(TAG, "LOAD STATE CHANGE $it")
                when (it) {
                    OnLoaderListener.STATE_START -> if (showInterceptor?.invoke() != true) {
                        if (parent != null) {
                            showLoading(parent)
                        } else {
                            showLoading()
                        }
                    }
                    else -> dismissLoading(true)
                }
            })
        }
    }

    fun showLoading(): LoadingController {
        checkShowLoading(false, null, delayShowTime)
        return this
    }

    fun showLoading(isDialog: Boolean): LoadingController {
        checkShowLoading(isDialog, null, delayShowTime)
        return this
    }

    fun getLoadingShowStartTime(): Long = mLoadingShowStartTime

    fun isShowing(): Boolean = mIsShowing

    fun showLoading(parent: ViewGroup?): LoadingController {
        checkShowLoading(false, parent, delayShowTime)
        return this
    }

    private fun checkShowLoading(isDialog: Boolean, parent: ViewGroup?, delay: Long = -1L) {
        if (mIsShowing) {
            Log.w(TAG, "Loading is showing, please try again after dismiss ")
            if (mIsDialog != isDialog) {
                checkHiddenLoading()
            } else {
                mLoadingShowStartTime = SystemClock.elapsedRealtime()
            }
            return
        }
        mParentLayout = parent
        mIsDialog = isDialog
        if (delay != -1L) {
            showLoadingDelay(delay)
        } else {
            showLoadingDelay(SHOW_LOADING_DELAY_TIME)
        }
    }

    fun dismissLoading(isDismissNow: Boolean = false) {
        if (isDismissNow) {
            realHiddenLoading()
        } else {
            checkHiddenLoading()
        }
    }

    /**
     * @param [resId] Tips for LoadingHelper
     * Note: Loading Dialog displays "Loading data..." by default, which can be changed by this method.
     * Loading View does not display prompts by default
     */
    fun setTip(resId: Int): LoadingController {
        mTipMsgId = resId
        try {
            setTip(MyApplication.sAppContext.getString(resId))
        } catch (e: Exception) {
            Log.e(TAG, "setTip resId error")
        }
        return this
    }

    /**
     * @param [tipMsg] Tips for LoadingHelper
     * Note: Loading Dialog displays "Loading data..." by default, which can be changed by this method.
     * Loading View does not display prompts by default
     */
    fun setTip(tipMsg: String): LoadingController {
        mTipMsg = tipMsg
        return this
    }

    /**
     * @param [isAutoClose] Whether to close automatically, default false
     * @param [delayMillis] If it closes automatically, how long (Millis) will it close automatically
     */
    fun setAutoCloseAfterTime(isAutoClose: Boolean, delayMillis: Long): LoadingController {
        mAutoClose = isAutoClose
        if (delayMillis >= 0) {
            mAutoCloseDelay = delayMillis
        }
        return this
    }

    /**
     * @param [flag] default:false
     *  Only loading dialog can set cancel button
     */
    fun setCancelable(flag: Boolean): LoadingController {
        mCancelButton = flag
        if (!mIsDialog) {
            Log.w(TAG, "only loading dialog can set cancel button")
        }
        return this
    }

    /**
     * 设置loadingView的背景颜色
     */
    fun setBackgroundColor(@ColorInt color: Int): LoadingController {
        backgroundColor = color
        return this
    }


    /**
     * 设置loadingView的延时，延时内dismiss时不会显示loading
     */
    fun setDeleyShowTime(delayTime: Long): LoadingController {
        delayShowTime = delayTime
        return this
    }


    /**
     * 设置loading显示是是否使用alpha动效
     */
    fun setShowAinimate(animate: Boolean): LoadingController {
        showAnimate = animate
        return this
    }
    /**
     *设置loading结束时的透明动画
     */
    fun setTheEndAnimation(animate: Float): LoadingController {
        endAnimation = animate
        return this
    }
    /**
     * 设置loading消失时是否使用alpha动效
     */
    fun setDissapearAnimate(animate: Boolean): LoadingController {
        disappearAnimate = animate
        return this
    }

    /**
     * 设置是否需要显示Loading的progressBar底部的文字说明
     */
    fun setShowLoadingTips(showLoadingTips: Boolean): LoadingController {
        loadingShowTips = showLoadingTips
        return this
    }

    /**
     * If it is LoadingView, it will intercept the external click event and decide whether to click to dismiss by the flag
     */
    fun setCanceledOnTouchOutside(flag: Boolean): LoadingController {
        mCancelableOut = flag
        return this
    }

    private fun handleLoadingMsg(what: Int) {
        when (what) {
            MSG_SHOW_LOADING_NOW -> realShowLoading()
            MSG_SHOW_LOADING_DELAY -> showLoadingDelay(SHOW_LOADING_DELAY_TIME)
            MSG_CLOSE_LOADING -> checkHiddenLoading()
            MSG_AUTO_CLOSE_LOADING -> hiddenLoadingDelay(mAutoCloseDelay)
            else -> {
            }
        }
    }

    private fun checkLoadingDialog() {
        if (mLoadingDialog == null) {
            initLoadingDialog()
        } else {
            updateSetInfo()
        }
    }

    private fun checkLoadingView() {
        if ((mLoadingLayout == null) || (mLoadingView == null)) {
            initLoadingView()
        } else {
            updateSetInfo()
        }
    }

    private fun initLoadingDialog() {
        getActivity()?.let {
            if (!checkActivityEnable()) {
                return
            }
            mLoadingDialog = COUIRotatingDialogBuilder(it, stringResource(R.string.os12_tagprogressbar)).show()
            mLoadingDialog?.setCanceledOnTouchOutside(false)
            updateSetInfo()
        }
    }

    private fun initLoadingView() {
        getActivity()?.let {
            if (!checkActivityEnable()) {
                return
            }
            mLoadingLayout = LayoutInflater.from(it).inflate(R.layout.loading_layout, null)
            mLoadingView = mLoadingLayout?.findViewById(R.id.loading_progress_bar)
            mLoadingLayout?.setOnClickListener(null)
            mLoadingLayout?.isClickable = false
            mLoadingLayout?.isEnabled = false
            if (loadingShowTips) {
                val defaultTipString = it.resources.getString(com.support.progressbar.R.string.coui_loading_view_access_string)
                mLoadingView?.setLoadingTips(defaultTipString)
            }
            backgroundColor?.let {
                mLoadingLayout?.setBackgroundColor(it)
            }
            updateSetInfo()
        }
    }

    private fun updateSetInfo() {
        Log.d(TAG, "updateSetInfo")
        if (mParentLayout == null) {
            mParentLayout = getActivity()?.window?.decorView?.findViewById(android.R.id.content)
        }
        mTipMsgId?.let { resId ->
            mLoadingDialog?.setTitle(resId)
            mLoadingView?.setLoadingTips(resId)
        }
        mTipMsg?.let { msg ->
            mLoadingDialog?.setTitle(msg)
            mLoadingView?.tooltipText = msg
            mLoadingView?.setLoadingTips(msg)
        }
        mLoadingDialog?.setCancelable(mCancelButton)
        mCancelableOut?.let {
            mLoadingDialog?.setCanceledOnTouchOutside(it)
            mLoadingLayout?.isClickable = true
            mLoadingLayout?.isEnabled = true
            mLoadingLayout?.setOnClickListener(this)
        }
    }

    private fun showLoadingDelay(delay: Long) {
        mLoadingHandler.sendEmptyMessageDelayed(MSG_SHOW_LOADING_NOW, delay)
    }

    private fun realShowLoading() {
        if (mIsShowing) {
            Log.w(TAG, "Loading is showing")
            return
        }
        if (!checkActivityEnable()) {
            Log.w(TAG, "show Loading, activity is not enable")
            return
        }
        if (mIsDialog) {
            checkLoadingDialog()
            mLoadingDialog?.show()
        } else {
            checkLoadingView()
            mParentLayout?.also {
                when(it) {
                    is CoordinatorLayout -> {
                        val params = CoordinatorLayout.LayoutParams(
                            CoordinatorLayout.LayoutParams.MATCH_PARENT,
                            CoordinatorLayout.LayoutParams.MATCH_PARENT
                        )
                        params.gravity = Gravity.CENTER
                        removeView(mLoadingLayout)
                        it.addView(mLoadingLayout, params)
                    }
                    is RelativeLayout -> {
                        val params = RelativeLayout.LayoutParams(
                            RelativeLayout.LayoutParams.MATCH_PARENT,
                            RelativeLayout.LayoutParams.MATCH_PARENT
                        )
                        params.addRule(RelativeLayout.CENTER_IN_PARENT)
                        removeView(mLoadingLayout)
                        it.addView(mLoadingLayout, params)
                    }
                    is ConstraintLayout -> {
                        val params = ConstraintLayout.LayoutParams(
                            CoordinatorLayout.LayoutParams.MATCH_PARENT,
                            CoordinatorLayout.LayoutParams.MATCH_PARENT
                        )
                        params.startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                        params.endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                        params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
                        params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
                        removeView(mLoadingLayout)
                        it.addView(mLoadingLayout, params)
                    }

                    is FrameLayout -> {
                        val params = FrameLayout.LayoutParams(
                            FrameLayout.LayoutParams.MATCH_PARENT,
                            FrameLayout.LayoutParams.MATCH_PARENT
                        )
                        if (lockFrameLayoutVertical) {
                            params.gravity = Gravity.CENTER_HORIZONTAL
                            val screenHeight = dp2px(it.context, it.context.resources.configuration.screenHeightDp)
                            Log.i(TAG, "measuredHeight = $screenHeight")
                            params.topMargin = screenHeight / 2
                        } else {
                            params.gravity = Gravity.CENTER
                        }
                        removeView(mLoadingLayout)
                        it.addView(mLoadingLayout, params)
                    }
                    else -> {
                        removeView(mLoadingLayout)
                        it.addView(mLoadingLayout)
                    }
                }
                startPlayLoadingAnim()
                if (showAnimate) {
                    runShowAnimator()
                }
            }
        }
        mIsShowing = true
        mLoadingShowStartTime = SystemClock.elapsedRealtime()
        if (mAutoClose) {
            mLoadingHandler.sendEmptyMessage(MSG_AUTO_CLOSE_LOADING)
        }
    }

    private fun removeView(mLoadingLayout: View?) {
        mLoadingLayout?.let { loadingLayout ->
            (loadingLayout.parent as? ViewGroup)?.removeView(mLoadingLayout)
        }
    }

    private fun startPlayLoadingAnim() {
        mLoadingView?.visibility = View.VISIBLE
    }

    private fun checkHiddenLoading() {
        val diffTime = SystemClock.elapsedRealtime() - mLoadingShowStartTime
        if (diffTime > LOADING_SHOW_MIN_TIME) {
            realHiddenLoading()
        } else {
            hiddenLoadingDelay(LOADING_SHOW_MIN_TIME - diffTime)
        }
    }

    private fun hiddenLoadingDelay(delay: Long) {
        mLoadingHandler.sendEmptyMessageDelayed(MSG_CLOSE_LOADING, delay)
    }

    private fun realHiddenLoading() {
        mLoadingHandler.removeMessages(MSG_SHOW_LOADING_NOW)
        mLoadingHandler.removeMessages(MSG_SHOW_LOADING_DELAY)
        if (mIsDialog) {
            mLoadingDialog?.dismiss()
            mLoadingDialog = null
        } else {
            if (disappearAnimate) {
                runDismissAnimator {
                    mLoadingView?.apply {
                        clearAnimation()
                    }
                    mParentLayout?.removeView(mLoadingLayout)
                }
            } else {
                mLoadingView?.apply {
                    clearAnimation()
                }
                mParentLayout?.removeView(mLoadingLayout)
            }
        }
        mIsShowing = false
    }

    private fun getActivity(): ComponentActivity? {
        return mWeakRefAct?.get()
    }

    private fun runShowAnimator() {
        mLoadingLayout?.let {
            val animation = ObjectAnimator.ofFloat(it, "alpha", 0f, 1f)
            animation.setDuration(ALPHA_ANIMATE_DURATION)
            animation.interpolator = PathInterpolator(
                LOAD_SHOW_ANIMATOR_X1,
                LOAD_SHOW_ANIMATOR_Y1,
                LOAD_SHOW_ANIMATOR_X2,
                LOAD_SHOW_ANIMATOR_Y2
            )
            animation.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    Log.d(TAG, "runShowAnimator END")
                    showAinimatorRunning = false
                }

                override fun onAnimationStart(animation: Animator, isReverse: Boolean) {
                    Log.d(TAG, "runShowAnimator START")
                    showAinimatorRunning = true
                }
            })
            animation.start()
        }
    }

    private fun runDismissAnimator(runnable: Runnable? = null) {
        mLoadingLayout?.let {
            val animation = ObjectAnimator.ofFloat(it, "alpha", 1f, endAnimation)
            animation.setDuration(ALPHA_ANIMATE_DURATION)
            animation.interpolator = COUIEaseInterpolator()
            animation.addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    Log.d(TAG, "runDismissAnimator END")
                    dismissAnimatorRunning = false
                    runnable?.run()
                }

                override fun onAnimationStart(animation: Animator, isReverse: Boolean) {
                    Log.d(TAG, "runDismissAnimator START")
                    dismissAnimatorRunning = true
                }
            })
            animation.start()
        }
    }


    private fun checkActivityEnable(): Boolean {
        getActivity()?.let {
            if (it.isDestroyed || it.isFinishing) {
                return false
            }
            return true
        }
        return false
    }

    private class LoadingHandler(a: LoadingController?) : StaticHandler<LoadingController?>(a) {
        override fun handleMessage(msg: Message?, t: LoadingController?) {
            msg?.let {
                t?.handleLoadingMsg(it.what)
            }
        }
    }

    override fun onClick(v: View?) {
        when (v?.id) {
            R.id.loadingView -> {
                if (mCancelableOut == true) {
                    dismissLoading()
                } else {
                    Log.d(TAG, "onClick loadingView cancel invalid")
                }
            }
        }
    }

    @OnLifecycleEvent(Lifecycle.Event.ON_RESUME)
    fun onResume() {
        Log.d(TAG, "LifeCycle on onResume")
    }

    fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (mIsShowing.not()) {
            return
        }
        if (lockFrameLayoutVertical.not()) {
            return
        }
        if (mParentLayout is FrameLayout && mLoadingLayout != null) {
            mLoadingLayout?.apply {
                val screenHeight = dp2px(context, context.resources.configuration.screenHeightDp)
                Log.i(TAG, "onUIConfigChanged screenHeight = $screenHeight")
                (layoutParams as? FrameLayout.LayoutParams)?.topMargin = screenHeight / 2
            }
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "LifeCycle on Destroyed")
        realHiddenLoading()
        mLoadingHandler.removeCallbacksAndMessages(null)
        mLoadingDialog = null
        mLoadingView = null
        mLoadingLayout = null
        mParentLayout = null
        mLifecycleOwner?.apply {
            lifecycle.removeObserver(this@LoadingController)
        }
        mLifecycleOwner = null
        mWeakRefAct?.clear()
    }

    override fun needSkipRemoveAnimator(): Boolean {
        //当loading正在显示时，表明文件夹的列表动画中remove动画需要skip了
        val isShowing = if (mIsDialog) {
            mLoadingDialog?.isShowing ?: false
        } else {
            mLoadingLayout?.let { mParentLayout?.contains(it) } ?: false
        }
        //Log.d(TAG, "needSkipRemoveAnimator isShowing $isShowing")
        return isShowing
    }
}