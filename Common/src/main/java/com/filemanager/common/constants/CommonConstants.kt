/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/4/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.constants

import android.os.Environment
import android.util.ArrayMap
import java.io.File

object CommonConstants {
    const val DISABLE_KING_DOC = false
    const val DISABLE_TENCENT_DOC = true
    const val TYPE_ALL = "all"
    const val TYPE_RAR = ".rar"
    const val TYPE_ZIP = ".zip"
    const val TYPE_JAR = ".jar"
    const val TYPE_7Z = ".7z"
    const val COPY_BUF_SIZE = 512 * 1024

    @kotlin.jvm.JvmField
    val COMPRESS_EXT_ARRAY = java.util.ArrayList<String?>().apply {
        add(".rar")
        add(".zip")
        add(".jar")
        add(".7z")
    }
    private val FILE_HEADER_RAR = "526172"
    private val FILE_HEADER_ZIP = "504b3"
    private val FILE_HEADER_TAR = "757374"
    val FILE_HEADER_MAP = ArrayMap<String, Int>().let {
        it[FILE_HEADER_RAR] = RAR
        it[FILE_HEADER_ZIP] = ZIP
        it[FILE_HEADER_TAR] = TAR
        it
    }
    const val ZIP = 1
    const val JAR = 2
    const val TAR = 3
    const val RAR = 4
    const val P7ZIP = 5
    const val UNKNOWN_TYPE = 100

    const val MAX_SEND_COUNT = 99
    const val CUSTOM_NAME_LEN = 50
    const val NAME_BYTES_LEN = 255
    const val LABEL_NAME_LEN: Int = 32
    const val LABEL_NAME_BYTES_LEN: Int = 164
    const val DRAG_FROM_FILE_MANAGER = "drag_from_file_manager"
    const val DRAG_FROM_FRAGMENT = "drag_from_fragment_"
    const val DRAG_FROM_MAC_FILES = "files_key"
    const val NEED_SHOW_HIDDEN_FILES = "need_show_hidden_files"
    const val NEED_SHOW_ENCRYPT_BOX = "need_show_encrypt_box"
    const val NEED_SHOW_RECENT_CAMERA = "need_show_recent_camera_screenshot"
    const val CLOUD_FUNCTION_SHOW = "cloud_function_show"
    const val CLEANUP_FUNCTION_SHOW = "cleanup_function_show"
    const val OWORK_FUNCTION_SHOW = "owork_function_show"
    const val OWORK_ALREDY_SHOW = "owork_alredy_show"
    const val TENCENT_DOCS_FUNCTION_SHOW = "tencent_docs_function_show"
    const val K_DOCS_FUNCTION_SHOW = "k_docs_function_show"
    const val THIRD_APP_SEARCH_FUNCTION_SHOW = "third_app_search_function_show"
    const val THIRD_APP_SEARCH_INTRODUCE_DIALOG_SHOW = "third_app_search_introduce_dialog_show"
    const val FEED_BACK_FUNCTION_SHOW = "feed_back_function_show"
    const val THIRD_APP_SEARCH_CARD_IGNORE = "third_app_card_ignore"
    const val AD_SWITCH_STATUS = "ad_switch_status"
    const val AD_SWITCH_UPLOAD_TIME = "ad_switch_upload_time"
    const val OWORK = "com.oplus.owork"
    const val PRIVACY_PASSWORD_SHOW = "privacy_password_show"
    const val PRIVACY_PASSWORD_TIP_SHOW = "privacy_password_tip_show"
    const val DOWNLOAD_TRAFFIC_REMIND_SHOW = "download_traffic_remind"
    const val DOWNLOAD_NOTIFY_REMIND_SHOW = "download_notify_remind"
    const val DOWNLOAD_REMOTE_PC_SAVE_PATH = "download_remote_pc_save_path"
    const val CLOUD_DRIVE_SUBTITLE = "cloud_drive_subtitle"

    val OWORK_DEFAULT_PATHS = arrayOf("Download/OWork/")
    /**
     * 根路径
     */
    val ROOT_PATH: String by lazy {
        Environment.getExternalStorageDirectory().absolutePath
    }
    /**
     * 默认存储路径
     */
    val DEFAULT_DOWNLOAD_PATH by lazy {
        "$ROOT_PATH${File.separator}Download${File.separator}Remote PC Control"
    }
    /**
     *判断拖拽文件或者分享文件里边是否有文件夹
     */
    const val IS_FOLDER = "is_folder"
    const val KEY_FOLDER_PATH_LIST = "key_folder_path_list"
    const val KEY_NONE_MEDIA_PATH_LIST = "key_none_media_path_list"
    const val KEY_FOLDER_PATH_SIZE = "key_folder_path_size"
    const val KEY_HAS_ANDROID_DATA_FILE = "key_has_android_data_file"
    const val PREF_NAME_FILE_CLOUD_DOCS = "file_cloud_docs"
    const val KEY_SUPPORT_TENCENT_DOCS = "support_tencent_docs"
    const val KEY_SUPPORT_K_DOCS = "support_k_docs"
    const val KEY_IS_AUTHORIZING = "is_authorizing"
    const val KEY_START_FILE_CLOUD_DRIVE = "start_file_cloud_drive"
    const val MAIN_SHOW_MANAGER_FILES_PERMISSION_DIALOG = "main_show_manager_files_permission_dialog"
    const val PREF_CN_CATEGORY_APP_LIST = "cn_category_app_list"
    const val KEY_CATEGORY_APP_LIST = "category_app_list"
    /**
     * 来源App排序和开关状态的持久化
     */
    const val SUPER_APP_STATUS_SP_NAME = "super_app_status_list"
    const val SUPER_APP_STATUS_LIST_KEY = "super_app_status_list_key"
}