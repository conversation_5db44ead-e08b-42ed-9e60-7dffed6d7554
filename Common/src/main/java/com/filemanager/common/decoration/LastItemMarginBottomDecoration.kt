/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : LastItemMarginBottomDecoration
 * * Description : 上滑时最后一个item距离底部间距
 * * Version     : 1.0
 * * Date        : 2025/01/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.decoration

import android.graphics.Rect
import android.view.View
import androidx.annotation.DimenRes
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.filemanager.common.MyApplication

class LastItemMarginBottomDecoration(@DimenRes marginBottomRes: Int) : ItemDecoration() {

    companion object {
        private const val TAG = "LastItemMarginBottomDecoration"
    }

    private var margin: Int = 0

    init {
        margin = MyApplication.appContext.resources.getDimensionPixelOffset(marginBottomRes)
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val position = parent.getChildAdapterPosition(view)
        val count = parent.adapter?.itemCount ?: 0
        if (position == count - 1) { // 最后一个
            outRect.bottom = margin
        }
    }
}