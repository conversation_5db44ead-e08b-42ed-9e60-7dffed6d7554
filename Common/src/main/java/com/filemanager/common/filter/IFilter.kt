/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : IFilter
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/9/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/9/11       1      create
 ***********************************************************************/
package com.filemanager.common.filter

import com.filemanager.common.helper.MimeTypeHelper

interface IFilter<T> {

    fun filterTarget(target: String, list: List<T>): List<T>

    companion object{
        @JvmStatic
        fun isTargetType(target: String, ext: String): Boolean {
            return target.equals(ext, true)
        }

        @JvmStatic
        fun isTargetImageType(ext: String): Boolean {
            return MimeTypeHelper.PSD_FORMAT.equals(ext, true)
                    || MimeTypeHelper.DWG_FORMAT.equals(ext, true)
                    || MimeTypeHelper.DXF_FORMAT.equals(ext, true)
        }
    }
}