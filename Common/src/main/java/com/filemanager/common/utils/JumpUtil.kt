/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : JumpUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/4/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/4/19       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.app.Activity
import android.content.Intent
import com.filemanager.common.constants.Constants

/**
 * 跳转到最近页面
 */
fun jumpToRecentFiles(activity: Activity, selectMode: Boolean = false) {
    val intent = Intent(Constants.START_FILE_MANAGER_ACTION)
    PreferencesUtils.put(key = Constants.CHANGE_TO_SELECT_MODE, value = selectMode)
    intent.putExtra(Constants.KEY_IS_FROM_LABEL_FILE_LIST, true)
    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK)
    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
    activity.startActivity(intent)
    activity.finish()
}