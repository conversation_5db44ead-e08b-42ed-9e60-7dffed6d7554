/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  AlbumSetItemDecoration
 * * Description : 计算图集(AlbumSet)页面的显示间距，当列数=3/5/6时，有不同的两边间距及itemSpace
 * * Version     : 1.0
 * * Date        : 2022/3/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.filemanager.common.decoration

import android.graphics.Rect
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseItemDecoration
import com.filemanager.common.utils.Utils

class AlbumSetItemDecoration(spanCount: Int) : BaseItemDecoration() {

    companion object {
        private const val TAG = "AlbumSetItemDecoration"
        private const val ALBUM_SET_GRID_COL_3 = 3
        private const val ALBUM_SET_GRID_COL_5 = 5
        private const val ALBUM_SET_GRID_COL_6 = 6
    }

    @VisibleForTesting
    var mSpace = 0
    @VisibleForTesting
    var mEdgeSpace: Int = 0
    private var mVertical: Int = 0
    private var mVerticalFirst: Int = 0
    private val mIsRtl by lazy { Utils.isRtl() }
    private val mStickyFooterDelegation: StickyFooterDelegation by lazy {
        StickyFooterDelegation()
    }

    init {
        mSpanCount = spanCount
        mVerticalFirst = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_item_margin_top)
        mVertical = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_padding_bottom)
        updateSpanCount()
    }

    override fun updateSpanCount() {
        when (mSpanCount) {
            ALBUM_SET_GRID_COL_3 -> {
                mSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_item_horizontal_spacing_col_3)
                mEdgeSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_padding_left_col_3)
            }
            ALBUM_SET_GRID_COL_5 -> {
                mSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_item_horizontal_spacing_col_5)
                mEdgeSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_padding_left_col_5)
            }
            ALBUM_SET_GRID_COL_6 -> {
                mSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_item_horizontal_spacing_col_6)
                mEdgeSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_padding_left_col_6)
            }
            else -> {
                mSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_item_horizontal_spacing_col_5)
                mEdgeSpace =
                    MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_set_grid_padding_left_col_5)
            }
        }
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        if (mStickyFooterDelegation.isFootView(parent, view)) {
            mStickyFooterDelegation.getItemOffsets(mSpace, outRect, view, parent, state)
        } else if (mSpanCount > 1) {
            val position = parent.getChildAdapterPosition(view) // item position
            val viewType = parent.adapter?.getItemViewType(position)
            if (BaseFileBean.TYPE_FILE_AD == viewType) {
                outRect.set(mSpace, 0, mSpace, mVertical)
                return
            }
            val layoutManager = parent.layoutManager as GridLayoutManager
            val column = layoutManager.spanSizeLookup.getSpanIndex(position, layoutManager.spanCount)
            val row = position / mSpanCount
            val mEachSpace = (mEdgeSpace * 2 + (mSpanCount - 1) * mSpace) / mSpanCount
            val diff = ((mEachSpace - mEdgeSpace) - mEdgeSpace) / (mSpanCount - 1)
            val left = (column + 1 - 1) * diff + mEdgeSpace
            val right = mEachSpace - left
            if (mIsRtl) {
                when (row) {
                    0 -> outRect.set(right, mVerticalFirst, left, mVertical)
                    else -> outRect.set(right, 0, left, mVertical)
                }
            } else {
                when (row) {
                    0 -> outRect.set(left, mVerticalFirst, right, mVertical)
                    else -> outRect.set(left, 0, right, mVertical)
                }
            }
        }
    }
}