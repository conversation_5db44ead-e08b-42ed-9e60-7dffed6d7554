/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OplusResourceManagerCompat
 ** Description : OplusResourceManagerCompat
 ** Version     : 1.0
 ** Date        : 2024/09/12 16:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/12     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

class OplusResourceManagerCompat private constructor(private var resourceMgr: Any?) {

    companion object {

        private var instance: OplusResourceManagerCompat? = null

        @JvmStatic
        fun <T> getInstance(clazz: Class<T>): OplusResourceManagerCompat {
            if (instance == null) {
                val manager = CompatUtils.compactSApi({
                    com.oplus.orms.OplusResourceManager.getInstance(clazz)
                }, {
                    com.heytap.addon.orms.OplusResourceManager.getInstance(clazz)
                })
                instance = OplusResourceManagerCompat(manager)
            }
            return instance!!
        }
    }

    fun ormsSetSceneAction(scene: String, action: String, timeout: Int): Long {
        return CompatUtils.compactSApi({
            (resourceMgr as com.oplus.orms.OplusResourceManager).ormsSetSceneAction(com.oplus.orms.info.OrmsSaParam(scene, action, timeout))
        }, {
            (resourceMgr as com.heytap.addon.orms.OplusResourceManager).ormsSetSceneAction(
                com.heytap.addon.orms.info.OrmsSaParam(
                    scene,
                    action,
                    timeout
                )
            )
        })
    }

    fun ormsClrSceneAction(requestId: Long) {
        CompatUtils.compactSApi({
            (resourceMgr as com.oplus.orms.OplusResourceManager).ormsClrSceneAction(requestId)
        }, {
            (resourceMgr as com.heytap.addon.orms.OplusResourceManager).ormsClrSceneAction(requestId)
        })
    }
}