/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - DeleteSoundUtil.java
 ** Description: DeleteSound Utils
 ** Version: 1.0
 ** Date : 2020/12/16
 ** Author: W9001165
 **
 ** ---------------------Revision History: ---------------------
 **  <author>       <date>        <version >    <desc>
 **  W9001165      2020/04/13      1.0          create
 ****************************************************************/
package com.filemanager.common.utils

import android.content.ContentUris
import android.content.Context
import android.media.SoundPool
import android.net.Uri
import android.os.ParcelFileDescriptor
import android.provider.MediaStore
import android.provider.MediaStore.Audio
import android.provider.Settings
import androidx.annotation.MainThread
import com.filemanager.common.MyApplication
import kotlinx.coroutines.*
import java.io.FileNotFoundException

object DeleteSoundUtil {
    private const val TAG = "DeleteSoundUtils"
    private const val DELETE_PATH_ANDROID_Q = "/system/media/audio/ui/global_delete.ogg"
    private const val DELETE_DISPLAY_NAME_ANDROID_R = "global_delete.ogg"
    private const val SUPPORT_KEY = "global_delete_sound"
    private const val DELETE_SOUND_OPEN = 1
    private const val TIMEOUT_GET_SOUND_ID = 1000L

    enum class DeleteSoundStatus {
        PREPARING, DONE, IDLE
    }

    var mPrepared = DeleteSoundStatus.IDLE
    var mDeleteSoundId = 0

    private fun initSound() {
        if (mPrepared == DeleteSoundStatus.IDLE) {
            mPrepared = DeleteSoundStatus.PREPARING
        } else {
            return
        }

        GlobalScope.launch {
            try {
                withContext(Dispatchers.IO) {
                    withTimeout(TIMEOUT_GET_SOUND_ID) {
                        mDeleteSoundId = getSoundId(SoundPoolUtil.sInstance)
                        SoundPoolUtil.sInstance.setCompleteListener(SoundPool.OnLoadCompleteListener { _, _, _ ->
                            mPrepared = DeleteSoundStatus.DONE
                            playDeleteSoundInternal()
                            Log.d(TAG, "loadComplete:mDeleteId =$mDeleteSoundId")
                        })
                    }
                }
            } catch (e: Exception) {
                mPrepared = DeleteSoundStatus.IDLE
                Log.e(TAG, "loadSound has error: ${e.message}")
            }
        }
    }

    private fun getSoundId(soundLoadUtil: SoundPoolUtil): Int {
        return if (SdkUtils.isAtLeastR()) {
            getSoundIdOnAndroidR(soundLoadUtil)
        } else {
            getSoundIdOnAndroidQ(soundLoadUtil)
        }
    }

    @JvmStatic
    fun getSoundIdOnAndroidQ(soundLoadUtil: SoundPoolUtil): Int {
        return soundLoadUtil.loadFile(DELETE_PATH_ANDROID_Q, 1)
    }

    @JvmStatic
    fun getSoundIdOnAndroidR(soundLoadUtil: SoundPoolUtil): Int {
        try {
            openFileDescriptor()?.use { pfd ->
                return soundLoadUtil.loadFile(pfd.fileDescriptor, 0, pfd.statSize, 1)
            } ?: kotlin.run {
                Log.w(TAG, "getSoundIdOnAndroidR openFile failed")
                return 0
            }
        } catch (ex: Exception) {
            Log.w(TAG, "getSoundIdOnAndroidR error: $ex")
            return 0
        }
    }

    @Throws(FileNotFoundException::class)
    private fun openFileDescriptor(): ParcelFileDescriptor? {
        getSoundUri(MyApplication.sAppContext, DELETE_DISPLAY_NAME_ANDROID_R)?.let {
            return MyApplication.sAppContext.contentResolver.openFileDescriptor(it, "r")
        } ?: return null
    }

    @JvmStatic
    fun getSoundUri(context: Context, oggName: String): Uri? {
        if (oggName.isEmpty()) {
            return null
        }
        try {
            context.contentResolver.query(
                Audio.Media.INTERNAL_CONTENT_URI, arrayOf(MediaStore.Files.FileColumns._ID),
                MediaStore.Files.FileColumns.DISPLAY_NAME + " LIKE ?", arrayOf(oggName), null
            ).use { cursor ->
                if (cursor != null && cursor.moveToFirst()) {
                    val id = cursor.getLong(0)
                    return ContentUris.withAppendedId(Audio.Media.INTERNAL_CONTENT_URI, id)
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "getSoundUri error: $e")
        }
        return null
    }

    /**
     * attention: playDeleteSound must be called in MainThread
     */
    @MainThread
    fun playDeleteSound() {
        if (!isGlobalDeleteSoundSwitchOpen(MyApplication.sAppContext)) {
            return
        }

        when (mPrepared) {
            DeleteSoundStatus.IDLE -> {
                initSound()
            }

            DeleteSoundStatus.DONE -> {
                playDeleteSoundInternal()
            }

            DeleteSoundStatus.PREPARING -> {
                Log.d(TAG, "DeleteSoundStatus is preparing")
                return
            }

            else -> {
                Log.w(TAG, "mPrepared is error DeleteSoundStatus: $mPrepared")
                return
            }
        }
    }

    private fun playDeleteSoundInternal() {
        try {
            if (mDeleteSoundId != 0) {
                SoundPoolUtil.sInstance.play(mDeleteSoundId, 1f, 1f, 1, 0, 1f)
            } else {
                Log.w(TAG, "playDeleteSound can't play mDeleteSoundId: $mDeleteSoundId")
            }
        } catch (ex: Exception) {
            Log.e(TAG, "playDeleteSound has error: ${ex.message}")
        }
    }

    @JvmStatic
    fun isGlobalDeleteSoundSwitchOpen(mContext: Context): Boolean {
        val switchInt = try {
            Settings.Secure.getInt(mContext.contentResolver, SUPPORT_KEY, DELETE_SOUND_OPEN)
        } catch (ex: Exception) {
            Log.w(TAG, "isGlobalDeleteSoundSwitchOpen error: $ex")
            DELETE_SOUND_OPEN
        }
        Log.d(TAG, "isGlobalDeleteSoundSwitchOpen switchInt: $switchInt")
        return switchInt == DELETE_SOUND_OPEN
    }
}