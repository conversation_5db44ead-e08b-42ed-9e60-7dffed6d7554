/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: PreviewEmptyFragment
 ** Description: Preview Empty Fragment
 ** Version: 1.0
 ** Date : 2024/10/17
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.fragment.app.Fragment
import com.filemanager.common.R
import com.filemanager.common.utils.Log

class PreviewEmptyFragment : Fragment() {

    companion object {
        const val TAG = "PreviewEmptyFragment"
    }

    private var rootView: View? = null
    private var emptyDesView: TextView? = null
    private var isShowEmptyDes = true
    private var isHideContent = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        kotlin.runCatching {
            return inflater.inflate(R.layout.preview_empty_layout, container, false).also {
                emptyDesView = it.findViewById(R.id.empty_des_tv)
                rootView = it.findViewById(R.id.empty_view_layout)
                applyShowEmptyDes()
                applyHideContent()
            }
        }.onFailure {
            Log.e(TAG, "onCreateView ${it.message}")
        }
        return null
    }

    fun setHideContent(isHide: Boolean) {
        isHideContent = isHide
        applyHideContent()
    }

    fun setShowEmptyDes(isShowDes: Boolean) {
        setHideContent(false)
        isShowEmptyDes = isShowDes
        applyShowEmptyDes()
    }

    private fun applyShowEmptyDes() {
        if (isShowEmptyDes) {
            emptyDesView?.visibility = View.VISIBLE
        } else {
            emptyDesView?.visibility = View.INVISIBLE
        }
    }

    private fun applyHideContent() {
        rootView?.visibility = if (isHideContent) View.INVISIBLE else View.VISIBLE
    }
}