/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - SelectionViewModel.kt
 ** Description: SelectionViewModel
 ** Version: 1.0
 ** Date : 2020/06/04
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/04    1.0     create
 ****************************************************************/

package com.filemanager.common.base

import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.back.PredictiveBackSelectionChecker
import com.filemanager.common.back.PredictiveBackStateChecker
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.fileutils.hasDrmFile
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils

abstract class SelectionViewModel<T : BaseFileBean, V : BaseUiModel<T>> : BaseViewModel() {
    companion object {
        private const val TAG = "SelectionViewModel"
        private const val REFRESH_DELAY_TIME = 300L
        private const val HANDLER_MESSAGE_REFRESH = 1
    }

    val uiState = MutableLiveData<V>()

    /**
     * Data is one of STATE_START/STATE_DONE/STATE_CANCEL in OnLoaderListener.
     */
    val dataLoadState = MutableLiveData<Int>()

    /**
     * Add legacy offending naming alias for corrected naming member [uiState].
     * Pls merge [uiState] and [mUiState] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("uiState"))
    val mUiState: MutableLiveData<V>
        get() = uiState

    /**
     * Add legacy offending naming alias for corrected naming member [dataLoadState].
     * Pls merge [dataLoadState] and [mDataLoadState] after rectify all references in codes.
     */
    @Deprecated("Legacy offending naming", ReplaceWith("dataLoadState"))
    val mDataLoadState: MutableLiveData<Int>
        get() = dataLoadState

    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                HANDLER_MESSAGE_REFRESH -> {
                    loadData()
                }
            }
        }
    }

    val previewClickedFileLiveData = MutableLiveData<BaseFileBean?>()

    private val checker: PredictiveBackStateChecker by lazy {
        PredictiveBackSelectionChecker() {
            isInSelectMode().not()
        }
    }

    override fun onCleared() {
        handler.removeCallbacksAndMessages(null)
        super.onCleared()
    }

    open fun enterSelectionMode(key: Int): Boolean {
        val listModel = uiState.value?.stateModel?.listModel
        Log.d(TAG, "enterSelectionMode listModel=${listModel?.value},key=$key")
        if (listModel?.value != KtConstants.LIST_NORMAL_MODE) {
            return true
        }
        uiState.value?.apply {
            selectedList.clear()
            selectedList.add(key)
        }
        Log.d(TAG, "enterSelectionMode mSelectedList.size=${uiState.value?.selectedList?.size}")
        changeListMode(KtConstants.LIST_SELECTED_MODE)
        return true
    }

    open fun changeListMode(listMode: Int) {
        if (Utils.isQuickChangeModel()) {
            return
        }
        if (listMode == KtConstants.LIST_NORMAL_MODE) {
            uiState.value?.selectedList?.clear()
        }
        uiState.value?.stateModel?.listModel?.value = listMode
        PredictiveBackUtils.checkEnableBackAnim(checker)
    }

    fun isInSelectMode(): Boolean {
        return uiState.value?.stateModel?.listModel?.value == KtConstants.LIST_SELECTED_MODE
    }

    open fun selectItems(keys: List<Int>): Boolean {
        Log.d(TAG, "selectItems keys.size=${keys.size}")
        if (uiState.value?.stateModel?.listModel?.value != KtConstants.LIST_SELECTED_MODE) {
            return false
        }
        uiState.value?.apply {
            for (key in keys) {
                if (selectedList.contains(key).not() && canSelectItem(key, keyMap.get(key))) {
                    Log.i(TAG, "selectItems ADD key $key")
                    selectedList.add(key)
                }
            }
        }
        uiState.value = uiState.value.also {
            Log.d(TAG, "selectItems mode end, selectSize=${it?.selectedList?.size}")
        }
        return true
    }

    open fun canSelectItem(key: Int, fileBean: BaseFileBean?): Boolean {
        return fileBean != null
    }

    open fun deselectItems(keys: List<Int>): Boolean {
        Log.d(TAG, "deselectItems keys.size=${keys.size}")
        if (uiState.value?.stateModel?.listModel?.value != KtConstants.LIST_SELECTED_MODE) {
            return false
        }
        uiState.value?.selectedList?.let {
            for (key in keys) {
                it.remove(key)
            }
        }
        uiState.value = uiState.value.also {
            Log.d(TAG, "selectItems mode end, selectSize=${it?.selectedList?.size}")
        }
        return true
    }

    fun isItemSelected(key: Int): Boolean {
        return uiState.value?.selectedList?.contains(key) ?: false
    }

    fun getSelectionKeyList(): ArrayList<Int> {
        return uiState.value?.selectedList ?: arrayListOf()
    }

    fun getSelectItems(): ArrayList<T> {
        val fileList = ArrayList<T>()
        uiState.value?.run {
            selectedList.forEach {
                keyMap[it]?.let(fileList::add)
            }
        }
        Log.d(TAG, "getSelectItems fileList.size=${fileList.size}")
        return fileList
    }

    fun setNavigateItemAble(navigationInterface: NavigationInterface) {
        var isEnable = uiState.value?.selectedList?.isNotEmpty() ?: false
        isEnable = isEnable && !DragUtils.isDragging
        val selectItem = getSelectItems()
        val hasDrmFile = hasDrmFile(selectItem)
        Log.d(TAG, "isEnable $isEnable hasDrmFile $hasDrmFile")
        navigationInterface.setNavigateItemAble(isEnable, hasDrmFile, AndroidDataHelper.hasAndroidDataFile(selectItem))
    }


    /**
     * If the file list contains headers, tags, and footers,
     * need to  subtract the number of these
     */
    abstract fun getRealFileSize(): Int

    abstract fun loadData()

    fun delayLoadData(delay: Long = REFRESH_DELAY_TIME) {
        handler.removeMessages(HANDLER_MESSAGE_REFRESH)
        handler.sendEmptyMessageDelayed(HANDLER_MESSAGE_REFRESH, delay)
    }

    /**
     * @return true if can drag and drop. Note: dragdrop conflicted with slide selection
     */
    open fun canDragDrop(): Boolean {
        return if (SdkUtils.isAtLeastR()) {
            when {
                PCConnectAction.isScreenCast() -> false
                PCConnectAction.isPadScreenCast() -> true
                //全屏、分屏、小窗支持拖拽
                else -> true
            }
        } else { // forbid the dragdrop on android Q
            false
        }
    }

    open fun getRecyclerViewScanMode(): com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE {
        return com.oplus.dropdrag.SelectionTracker.LAYOUT_TYPE.LIST
    }

    open fun onCreateFolderPath(path: String) {}
}