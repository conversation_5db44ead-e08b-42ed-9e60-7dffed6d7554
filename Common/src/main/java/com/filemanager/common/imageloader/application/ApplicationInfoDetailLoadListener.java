/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  ApplicationInfoDetailLoadListener.java
 *  * * Description : ApplicationInfoDetailLoadListener.java
 *  * * Version     : 1.0
 *  * * Date        : 19-9-20 下午4:58
 *  * * Author      : <EMAIL>
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.filemanager.common.imageloader.application;

public interface ApplicationInfoDetailLoadListener {
    void onLoadDetail(ApplicationInfoDetail applicationInfodetail);
}
