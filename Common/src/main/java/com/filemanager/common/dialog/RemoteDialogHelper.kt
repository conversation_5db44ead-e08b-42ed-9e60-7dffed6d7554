/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteDialogHelper
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/04/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.dialog

import android.content.Context
import android.content.DialogInterface
import androidx.appcompat.app.AlertDialog

class RemoteDialogHelper(private val context: Context) {
    private var passwdErrorDialog: AlertDialog? = null
    private var exitAccountDialog: AlertDialog? = null
    private var noInternetDialog: AlertDialog? = null
    private var deviceOfflineDialog: AlertDialog? = null
    private var remoteFMUnavailableDialog: AlertDialog? = null
    private var connectionFailAfterRetryDialog: AlertDialog? = null
    private var phoneConnectedAnotherPcDialog: AlertDialog? = null
    private var phoneConnectingAnotherPcDialog: AlertDialog? = null
    private var pcConnectedAnotherPhoneDialog: AlertDialog? = null
    private var pcConnectingAnotherPhoneDialog: AlertDialog? = null

    fun showPasswdErrorDialog(listener: DialogInterface.OnClickListener) {
        if (passwdErrorDialog == null) {
            passwdErrorDialog = RemoteDeviceDialogUtils.createPasswdErrorDialog(context, listener)
        }
        passwdErrorDialog?.show()
    }

    fun showExitAccountDialogDialog(listener: DialogInterface.OnClickListener) {
        if (exitAccountDialog == null) {
            exitAccountDialog = RemoteDeviceDialogUtils.createExitAccountDialog(context, listener)
        }
        exitAccountDialog?.show()
    }

    fun showNoInternetDialog() {
        if (noInternetDialog == null) {
            noInternetDialog = RemoteDeviceDialogUtils.createNoInternetDialog(context)
        }
        noInternetDialog?.show()
    }

    fun showDeviceOfflineDialog() {
        if (deviceOfflineDialog == null) {
            deviceOfflineDialog = RemoteDeviceDialogUtils.createDeviceOfflineDialog(context)
        }
        deviceOfflineDialog?.show()
    }

    fun showRemoteFMUnavailableDialog() {
        if (remoteFMUnavailableDialog == null) {
            remoteFMUnavailableDialog =
                RemoteDeviceDialogUtils.createRemoteFMUnavailableDialog(context)
        }
        remoteFMUnavailableDialog?.show()
    }

    fun showConnectionFailAfterRetryDialog() {
        if (connectionFailAfterRetryDialog == null) {
            connectionFailAfterRetryDialog =
                RemoteDeviceDialogUtils.createConnectionFailAfterRetryDialog(context)
        }
        connectionFailAfterRetryDialog?.show()
    }


    fun showConnectionFailDialog(code: Int) {
        when (code) {
            RemoteDeviceDialogUtils.PHONE_CONNECTED_TO_ANOTHER -> {
                if (phoneConnectedAnotherPcDialog == null) {
                    phoneConnectedAnotherPcDialog = RemoteDeviceDialogUtils.createConnectionFailDialog(
                        context,
                        RemoteDeviceDialogUtils.PHONE_CONNECTED_TO_ANOTHER
                    )
                }
                phoneConnectedAnotherPcDialog?.show()
            }

            RemoteDeviceDialogUtils.PHONE_CONNECTING_TO_ANOTHER -> {
                if (phoneConnectingAnotherPcDialog == null) {
                    phoneConnectingAnotherPcDialog = RemoteDeviceDialogUtils.createConnectionFailDialog(
                        context,
                        RemoteDeviceDialogUtils.PHONE_CONNECTING_TO_ANOTHER
                    )
                }
                phoneConnectingAnotherPcDialog?.show()
            }

            RemoteDeviceDialogUtils.PC_CONNECTED_TO_ANOTHER -> {
                if (phoneConnectedAnotherPcDialog == null) {
                    pcConnectedAnotherPhoneDialog = RemoteDeviceDialogUtils.createConnectionFailDialog(
                        context,
                        RemoteDeviceDialogUtils.PC_CONNECTED_TO_ANOTHER
                    )
                }
                pcConnectedAnotherPhoneDialog?.show()
            }

            RemoteDeviceDialogUtils.PC_CONNECTING_TO_ANOTHER -> {
                if (pcConnectingAnotherPhoneDialog == null) {
                    phoneConnectedAnotherPcDialog = RemoteDeviceDialogUtils.createConnectionFailDialog(
                        context,
                        RemoteDeviceDialogUtils.PC_CONNECTING_TO_ANOTHER
                    )
                }
                pcConnectingAnotherPhoneDialog?.show()
            }
        }
    }

    fun destroy() {
        passwdErrorDialog = null
        exitAccountDialog = null
        noInternetDialog = null
        deviceOfflineDialog = null
        remoteFMUnavailableDialog = null
        connectionFailAfterRetryDialog = null
        phoneConnectedAnotherPcDialog = null
        phoneConnectingAnotherPcDialog = null
        pcConnectedAnotherPhoneDialog = null
        pcConnectingAnotherPhoneDialog = null
    }
}