/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/2/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.LocaleList
import android.text.TextUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.FeatureCompat.sIsSupportMultiApp
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Utils.isRtl
import java.io.File
import java.util.Locale

object KtUtils {

    private const val TAG = "KtUtils"
    const val STORAGE_INTERNAL = 1
    const val STORAGE_EXTERNAL = 2
    const val STORAGE_OTG = 3
    const val STORAGE_DMF = 4
    const val SHORTCUT_FOLDER = 5
    const val STORAGE_INTERNAL_MULTI_APP = 11
    const val FLAVOR_ONEPLUS = "oneplus"
    const val FLAVOR_OPPO = "oppo"
    const val FLAVOR_REALME = "realme"
    const val OWORK_NAME_TYPE_1 = 1
    const val OWORK_NAME_TYPE_2 = 2
    const val OWORK_NAME_TYPE_3 = 3
    const val STRING_FOUR_SPACES = "    "

    fun formatSize(file: BaseFileBean?): String {
        return if (file == null) {
            ""
        } else {
            if (file.mIsDirectory) {
                ""
            } else {
                Utils.byteCountToDisplaySize(file.mSize)
            }
        }
    }

    fun getApplicationDetailFormat(context: Context?, title: String, detail: String, file: BaseFileBean): String {
        if (context == null) {
            return ""
        }
        if (!TextUtils.isEmpty(detail)) {
            val apkDetail = formatSize(file)
            if (!TextUtils.isEmpty(apkDetail)) {
                val isRtl = isRtl()
                val stringBuilder = StringBuilder()
                if (!TextUtils.isEmpty(title)) {
                    if (isRtl) {
                        stringBuilder.append("\u200F")
                    }
                    stringBuilder.append(title)
                    stringBuilder.append("    ")
                }
                stringBuilder.append(apkDetail)
                stringBuilder.append("    ")
                stringBuilder.append(context.resources.getString(R.string.apk_version_name))
                if (isRtl) {
                    stringBuilder.append("\u202D")
                }
                stringBuilder.append(detail)
                if (isRtl) {
                    stringBuilder.append("\u202C")
                }
                return stringBuilder.toString()
            }
        }
        return ""
    }

    fun needFilterByPosition(position: Int, type: Int): Boolean {
        when (position) {
            Constants.TAB_ALL -> {
                return false
            }
            Constants.TAB_IMAGE -> if (type != MimeTypeHelper.IMAGE_TYPE) {
                return true
            }
            Constants.TAB_VIDEO -> if (type != MimeTypeHelper.VIDEO_TYPE) {
                return true
            }
            Constants.TAB_AUDIO -> if (type != MimeTypeHelper.AUDIO_TYPE) {
                return true
            }
            Constants.TAB_DOCUMENT -> if (!(type == MimeTypeHelper.TXT_TYPE || type == MimeTypeHelper.DOC_TYPE || type == MimeTypeHelper.DOCX_TYPE
                            || type == MimeTypeHelper.XLS_TYPE || type == MimeTypeHelper.XLSX_TYPE || type == MimeTypeHelper.PPT_TYPE
                            || type == MimeTypeHelper.PPTX_TYPE || type == MimeTypeHelper.PDF_TYPE || type == MimeTypeHelper.OFD_TYPE)) {
                return true
            }
            Constants.TAB_OTHER -> if (type == MimeTypeHelper.TXT_TYPE || type == MimeTypeHelper.DOC_TYPE || type == MimeTypeHelper.DOCX_TYPE
                    || type == MimeTypeHelper.XLS_TYPE || type == MimeTypeHelper.XLSX_TYPE || type == MimeTypeHelper.PPT_TYPE
                    || type == MimeTypeHelper.PPTX_TYPE || type == MimeTypeHelper.PDF_TYPE || type == MimeTypeHelper.OFD_TYPE
                    || type == MimeTypeHelper.VIDEO_TYPE || type == MimeTypeHelper.IMAGE_TYPE || type == MimeTypeHelper.AUDIO_TYPE) {
                return true
            }
        }
        return false
    }

    fun getParentFilePath(currentPath: String): String {
        val index = currentPath.lastIndexOf(File.separator)
        if (index > -1) {
            return currentPath.substring(0, index)
        }
        return ""
    }

    @JvmStatic
    fun isPrivateDataPath(context: Context, path: String): Boolean {
        return path.startsWith(context.filesDir.absolutePath) || path.startsWith(
            context.cacheDir.absolutePath
        )
    }

    fun getFileNameWithOutExtension(fileName: String): String {
        val lastPointIndex = fileName.lastIndexOf(".")
        if (lastPointIndex != -1) {
            return fileName.substring(0, lastPointIndex)
        }
        return ""
    }


    fun formatVideoTime(duration: Long): String {
        val seconds: Long = duration % 60
        val minutes: Long = duration / 60 % 60
        val hours: Long = duration / (60 * 60)
        return when {
            duration >= 3600 -> {
                String.format("%d:%02d:%02d", hours, minutes, seconds)
            }
            else -> {
                String.format("%02d:%02d", minutes, seconds)
            }
        }
    }

    fun isChineseLanguage(): Boolean {
        var country = ""
        val localeList: LocaleList = appContext.resources.configuration.locales
        if (!localeList.isEmpty) {
            val locale: Locale? = localeList[0]
            country = locale?.country ?: ""
        }
        return country.isNotEmpty() && "CN".equals(country, ignoreCase = true)
    }

    fun getDefaultImageBackground(radius: Float): Drawable {
        val drawable = GradientDrawable()
        drawable.cornerRadius = radius
        drawable.setColor(appContext.getColor(R.color.color_text_ripple_bg_color))
        return drawable
    }

    /**
     * Check if the [path] passed in is an OTG path
     * @param context
     * @param path
     * @return if the [context] or [path] passed in is null or empty, return false
     */
    @JvmStatic
    fun checkIsOTGPath(
        context: Context?,
        path: String,
        internalPath: String? = "",
        externalPath: String? = "",
        isSingleSdcard: Boolean? = false
    ): Boolean {
        if (TextUtils.isEmpty(path) || (context == null)) {
            return false
        }
        if (checkIsMultiAppPath(path)) {
            return false
        }
        return if (!TextUtils.isEmpty(internalPath) || !TextUtils.isEmpty(externalPath)) {
            getStorageByPath(
                path,
                internalPath,
                externalPath,
                isSingleSdcard ?: false
            ) == STORAGE_OTG
        } else {
            getStorageByPath(context, path) == STORAGE_OTG
        }
    }

    /**
     * Check if the [path] passed in is an MultiApp path
     * @param path
     * @return if the [path] passed in is null or empty, return false
     */
    fun checkIsMultiAppPath(path: String?): Boolean {
        if (TextUtils.isEmpty(path)) {
            return false
        }
        return if (sIsSupportMultiApp) {
            (path?.startsWith(KtConstants.LOCAL_VOLUME_MULTI_APP_PATH, true) == true)
        } else {
            false
        }
    }

    /**
     * Check if the [path] passed in is an SD-Card path
     * * @param context
     * @param path
     * @return if the [path] passed in is null or empty, return false
     */
    @JvmStatic
    fun checkIsSDPath(
        context: Context?,
        path: String,
        externalPath: String? = ""
    ): Boolean {
        if (TextUtils.isEmpty(path) || (context == null)) {
            return false
        }
        var tempExternalPath = externalPath
        if (TextUtils.isEmpty(externalPath)) {
            tempExternalPath = VolumeEnvironment.getExternalSdPath(context)
        }
        if (tempExternalPath == null) {
            return false
        }
        return path.startsWith(tempExternalPath, ignoreCase = true)
    }

    /**
     * Get the storage type by path
     *
     * @param context If the [context] is null and the [path] is not the multi-app path, it will return [STORAGE_INTERNAL]
     * @param path If the [path] is null or empty , it will return [STORAGE_INTERNAL]
     * @return value in {[STORAGE_INTERNAL] , [STORAGE_INTERNAL_MULTI_APP] , [STORAGE_EXTERNAL] , [STORAGE_OTG] , [STORAGE_DMF]}
     */
    fun getStorageByPath(context: Context?, path: String?): Int {
        Log.d(TAG, "getStorageByPath $path")
        if (path.isNullOrEmpty()) {
            return STORAGE_INTERNAL
        }
        val flagIMulti = checkIsMultiAppPath(path)
        if (flagIMulti) {
            return STORAGE_INTERNAL_MULTI_APP
        }
        if (checkIsDfmPath(path)) {
            return STORAGE_DMF
        }
        if (VolumeEnvironment.isSingleSdcard(context)) {
            return STORAGE_INTERNAL
        }
        val internalPath = VolumeEnvironment.getInternalSdPath(context)
        val flagI = ((null != internalPath) && path.startsWith(internalPath, true))
        if (flagI) {
            return STORAGE_INTERNAL
        }
        val externalPath = VolumeEnvironment.getExternalSdPath(context)
        val flagE = ((null != externalPath) && path.startsWith(externalPath, true))
        if (flagE) {
            return STORAGE_EXTERNAL
        }
        return STORAGE_OTG
    }

    @JvmStatic
    private fun getStorageByPath(
        path: String?,
        internalPath: String?,
        externalPath: String?,
        isSingleSdcard: Boolean
    ): Int {
        Log.d(TAG, "getStorageByPath $path")
        if (path.isNullOrEmpty()) {
            return STORAGE_INTERNAL
        }
        val flagIMulti = checkIsMultiAppPath(path)
        if (flagIMulti) {
            return STORAGE_INTERNAL_MULTI_APP
        }
        if (checkIsDfmPath(path)) {
            return STORAGE_DMF
        }
        if (isSingleSdcard) {
            return STORAGE_INTERNAL
        }
        val flagI = ((null != internalPath) && path.startsWith(internalPath, true))
        if (flagI) {
            return STORAGE_INTERNAL
        }
        val flagE = ((null != externalPath) && path.startsWith(externalPath, true))
        if (flagE) {
            return STORAGE_EXTERNAL
        }
        return STORAGE_OTG
    }

    /**
     * Check if the [path] passed in is an DFM path
     * * @param context
     * @param path path
     * @return if the [path] passed in is the dfm path, return true. or return false
     * 判断是否是dfm的路径
     */
    @JvmStatic
    fun checkIsDfmPath(path: String?): Boolean {
        if (path == null) return false
        return path.startsWith(KtConstants.DFM_MOUNT_PATH_SUFFIX, ignoreCase = true)
    }

    /**
     * get the root path of dfm path, like /mnt/dfs/XXXX/
     * @param dfmPath the dfm path
     * @return root path of dfm path, like /mnt/dfs/XXXX/
     */
    @JvmStatic
     fun getDfmRootPath(dfmPath: String): String {
        val dfmRootPathBuilder = StringBuilder()
        val pathList = dfmPath.split(File.separator)
        if (pathList.size >= KtConstants.DFM_ROOT_PATH_SIZE) {
            dfmRootPathBuilder.append(File.separator)
            for (i in 1 until KtConstants.DFM_ROOT_PATH_SIZE) {
                dfmRootPathBuilder.append(pathList[i])
                dfmRootPathBuilder.append(File.separator)
            }
        }
        val rootPath = dfmRootPathBuilder.toString()
        Log.d(TAG, "getDfmRootPath $rootPath")
        return rootPath
    }

    /**
     * 获取 /mnt/dfs/XXXX/ 中的XXXX
     * @param dfmPath the dfm path
     * @return deviceId XXXX
     */
    @JvmStatic
    fun getDfmDeviceIdFromPath(dfmPath: String): String {
        val dfmRootPathBuilder = StringBuilder()
        val pathList = dfmPath.split(File.separator)
        if (pathList.size >= KtConstants.DFM_ROOT_PATH_SIZE) {
            dfmRootPathBuilder.append(pathList[KtConstants.DFM_ROOT_PATH_SIZE - 1])
        }
        val deviceId = dfmRootPathBuilder.toString()
        Log.d(TAG, "getDfmDeviceIdFromPath path $dfmPath, deviceId $deviceId")
        return deviceId
    }

    /**
     * @return true: mean the dfm root file not exist,so that the dfm is disconnected
     * @return false: mean other problems
     */
    @JvmStatic
    fun checkDfmFileAndDfmDisconnected(filePath: String?): Boolean {
        if (checkIsDfmPath(filePath) && filePath != null) {
            val rootPath = getDfmRootPath(filePath)
            if (!File(rootPath).exists()) {
                return true
            }
        }
        return false
    }


    /**
     * run the block and get run time
     * @return the run time in ms
     */
    @JvmStatic
    fun runAndGetTime(block: () -> Unit): Long {
        val startTime = System.currentTimeMillis()
        block.invoke()
        val endTime = System.currentTimeMillis()
        return endTime - startTime
    }

    /**
     * 判断是否插入sdcard
     */
    @JvmStatic
    fun checkHasSdcard(context: Context): Boolean {
        val singleSdcard = VolumeEnvironment.isSingleSdcard(context)
        val externalSdcardMounted = VolumeEnvironment.isExternalSdMounted(context)
        val result = !singleSdcard && externalSdcardMounted
        Log.i(TAG, "checkHasSdcard singleSdcard: $singleSdcard, externalSdcardMounted $externalSdcardMounted, result $result")
        return result
    }

    /**
     * 判断是否插入OTG
     */
    @JvmStatic
    fun checkHasOTG(context: Context): Boolean {
        val otgStatus = VolumeEnvironment.getExternalOTGState(context, false)
        Log.i(TAG, "checkHasOTG otgStatus: $otgStatus")
        return otgStatus
    }


    /**
     * 检查列表中是否存在DFM, sdcard 或 otg的dataBean
     */
    @JvmStatic
    fun checkHasDynamicBeans(selectList: List<BaseFileBean>?): Boolean {
        Log.d(TAG, "checkHasDynamicBeans start")
        var containsDFMBean = false
        var containsOTGBean = false
        var containsSdCardBean = false
        if (selectList.isNullOrEmpty()) {
            Log.w(TAG, "checkHasDynamicBeans input null or empty, return false")
            return false
        }
        val internalPath = VolumeEnvironment.getInternalSdPath(appContext)
        val externalPath = VolumeEnvironment.getExternalSdPath(appContext)
        val isSingleSdcard = VolumeEnvironment.isSingleSdcard(appContext)
        for (bean in selectList) {
            val path = bean.mData
            if (path.isNullOrEmpty()) {
                continue
            }
            val isDfm = checkIsDfmPath(path)
            if (isDfm) {
                containsDFMBean = true
            }
            val isSdcardBean = externalPath != null && checkIsSDPath(appContext, path, externalPath)
            if (isSdcardBean) {
                containsSdCardBean = true
            }
            val isOtgBean = internalPath != null && externalPath != null &&
                    checkIsOTGPath(appContext, path, internalPath, externalPath, isSingleSdcard)
            if (isOtgBean) {
                containsOTGBean = true
            }
        }
        val result = containsDFMBean || containsOTGBean || containsSdCardBean
        Log.d(TAG, "checkHasDynamicBeans selectList size ${selectList.size}, containsDFMBean $containsDFMBean, " +
                "containsOTGBean $containsOTGBean, containsSdCardBean $containsSdCardBean, result $result")
        return result
    }

    @JvmStatic
    fun isExportMainActivity(): Boolean {
        return FeatureCompat.sIsExpRom && !Utils.isOnePlusPhone()
    }

    @JvmStatic
    fun isIllegalPathString(path: String): Boolean {
        return path.contains("../") || path.contains("./")
    }

    @JvmStatic
    fun getOWorkName(type: Int): String {
        val nameId = getOWorkNameResId(type)
        return resources().getString(nameId)
    }

    @JvmStatic
    fun getOWorkNameResId(type: Int): Int {
        return if (SdkUtils.isAtLeastOS16()) {
            when (type) {
                OWORK_NAME_TYPE_1 -> R.string.owork_space_web_page
                OWORK_NAME_TYPE_2 -> R.string.owork_space_web_page_files
                OWORK_NAME_TYPE_3 -> R.string.owork_space_new
                else -> R.string.owork_space_new
            }
        } else {
            R.string.owork_space
        }
    }
}