package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.sort.AbstractFileComparator


/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2020/2/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
open class LastModifiedComparatorGenerator : AbstractFileComparator(), Comparator<BaseFileBean> {

    override fun compare1(o1: BaseFileBean, o2: BaseFileBean, isDesc: Boolean): Int {
        val result = o1.mDateModified - o2.mDateModified
        return when {
            result < 0 -> {
                -1
            }
            result > 0 -> {
                1
            }
            else -> {
                0
            }
        }
    }

    override fun compare(file: BaseFileBean, t1: BaseFileBean): Int {
        return compare1(file, t1, true)
    }

    companion object {

        val comparator = LastModifiedComparatorGenerator()
        val REVERSE_COMPARATOR = comparator.genReverseComparator()

        fun getComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return if (isDesc) {
                comparator.genReverseComparator()
            } else {
                comparator.genComparator()
            }
        }
    }
}