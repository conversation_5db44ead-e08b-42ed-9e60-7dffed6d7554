/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SearchDMPFileWrapper.kt
 ** Description : Search DMP File Wrapper
 ** Version     : 1.0
 ** Date        : 2024/04/28
 ** Author      : W9059186
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9059186      2024/04/28       1.0      create
 ***********************************************************************/
package com.filemanager.common.base

import android.database.Cursor
import android.net.Uri
import android.text.TextUtils
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.RecallTypeUtil
import com.filemanager.common.wrapper.PathFileWrapper
import java.nio.ByteBuffer

class SearchDMPFileWrapper(cursor: Cursor, uri: Uri?) : SearchFileWrapper(cursor, null) {

    companion object {
        private const val TAG = "SearchDMPFileWrapper"

        const val ABSOLUTE_PATH_INDEX = "absolutePath"
        const val BRIEF_TIME_INDEX = "lastModified"
        const val HIGHLIGHT_INDEX = "highlight"
        const val TYPE_INDEX = "type"
        const val SIZE_INDEX = "size"
        const val FILENAME_INDEX = "filename"
        const val IDENTIFICATION_INDEX = "identification"
        const val RECALL_TYPE_INDEX = "recallType"
        const val DIRECTORY_TYPE = 7
    }

    override fun createData(cursor: Cursor, uri: Uri?) {
        val absolutePathIndex = cursor.getColumnIndex(ABSOLUTE_PATH_INDEX)
        val briefTimeIndex = cursor.getColumnIndex(BRIEF_TIME_INDEX)
        val highlightIndex = cursor.getColumnIndex(HIGHLIGHT_INDEX)
        val typeIndex = cursor.getColumnIndex(TYPE_INDEX)
        val sizeIndex = cursor.getColumnIndex(SIZE_INDEX)
        val filenameIndex = cursor.getColumnIndex(FILENAME_INDEX)
        val identificationIndex = cursor.getColumnIndex(IDENTIFICATION_INDEX)
        val recallTypeIndex = cursor.getColumnIndex(RECALL_TYPE_INDEX)

        val recallTypeBlob = cursor.getBlob(recallTypeIndex)
       /* val recallTypes: IntArray = toIntArray(recallTypeBlob)
        val recallTypesStr = StringBuffer()
        recallTypes?.let {
            for (recallType in it) {
                recallTypesStr.append("$recallType ")
            }
            if (it.isNotEmpty()) {
                recallType = it[0]
            }
        }*/
        recallType = RecallTypeUtil.getRecallType(recallTypeBlob)
        mId = cursor.getInt(identificationIndex)
        mData = cursor.getString(absolutePathIndex)
        mDisplayName = cursor.getString(filenameIndex)
        mSize = cursor.getLong(sizeIndex)
        mDateModified = cursor.getLong(briefTimeIndex)
        mLocalFileUri = uri?.buildUpon()?.appendPath(mId.toString())?.build()
        if (highlightIndex != -1) {
            val highlightsStr = cursor.getString(highlightIndex)
            if (TextUtils.isEmpty(highlightsStr).not()) {
                highlight =
                    GsonUtil.fromJson(highlightsStr, HighLightEntity::class.java)
            }
        }
        mData?.let {
            val file = PathFileWrapper(it)
            //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
            if (mSize == 0L) {
                mSize = file.mSize
                mDateModified = file.mDateModified
            }
        }
        val isDir = cursor.getInt(typeIndex) == DIRECTORY_TYPE
        mLocalType = if (isDir) {
            MimeTypeHelper.DIRECTORY_TYPE
        } else {
            MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName)) ?: MimeTypeHelper.UNKNOWN_TYPE
        }
    }
}