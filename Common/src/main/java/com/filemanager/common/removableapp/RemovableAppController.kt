/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemovableAppController
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/29 11:34
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/12/29       1.0      create
 ***********************************************************************/
package com.filemanager.common.removableapp

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Bundle
import android.os.IBinder
import android.os.RemoteException
import androidx.annotation.WorkerThread
import com.filemanager.common.utils.Log
import com.oplus.exsystemservice.removableapp.aidl.IRemovableApp
import com.oplus.exsystemservice.removableapp.aidl.IRemovableAppClient

class RemovableAppController(private val context: Context) {

    private var mIsBind = false
    private var isConnected = false
    private var mIRemovableApp: IRemovableApp? = null
    private var mCallback: RemovableAppStateCallback? = null

    private val mServiceConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            Log.d(TAG, "onServiceConnected")
            mIRemovableApp = IRemovableApp.Stub.asInterface(service)
            isConnected = true
            mIsBind = true
            mCallback?.onOpen()
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            Log.d(TAG, "onServiceDisconnected")
            isConnected = false
            mIsBind = false
            mCallback?.onClose()
        }
    }

    @WorkerThread
    fun open() {
        Log.d(TAG, "open -> isBind = $mIsBind")
        if (!mIsBind) {
            bindService()
        } else {
            mCallback?.onOpen()
        }
    }

    fun restoreApp(packageName: String, action: (Boolean) -> Unit) {
        Log.d(TAG, "restoreApp -> isConnected = $isConnected")
        if (isConnected) {
            try {
                val bundle = Bundle()
                bundle.putString(KEY_INSTALLER, packageName)
                mIRemovableApp?.restoreRemovableApp(packageName, object : IRemovableAppClient.Stub() {
                    override fun onRestoreFinished(returnCode: Int, packageName: String?, intent: Intent?) {
                        val installSucceeded = returnCode == INSTALL_SUCCEEDED
                        Log.d(TAG, "restoreApp -> $packageName success $installSucceeded")
                        action.invoke(installSucceeded)
                        unBindService()
                    }
                }, bundle)
            } catch (e: RemoteException) {
                Log.e(TAG, "restoreApp -> error: ", e)
            }
        }
    }

    fun registerCallback(callback: RemovableAppStateCallback) {
        mCallback = callback
    }

    private fun bindService() {
        Log.d(TAG, "bindService")
        val serviceIntent = RemovableAppCompat.obtainBindServiceIntent()
        try {
            context.bindService(serviceIntent, mServiceConnection, Context.BIND_AUTO_CREATE)
        } catch (e: SecurityException) {
            Log.e(TAG, "bindService error: ", e)
        }
    }

    private fun unBindService() {
        Log.d(TAG, "unBindService")
        isConnected = false
        mIsBind = false
        context.unbindService(mServiceConnection)
    }

    companion object {
        private const val TAG = "RemovableAppController"

        /**
         * Installation return code: [android.content.pm.PackageManager]#INSTALL_SUCCEEDED
         */
        private const val INSTALL_SUCCEEDED = 1

        private const val KEY_INSTALLER = "installer"
    }
}