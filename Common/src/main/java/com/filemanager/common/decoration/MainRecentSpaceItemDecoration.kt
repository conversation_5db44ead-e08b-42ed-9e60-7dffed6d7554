/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.view
 * * Version     : 1.0
 * * Date        : 2020/6/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView


class MainRecentSpaceItemDecoration(private val mSpace: Int, private val mIsRtl: Boolean) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        val layoutParams = view.layoutParams as GridLayoutManager.LayoutParams
        val spanSize = layoutParams.spanSize
        if (spanSize == 1) {
            val spanIndex = layoutParams.spanIndex
            when (spanIndex) {
                0 -> {
                    if (mIsRtl) {
                        outRect.right = mSpace
                    } else {
                        outRect.left = mSpace
                    }
                }
                1 -> {
                    if (mIsRtl) {
                        outRect.right = mSpace / 2
                    } else {
                        outRect.left = mSpace / 2
                    }
                }
                3 -> {
                    if (mIsRtl) {
                        outRect.right = -mSpace / 2
                    } else {
                        outRect.left = -mSpace / 2
                    }
                }
            }
        }
    }
}