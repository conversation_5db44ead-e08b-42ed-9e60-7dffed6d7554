/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : DialogUtil
 * * Version     : 1.0
 * * Date        : 2024/7/9
 * * Author      : W059186
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>      <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import androidx.fragment.app.FragmentActivity
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.utils.Log

object DialogUtil {
    private const val TAG: String = "DialogUtil"

    @JvmStatic
    fun showPermissionDialog(activity: FragmentActivity?, msgId: Int, block: (() -> Unit)) {
        activity?.let { act ->
            if (PrivacyPolicyController.hasAgreeUseNet()) {
                Log.d(TAG, "hasAgreeAdditionalFunctions")
                block.invoke()
            } else {
                Log.d(TAG, "showPermissionDialog")
                val builder: COUIAlertDialogBuilder = COUIAlertDialogBuilder(act).apply {
                    setTitle(com.filemanager.common.R.string.use_net_tips_title)
                    setMessage(msgId)
                    setCancelable(false)
                    setNegativeButton(com.filemanager.common.R.string.disagree) { dialog, _ ->
                        dialog.dismiss()
                    }
                    setPositiveButton(com.filemanager.common.R.string.agree) { _, _ ->
                        block.invoke()
                        PrivacyPolicyController.saveAgreeUseNet(true)
                    }
                }
                val couiAlertDialog = builder.create()
                couiAlertDialog.show()
            }
        }
    }
}