/*********************************************************************************
 * Copyright (C), 2008-2024, Oplus, All rights reserved.
 *
 * File: - SystemBarUtils.kt
 * Description:
 *     The utils to check system bar
 *
 * Version: 1.0
 * Date: 2024-12-02
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-12-02   1.0    Create this module
 *********************************************************************************/
package com.filemanager.common.utils

import android.content.ContentResolver
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.view.View
import androidx.annotation.RequiresApi

object SystemBarUtils {

    const val KEY_NAVIGATION_MODE = "navigation_mode"
    const val KEY_NAVIGATION_MODE_LEGACY = "navigation_gesture"
    const val NAV_GESTURE_STATE_OFF = 0
    const val NAV_GESTURE_STATE_ON = 1
    const val NAV_MODE_GESTURE = 2

    @JvmStatic
    fun isSystemNavigationGestureMode(context: Context): Boolean =
        if (SdkUtils.isAtLeastS()) {
            isNavigationGestureMode(context.contentResolver)
        } else {
            isNavigationGestureModeLegacy(context.contentResolver)
        }

    @RequiresApi(Build.VERSION_CODES.S)
    @JvmStatic
    private fun isNavigationGestureMode(contentResolver: ContentResolver): Boolean {
        val mode =
            Settings.Secure.getInt(contentResolver, KEY_NAVIGATION_MODE, NAV_GESTURE_STATE_OFF)
        return mode == NAV_MODE_GESTURE
    }

    @JvmStatic
    private fun isNavigationGestureModeLegacy(contentResolver: ContentResolver): Boolean {
        val state = Settings.Secure.getInt(
            contentResolver,
            KEY_NAVIGATION_MODE_LEGACY,
            NAV_GESTURE_STATE_OFF
        )
        return state == NAV_GESTURE_STATE_ON
    }

    @JvmStatic
    fun isTaskBarShowing(view: View): Boolean {
        kotlin.runCatching {
            val decorView = com.oplus.wrapper.view.View(view).viewRootImpl?.view ?: return false
            return StatusBarUtil.isDisplayTaskbar(decorView)
        }
        return false
    }

    @JvmStatic
    fun isNavGestureWithoutTaskBar(rootView: View?): Boolean {
        rootView ?: return false
        val isGestureMode = isSystemNavigationGestureMode(rootView.context)
        if (!isGestureMode) {
            return false
        }
        return !StatusBarUtil.isDisplayTaskbar(rootView)
    }
}