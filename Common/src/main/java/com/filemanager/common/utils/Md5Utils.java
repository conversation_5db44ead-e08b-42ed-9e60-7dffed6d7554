/***********************************************************
** Copyright (C), 2008-2017 Oplus. All rights reserved..
** VENDOR_EDIT
** File:
** Description:
** Version:
** Date :
** Author:
**
** ---------------------Revision History: ---------------------
**  <author>    <data>    <version >    <desc>
****************************************************************/
package com.filemanager.common.utils;

import android.annotation.SuppressLint;

import androidx.annotation.VisibleForTesting;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class Md5Utils {

    @SuppressLint("UnsafeHashAlgorithmDetector")
    public static String toKey(String path) {
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(path.getBytes(StandardCharsets.UTF_8));
            return convertToHexString(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            return String.valueOf(path.hashCode());
        }
    }

    public static String convertToHexString(byte data[]) {
        int i = 0;
        final int max = 256;
        final int min = 16;
        StringBuilder buf = new StringBuilder("");
        for (int offset = 0; offset < data.length; offset++) {
            i = data[offset];
            if (i < 0) {
                i += max;
            }
            if (i < min) {
                buf.append("0");
            }
            buf.append(Integer.toHexString(i));
        }

        return buf.toString();
    }
}
