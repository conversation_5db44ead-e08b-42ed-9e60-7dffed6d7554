/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : FilterStrategy
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/9/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/9/11       1      create
 ***********************************************************************/
package com.filemanager.common.filter

class FilterStrategy<T>(val filter: IFilter<T>) {

    fun filterTarget(target: String, list: List<T>): List<T> {
        return filter.filterTarget(target, list)
    }

}