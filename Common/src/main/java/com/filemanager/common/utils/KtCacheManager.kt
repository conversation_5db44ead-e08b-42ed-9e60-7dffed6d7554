/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/5/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.content.pm.PackageManager
import com.filemanager.common.DiskLruCache
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.VolumeEnvironment
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream

private const val TAG = "KtCacheManager"

object KtCacheManager {

    fun getCacheKey(file: BaseFileBean): String? {
        if (file.mData.isNullOrEmpty()) {
            return null
        }
        return Md5Utils.toKey(file.mData.plus(file.mDateModified))
    }

    fun addPackageNameToDisk(file: BaseFileBean, packageName: String, diskLruCache: DiskLruCache?) {
        if (diskLruCache == null) {
            Log.d(TAG, "Failed to addPackageNameToDisk: diskLruCache is null")
            return
        }
        val key = getCacheKey(file)
        if (key.isNullOrEmpty()) {
            Log.d(TAG, "Failed to addPackageNameToDisk: key is null")
            return
        }
        val buf = packageName.toByteArray()
        var editor: DiskLruCache.Editor? = null
        var out: OutputStream? = null
        try {
            editor = diskLruCache?.edit(key)
            out = editor?.newOutputStream(0)
            out?.write(buf)
            out?.flush()
            editor?.commit()
        } catch (e: IOException) {
            Log.e(TAG, "addPackageNameToDisk : ${e.message}")
        } finally {
            if (out != null) {
                try {
                    out.close()
                } catch (e: IOException) {
                    // do nothing
                }

            }
        }
    }

    fun getPackageNameAndSave(context: Context, file: BaseFileBean, diskLruCache: DiskLruCache?): String {
        if (file.mData.isNullOrEmpty()) {
            return ""
        }
        try {
            val pm = context.packageManager
            val packageInfo = pm.getPackageArchiveInfo(file.mData!!, PackageManager.GET_CONFIGURATIONS)
            if (null != packageInfo) {
                addPackageNameToDisk(file, packageInfo.packageName, diskLruCache)
                return packageInfo.packageName
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
        return ""
    }

    fun getPackageNameDiskCache(file: BaseFileBean, diskLruCache: DiskLruCache?): String? {
        if (diskLruCache == null) {
            return null
        }
        val key = getCacheKey(file)
        if (key.isNullOrEmpty()) {
            Log.d(TAG, "Failed to getPackageNameDiskCache: key is null")
            return null
        }
        var inputStream: InputStream? = null
        var snapshot: DiskLruCache.Snapshot? = null
        try {
            snapshot = diskLruCache.get(key)
            if (snapshot != null) {
                inputStream = snapshot.getInputStream(0)
                if (inputStream != null) {
                    val bytes = ByteArray(inputStream.available())
                    inputStream.read(bytes)
                    return String(bytes)
                }
            }
        } catch (e: IOException) {
            Log.e(TAG, "getPackageNameDiskCache : ${e.message}")
        } finally {
            try {
                inputStream?.close()
                snapshot?.close()
            } catch (e: IOException) {
                // do nothing
            }

        }
        return null
    }

    fun getDiskLruCache(context: Context, dirName: String): DiskLruCache? {
        val maxBytesSize = (10 * 1024 * 1024).toLong() // 10M
        return getDiskLruCache(context, dirName, maxBytesSize)
    }

    fun getDiskLruCache(context: Context, dirName: String, maxBytesSize: Long): DiskLruCache? {
        val directory = getCacheDirectory(context, dirName)
        if (!directory.exists()) {
            return null
        }
        val version = getAppVersion(context)

        var diskLruCache: DiskLruCache? = null
        try {
            diskLruCache = DiskLruCache.open(directory, version, 1, maxBytesSize)
        } catch (e: IOException) {
            Log.e(TAG, "getDiskLruCache : ${e.message}")
            diskLruCache = null
        }

        return diskLruCache
    }

    private fun getAppVersion(context: Context): Int {
        val manager = context.packageManager
        try {
            val info = manager.getPackageInfo(context.packageName, 0)
            return info.versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "getAppVersion : ${e.message}")
        }

        return 1
    }

    private fun getCacheDirectory(context: Context, dirname: String): File {
        var path = VolumeEnvironment.getDataDirPath(context, "KtCache")

        val file = File(path + File.separator + dirname)
        if (!file.exists()) {
            if (!file.mkdirs()) {
                Log.e(TAG, "mkdirs error")
            }
        }
        return file
    }
}