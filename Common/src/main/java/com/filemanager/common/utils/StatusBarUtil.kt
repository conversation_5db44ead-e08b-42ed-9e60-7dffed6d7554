/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:StatusBarUtil.kt
 * * Description:
 * * Version:1.0
 * * Date :2021/7/6
 * * Author:W9001165
 * * ---------------------Revision History: ---------------------
 * *  <author>      <data>        <version>       <desc>
 ****************************************************************/
package com.filemanager.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.Build
import android.provider.Settings
import android.view.View
import android.view.ViewGroup
import android.view.WindowInsets
import android.view.WindowManager
import android.widget.ImageView
import androidx.annotation.ColorInt
import androidx.annotation.ColorRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.FragmentActivity
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.coui.appcompat.statusbar.COUIStatusbarTintUtil
import com.coui.appcompat.version.COUIVersionUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.UIConfigMonitor

object StatusBarUtil {
    const val TAG = "StatusBarUtil"

    /**
     * 隐藏导航栏
     */
    private const val HIDE_NAV_ENABLE_KEY = "hide_navigationbar_enable"
    private const val NAV_STATE_VIRTUAL_KEY = 0 //导航栏状态-虚拟按键
    private const val NAV_STATE_SWIPE_UP_GESTURE = 2 //导航栏状态-全屏手势 针对Oppo 老机型的一种手势导航
    private const val NAV_STATE_SWIPE_SIDE_GESTURE = 3    //导航栏状态-全屏手势

    /**
     * 全面屏手势-隐藏手势指示条
     */
    private const val GESTURE_SIDE_HIDE_BAR_ENABLE_KEY = "gesture_side_hide_bar_prevention_enable"

    @SuppressLint("ObsoleteSdkInt")
    @JvmStatic
    fun setStatusBarTransparentAndBlackFont(activity: Activity) {
        val window = activity.window
        val decorView = activity.window.decorView
        if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.LOLLIPOP) {
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            window.statusBarColor = Color.TRANSPARENT
        }
        var flag = decorView.systemUiVisibility
        val versionCode = COUIVersionUtil.getOSVersionCode()
        val white = activity.resources.getBoolean(R.bool.is_status_white)
        if (versionCode >= COUIVersionUtil.COUI_3_0 || versionCode == 0) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            if (COUIDarkModeUtil.isNightMode(activity)) {
                flag = flag and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
                flag = flag and View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv()
            } else {
                flag = if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.M) {
                    if (!white) {
                        flag or View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR
                    } else {
                        flag or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    }
                } else {
                    flag or COUIStatusbarTintUtil.SYSTEM_UI_FLAG_OP_STATUS_BAR_TINT
                }
            }
            decorView.systemUiVisibility = flag
        }
    }

    @JvmStatic
    fun getStatusBarView(context: Context?, @ColorRes color: Int = com.support.appcompat.R.color.coui_transparence): View? {
        context?.let {
            val statusHeight = COUIPanelMultiWindowUtils.getStatusBarHeight(context)
            val imageView = ImageView(context)
            if (color == 0) {
                imageView.setBackgroundResource(com.support.appcompat.R.drawable.coui_statusbar_bg)
            } else {
                imageView.setBackgroundColor(context.getColor(color))
            }
            imageView.scaleType = ImageView.ScaleType.FIT_XY
            imageView.layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, statusHeight);
            return imageView
        } ?: return null
    }

    @JvmStatic
    fun getStatusBarHeight(): Int = COUIPanelMultiWindowUtils.getStatusBarHeight(MyApplication.sAppContext)

    @JvmStatic
    fun setNavigationBarColor(activity: Activity, navigationBarColorRes: Int = -1) {
        if (!checkIsMainActivity(activity)) {
            if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.LOLLIPOP) {
                if (!COUIDarkModeUtil.isNightMode(activity)) {
                    activity.window.navigationBarColor = if (navigationBarColorRes != -1) {
                        activity.resources.getColor(navigationBarColorRes, activity.theme)
                    } else {
                        COUIContextUtil.getAttrColor(activity, com.support.appcompat.R.attr.couiColorBackgroundWithCard)
                    }
                } else {
                    //bug4154313 分屏resize时会导致闪白。解决方案是：应用暗色模式时，设置导航栏背景色为透明，就不会绘制白色
                    activity.window.isNavigationBarContrastEnforced = false
                    activity.window.navigationBarColor = Color.TRANSPARENT
                }
            }
            adaptNavigationBarForOS12(activity)
        }
    }

    /**
     * 获取底部导航栏，工具栏颜色
     */
    @JvmStatic
    @ColorInt
    fun getNavBarColor(context: Context): Int {
        return COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorBar)
    }

    /**
     * 为适配OS12底部导航栏，当是手势导航时，并且需要适配的页面（通常为列表页，可滚动页面）
     * 将底部导航条设为透明，且隐藏导航栏空间，即可出现底部沉浸式效果
     */
    @JvmStatic
    fun adaptNavigationBarForOS12(activity: Activity) {
        if (SdkUtils.isAtLeastS()) {
            val isAdaptNavigationBar = checkIsAdaptNavigationBar(activity)
            //适配12底部导航栏
            val isGestureNavMode = checkIsGestureNavMode(activity)
            Log.d(TAG, "adaptNavigationBarForOS12 isGestureNavMode :$isGestureNavMode  isAdaptNavigationBar:$isAdaptNavigationBar")
            if (isAdaptNavigationBar && isGestureNavMode && !checkIsMainActivity(activity)) {
                activity.window.setDecorFitsSystemWindows(false)
                activity.window.isNavigationBarContrastEnforced = false
                activity.window.navigationBarColor = Color.TRANSPARENT
            }
        }
    }

    /**
     * 为适配OS12底部导航栏
     * 当弹起底部文件操作栏时，需要重置底部导航栏为不透明，
     */
    @JvmStatic
    fun resetAdaptingNavigationBarForOS12(activity: Activity) {
        if (SdkUtils.isAtLeastS()) {
            //适配12底部导航栏
            val isGestureNavMode = checkIsGestureNavMode(activity)
            val isAdaptNavigationBar = checkIsAdaptNavigationBar(activity)
            Log.d(TAG, "resetAdaptingNavigationBarForOS12 isGestureNavMode :$isGestureNavMode  isAdaptNavigationBar:$isAdaptNavigationBar")
            if (isAdaptNavigationBar && isGestureNavMode && !checkIsMainActivity(activity)) {
                activity.window.setDecorFitsSystemWindows(true)
                activity.window.isNavigationBarContrastEnforced = true
                activity.window.navigationBarColor = getNavBarColor(activity)
            }
        }
    }


    /**
     * activity 是否需要适配底部导航栏，由各个 activity中重写，默认为true
     */
    @JvmStatic
    private fun checkIsAdaptNavigationBar(activity: Activity): Boolean {
        if (activity is BaseVMActivity) {
            return activity.isAdaptNavigationBar()
        }
        return false
    }

    /**
     * 判断是否是MainActivity
     */
    @JvmStatic
    fun checkIsMainActivity(activity: Activity): Boolean {
        return activity.javaClass.name == "com.oplus.filemanager.main.ui.MainActivity"
                || activity.javaClass.name == "com.oplus.filemanager.main.ui.ExportMainActivity"
    }

    /**
     * 检测是否是手势导航，是返回True
     */
    @JvmStatic
    fun checkIsGestureNavMode(activity: Activity? = null): Boolean {
        val secureIndex = Settings.Secure.getInt(
                MyApplication.appContext.contentResolver, HIDE_NAV_ENABLE_KEY, NAV_STATE_VIRTUAL_KEY)

        return (secureIndex == NAV_STATE_SWIPE_UP_GESTURE
                || secureIndex == NAV_STATE_SWIPE_SIDE_GESTURE)
    }


    /**
     * 判断导航方式为全面屏时，是否显示手势指示条
     */
    @JvmStatic
    fun checkShowGestureNavBar(context: Context): Boolean {
        val value = Settings.Secure.getInt(context.contentResolver, GESTURE_SIDE_HIDE_BAR_ENABLE_KEY, 0)
        Log.e(TAG, "checkShowGestureNavBar value:$value")
        return value == 0
    }

    /**
     * 适配Taskbar，不是所有页面都需要使用
     * 当有被taskbar遮挡的页面，调用此方法适配，增加底部高度进行适配
     * 使用此方法需要在页面销毁时注销监听
     * @param view 页面容器
     */
    @JvmStatic
    fun adaptTaskbar(activity: AppCompatActivity, view: View) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            view.setOnApplyWindowInsetsListener(object : View.OnApplyWindowInsetsListener {

                override fun onApplyWindowInsets(view: View, insets: WindowInsets): WindowInsets {
                    if (FeatureCompat.isSupportTaskbar && isDisplayTaskbar(activity.window.decorView)) {
                        //非手势导航条或分屏,或浮窗情况，重置padding bottom
                        if (checkNotDisplayTaskbarHeight(activity)) {
                            view.setPadding(view.paddingLeft, view.paddingTop, view.paddingRight, 0)
                        } else {
                            //此高度由适配文档SDK提供，30DP
                            val height = activity.resources.getDimensionPixelSize(R.dimen.navigation_gesture_taskbar_height)
                            view.setPadding(view.paddingLeft, view.paddingTop, view.paddingRight, height)
                        }
                        Log.d(TAG, "adaptTaskbar onApplyWindowInsets height:${view.paddingBottom}")
                    }
                    return insets
                }
            })
        }
    }

    /**
     * 检测当前窗口是否需要兼容taskbar的高度
     * 返回true为不需要
     */
    @JvmStatic
    fun checkNotDisplayTaskbarHeight(activity: FragmentActivity): Boolean {
        return !checkIsGestureNavMode(activity) || activity.isInMultiWindowMode || UIConfigMonitor.isCurrentZoomWindowShow().apply {
            Log.d(TAG, "checkDisplayTaskbarHeight return:$this")
        }
    }

    /**
     * 是否显示taskbar ,可在设置-》大屏应用-》任务栏-》仅显示在桌面 中隐藏taskbar
     * 支持taskbar并且 导航栏bottom高于30dp
     */
    @JvmStatic
    fun isDisplayTaskbar(rootView: View?): Boolean {
        rootView ?: return false
        val navigationBarInsets = ViewCompat.getRootWindowInsets(rootView)?.getInsets(WindowInsetsCompat.Type.navigationBars())
        val taskbarHeight = rootView.resources.getDimensionPixelSize(R.dimen.navigation_gesture_taskbar_height)
        val navigationBarHeight = navigationBarInsets?.bottom ?: 0
        Log.d(TAG, "isDisplayTaskbar navigationBarHeight ${navigationBarInsets?.bottom}  taskbarHeight:$taskbarHeight")
        return navigationBarHeight > taskbarHeight
    }

    /**
     * 防止内存泄漏，在页面销毁的时候取消监听
     */
    @JvmStatic
    fun adaptTaskbarByDestroy(view: View) {
        view.setOnApplyWindowInsetsListener(null)
    }

    @SuppressLint("ObsoleteSdkInt")
    @JvmStatic
    fun setStatusBarBlack(activity: Activity) {
        val window = activity.window
        val decorView = activity.window.decorView
        if (SdkUtils.getSDKVersion() >= Build.VERSION_CODES.LOLLIPOP) {
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            window.statusBarColor = Color.TRANSPARENT
        }
        var flag = decorView.systemUiVisibility
        val versionCode = COUIVersionUtil.getOSVersionCode()
        if (versionCode >= COUIVersionUtil.COUI_3_0 || versionCode == 0) {
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            flag = flag and View.SYSTEM_UI_FLAG_LIGHT_STATUS_BAR.inv()
            flag = flag and View.SYSTEM_UI_FLAG_LIGHT_NAVIGATION_BAR.inv()
            decorView.systemUiVisibility = flag
        }
    }
}