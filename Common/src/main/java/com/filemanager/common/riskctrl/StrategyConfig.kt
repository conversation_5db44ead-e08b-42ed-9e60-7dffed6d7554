/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: StrategyConfig.kt
 ** Description: 风控策略的数据类
 ** Version: 1.0
 ** Date : 2025/04/03
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/03   1.0    create
 ****************************************************************/
package com.filemanager.common.riskctrl

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class StrategyConfig(
    val strategy: List<StrategyItem> = emptyList(),
    val version: String,
    @SerializedName("strategyId")
    val strategyId: String,
    @SerializedName("expireTime")
    val expireTime: Long = -1L,
    val forceUpdate: Boolean = false
)