/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DynamicPressBaseSelectionViewHolder
 ** Description : DynamicPressBaseSelectionViewHolder 提供可以动态决定是否进入选择模式的ViewHolder的处理
 ** Version     : 1.0
 ** Date        : 2024/04/25 19:42
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/25       1.0      create
 ***********************************************************************/
package com.filemanager.common.base

import android.graphics.Rect
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.COUIRecyclerView
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.dragselection.SlideUtils
import com.filemanager.common.utils.PCConnectAction
import com.oplus.dropdrag.base.SelectionViewHolder
import com.oplus.dropdrag.recycleview.ItemDetailsLookup

abstract class DynamicPressBaseSelectionViewHolder(itemView: View) :
    SelectionViewHolder<Int>(itemView), COUIRecyclerView.ICOUIDividerDecorationInterface, IDynamicLongPress {

    companion object {
        private const val TAG = "DynamicPressBaseSelectionViewHolder"
    }

    private var mKey: Int? = null
    var mCheckBox: COUICheckBox? = null
    var dividerLine: View? = null
    var itemCount: Int = 0

    init {
        mDetails = StorageItemDetails(this)
        dividerLine = itemView.findViewById(R.id.divider_line)
    }

    fun updateDividerVisible(lastItemIndex: Int, position: Int) {
        if (lastItemIndex == position) {
            dividerLine?.visibility = View.GONE
        } else {
            dividerLine?.visibility = View.VISIBLE
        }
    }

    override fun updateKey(key: Int?) {
        mKey = key
    }

    open fun isInDragRegionImpl(event: MotionEvent): Boolean {
        return true
    }

    final override fun isInDragRegion(event: MotionEvent): Boolean {
        // When PC Assistant connect, disable drag
        return if (PCConnectAction.isScreenCast()) {
            false
        } else {
            isInDragRegionImpl(event)
        }
    }

    open fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        mCheckBox?.let {
            val checkBoxRect = Rect()
            it.getGlobalVisibleRect(checkBoxRect)
            // add offset to left and right border.consistent with ColorListView
            val leftOffset = SlideUtils.getCheckBoxLeftOffset(MyApplication.sAppContext)
            val rightOffset = SlideUtils.getCheckBoxRightOffset(MyApplication.sAppContext)
            return ((event.rawX > (checkBoxRect.left - leftOffset))
                    && (event.rawX < (checkBoxRect.right + rightOffset)))
        }
        return false
    }


    override fun canLongPressOrClick(): Boolean {
        return true
    }

    final override fun isInSelectRegion(event: MotionEvent): Boolean {
        // When PC Assistant connect, disable slide to choose
        if (PCConnectAction.isScreenCast()) {
            return false
        }
        return isInSelectRegionImpl(event)
    }

    open fun isInClickArea(event: MotionEvent): Boolean {
        // When PC Assistant connect, disable slide to choose
        if (PCConnectAction.isScreenCast()) {
            return false
        }
        return true
    }


    private inner class StorageItemDetails(var canLongPress: IDynamicLongPress) : ItemDetailsLookup.ItemDetails<Int>() {

        override fun inSelectionHotspot(e: MotionEvent): Boolean {
            return isInSelectRegion(e)
        }

        override fun isClickArea(e: MotionEvent): Boolean {
            return isInClickArea(e)
        }

        override fun canLongPressOrClick(): Boolean {
            return canLongPress.canLongPressOrClick()
        }

        override fun inDragRegion(e: MotionEvent): Boolean {
            return isInDragRegion(e)
        }

        override val position: Int
            get() = adapterPosition

        override val selectionKey: Int?
            get() = mKey
    }

    override fun drawDivider(): Boolean {
        return false
    }
}

/**
 * 子类重写这个方法，动态控制长按是否进入选择态
 */
interface IDynamicLongPress {

    /**
     * 判断是否可以长按进入选择模式的接口
     */
    fun canLongPressOrClick(): Boolean
}