/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  ApplicationInfoImageLoadListener.java
 *  * * Description : ApplicationInfoImageLoadListener.java
 *  * * Version     : 1.0
 *  * * Date        : 19-9-20 下午4:58
 *  * * Author      : <EMAIL>
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.filemanager.common.imageloader.application;

import android.graphics.Bitmap;

public interface ApplicationInfoImageLoadListener {
    void onLoadSuccess(String tag, Bitmap bitmap);

    void onLoadFail(String tag, String errorMsg);
}
