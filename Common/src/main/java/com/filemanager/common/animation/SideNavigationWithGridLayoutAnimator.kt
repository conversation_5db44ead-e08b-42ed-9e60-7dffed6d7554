/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: SideNavigationWithGridLayoutAnimator
 ** Description: The grid layout animation when side navigation open or close
 ** Version: 1.0
 ** Date : 2024/11/14
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.animation

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.TimeInterpolator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.PathInterpolator
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.filemanager.common.utils.Log

private const val ITEM_REMOVE_ANIM_TIME = 100L
private const val ITME_ADD_ANIM_TIME = 100L
private const val ITME_CHANGE_ANIM_TIME = 100L
private const val ITME_REMOVE_AND_MOVE_ANIM_TIME = 400L

//0.3f, 0f, 0f, 1f
private const val CONTROL_X1_MOVE = 0.3f
private const val CONTROL_Y1_MOVE = 0f
private const val CONTROL_X2_MOVE = 0f
private const val CONTROL_Y2_MOVE = 1f

//0.33f, 0f, 0.67f, 1f
private const val CONTROL_X1_ALPHA = 0.33f
private const val CONTROL_Y1_ALPHA = 0f
private const val CONTROL_X2_ALPHA = 0.67f
private const val CONTROL_Y2_ALPHA = 1f

class SideNavigationWithGridLayoutAnimator : SimpleItemAnimator() {

    companion object {
        private const val TAG = "SideNavigationWithGridLayoutAnimator"
        private const val DEBUG = false
    }

    private var sDefaultInterpolator: TimeInterpolator? = null
    private val mAdditionsList = ArrayList<ArrayList<RecyclerView.ViewHolder>>()
    private val mMovesList: ArrayList<ArrayList<MoveInfo>> = ArrayList<ArrayList<MoveInfo>>()
    private val mChangesList: ArrayList<ArrayList<ChangeInfo>> = ArrayList<ArrayList<ChangeInfo>>()
    private val mAddAnimations = ArrayList<RecyclerView.ViewHolder>()
    private val mMoveAnimations = ArrayList<RecyclerView.ViewHolder>()
    private val mRemoveAnimations = ArrayList<RecyclerView.ViewHolder>()
    private val mChangeAnimations = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingRemovals = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingAdditions = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingMoves: ArrayList<MoveInfo> = ArrayList<MoveInfo>()
    private val mPendingChanges: ArrayList<ChangeInfo> = ArrayList<ChangeInfo>()

    private var itemAnimMoveListener: ItemAnimMoveListener? = null

    fun setItemAnimMoveListener(listener: ItemAnimMoveListener?) {
        this.itemAnimMoveListener = listener
    }

    fun checkAnim() {
        val removalsPending = mPendingRemovals.isNotEmpty()
        val movesPending = mPendingMoves.isNotEmpty()
        val changesPending = mPendingChanges.isNotEmpty()
        val additionsPending = mPendingAdditions.isNotEmpty()
        if (!removalsPending && !movesPending && !additionsPending && !changesPending) {
            Log.d(TAG, "no item anim")
            itemAnimMoveListener?.onStartMoveDelay(0)
        }
    }

    @Suppress("LongMethod")
    override fun runPendingAnimations() {
        addDuration = ITME_ADD_ANIM_TIME
        removeDuration = ITEM_REMOVE_ANIM_TIME
        changeDuration = ITME_CHANGE_ANIM_TIME
        moveDuration = ITME_REMOVE_AND_MOVE_ANIM_TIME
        val removalsPending = !mPendingRemovals.isEmpty()
        val movesPending = !mPendingMoves.isEmpty()
        val changesPending = !mPendingChanges.isEmpty()
        val additionsPending = !mPendingAdditions.isEmpty()
        if (!removalsPending && !movesPending && !additionsPending && !changesPending) {
            itemAnimMoveListener?.onStartMoveDelay(0)
            return
        }
        // First, remove stuff
        for (holder in mPendingRemovals) {
            animateRemoveImpl(holder)
        }
        mPendingRemovals.clear()
        // Next, move stuff
        if (movesPending) {
            val moves: ArrayList<MoveInfo> = ArrayList<MoveInfo>()
            moves.addAll(mPendingMoves)
            mMovesList.add(moves)
            mPendingMoves.clear()
            val mover = Runnable {
                for (moveInfo in moves) {
                    animateMoveImpl(moveInfo.mHolder, moveInfo.mFromX, moveInfo.mFromY,
                            moveInfo.mToX, moveInfo.mToY)
                }
                moves.clear()
                mMovesList.remove(moves)
            }
            if (removalsPending) {
                val view: View = moves[0].mHolder.itemView
                moveDuration = ITME_REMOVE_AND_MOVE_ANIM_TIME
                itemAnimMoveListener?.onStartMoveDelay(0)
                ViewCompat.postOnAnimationDelayed(view, mover, 0)
            } else {
                itemAnimMoveListener?.onStartMoveDelay(0)
                mover.run()
            }
        } else {
            itemAnimMoveListener?.onStartMoveDelay(0)
        }
        // Next, change stuff, to run in parallel with move animations
        if (changesPending) {
            val changes: ArrayList<ChangeInfo> = ArrayList<ChangeInfo>()
            changes.addAll(mPendingChanges)
            mChangesList.add(changes)
            mPendingChanges.clear()
            val changer = Runnable {
                for (change in changes) {
                    animateChangeImpl(change)
                }
                changes.clear()
                mChangesList.remove(changes)
            }
            if (removalsPending) {
                val holder: RecyclerView.ViewHolder = changes[0].mOldHolder
                ViewCompat.postOnAnimationDelayed(holder.itemView, changer, removeDuration)
            } else {
                changer.run()
            }
        }
        // Next, add stuff
        if (additionsPending) {
            val additions = ArrayList<RecyclerView.ViewHolder>()
            additions.addAll(mPendingAdditions)
            mAdditionsList.add(additions)
            mPendingAdditions.clear()
            val adder = Runnable {
                for (holder in additions) {
                    animateAddImpl(holder)
                }
                additions.clear()
                mAdditionsList.remove(additions)
            }
            if (removalsPending || movesPending || changesPending) {
                val removeDuration: Long = if (removalsPending) removeDuration else 0
                val havePastMoveDur: Long = moveDuration / 2
                val moveDuration = if (movesPending) havePastMoveDur else 0
                val changeDuration: Long = if (changesPending) changeDuration else 0
                val totalDelay = removeDuration + Math.max(moveDuration, changeDuration)
                val view = additions[0].itemView
                ViewCompat.postOnAnimationDelayed(view, adder, totalDelay)
            } else {
                adder.run()
            }
        }
    }

    @Suppress("LongMethod")
    override fun endAnimation(item: RecyclerView.ViewHolder) {
        val view = item.itemView
        // this will trigger end callback which should set properties to their target values.
        view.animate().cancel()
        // if some other animations are chained to end, how do we cancel them as well?
        for (i in mPendingMoves.indices.reversed()) {
            val moveInfo: MoveInfo = mPendingMoves[i]
            if (moveInfo.mHolder === item) {
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(item)
                mPendingMoves.removeAt(i)
            }
        }
        endChangeAnimation(mPendingChanges, item)
        if (mPendingRemovals.remove(item)) {
            view.alpha = 1f
            dispatchRemoveFinished(item)
        }
        if (mPendingAdditions.remove(item)) {
            view.alpha = 1f
            dispatchAddFinished(item)
        }
        for (i in mChangesList.indices.reversed()) {
            val changes: ArrayList<ChangeInfo> = mChangesList[i]
            endChangeAnimation(changes, item)
            if (changes.isEmpty()) {
                mChangesList.removeAt(i)
            }
        }
        for (i in mMovesList.indices.reversed()) {
            val moves: ArrayList<MoveInfo> = mMovesList[i]
            for (j in moves.indices.reversed()) {
                val moveInfo: MoveInfo = moves[j]
                if (moveInfo.mHolder === item) {
                    view.translationY = 0f
                    view.translationX = 0f
                    dispatchMoveFinished(item)
                    moves.removeAt(j)
                    if (moves.isEmpty()) {
                        mMovesList.removeAt(i)
                    }
                    break
                }
            }
        }
        for (i in mAdditionsList.indices.reversed()) {
            val additions = mAdditionsList[i]
            if (additions.remove(item)) {
                view.alpha = 1f
                dispatchAddFinished(item)
                if (additions.isEmpty()) {
                    mAdditionsList.removeAt(i)
                }
            }
        }

        // animations should be ended by the cancel above.
        check(!(mRemoveAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mRemoveAnimations list")
        }
        check(!(mAddAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mAddAnimations list")
        }
        check(!(mChangeAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mChangeAnimations list")
        }
        check(!(mMoveAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mMoveAnimations list")
        }
        dispatchFinishedWhenDone()
    }

    @Suppress("LongMethod")
    override fun endAnimations() {
        var count = mPendingMoves.size
        for (i in count - 1 downTo 0) {
            val item: MoveInfo = mPendingMoves[i]
            val view: View = item.mHolder.itemView
            view.translationY = 0f
            view.translationX = 0f
            dispatchMoveFinished(item.mHolder)
            mPendingMoves.removeAt(i)
        }
        count = mPendingRemovals.size
        for (i in count - 1 downTo 0) {
            val item = mPendingRemovals[i]
            dispatchRemoveFinished(item)
            mPendingRemovals.removeAt(i)
        }
        count = mPendingAdditions.size
        for (i in count - 1 downTo 0) {
            val item = mPendingAdditions[i]
            item.itemView.alpha = 1f
            dispatchAddFinished(item)
            mPendingAdditions.removeAt(i)
        }
        count = mPendingChanges.size
        for (i in count - 1 downTo 0) {
            endChangeAnimationIfNecessary(mPendingChanges[i])
        }
        mPendingChanges.clear()
        if (!isRunning) {
            return
        }
        var listCount = mMovesList.size
        for (i in listCount - 1 downTo 0) {
            val moves: ArrayList<MoveInfo> = mMovesList[i]
            count = moves.size
            for (j in count - 1 downTo 0) {
                val moveInfo: MoveInfo = moves[j]
                val item: RecyclerView.ViewHolder = moveInfo.mHolder
                val view = item.itemView
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(moveInfo.mHolder)
                moves.removeAt(j)
                if (moves.isEmpty()) {
                    mMovesList.remove(moves)
                }
            }
        }
        listCount = mAdditionsList.size
        for (i in listCount - 1 downTo 0) {
            val additions = mAdditionsList[i]
            count = additions.size
            for (j in count - 1 downTo 0) {
                val item = additions[j]
                val view = item.itemView
                view.alpha = 1f
                dispatchAddFinished(item)
                additions.removeAt(j)
                if (additions.isEmpty()) {
                    mAdditionsList.remove(additions)
                }
            }
        }
        listCount = mChangesList.size
        for (i in listCount - 1 downTo 0) {
            val changes: ArrayList<ChangeInfo> = mChangesList[i]
            count = changes.size
            for (j in count - 1 downTo 0) {
                endChangeAnimationIfNecessary(changes[j])
                if (changes.isEmpty()) {
                    mChangesList.remove(changes)
                }
            }
        }
        cancelAll(mRemoveAnimations)
        cancelAll(mMoveAnimations)
        cancelAll(mAddAnimations)
        cancelAll(mChangeAnimations)
        dispatchAnimationsFinished()
    }

    open fun endChangeAnimationIfNecessary(changeInfo: ChangeInfo) {
        if (changeInfo.mOldHolder != null) {
            endChangeAnimationIfNecessary(changeInfo, changeInfo.mOldHolder)
        }
        if (changeInfo.mNewHolder != null) {
            endChangeAnimationIfNecessary(changeInfo, changeInfo.mNewHolder)
        }
    }

    fun cancelAll(viewHolders: List<RecyclerView.ViewHolder>) {
        for (i in viewHolders.indices.reversed()) {
            viewHolders[i].itemView.animate().cancel()
        }
    }

    override fun isRunning(): Boolean {
        return (!mPendingAdditions.isEmpty()
                || !mPendingChanges.isEmpty()
                || !mPendingMoves.isEmpty()
                || !mPendingRemovals.isEmpty()
                || !mMoveAnimations.isEmpty()
                || !mRemoveAnimations.isEmpty()
                || !mAddAnimations.isEmpty()
                || !mChangeAnimations.isEmpty()
                || !mMovesList.isEmpty()
                || !mAdditionsList.isEmpty()
                || !mChangesList.isEmpty())
    }

    /**
     * {@inheritDoc}
     *
     *
     * If the payload list is not empty, DefaultItemAnimator returns `true`.
     * When this is the case:
     *
     *  * If you override [.animateChange], both
     * ViewHolder arguments will be the same instance.
     *
     *  *
     * If you are not overriding [.animateChange],
     * then DefaultItemAnimator will call [.animateMove] and
     * run a move animation instead.
     *
     *
     */
    override fun canReuseUpdatedViewHolder(viewHolder: RecyclerView.ViewHolder, payloads: List<Any?>): Boolean {
        return !payloads.isEmpty() || super.canReuseUpdatedViewHolder(viewHolder, payloads)
    }

    override fun animateRemove(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder)
        mPendingRemovals.add(holder)
        return true
    }

    override fun animateAdd(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder)
        holder.itemView.alpha = 0f
        mPendingAdditions.add(holder)
        return true
    }

    override fun animateMove(
        holder: RecyclerView.ViewHolder,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ): Boolean {
        var fromX = fromX
        var fromY = fromY
        val view = holder.itemView
        fromX += holder.itemView.translationX.toInt()
        fromY += holder.itemView.translationY.toInt()
        resetAnimation(holder)
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if (deltaX == 0 && deltaY == 0) {
            dispatchMoveFinished(holder)
            return false
        }
        if (deltaX != 0) {
            view.translationX = -deltaX.toFloat()
        }
        if (deltaY != 0) {
            view.translationY = -deltaY.toFloat()
        }
        mPendingMoves.add(MoveInfo(holder, fromX, fromY, toX, toY))
        return true
    }

    override fun animateChange(
        oldHolder: RecyclerView.ViewHolder,
        newHolder: RecyclerView.ViewHolder?,
        fromX: Int,
        fromY: Int,
        toX: Int,
        toY: Int
    ): Boolean {
        if (oldHolder === newHolder) {
            /* Don't know how to run change animations when the same view mHolder is re-used.
             run a move animation to handle position changes.*/
            return animateMove(oldHolder, fromX, fromY, toX, toY)
        }
        val prevTranslationX = oldHolder.itemView.translationX
        val prevTranslationY = oldHolder.itemView.translationY
        val prevAlpha = oldHolder.itemView.alpha
        resetAnimation(oldHolder)
        val deltaX = (toX - fromX - prevTranslationX).toInt()
        val deltaY = (toY - fromY - prevTranslationY).toInt()
        // recover prev translation state after ending animation
        oldHolder.itemView.translationX = prevTranslationX
        oldHolder.itemView.translationY = prevTranslationY
        oldHolder.itemView.alpha = prevAlpha
        if (newHolder != null) {
            // carry over translation values
            resetAnimation(newHolder)
            newHolder.itemView.translationX = -deltaX.toFloat()
            newHolder.itemView.translationY = -deltaY.toFloat()
            newHolder.itemView.alpha = 0f
            mPendingChanges.add(ChangeInfo(oldHolder, newHolder, fromX, fromY, toX, toY))
        }
        return true
    }

    open fun resetAnimation(holder: RecyclerView.ViewHolder) {
        if (sDefaultInterpolator == null) {
            sDefaultInterpolator = ValueAnimator().interpolator
        }
        holder.itemView.animate().interpolator = sDefaultInterpolator
        endAnimation(holder)
    }

    @Suppress("CollapsibleIfStatements")
    open fun endChangeAnimation(infoList: MutableList<ChangeInfo>, item: RecyclerView.ViewHolder) {
        for (i in infoList.indices.reversed()) {
            val changeInfo: ChangeInfo = infoList[i]
            if (endChangeAnimationIfNecessary(changeInfo, item)) {
                if (changeInfo.mOldHolder == null && changeInfo.mNewHolder == null) {
                    infoList.remove(changeInfo)
                }
            }
        }
    }

    /**
     * Check the state of currently pending and running animations. If there are none
     * pending/running, call [.dispatchAnimationsFinished] to notify any
     * listeners.
     */
    fun dispatchFinishedWhenDone() {
        if (!isRunning) {
            dispatchAnimationsFinished()
        }
    }

    open fun endChangeAnimationIfNecessary(changeInfo: ChangeInfo, item: RecyclerView.ViewHolder): Boolean {
        var oldItem = false
        if (changeInfo.mNewHolder === item) {
            oldItem = false
        } else if (changeInfo.mOldHolder === item) {
            oldItem = true
        } else {
            return false
        }
        item.itemView.alpha = 1f
        item.itemView.translationX = 0f
        item.itemView.translationY = 0f
        dispatchChangeFinished(item, oldItem)
        return true
    }

    open fun animateRemoveImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val animation = view.animate()
        mRemoveAnimations.add(holder)
        animation.setDuration(removeDuration)
                .setInterpolator(
                    PathInterpolator(
                        CONTROL_X1_ALPHA,
                        CONTROL_Y1_ALPHA,
                        CONTROL_X2_ALPHA,
                        CONTROL_Y2_ALPHA
                    )
                )
                .alpha(0f).setListener(
                        object : AnimatorListenerAdapter() {
                            override fun onAnimationEnd(animator: Animator) {
                                animation.setListener(null)
                                view.alpha = 1f
                                dispatchRemoveFinished(holder)
                                mRemoveAnimations.remove(holder)
                                dispatchFinishedWhenDone()
                            }

                            override fun onAnimationStart(animator: Animator) {
                                dispatchRemoveStarting(holder)
                            }
                        }).start()
    }

    fun animateAddImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val animation = view.animate()
        mAddAnimations.add(holder)
        animation.alpha(1f)
                .setInterpolator(
                    PathInterpolator(
                        CONTROL_X1_ALPHA,
                        CONTROL_Y1_ALPHA,
                        CONTROL_X2_ALPHA,
                        CONTROL_Y2_ALPHA
                    )
                )
                .setDuration(addDuration)
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationCancel(animator: Animator) {
                        view.alpha = 1f
                    }

                    override fun onAnimationEnd(animator: Animator) {
                        animation.setListener(null)
                        dispatchAddFinished(holder)
                        mAddAnimations.remove(holder)
                        dispatchFinishedWhenDone()
                    }

                    override fun onAnimationStart(animator: Animator) {
                        dispatchAddStarting(holder)
                    }
                }).start()
    }

    fun animateMoveImpl(holder: RecyclerView.ViewHolder, fromX: Int, fromY: Int, toX: Int, toY: Int) {
        val view = holder.itemView
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if (deltaX != 0) {
            view.animate().translationX(0f)
        }
        if (deltaY != 0) {
            view.animate().translationY(0f)
        }
        /* make EndActions end listeners instead, since end actions aren't called when
         vpas are canceled (and can't end them. why?)
         need listener functionality in VPACompat for this. Ick.*/
        val animation = view.animate()
        mMoveAnimations.add(holder)
        animation.setDuration(moveDuration)
                .setInterpolator(
                    PathInterpolator(
                        CONTROL_X1_MOVE,
                        CONTROL_Y1_MOVE,
                        CONTROL_X2_MOVE,
                        CONTROL_Y2_MOVE
                    )
                )
                .setListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationCancel(animator: Animator) {
                        if (deltaX != 0) {
                            view.translationX = 0f
                        }
                        if (deltaY != 0) {
                            view.translationY = 0f
                        }
                    }

                    override fun onAnimationEnd(animator: Animator) {
                        animation.setListener(null)
                        dispatchMoveFinished(holder)
                        mMoveAnimations.remove(holder)
                        dispatchFinishedWhenDone()
                    }

                    override fun onAnimationStart(animator: Animator) {
                        dispatchMoveStarting(holder)
                    }
                }).start()
    }

    fun animateChangeImpl(changeInfo: ChangeInfo) {
        val holder: RecyclerView.ViewHolder = changeInfo.mOldHolder
        val view = holder.itemView
        val newHolder: RecyclerView.ViewHolder = changeInfo.mNewHolder
        val newView = newHolder.itemView
        if (view != null) {
            val oldViewAnim = view.animate().setDuration(
                    changeDuration)
            mChangeAnimations.add(changeInfo.mOldHolder)
            oldViewAnim.translationX(changeInfo.mToX - changeInfo.mFromX.toFloat())
            oldViewAnim.translationY(changeInfo.mToY - changeInfo.mFromY.toFloat())
            oldViewAnim.alpha(0f).setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animator: Animator) {
                    oldViewAnim.setListener(null)
                    view.alpha = 1f
                    view.translationX = 0f
                    view.translationY = 0f
                    dispatchChangeFinished(changeInfo.mOldHolder, true)
                    mChangeAnimations.remove(changeInfo.mOldHolder)
                    dispatchFinishedWhenDone()
                }

                override fun onAnimationStart(animator: Animator) {
                    dispatchChangeStarting(changeInfo.mOldHolder, true)
                }
            }).start()
        }
        if (newView != null) {
            val newViewAnimation = newView.animate()
            mChangeAnimations.add(changeInfo.mNewHolder)
            newViewAnimation.translationX(0f).translationY(0f).setDuration(changeDuration)
                    .alpha(1f).setListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationEnd(animator: Animator) {
                            newViewAnimation.setListener(null)
                            newView.alpha = 1f
                            newView.translationX = 0f
                            newView.translationY = 0f
                            dispatchChangeFinished(changeInfo.mNewHolder, false)
                            mChangeAnimations.remove(changeInfo.mNewHolder)
                            dispatchFinishedWhenDone()
                        }

                        override fun onAnimationStart(animator: Animator) {
                            dispatchChangeStarting(changeInfo.mNewHolder, false)
                        }
                    }).start()
        }
    }

    @Suppress("UseDataClass")
    class MoveInfo(var mHolder: RecyclerView.ViewHolder, var mFromX: Int, var mFromY: Int, var mToX: Int, var mToY: Int)

    @Suppress("UseDataClass")
    class ChangeInfo private constructor(var mOldHolder: RecyclerView.ViewHolder, var mNewHolder: RecyclerView.ViewHolder) {
        var mFromX = 0
        var mFromY = 0
        var mToX = 0
        var mToY = 0

        constructor(
            oldHolder: RecyclerView.ViewHolder,
            newHolder: RecyclerView.ViewHolder,
            fromX: Int,
            fromY: Int,
            toX: Int,
            toY: Int
        ) : this(oldHolder, newHolder) {
            mFromX = fromX
            mFromY = fromY
            mToX = toX
            mToY = toY
        }

        override fun toString(): String {
            return ("ChangeInfo{"
                    + "mOldHolder=" + mOldHolder
                    + ", mNewHolder=" + mNewHolder
                    + ", mFromX=" + mFromX
                    + ", mFromY=" + mFromY
                    + ", mToX=" + mToX
                    + ", mToY=" + mToY
                    + '}')
        }
    }

    interface ItemAnimMoveListener {
        fun onStartMoveDelay(delayMillis: Long)
    }
}