/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** All rights reserved.
 ** FileName: EncoderUtils
 ** Description: 加密算法的工具类
 ** Version: 1.0
 ** Date : 2025/03/24
 ** Author: <EMAIL>
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 * chao.xue  2025/03/24      1.0     create
 ****************************************************************/
package com.filemanager.common.utils;

import com.filemanager.common.compat.PropertyCompat;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import kotlin.text.Charsets;

public class EncoderUtils {

    private static final String TAG = "EncryptAlgorithmUtils";
    private static final String SHA_256 = "SHA-256";
    private static final String ENCRYPT_ASTERISK = "*";

    /**
     * sha256加密
     *
     * @param input 要加密的数据
     * @return 加密结果
     */
    public static String sha256(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance(SHA_256);
            byte[] hash = digest.digest(input.getBytes(Charsets.UTF_8));
            return Md5Utils.convertToHexString(hash);
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "sha256 error", e);
        }
        return input;
    }

    /**
     * 加密包名
     * <a href="https://odocs.myoas.com/docs/0l3NVJWX8nSbnV3R">埋点数据整改处理方案指导</a>
     * SHA256(包名/应用名 + SHA256(ro.build.version.release))去标识化
     *
     * @param pkgName   包名
     * @param osVersion android os 版本
     * @return 加密结果
     */
    public static String encodePkg(String pkgName, String osVersion) {
        String osHash = sha256(osVersion);
        return sha256(pkgName + osHash);
    }

    public static String encodePkg(String pkgName) {
        String osVersion = PropertyCompat.getSAndroidVersion();
        return encodePkg(pkgName, osVersion);
    }

    /**
     * 采用部分屏蔽加密包名
     * 屏蔽应用包名中的一部分敏感信息。如“com.oppo.camera”可以使用
     * “com.oppo.c****a”代替；
     * com.tencent.mm 替换为：com.tencent.**
     */
    public static String encryptPackage(String content) {
        int lastPointIndex = content.lastIndexOf(".");
        if (lastPointIndex == -1) { //
            return replaceCenterContent(content, 1);
        }
        String firstHalfContent = content.substring(0, lastPointIndex + 1);
        String endHalfContent = content.substring(lastPointIndex + 1, content.length());
        int keep = 1;
        if (endHalfContent.length() <= 2) {
            keep = 0;
        }
        String endHalfEncryptContent = replaceCenterContent(endHalfContent, keep);
        return firstHalfContent + endHalfEncryptContent;
    }

    /**
     * 替换内容，中间用*替换，前后保留原来的内容
     *
     * @param content 原内容
     * @param keep    前后保留的长度
     */
    public static String replaceCenterContent(String content, int keep) {
        int length = content.length();
        if (keep >= length) {
            return content;
        }
        // 0 <= keep <= length / 2
        int endIndex = length - keep;
        return replaceContent(content, keep, endIndex);
    }

    /**
     * 替换内容，中间用*替换，前后保留原来的内容
     *
     * @param content    原内容
     * @param startIndex 开始位置
     * @param endIndex   结束位置
     */
    public static String replaceContent(String content, int startIndex, int endIndex) {
        int length = content.length();
        // 0 <= startIndex < endIndex <= length
        StringBuilder builder = new StringBuilder();
        builder.append(content.substring(0, startIndex));
        for (int i = startIndex; i < endIndex; i++) {
            builder.append(ENCRYPT_ASTERISK);
        }
        builder.append(content.substring(endIndex, length));
        return builder.toString();
    }
}