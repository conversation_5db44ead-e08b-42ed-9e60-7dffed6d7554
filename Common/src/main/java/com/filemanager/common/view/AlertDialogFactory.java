/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.view;

import static com.filemanager.common.helper.DialogHelperKt.getBottomAlertDialogWindowAnimStyle;
import static com.filemanager.common.helper.DialogHelperKt.getBottomAlertDialogWindowGravity;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.annotation.VisibleForTesting;
import androidx.appcompat.app.AlertDialog;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.dialog.COUIAlertDialogBuilder;
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder;
import com.filemanager.common.R;

import java.util.Objects;

public class AlertDialogFactory {

    public static AlertDialog showCompressCOUIAlertDialog(Context context, DialogInterface.OnClickListener listener) {

        COUIAlertDialogBuilder builder = new COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setWindowGravity(getBottomAlertDialogWindowGravity(context, null))
                .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context, false, null))
                .setNeutralButton(
                        R.string.compress_to_current_folder,
                        listener, false
                )
                .setPositiveButton(R.string.select_target_path, listener)
                .setTitle(R.string.menu_file_list_compress)
                .setNegativeButton(R.string.dialog_cancel, (dialog, whichButton) -> {
                    /* User clicked OK so do some stuff */
                });
        return setDialogButtonTextColor(builder,context);
    }

    public static AlertDialog showDeCompressCOUIAlertDialog(Context context,
                                                            boolean isPreviewMode,
                                                            DialogInterface.OnClickListener listener,
                                                            String directoryName,
                                                            boolean decompressCurrentDir) {
        COUIAlertDialogBuilder builder = new COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setWindowGravity(getBottomAlertDialogWindowGravity(context, null))
                .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context, false, null))
                .setTitle(R.string.menu_file_list_decompress)
                .setNegativeButton(R.string.dialog_cancel, (dialog, whichButton) -> {
                    /* User clicked OK so do some stuff */
                });
        if (isPreviewMode && !TextUtils.isEmpty(directoryName)) {
            String decompressItem1 = context.getResources().getString(R.string.decompress_to_current_folder);
            String decompressItem2 = context.getResources().getString(R.string.select_target_path);
            if (decompressCurrentDir) {
                builder.setNeutralButton(decompressItem1,listener);
            }
            builder.setPositiveButton(decompressItem2,listener);
        } else {
            if (decompressCurrentDir) {
                builder.setNeutralButton(R.string.decompress_to_current_folder,listener);
            }
            builder.setPositiveButton(R.string.select_target_path,listener);
        }
        return setDialogButtonTextColor(builder,context);
    }

    private static @NonNull AlertDialog setDialogButtonTextColor(COUIAlertDialogBuilder builder, Context context) {
        AlertDialog dialog = builder.show();
        setTextColor(dialog, DialogInterface.BUTTON_POSITIVE, context);
        setTextColor(dialog, DialogInterface.BUTTON_NEGATIVE, context);
        setTextColor(dialog, DialogInterface.BUTTON_NEUTRAL, context);
        return dialog;
    }

    private static void setTextColor(AlertDialog dialog, int button, Context context) {
        int textColor = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorPrimary);
        (Objects.requireNonNull(dialog.getButton(button))).setTextColor(textColor);
    }

    public static AlertDialog showDeCompressCOUIAlertDialog(Context context,
                                                            boolean isPreviewMode,
                                                            DialogInterface.OnClickListener listener,
                                                            String directoryName,
                                                            String msg) {
        COUIAlertDialogBuilder builder = new COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
                .setTitle(R.string.menu_file_list_decompress)
                .setNegativeButton(R.string.dialog_cancel, (dialog, whichButton) -> {
                    /* User clicked OK so do some stuff */
                });
        builder.setMessage(msg);
        if (isPreviewMode && !TextUtils.isEmpty(directoryName)) {
            String decompressItem1 = context.getResources().getString(R.string.decompress_to_current_folder);
            String decompressItem2 = context.getResources().getString(R.string.select_target_path);
            builder.setNeutralButton(decompressItem1,listener);
            builder.setPositiveButton(decompressItem2,listener);
        } else {
            builder.setNeutralButton(R.string.decompress_to_current_folder,listener);
            builder.setPositiveButton(R.string.select_target_path,listener);
        }
        return setDialogButtonTextColor(builder,context);
    }

    public static Dialog createCalculateDialog(Activity activity) {
        return getProgressDialog(activity, activity.getString(R.string.tagprogressbar));
    }

    public static Dialog getProgressDialog(Context context, String message) {
        AlertDialog alertDialog = new COUIRotatingDialogBuilder(context, message).show();
        alertDialog.setCancelable(false);
        return alertDialog;
    }

    /**
     * 只显示标题的弹窗
     * @param context
     * @param titleRes
     * @return
     */
    public static Dialog showSingleTitleDialog(Context context, @StringRes int titleRes) {
        return showSingleTitleDialog(context, context.getString(titleRes));
    }

    /**
     * 只显示标题的弹窗
     * @param context
     * @param title
     * @return
     */
    public static Dialog showSingleTitleDialog(Context context,String title) {
        COUIAlertDialogBuilder builder = getAlertDialogBuilder(context);
        builder.setTitle(title);
        builder.setPositiveButton(R.string.positive_ok, null);
        builder.setCancelable(false);
        return builder.show();
    }

    @VisibleForTesting
    static COUIAlertDialogBuilder getAlertDialogBuilder(Context context) {
        return new COUIAlertDialogBuilder(context);
    }
}
