/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/3/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.graphics.drawable.Drawable
import android.util.SparseArray
import androidx.core.content.ContextCompat
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.helper.MimeTypeHelper
import java.io.File
import java.lang.ref.SoftReference

object KtThumbnailHelper {
    const val DEFAULT_IMAGE_SIZE = 144
    const val DEFAULT_ROUND_RADIUS = 33f
    private var sIsGridMode: Boolean? = null
    private val sImageCache = SparseArray<SoftReference<Drawable?>>()
    private const val TAG = "ThumbnailHelper"
    private const val THUMBNAIL_SUFFIX = ".png"

    private fun releaseImageCache() {
        sImageCache.clear()
    }

    fun getClassifyDrawable(context: Context?, type: Int, isGridMode: Boolean = false): Drawable? {
        if (context == null) {
            return null
        }

        if (sIsGridMode != isGridMode) {
            if (sIsGridMode != null) {
                releaseImageCache()
            }
            sIsGridMode = isGridMode
        }

        var drawable: Drawable? = sImageCache.get(type)?.get()

        if (null == drawable) {
            when (type) {
                MimeTypeHelper.DIRECTORY_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_folder_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.AUDIO_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_audio)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.VIDEO_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_video)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.IMAGE_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_image)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.HTML_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_html_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.TXT_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_txt)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.APPLICATION_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_apk)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.CHM_TYPE, MimeTypeHelper.EPUB_TYPE, MimeTypeHelper.EBK_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_txt)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.DOC_TYPE, MimeTypeHelper.DOCX_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_doc)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.XLS_TYPE, MimeTypeHelper.XLSX_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_excl)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.PPT_TYPE, MimeTypeHelper.PPTX_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_ppt)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.PDF_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_pdf)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.OFD_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_ofd)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.VMSG_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_sms_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.VCF_TYPE, MimeTypeHelper.CSV_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_vcard_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.THEME_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_theme_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.ICS_TYPE, MimeTypeHelper.VCS_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_calendar_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.LRC_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_lrc_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.JAR_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_compress_jar)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.RAR_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_compress_rar)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.ZIP_TYPE, MimeTypeHelper.COMPRESSED_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_compress_zip)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.P7ZIP_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_compress_7z)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.TORRENT_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_torrent_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.KEYNOTE_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_keynote)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.PAGES_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_pages)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.NUMBERS_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_numbers)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.MARKDOWN_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_markdown)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.DWG_TYPE, MimeTypeHelper.DWT_TYPE, MimeTypeHelper.DXF_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_dwg)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.XMIND_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_xmind)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.PSD_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_photoshop)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.AI_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_ai)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.VSDX_TYPE, MimeTypeHelper.VSDM_TYPE, MimeTypeHelper.VSTX_TYPE, MimeTypeHelper.VSTM_TYPE, MimeTypeHelper.VSSX_TYPE,
                MimeTypeHelper.VSSM_TYPE, MimeTypeHelper.VSD_TYPE, MimeTypeHelper.VSS_TYPE, MimeTypeHelper.VST_TYPE, MimeTypeHelper.VDW_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_visio)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.JS_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_js)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.EXE_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_exe)
                    sImageCache.put(type, SoftReference(drawable))
                }

                MimeTypeHelper.DMG_TYPE -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_dmg)
                    sImageCache.put(type, SoftReference(drawable))
                }

                else -> {
                    drawable = ContextCompat.getDrawable(context, R.drawable.ic_file_other_icon)
                    sImageCache.put(type, SoftReference(drawable))
                }
            }
        }
        return drawable
    }

    fun getClassifyResId(type: Int): Int {
        when (type) {
            MimeTypeHelper.DIRECTORY_TYPE -> return R.drawable.ic_file_folder_icon
            MimeTypeHelper.AUDIO_TYPE -> return R.drawable.ic_file_audio
            MimeTypeHelper.VIDEO_TYPE -> return R.drawable.ic_file_video
            MimeTypeHelper.IMAGE_TYPE -> return R.drawable.ic_file_image
            MimeTypeHelper.HTML_TYPE -> return R.drawable.ic_file_html_icon
            MimeTypeHelper.TXT_TYPE -> return R.drawable.ic_file_txt
            MimeTypeHelper.APPLICATION_TYPE -> return R.drawable.ic_file_apk
            MimeTypeHelper.CHM_TYPE, MimeTypeHelper.EPUB_TYPE, MimeTypeHelper.EBK_TYPE -> return R.drawable.ic_file_txt
            MimeTypeHelper.DOC_TYPE, MimeTypeHelper.DOCX_TYPE -> return R.drawable.ic_file_doc
            MimeTypeHelper.XLS_TYPE, MimeTypeHelper.XLSX_TYPE -> return R.drawable.ic_file_excl
            MimeTypeHelper.PPT_TYPE, MimeTypeHelper.PPTX_TYPE -> return R.drawable.ic_file_ppt
            MimeTypeHelper.PDF_TYPE -> return R.drawable.ic_file_pdf
            MimeTypeHelper.OFD_TYPE -> return R.drawable.ic_file_ofd
            MimeTypeHelper.VMSG_TYPE -> return R.drawable.ic_file_sms_icon
            MimeTypeHelper.VCF_TYPE, MimeTypeHelper.CSV_TYPE -> return R.drawable.ic_file_vcard_icon
            MimeTypeHelper.THEME_TYPE -> return R.drawable.ic_file_theme_icon
            MimeTypeHelper.ICS_TYPE, MimeTypeHelper.VCS_TYPE -> return R.drawable.ic_file_calendar_icon
            MimeTypeHelper.LRC_TYPE -> return R.drawable.ic_file_lrc_icon
            MimeTypeHelper.JAR_TYPE -> return R.drawable.ic_file_compress_jar
            MimeTypeHelper.RAR_TYPE -> return R.drawable.ic_file_compress_rar
            MimeTypeHelper.ZIP_TYPE, MimeTypeHelper.COMPRESSED_TYPE -> return R.drawable.ic_file_compress_zip
            MimeTypeHelper.P7ZIP_TYPE -> return R.drawable.ic_file_compress_7z
            MimeTypeHelper.TORRENT_TYPE -> return R.drawable.ic_file_torrent_icon
            MimeTypeHelper.KEYNOTE_TYPE -> return R.drawable.ic_file_keynote
            MimeTypeHelper.PAGES_TYPE -> return R.drawable.ic_file_pages
            MimeTypeHelper.NUMBERS_TYPE -> return R.drawable.ic_file_numbers
            MimeTypeHelper.MARKDOWN_TYPE -> return R.drawable.ic_file_markdown
            MimeTypeHelper.DWG_TYPE, MimeTypeHelper.DWT_TYPE, MimeTypeHelper.DXF_TYPE -> return R.drawable.ic_file_dwg
            MimeTypeHelper.XMIND_TYPE -> return R.drawable.ic_file_xmind
            MimeTypeHelper.PSD_TYPE -> return R.drawable.ic_file_photoshop
            MimeTypeHelper.AI_TYPE -> return R.drawable.ic_file_ai
            MimeTypeHelper.VSDX_TYPE, MimeTypeHelper.VSDM_TYPE, MimeTypeHelper.VSTX_TYPE, MimeTypeHelper.VSTM_TYPE, MimeTypeHelper.VSSX_TYPE,
            MimeTypeHelper.VSSM_TYPE -> return R.drawable.ic_file_visio
            MimeTypeHelper.JS_TYPE -> return R.drawable.ic_file_js
            MimeTypeHelper.EXE_TYPE -> return R.drawable.ic_file_exe
            MimeTypeHelper.DMG_TYPE -> return R.drawable.ic_file_dmg
            else -> return R.drawable.ic_file_other_icon
        }
    }

    /**
     * 生成缩略图的地址
     * @param thumbnailDir 缩略图的保存文件夹
     * @param path 文件的源路径
     * @param modifyTime 文件修改时间
     * @param size 文件大小
     * @return 对应缩略图的地址 Android/data/com.coloros.filemanager/files/<thumbnailDir>/xxx.png
     */
    fun generatorThumbnailPath(thumbnailDir: String, path: String, modifyTime: Long, size: Long): String {
        val context = MyApplication.appContext
        val dir = context.getExternalFilesDir(null)?.absolutePath + File.separator + thumbnailDir
        val thumbnailName = Md5Utils.toKey("${path}_${modifyTime}_$size")
        val sb = StringBuffer()
        sb.append(dir).append(File.separator).append(thumbnailName).append(THUMBNAIL_SUFFIX)
        return sb.toString().apply {
            Log.d(TAG, "generatorThumbnailPath path=$path thumbnail=$this")
        }
    }
}