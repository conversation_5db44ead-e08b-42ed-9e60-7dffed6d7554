/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: JavaFileHelper.kt
 ** Description: File operation function by Java file
 ** Version: 1.0
 ** Date: 2020/2/24
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.fileutils

import androidx.annotation.MainThread
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.HiddenFileHelper.isHiddenFile
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.common.utils.Log
import com.oplus.filemanager.dfm.DFMManager
import kotlinx.coroutines.*
import java.io.File
import java.nio.file.Files
import java.nio.file.Paths

object JavaFileHelper {
    private const val TAG = "JavaFileHelper"
    private const val TIMEOUT_CHECK = 500L

    /**
     * The method is used to invoke the java file interface in main thread for avoid ANR
     *
     * @param method the checked method
     * @param default the default value when checking timeout, you should set it depend on your logic
     */
    @MainThread
    fun <T> safeCheck(method: () -> T, default: T): T {
        Log.d(TAG, "safeCheck start")
        return try {
            runBlocking {
                withTimeoutOrNull(TIMEOUT_CHECK) {
                    // Don't use witContext(Dispatchers.IO), maybe cannot cancel when timeout
                    GlobalScope.async(Dispatchers.IO) {
                        method.invoke()
                    }.await()
                }.let {
                    if (it == null) {
                        Log.d(TAG, "safeCheck timeout: default=$default")
                        default
                    } else it
                }
            }
        } catch (e: Exception) {
            Log.d(TAG, "safeCheck failed: default=$default, ${e.message}")
            default
        }
    }

    fun checkAnyPathExist(paths: Array<String>): Boolean {
        return paths.any { checkPathExist(it) }
    }

    @VisibleForTesting
    fun checkPathExist(path: String): Boolean {
        val volume = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
        val dir = File(volume, path)
        return dir.exists()
    }

    @JvmStatic
    fun exists(file: BaseFileBean): Boolean {
        try {
            val javaFile = File(file.mData)
            return javaFile.exists()
        } catch (e: Exception) {
            Log.w(TAG, "exists file failed: ${file.mData}, ${e.message}")
        }
        return false
    }

    fun renameTo(file: BaseFileBean, name: String): Boolean {
        if (name.isNullOrEmpty() || name.isNullOrBlank()) {
            return false
        }
        try {
            val javaFile = File(file.mData)
            return javaFile.renameTo(File(javaFile.parent, name))
        } catch (e: Exception) {
            Log.w(TAG, "Rename file failed: ${file.mData}, ${e.message}")
        }
        return false
    }

    fun delete(file: BaseFileBean): Boolean {
        try {
            return deleteForJavaFile(File(file.mData))
        } catch (e: Exception) {
            Log.w(TAG, "Delete file failed: ${file.mData}, ${e.message}")
        }
        return false
    }

    private fun deleteForJavaFile(file: File): Boolean {
        if (file.isDirectory) {
            listFiles(file)?.forEach {
                deleteForJavaFile(it)
            }
        }
        return file.delete()
    }

    fun cut(sourceFile: BaseFileBean, dest: BaseFileBean): Boolean {
        try {
            if (!sourceFile.mData.isNullOrEmpty() && !dest.mData.isNullOrEmpty()) {
                return File(sourceFile.mData).renameTo(File(dest.mData))
            }
        } catch (e: Exception) {
            Log.w(TAG, "Cut file failed: ${dest.mData}, ${e.message}")
        }
        return false
    }

    fun copy(sourceFile: BaseFileBean, dest: BaseFileBean): Boolean {
        try {
            if (!sourceFile.mData.isNullOrEmpty() && !dest.mData.isNullOrEmpty()) {
                return Files.copy(Paths.get(sourceFile.mData), Paths.get(dest.mData)) != null
            }
        } catch (e: Exception) {
            Log.w(TAG, "Copy file failed: ${dest.mData}, ${e.message}")
        }
        return false
    }

    /**
     * Get the file list where under the @param file, the file will be created as BaseFileBean
     *
     * @param file the parent dir
     * @param excludeHideFile ignore the hidden file or not
     *
     * @return the file list
     */
    @JvmStatic
    fun listFileBeans(file: BaseFileBean, excludeHideFile: Boolean = true): List<BaseFileBean>? {
        if (file.mData.isNullOrEmpty()) {
            return null
        }
        val fileList = arrayListOf<BaseFileBean>()
        try {
            listFiles(File(file.mData))?.forEach {
                if (!excludeHideFile || !isHiddenFile(it.name)) {
                    fileList.add(PathFileWrapper(it.absolutePath))
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "listFiles failed: ${file.mDisplayName}, ${e.message}")
        }
        return fileList
    }

    /**
     * Get the file list where under the @param file
     *
     * @param file the parent dir
     * @param excludeHideFile ignore the hidden file or not
     *
     * @return the file list
     */
    @JvmStatic
    fun listJavaFiles(file: File, excludeHideFile: Boolean = true): List<File>? {
        try {
            return listFiles(file)?.filter {
                !excludeHideFile || !isHiddenFile(it.name)
            }
        } catch (e: Exception) {
            Log.w(TAG, "listJavaFiles failed: ${file.name}, ${e.message}")
        }
        return null
    }

    /**
     * Get the file list where under the @param file
     * If the file is dfs path, use the DFMManager to get the file otherwise use file.listFiles()
     *
     * @param file the parent dir
     *
     * @return the file list
     */
    @JvmStatic
    fun listFiles(file: File): List<File>? {
        try {
            if (!KtUtils.checkIsDfmPath(file.absolutePath)) {
                val finalFile = File(AndroidDataHelper.replacePath(file.absolutePath))
                return finalFile.listFiles()?.toList()
            } else {
                Log.d(TAG, "listFiles ${file.absolutePath}")
                val fileNames = DFMManager.listFileNames(file.absolutePath)
                if (!fileNames.isNullOrEmpty()) {
                    val listFiles = arrayListOf<File>()
                    for (fileName in fileNames) {
                        val childFile = File(file.absolutePath, fileName)
                        listFiles.add(childFile)
                    }
                    return listFiles
                }
            }
        } catch (e: java.lang.Exception) {
            Log.w(TAG, "listFiles failed ${e.message}")
        }
        return null
    }

    /**
     * Get the file name list where under the @param file
     * If the file is dfs path, use the DFMManager to get the file name list otherwise use file.list()
     *
     * @param file the parent dir
     *
     * @return the file list
     */
    @JvmStatic
    fun list(file: File): Array<String>? {
        try {
            if (!KtUtils.checkIsDfmPath(file.absolutePath)) {
                val finalFile = File(AndroidDataHelper.replacePath(file.absolutePath))
                return finalFile.list()
            } else {
                Log.d(TAG, "list ${file.absolutePath}")
                val fileNames = DFMManager.listFileNames(file.absolutePath)
                if (!fileNames.isNullOrEmpty()) {
                    return fileNames
                }
            }
        } catch (e: java.lang.Exception) {
            Log.w(TAG, "list failed: ${file.name}, ${e.message}")
        }
        return null
    }

    /**
     * Get the directory count where under the @param file
     *
     * @param file the parent dir
     * @param excludeHideFile ignore the hidden file or not
     *
     * @return the directory count
     */
    fun listDirCount(file: BaseFileBean, excludeHideFile: Boolean = true): Int {
        if (file.mData.isNullOrEmpty()) {
            return 0
        }
        try {
            return listFiles(File(file.mData))?.filter {
                (!excludeHideFile || !isHiddenFile(it.name)) && it.isDirectory
            }?.size ?: 0
        } catch (e: Exception) {
            Log.w(TAG, "listDirCount failed: ${file.mData}, ${e.message}")
        }
        return 0
    }

    /**
     * Get the file count where under the @param file, include both file and dir
     *
     * @param file the parent dir
     * @param excludeHideFile ignore the hidden file or not
     *
     * @return the file count
     */
    fun listFilesCount(file: BaseFileBean, excludeHideFile: Boolean = true): Int {
        if (file.mData.isNullOrEmpty()) {
            return 0
        }
        try {
            return list(File(file.mData))?.filter {
                !excludeHideFile || !isHiddenFile(it)
            }?.size ?: 0
        } catch (e: Exception) {
            Log.w(TAG, "listFilesCount failed: ${file.mDisplayName}, ${e.message}")
        }
        return 0
    }

    fun mkdir(file: BaseFileBean): Boolean {
        try {
            return File(file.mData).mkdirs()
        } catch (e: Exception) {
            Log.w(TAG, "mkdir failed: ${file.mData}, ${e.message}")
        }
        return false
    }

    /**
     * File or Directory total size
     *
     * @return total size, include itself, and the child directory also included.
     */
    fun fileTotalSize(file: BaseFileBean): Long {
        if (file.mData.isNullOrEmpty()) {
            return 0
        }
        return fileTotalSize(File(file.mData))
    }

    /**
     * File or Directory total size
     *
     * @return total size, include itself, and the child directory also included.
     */
    fun fileTotalSize(file: File): Long {
        var size = 0L

        fun innerFileSizeAndCount(file: File) {
            listFiles(file)?.forEach {
                innerFileSizeAndCount(it)
            }
            size += file.length()
        }

        innerFileSizeAndCount(file)
        return size
    }

    fun hasFolder(list: List<BaseFileBean>): Boolean {
        if (list.isNotEmpty()) {
            for (baseFileBean in list) {
                if (baseFileBean.mIsDirectory) {
                    return true
                }
            }
        }
        return false
    }

    fun canRead(file: BaseFileBean): Boolean {
        try {
            val javaFile = File(file.mData)
            return javaFile.canRead()
        } catch (e: Exception) {
            Log.w(TAG, "canRead failed: ${file.mData}, ${e.message}")
        }
        return false
    }

    fun canWrite(file: BaseFileBean): Boolean {
        try {
            val javaFile = File(file.mData)
            return javaFile.canWrite()
        } catch (e: Exception) {
            Log.w(TAG, "canWrite failed: ${file.mData}, ${e.message}")
        }
        return false
    }

    fun ensureFileExist(file: File) {
        val parent = file.parentFile
        if (parent?.exists() != true) {
            parent?.mkdirs()
        }
        if (!file.exists()) {
            file.createNewFile()
        }
    }
}