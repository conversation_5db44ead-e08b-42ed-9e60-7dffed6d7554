/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.helper.uiconfig.UIConfigMonitor
 * * Description :
 * * Version     : 1.0
 * * Date        : 2021/5/24
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.helper.uiconfig.type

import android.view.View

/**
 * The layout direction. One of [View.LAYOUT_DIRECTION_RTL, View.LAYOUT_DIRECTION_LTR]
 */
data class LayoutDirectionConfig(val direction: Int) : IUIConfig {

    fun isRTL() = (direction == View.LAYOUT_DIRECTION_RTL)
}
