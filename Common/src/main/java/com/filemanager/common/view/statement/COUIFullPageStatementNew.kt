/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : COUIFullPageStatementNew
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/9/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view.statement

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.res.TypedArray
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.annotation.VisibleForTesting
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.button.COUIButton
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WindowUtils

class COUIFullPageStatementNew @JvmOverloads constructor(private val mContext: Context) : LinearLayout(mContext),
    UIConfigMonitor.OnUIConfigChangeListener {

    var appStatement: TextView? = null
        private set
    var appIconImg: ImageView? = null
        private set
    var appStatementLink: TextView? = null
        private set

    var confirmButton: COUIButton? = null
        private set

    var exitButton: TextView? = null
        private set
    var confirmButtonBig: COUIButton? = null
        private set
    var exitButtonBig: COUIButton? = null
        private set

    //    private TextView mTitle;
    private var mLayoutInflater: LayoutInflater? = null
    private var mOnButtonClickListener: OnButtonClickListener? = null
    private var onConfigChangeListener: OnConfigChangeListener? = null

    var scrollTextView: COUIMaxHeightScrollViewNested? = null
        private set
    @VisibleForTesting
    var mScrollButton: ScrollView? = null
    private var linearLayoutButtonBig: LinearLayout? = null
    private var mCustomView: LinearLayoutCompat? = null
    private var mStyle = 0
    private val mLayoutResourceId: Int = R.layout.coui_full_page_statement_new

    init {
        init()
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        // In tiny style, buttons should keep align bottom
        if (mLayoutResourceId == com.support.statement.R.layout.coui_full_page_statement_tiny) {
            val parent = mScrollButton!!.parent
            if (parent is LinearLayout) {
                val scrollButtonVerticalOffset = (parent.bottom - parent.top
                        - mScrollButton!!.top - mScrollButton!!.measuredHeight)
                mScrollButton!!.layout(
                    mScrollButton!!.left,
                    mScrollButton!!.top + scrollButtonVerticalOffset,
                    mScrollButton!!.right,
                    mScrollButton!!.bottom + scrollButtonVerticalOffset
                )
            }
        }
    }

    @SuppressLint("MissingInflatedId")
    private fun init() {
        mLayoutInflater =
            mContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        val view = mLayoutInflater!!.inflate(mLayoutResourceId, this)
        appStatement = view.findViewById(R.id.txt_statement)
        appIconImg = view.findViewById(R.id.app_icon_img)
        appStatementLink = view.findViewById(R.id.txt_statement_detail_link)
        scrollTextView = view.findViewById(R.id.scroll_text)
        confirmButton = view.findViewById(R.id.btn_confirm)
        exitButton = view.findViewById(R.id.txt_exit)
        confirmButtonBig = view.findViewById(R.id.btn_confirm_big)
        exitButtonBig = view.findViewById(R.id.btn_disagree_big)
        mScrollButton = view.findViewById(R.id.scroll_button)
        linearLayoutButtonBig = view.findViewById(R.id.linear_button_big)
        mCustomView = view.findViewById(com.support.statement.R.id.custom_functional_area)
        updateViewLayout()
        refreshParams()
        appStatement?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G2)
        }
        appStatementLink?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G2)
        }
        confirmButton?.let { confirmButton ->
            confirmButton.isSingleLine = false
            confirmButton.maxLines = CONFIRM_BUTTON_MAX_LINES
            confirmButton.setOnClickListener(OnClickListener {
                mOnButtonClickListener?.onBottomButtonClick()
            })
        }

        exitButton?.setOnClickListener(OnClickListener {
            mOnButtonClickListener?.onExitButtonClick()
        })
        confirmButtonBig?.let { confirmButton ->
            confirmButton.isSingleLine = false
            confirmButton.maxLines = CONFIRM_BUTTON_MAX_LINES
            confirmButton.setOnClickListener(OnClickListener {
                mOnButtonClickListener?.onBottomButtonClick()
            })
        }

        exitButtonBig?.setOnClickListener(OnClickListener {
            mOnButtonClickListener?.onExitButtonClick()
        })
        COUITextViewCompatUtil.setButtonTextView(exitButton)
    }

    override fun onAttachedToWindow() {
        super.onAttachedToWindow()
        UIConfigMonitor.instance.addOnUIConfigChangeListener(this)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        UIConfigMonitor.instance.removeOnUIConfigChangeListener(this)
    }

    @VisibleForTesting
    fun updateViewLayout() {
        val isMultiWindow = UIConfigMonitor.isMultiWindow()
        val currentWindowType = UIConfigMonitor.getWindowType()
        Log.d(TAG, "updateViewLayout currentWindowType:$currentWindowType")
        if (currentWindowType != UIConfig.WindowType.SMALL) {
            Log.d(TAG, " isMiddleAndLargeScreen true")
            linearLayoutButtonBig?.visibility = VISIBLE
            mScrollButton?.visibility = GONE
        } else {
            Log.d(TAG, " isMiddleAndLargeScreen false")
            linearLayoutButtonBig?.visibility = GONE
            mScrollButton?.visibility = VISIBLE
        }
        //小屏分屏; 上下分屏
        if (isMultiWindow && (FeatureCompat.isSmallScreenPhone || isMultiWindowPortrait())) {
            appIconImg?.updateLayoutParams<LayoutParams> {
                topMargin = resources.getDimensionPixelSize(R.dimen.dimen_16dp)
            }
        } else {
            appIconImg?.updateLayoutParams<LayoutParams> {
                topMargin = resources.getDimensionPixelSize(R.dimen.full_page_statement_margin_top)
            }
        }
        //折叠屏全屏切半屏时，Button的宽度异常，重新设置一下
        confirmButton?.updateLayoutParams {
            width =
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.operation_btn_width)
        }
        onConfigChangeListener?.showMaxHeight()
    }

    @VisibleForTesting
    fun isMultiWindowPortrait(): Boolean {
        val width = (context as? Activity)?.resources?.displayMetrics?.widthPixels ?: 0
        val height = (context as? Activity)?.resources?.displayMetrics?.heightPixels ?: 0
        val screenW = WindowUtils.getScreenWidth(context)
        val screenH = WindowUtils.getScreenHeight(context)
        //width==screenW height<screenH 上下分屏
        val isMultiWindowPortrait = (width == screenW && height < screenH)
        Log.d(TAG, "isMultiWindowPortrait:$isMultiWindowPortrait width:$width height:$height screenW:$screenW screenH:$screenH")
        return isMultiWindowPortrait
    }

    fun setAppStatement(statement: CharSequence?) {
        appStatement?.text = statement
    }

    fun setAppStatementLink(statement: CharSequence?) {
        appStatementLink?.text = statement
    }

    fun setButtonText(text: CharSequence?) {
        confirmButton?.text = text
    }

    fun setExitButtonText(text: CharSequence?) {
        exitButton!!.text = text
    }

    fun setButtonListener(listener: OnButtonClickListener?) {
        mOnButtonClickListener = listener
    }
    fun setConfigChangeListener(listener: OnConfigChangeListener?) {
        onConfigChangeListener = listener
    }

    fun setStatementMaxHeight(height: Int) {
        scrollTextView!!.setMaxHeight(height)
    }

    fun setCustomView(content: View?) {
        if (mCustomView != null) {
            if (content == null) {
                mCustomView!!.removeAllViews()
                mCustomView!!.visibility = GONE
            } else {
                mCustomView!!.visibility = VISIBLE
                mCustomView!!.removeAllViews()
                mCustomView!!.addView(content)
            }
        }
    }

    interface OnButtonClickListener {
        fun onBottomButtonClick()
        fun onExitButtonClick()
    }

    interface OnConfigChangeListener {
        fun showMaxHeight()
    }

    fun setExitTextColor(color: Int) {
        exitButton!!.setTextColor(color)
    }

    fun setAppStatementTextColor(color: Int) {
        appStatement!!.setTextColor(color)
    }

    fun refresh() {
        var array: TypedArray? = null
        val styleAttrType = resources.getResourceTypeName(mStyle)
        if ("attr" == styleAttrType) {
            array =
                mContext.obtainStyledAttributes(null, com.support.statement.R.styleable.COUIFullPageStatement, mStyle, 0)
        } else if ("style" == styleAttrType) {
            array =
                mContext.obtainStyledAttributes(null, com.support.statement.R.styleable.COUIFullPageStatement, 0, mStyle)
        }
        if (array != null) {
            exitButton!!.setTextColor(
                array.getColor(
                    com.support.statement.R.styleable.COUIFullPageStatement_couiFullPageStatementTextButtonColor,
                    0
                )
            )
            appStatement!!.setTextColor(
                array.getColor(
                    com.support.statement.R.styleable.COUIFullPageStatement_couiFullPageStatementTextColor,
                    0
                )
            )
            array.recycle()
        }
    }

    /**
     * 适配 超大字体下 1/3屏 4/1屏
     */
    fun refreshParams() {
        val isSmallWindow = context.resources.getBoolean(com.support.statement.R.bool.is_small_window)
        //刷新按钮部分 scrollview 高度
        val scrollParams = mScrollButton!!.layoutParams as RelativeLayout.LayoutParams
        if (isSmallWindow) {
            scrollParams.height =
                context.resources.getDimensionPixelSize(com.support.statement.R.dimen.coui_full_page_statement_button_scrollview_height)
        } else {
            scrollParams.height = RelativeLayout.LayoutParams.WRAP_CONTENT
        }
        mScrollButton!!.layoutParams = scrollParams

        //刷新 底部确认按钮 上下 margin
        val buttonParams = confirmButton!!.layoutParams as LayoutParams
        buttonParams.setMargins(
            0,
            context.resources.getDimensionPixelSize(com.support.statement.R.dimen.coui_full_page_statement_button_margin_top),
            0,
            context.resources.getDimensionPixelSize(com.support.statement.R.dimen.coui_full_page_statement_button_margin)
        )
        confirmButton!!.layoutParams = buttonParams
    }

    fun setButtonDrawableColor(drawableColor: Int) {
        confirmButton!!.setDrawableColor(drawableColor)
    }

    fun setButtonDisableColor(drawableColor: Int) {
        confirmButton!!.setDisabledColor(drawableColor)
    }

    companion object {
        private const val CONFIRM_BUTTON_MAX_LINES = 2
        private const val TAG = "COUIFullPageStatementNew"
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        Log.d(TAG, "onUIConfigChanged")
        updateViewLayout()
        refreshParams()
    }
}