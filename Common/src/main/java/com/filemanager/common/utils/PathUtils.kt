/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PathUtils
 * * Description: PathUtils
 * * Version: 1.0
 * * Date : 2024/11/22
 * * Author:chao.xue
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * *     chao.xue        2024/11/22      1.0            create
 ****************************************************************/
package com.filemanager.common.utils

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.bean.remotedevice.Constants
import com.filemanager.common.constants.Constants.REMOTE_PATH_PREFIX
import com.filemanager.common.constants.Constants.REMOTE_RECENT_DIR_PATH
import com.filemanager.common.helper.VolumeEnvironment
import com.oplus.filemanager.interfaze.remotedevice.IRemoteDevice
import java.io.File

object PathUtils {

    private const val TAG = "PathUtils"

    @JvmStatic
    val sInternalAndroidDataPath by lazy {
        "${VolumeEnvironment.getInternalSdPath(MyApplication.appContext)}${File.separator}" +
                "android${File.separator}data"
    }

    @JvmStatic
    val sInternalAndroidObbPath by lazy {
        "${VolumeEnvironment.getInternalSdPath(MyApplication.appContext)}${File.separator}" +
                "android${File.separator}obb"
    }


    /**
     * 判断该路径是否是受限路径，需要DocumentUI才能打开的路径
     * @param path
     */
    @JvmStatic
    fun isDocumentsUIPath(path: String): Boolean {
        Log.d(TAG, "isDocumentUIPath path:$path")
        if (!SdkUtils.isAtLeastR()) {
            Log.w(TAG, "isDocumentUIPath os version < R,path: $path")
            return false
        }
        return isChildPath(sInternalAndroidDataPath, path) || isChildPath(sInternalAndroidObbPath, path)
    }

    /**
     * 判断某个路径是否是对应目录的子文件夹及本身
     * @param parentPath 父目录
     * @param path 要检查的子目录
     */
    @JvmStatic
    private fun isChildPath(parentPath: String, path: String): Boolean {
        val parentPathWithSeparator = if (parentPath.endsWith(File.separator)) {
            parentPath
        } else {
            parentPath.plus(File.separator)
        }
        return parentPath.equals(path, ignoreCase = true) || path.startsWith(parentPathWithSeparator, ignoreCase = true)
    }

    /**
     * 获取当前远程已经链接的Mac设备的根目录路径
     */
    @JvmStatic
    fun getCurrentRemoteMacRootPath(): String? {
        val remoteDeviceApi = Injector.injectFactory<IRemoteDevice>() ?: return null
        val currentRootPath = remoteDeviceApi.getCurrentLinkedUserPath(null, true)
        android.util.Log.d(TAG, "getCurrentRootPath $currentRootPath")
        return currentRootPath.rootPath
    }

    /**
     * 判断当前的路径是否属于远程Mac根目录路径一部分
     */
    @JvmStatic
    fun checkPathInRemoteMacRootPath(newPath: String): Boolean {
        val currentRemoteRootPath = getCurrentRemoteMacRootPath()
        if (currentRemoteRootPath == null) {
            Log.d(TAG, "checkPathInRemoteMacRootPath no rootPath found, return")
            return false
        }
        val pathWithoutPrefix = if (newPath.startsWith(REMOTE_PATH_PREFIX)) {
            newPath.substring(REMOTE_PATH_PREFIX.length)
        } else {
            newPath
        }
        val result = currentRemoteRootPath.contains(pathWithoutPrefix, true) && (pathWithoutPrefix.length < currentRemoteRootPath.length)
        Log.d(TAG, "checkPathInRemoteMacRootPath currentRemoteRootPath $currentRemoteRootPath, inputPath $newPath, result $result")
        return result
    }

    /**
     * 判断当前路径是不是mac内的路径
     */
    @JvmStatic
    fun checkIsFromRemoteMac(newPath: String): Boolean {
        val currentRemoteRootPath = getCurrentRemoteMacRootPath()
        if (currentRemoteRootPath == null) {
            Log.d(TAG, "checkPathInRemoteMacRootPath no rootPath found, return")
            return false
        }
        val pathWithoutPrefix = if (newPath.startsWith(REMOTE_PATH_PREFIX)) {
            newPath.substring(REMOTE_PATH_PREFIX.length)
        } else {
            newPath
        }
        val result = pathWithoutPrefix.contains(currentRemoteRootPath, true)
        Log.d(TAG, "checkPathInRemoteMacRootPath currentRemoteRootPath $currentRemoteRootPath, inputPath $newPath, result $result")
        return result
    }

    /**
     * 判断当前路径是否是远程电脑文件上面的路径
     */
    @JvmStatic
    fun checkIsRemoteMacPath(path: String): Boolean {
        return path.startsWith(REMOTE_PATH_PREFIX)
    }

    /**
     * 将本地UI上显示的Path转换为远程sdk需要的path（去掉remote_mac前缀，最近使用的文件夹路径做特殊处理）
     */
    @JvmStatic
    fun convertUIPathToSdkPath(inputPath: String): String {
        var sdkPath = if (inputPath.startsWith(Constants.REMOTE_PATH_PREFIX)) {
            inputPath.substring(Constants.REMOTE_PATH_PREFIX.length)
        } else {
            inputPath
        }
        val lastPathItem = sdkPath.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.last()
        if (lastPathItem.contentEquals(REMOTE_RECENT_DIR_PATH, true)) {
            sdkPath = REMOTE_RECENT_DIR_PATH
        }
        Log.d(TAG, "convertUIPathToSdkPath inputPath $inputPath, result: $sdkPath")
        return sdkPath
    }

    /**
     * 获取相对路径
     *
     * @param context
     * @param absolutePath 绝对路径
     * @return 相对路径，将类似：/storage/emulated/0/Download/ 转成 Download/
     */
    @JvmStatic
    fun getRelativePath(context: Context, absolutePath: String): String {
        val rootPath = VolumeEnvironment.getInternalSdPath(context) + File.separator
        return absolutePath.replace(rootPath, "")
    }
}