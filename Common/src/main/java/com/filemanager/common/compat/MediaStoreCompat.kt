/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * File:  - MediaStoreCompat.java
 * Description: Compat class for mediastore query.support version on android Q and R
 * Version: 1.0
 * Date : 2020/04/13
 * Author: <PERSON>afei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * Jiafei.<PERSON>@Apps.FileManager      2020/04/13    1.0     create
 ****************************************************************/
package com.filemanager.common.compat

import android.content.ContentValues
import android.content.Context
import android.os.SystemClock
import android.provider.MediaStore
import android.provider.MediaStore.Images
import android.text.TextUtils
import androidx.annotation.VisibleForTesting
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.compat.compat29.CameraCompatQ
import com.filemanager.common.compat.compat29.GalleryCompatQ
import com.filemanager.common.compat.compat30.CameraCompatR
import com.filemanager.common.compat.compat30.GalleryCompatR
import com.filemanager.common.constants.CommonConstants.COMPRESS_EXT_ARRAY
import com.filemanager.common.constants.KtConstants.LOCAL_VOLUME_MULTI_APP_PATH_Q_S
import com.filemanager.common.constants.KtConstants.LOCAL_VOLUME_MULTI_APP_PATH_R
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.Companion.DOC_TYPE_OFD
import com.filemanager.common.helper.MimeTypeHelper.Companion.DWG_FORMAT
import com.filemanager.common.helper.MimeTypeHelper.Companion.DXF_FORMAT
import com.filemanager.common.helper.MimeTypeHelper.Companion.PSD_FORMAT
import com.filemanager.common.utils.*
import com.filemanager.common.wrapper.AlbumLoadResult
import com.filemanager.common.wrapper.ImageFileWrapper
import com.filemanager.common.wrapper.WebAlbumLoadResult
import java.io.File

object MediaStoreCompat {
    private const val TAG = "MediaStoreCompat"
    const val DEFAULT_CAPACITY = 100
    private const val QUERY_IMAGE_TOTAL_SIZE_GAP = 5 * 60 * 1000L
    const val MEDIA_STORE_CAMERA_RELATIVE_PATH = "DCIM/Camera/"
    private const val DEFAULT_MYALBUM_RELATIVE_PATH = "DCIM/MyAlbums/"
    const val APK_EXTENSION = ".apk"
    private var lastQueryImageCountTime = 0L

    private var SQL_MULTI_APP_FILTER = when {
        SdkUtils.isAtLeastS() -> " (${MediaStore.Files.FileColumns.DATA} " +
                "not like '${LOCAL_VOLUME_MULTI_APP_PATH_R}/%')"
        SdkUtils.isAtLeastR() -> " (${MediaStore.Files.FileColumns.DATA} " +
                "not like '${LOCAL_VOLUME_MULTI_APP_PATH_Q_S}/%')"
        else -> ""
    }

    private const val IMAGE_COUNT_SELECTION_LEGACY = " cshot_id =0 OR " +
            "(_id IN (SELECT _id FROM images WHERE cshot_id > 0 GROUP BY bucket_id)) "

    /**
     * Album path of oppogallery Self-built
     */
    val MYALBUM_PATH by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        getMyAlbumPath(appContext)
    }

    private const val MEDIA_STORE_FILES_COLUMN_ID = 0
    private const val MEDIA_STORE_FILES_COLUMN_DATA = 1
    private const val MEDIA_STORE_FILES_COLUMN_SIZE = 2

    private val imageProjection = arrayOf(
        MediaStore.Files.FileColumns._ID,
        MediaStore.Files.FileColumns.DATA,
        MediaStore.Files.FileColumns.SIZE
    )

    /**
     * @param categoryType category type in CATEGORY_IMAGE, CATEGORY_DOC and so on
     * @return sql string for query
     */
    @JvmStatic
    fun getMediaCountSqlQuery(categoryType: Int): String {
        var sql = when (categoryType) {
            CategoryHelper.CATEGORY_IMAGE -> imageCountSqlQuery
            CategoryHelper.CATEGORY_DOC -> documentCountSqlQuery
            CategoryHelper.CATEGORY_COMPRESS -> archiveCountSqlQuery
            CategoryHelper.CATEGORY_APK -> applicationCountSqlQuery
            else -> SQL_MULTI_APP_FILTER
        }
        sql = addFilterSize(sql)
        Log.d(TAG, "getMediaCountSqlQuery categoryType: $categoryType")
        Log.d(TAG, "getMediaCountSqlQuery sql = $sql")
        return sql
    }

    /**
     * @return all image count in mediastore
     */
    @JvmStatic
    val imageTotalCount: Int
        get() {
            val sqlQuery: String?
            var size = 0
            var totalMemorySize =0L
            if (SdkUtils.isAtLeastR()) {
                val bucketIds: ArrayList<Long> = getBucketIdsForCshot()
                size = bucketIds.size
                Log.d(TAG, "getImageTotalCount cshot size $size")
                val builder = StringBuilder(100)
                val cshotSqlQuery = formatSqlQuerySelectionArgument(bucketIds)
                if (!TextUtils.isEmpty(cshotSqlQuery)) {
                    builder.append(MediaStore.Files.FileColumns.BUCKET_ID)
                            .append(" NOT IN ")
                            .append("(")
                            .append(cshotSqlQuery)
                            .append(")")
                }
                addFilterMultiAppClause(builder)
                addFilterType(builder)
                builder.append(" AND " + MediaStore.Files.FileColumns.SIZE + " > ?")
                sqlQuery = builder.toString()
            } else {
                val builder = StringBuilder(100)
                builder.append(IMAGE_COUNT_SELECTION_LEGACY)
                addFilterType(builder)
                builder.append(" AND " + MediaStore.Files.FileColumns.SIZE + " > ?")
                sqlQuery = builder.toString()
            }
            try {
                Log.d(TAG, "getImageTotalCount start uri $sqlQuery")
                val selectionArgs = arrayOf("0")
                appContext.contentResolver.query(
                    Images.Media.EXTERNAL_CONTENT_URI, imageProjection, sqlQuery, selectionArgs, null
                )?.use { cursor ->
                    Log.d(TAG, "getImageTotalCount cursorCount ${cursor.count}")
                    size += cursor.count
                    if (SystemClock.elapsedRealtime() - lastQueryImageCountTime > QUERY_IMAGE_TOTAL_SIZE_GAP) {
                        Log.d(TAG, "getImageTotalCount statisticsCategoryMemorySize")
                        lastQueryImageCountTime = SystemClock.elapsedRealtime()
                        while (cursor.moveToNext()) {
                            val fileSize = cursor.getLong(MEDIA_STORE_FILES_COLUMN_SIZE)
                            totalMemorySize += fileSize
                        }
                        StatisticsUtils.statisticsCategoryMemorySize(CategoryHelper.CATEGORY_IMAGE, Utils.byteCountToDisplaySize(totalMemorySize))
                    }
                }
            } catch (ex: Exception) {
                Log.e(TAG, "getImageTotalCount error: $ex")
            }
            Log.d(TAG, "getImageTotalCount size: $size ,sqlQuery: $sqlQuery")
            OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_IMAGE, size.toString())
            return size
        }

    /**
     * sql string for image count query
     */
    private val imageCountSqlQuery: String by lazy {
        if (SdkUtils.isAtLeastR()) {
            SQL_MULTI_APP_FILTER
        } else {
            IMAGE_COUNT_SELECTION_LEGACY
        }
    }

    /**
     * sql string for app count query
     */
    private val applicationCountSqlQuery: String by lazy {
        val builder = StringBuilder(DEFAULT_CAPACITY)
        if (SdkUtils.isAtLeastR()) {
            builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                    .append(" LIKE '%")
                    .append(APK_EXTENSION)
                    .append("%'")
            addFilterMultiAppClause(builder)
        } else {
            builder.append(MediaStore.Files.FileColumns.MEDIA_TYPE)
                    .append("=")
                    .append(MediaFileCompat.MEDIA_TYPE_APK)
        }
        builder.toString()
    }

    /**
     * sql string for Archive(zip,rar,zip) count query
     */
    private val archiveCountSqlQuery: String by lazy {
        val builder = StringBuilder(DEFAULT_CAPACITY)
        if (!SdkUtils.isAtLeastR()) {
            builder.append(MediaStore.Files.FileColumns.MEDIA_TYPE)
                    .append("=")
                    .append(MediaFileCompat.MEDIA_TYPE_COMPRESS)
                    .append(" AND ")
        }
        builder.append("(")
        var extension: String?
        val selectLimit = COMPRESS_EXT_ARRAY.size - 1
        for (index in COMPRESS_EXT_ARRAY.indices) {
            extension = COMPRESS_EXT_ARRAY[index]
            builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                    .append(" LIKE '%")
                    .append(extension)
            if (index < selectLimit) {
                builder.append("' OR ")
            } else {
                builder.append("'")
            }
        }
        builder.append(")")
        if (SdkUtils.isAtLeastR()) {
            addFilterMultiAppClause(builder)
        }
        builder.toString()
    }

    /**
     * sql string for document(ppt,doc,xlsl, txt) count query
     */
    private val documentCountSqlQuery: String by lazy {
        val selectionArg = MimeTypeHelper.CATEGORY_DOC.toMutableList()
        getDocumentSqlQuery(selectionArg)
    }

    /**
     * @param categoryType category type in CATEGORY_IMAGE, CATEGORY_DOC and so on
     * @param selectionArg selection argument for query
     * @return sql string for mediastore query
     */
    @JvmStatic
    fun getMediaStoreSqlQuery(categoryType: Int, selectionArg: ArrayList<String?>): String {
        val sql = when (categoryType) {
            CategoryHelper.CATEGORY_DOC -> getDocumentSqlQuery(selectionArg)
            CategoryHelper.CATEGORY_APK -> applicationSqlQuery
            CategoryHelper.CATEGORY_COMPRESS -> getArchiveSqlQuery(selectionArg)
            else -> SQL_MULTI_APP_FILTER
        }
        Log.d(TAG, "getMediaStoreSqlQuery categoryType: $categoryType")
        Log.d(TAG, "getMediaStoreSqlQuery sql = $sql")
        return sql
    }

    /**
     * @param selectionArg selection argument for query
     * @return sql string for archive query
     */
    private fun getArchiveSqlQuery(selectionArg: ArrayList<String?>): String {
        val builder = StringBuilder(DEFAULT_CAPACITY)
        if (!SdkUtils.isAtLeastR()) {
            builder.append(MediaStore.Files.FileColumns.MEDIA_TYPE)
                    .append("=")
                    .append(MediaFileCompat.MEDIA_TYPE_COMPRESS)
        } else {
            addFilterMultiAppClause(builder)
        }
        // no selection arg,just return now
        if (selectionArg.isEmpty()) {
            return builder.toString()
        }
        builder.append(" AND ")
        builder.append("(")
        var index = 0
        for (selection in selectionArg) {
            if (TextUtils.isEmpty(selection)) {
                continue
            }
            builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                    .append(" LIKE '%")
                    .append(selection)
                    .append("'")
            if (index < selectionArg.size - 1) {
                builder.append(" OR ")
            }
            index++
        }
        builder.append(")")
        return builder.toString()
    }

    /**
     * because [LOCAL_VOLUME_MULTI_APP_PATH_Q_S] is still in MediaStore but files which are below it
     * can not be accessed by OplusGallery.(BugId: 585287) So filter it when query.
     */
    fun addFilterMultiAppClause(builder: StringBuilder) {
        if (SQL_MULTI_APP_FILTER.isEmpty().not()) {
            if (builder.isNotEmpty()) {
                builder.append(" AND ")
            }
            builder.append(SQL_MULTI_APP_FILTER)
        }
        builder.append(RecycleBinUtils.SQL_FILTER_RECYCLER_BIN)
    }
    /**
     * 过滤大小为0的文件
     */
    fun addFilterSize(builder: StringBuilder) {
        if (builder.isNotEmpty()) {
            builder.append(" AND")
        }
        builder.append(" (${MediaStore.Files.FileColumns.SIZE} > 0)")
    }

    /**
     * 过滤大小为0的文件
     */
    fun addFilterSize(selection: String?): String {
        val builder: StringBuilder = if (selection.isNullOrEmpty()) {
            StringBuilder()
        } else {
            StringBuilder(selection)
        }
        addFilterSize(builder)
        return builder.toString()
    }

    /**
     * 过滤掉类型为psd,dwg,dxf的文件
     */
    fun addFilterType(builder: StringBuilder) {
        addFilterType(builder, PSD_FORMAT)
        addFilterType(builder, DWG_FORMAT)
        addFilterType(builder, DXF_FORMAT)
    }

    @VisibleForTesting
    fun addFilterType(builder: StringBuilder, format: String) {
        builder.append(" AND (${MediaStore.Files.FileColumns.DISPLAY_NAME} not like '%.$format')")
    }


    /**
     * @param selectionArg selection argument for query
     * @return sql string for document query
     */
    private fun getDocumentSqlQuery(selectionArg: List<String?>): String {
        val emptySql = getDocumentSqlQueryIfEmpty(selectionArg)
        if (emptySql != null) {
            return emptySql
        }
        val result = getDocumentSqlQueryIfOnlyOFD(selectionArg)
        if (result != null) {
            return result
        }
        val builder = StringBuilder(DEFAULT_CAPACITY)
        val ofdSql = getOfdSqlQueryIfHasOFD(selectionArg)
        if (ofdSql != null) {
            builder.append("(").append(ofdSql).append(")")
        }
        val otherDocSql = getOtherDocSqlQuery(selectionArg)
        if (otherDocSql != null) {
            if (ofdSql != null) {
                builder.append(" OR ")
            }
            builder.append("(").append(otherDocSql).append(")")
        }
        Log.d(TAG, "getDocumentSqlQuery selection = $builder")
        return builder.toString()
    }

    private fun getDocumentSqlQueryIfEmpty(selectionArg: List<String?>): String? {
        if (selectionArg.isEmpty()) {
            Log.d(TAG, "getDocumentSqlQueryIfEmpty selectionArg is empty")
            val builder = StringBuilder(DEFAULT_CAPACITY)
            builder.append(MediaStore.Files.FileColumns.MEDIA_TYPE)
                .append("=")
                .append(MediaHelper.MEDIA_TYPE_DOC)
            if (SdkUtils.isAtLeastR()) {
                addFilterMultiAppClause(builder)
            }
            return builder.toString()
        }
        return null
    }

    private fun getDocumentSqlQueryIfOnlyOFD(selectionArg: List<String?>): String? {
        if (selectionArg.isEmpty()) {
            return null
        }
        if (selectionArg.size == 1 && selectionArg.contains(DOC_TYPE_OFD)) {
            val builder = StringBuilder(DEFAULT_CAPACITY)
            builder.append("(")
                .append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                .append(" LIKE ")
                .append("'%$DOC_TYPE_OFD'")
                .append(")")
            if (SdkUtils.isAtLeastR()) {
                addFilterMultiAppClause(builder)
            }
            return builder.toString()
        }
        return null
    }

    private fun getOtherDocSqlQuery(selectionArg: List<String?>): String? {
        if (selectionArg.isEmpty()) {
            return null
        }
        val innerSelectionArg = ArrayList(selectionArg)
        if (innerSelectionArg.size > 1 && innerSelectionArg.contains(DOC_TYPE_OFD)) {
            innerSelectionArg.remove(DOC_TYPE_OFD)
        }
        val builder = StringBuilder(DEFAULT_CAPACITY)
        builder.append(MediaStore.Files.FileColumns.MEDIA_TYPE)
            .append("=")
            .append(MediaHelper.MEDIA_TYPE_DOC)
        if (SdkUtils.isAtLeastR()) {
            addFilterMultiAppClause(builder)
        }
        // no selection arg,just return now
        if (builder.isNotEmpty()) {
            builder.append(" AND ")
        }
        builder.append("(")
        var index = 0
        val selectLimit = innerSelectionArg.size - 1
        for (selection in innerSelectionArg) {
            if (TextUtils.isEmpty(selection)) {
                continue
            }
            builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                .append(" LIKE ")
                .append("'%$selection'")
            if (index < selectLimit) {
                builder.append(" OR ")
            }
            index++
        }
        builder.append(")")
        return builder.toString()
    }

    private fun getOfdSqlQueryIfHasOFD(selectionArg: List<String?>): String? {
        if (selectionArg.isEmpty()) {
            return null
        }
        if (selectionArg.size > 1 && selectionArg.contains(DOC_TYPE_OFD)) {
            Log.d(TAG, "getOfdSqlQueryIfHasOFD")
            val builder = StringBuilder()
            builder.append("(")
                .append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                .append(" LIKE ")
                .append("'%$DOC_TYPE_OFD'")
                .append(")")
            if (SdkUtils.isAtLeastR()) {
                addFilterMultiAppClause(builder)
            }
            return builder.toString()
        }
        return null
    }

    /**
     * sql string for android app query
     */
    private val applicationSqlQuery: String by lazy {
        val builder = StringBuilder(DEFAULT_CAPACITY)
        if (SdkUtils.isAtLeastR()) {
            builder.append(MediaStore.Files.FileColumns.DISPLAY_NAME)
                    .append(" LIKE '%")
                    .append(APK_EXTENSION)
                    .append("'")
            addFilterMultiAppClause(builder)
        } else {
            builder.append(MediaStore.Files.FileColumns.MEDIA_TYPE)
                    .append("=")
                    .append(MediaFileCompat.MEDIA_TYPE_APK)
        }
        builder.toString()
    }

    /**
     * @return all album set in mediastore
     */
    @JvmStatic
    fun getAlbumSet(): ArrayList<AlbumItem> {
        return if (SdkUtils.isAtLeastR()) {
            val albumSet = GalleryCompatR.getAlbumSet()
            albumSet
        } else {
            GalleryCompatQ.getAlbumSet()
        }
    }

    /**
     * @param value sql argument for local album query
     * @return specify album in mediastore
     */
    @JvmStatic
    fun getLocalAlbum(value: ContentValues): AlbumLoadResult<Int> {
        return if (SdkUtils.isAtLeastR()) {
            GalleryCompatR.getLocalAlbum(value)
        } else {
            GalleryCompatQ.getLocalAlbum(value)
        }
    }

    /**
     * @param pageNo number of requested pages
     * @param pageSize the size of the page pair
     * @param limitPages Pages number of data returned
     * @return returns page data, including total size, number of modified pages,
     */
    @JvmStatic
    fun getAllLocalAlbum(relationPath: String, pageNo: Int, pageSize: Int, limitPages: Int = 1): WebAlbumLoadResult {
        return if (SdkUtils.isAtLeastR()) {
            GalleryCompatR.getAllLocalAlbum(relationPath, pageNo, pageSize, limitPages)
        } else {
            GalleryCompatQ.getAllLocalAlbum(relationPath, pageNo, pageSize, limitPages)
        }
    }

    @JvmStatic
    fun getImageItems(pageNumber: Int, pageSize: Int, sortOrder: String?): WebAlbumLoadResult {
        return if (SdkUtils.isAtLeastR()) {
            GalleryCompatR.getImageItems(pageNumber, pageSize, sortOrder)
        } else {
            GalleryCompatQ.getImageItems(pageNumber, pageSize, sortOrder)
        }
    }

    /**
     * @param key sql argument for local album query
     * @return item count of specify album
     */
    @JvmStatic
    fun getAlbumItemCount(key: String?): Int {
        return if (SdkUtils.isAtLeastR()) {
            GalleryCompatR.getAlbumItemCount(key)
        } else {
            GalleryCompatQ.getAlbumItemCount(key)
        }
    }

    /**
     * @param bucketIds parameter list to be formated
     * @return format string for sql query.update or delete
     */
    @JvmStatic
    fun formatSqlQuerySelectionArgument(bucketIds: ArrayList<Long>): String {
        if (bucketIds.isEmpty()) {
            Log.d(TAG, "formatSqlQuerySelectionArgument bucketId is empty")
            return ""
        }
        val result = StringBuilder(DEFAULT_CAPACITY)
        val size = bucketIds.size
        for (index in 0 until size - 1) {
            result.append(bucketIds[index])
            result.append(",")
        }
        result.append(bucketIds[size - 1])
        return result.toString()
    }

    /**
     * @return bucket_id list of all cover item of Cshot Image(include cshot image in Camera folder
     * and cshot image in oppo gallery My Albums)
     */
    private fun getBucketIdsForCshot(): ArrayList<Long> {
        return if (SdkUtils.isAtLeastR()) {
            CameraCompatR.getBucketIdsForCshotCompatR()
        } else {
            CameraCompatQ.getBucketIdsForCshotCompatQ()
        }
    }

    /**
     * @return _id list of all cover item of Camera Image
     * (include cshot image in Camera folder)
     */
    fun getIdsForCameraCshot(): ArrayList<Long> {
        return if (SdkUtils.isAtLeastR()) {
            CameraCompatR.getIdsForCameraCshotCompatR()
        } else {
            CameraCompatQ.getIdsForCameraCshotCompatQ()
        }
    }

    /**
     * @return _id list of all cover item of MyAlbums
     * (include cshot image in oppo gallery My Albums)
     */
    fun getIdsForMyAlbumCshot(key: String): ArrayList<Long> {
        return if (SdkUtils.isAtLeastR()) {
            CameraCompatR.getIdsForMyAlbumCshotCompatR(key)
        } else {
            CameraCompatQ.getIdsForMyAlbumCshotCompatQ(key)
        }
    }

    /**
     * @param path here must be relative_path.
     * @return cshot_id for specify image
     */
    fun getCshotId(path: String?): Long {
        return CameraCompatR.getCshotIdCompatR(path)
    }

    /**
     * @param context for getSharedPreferences
     * @return MyAlbum path of oppogallery to support update path by RUS
     */
    private fun getMyAlbumPath(context: Context?): String {
        if (context == null) {
            return DEFAULT_MYALBUM_RELATIVE_PATH
        }

        var path: String? = PreferencesUtils.getString(FolderNote.MYALBUM_DEFAULT_PATH,
                FolderNote.MYALBUM_DEFAULT_PATH, DEFAULT_MYALBUM_RELATIVE_PATH)
        Log.d(TAG, "getMyAlbumPath path: $path")

        if (path.isNullOrEmpty()) {
            path = DEFAULT_MYALBUM_RELATIVE_PATH
        } else {
            if (path.startsWith(File.separator)) {
                path = path.substring(1)
            }

            if (!path.endsWith(File.separator)) {
                path += File.separator
            }
        }
        Log.d(TAG, "getMyAlbumPath end path: $path")
        return path
    }

    /**
     * @return BaseFileBean list which contains all cShot items which under cShot_Cover
     */
    @WorkerThread
    fun getCShotFiles(fileList: ArrayList<BaseFileBean>) {
        val resultFiles = ArrayList<BaseFileBean>()
        val cShotFiles = ArrayList<BaseFileBean>()
        for (file in fileList) {
            if ((file is ImageFileWrapper) && (file.getCShot() > ImageFileWrapper.EMPTY_CSHOT)) {
                cShotFiles.add(file)
            } else {
                resultFiles.add(file)
            }
        }
        if (SdkUtils.isAtLeastR()) {
            resultFiles.addAll(CameraCompatR.getCShotFiles(cShotFiles))
        } else {
            resultFiles.addAll(CameraCompatQ.getCShotFiles(cShotFiles))
        }
        fileList.clear()
        fileList.addAll(resultFiles)
    }

    /**
     * Comparable class for album in WhiteList sorting
     */
    class AlbumComparable(val mAlbum: AlbumItem, private val mOrder: Int) : Comparable<AlbumComparable> {
        override fun compareTo(other: AlbumComparable): Int {
            return mOrder - other.mOrder
        }
    }
}