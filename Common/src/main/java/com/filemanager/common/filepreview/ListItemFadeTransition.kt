/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: ListItemFadeTransition
 ** Description: The fade transition for list item when preview opened or closed
 ** Version: 1.0
 ** Date : 2024/12/03
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.transition.Transition
import android.transition.TransitionListenerAdapter
import android.transition.TransitionValues
import android.transition.Visibility
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.TextView
import androidx.core.view.children

class ListItemFadeTransition : Visibility() {

    companion object {
        private const val TAG = "ListItemFadeTransition"
        private const val PROPNAME_TRANSITION_ALPHA = "android:fade:transitionAlpha"
        private const val ANIMATION_EASE_TIME = 133L
    }

    override fun captureStartValues(transitionValues: TransitionValues) {
        super.captureStartValues(transitionValues)
        transitionValues.values.put(
            PROPNAME_TRANSITION_ALPHA,
            transitionValues.view.transitionAlpha
        )
    }

    override fun onAppear(
        sceneRoot: ViewGroup?,
        view: View,
        startValues: TransitionValues?,
        endValues: TransitionValues?
    ): Animator? {
        var startAlpha = getStartAlpha(startValues, 0f)
        if (startAlpha == 1f) {
            startAlpha = 0f
        }
        return createAnimation(view, startAlpha, 1f, true)
    }

    override fun onDisappear(
        sceneRoot: ViewGroup?,
        view: View,
        startValues: TransitionValues?,
        endValues: TransitionValues?
    ): Animator? {
        val startAlpha = getStartAlpha(startValues, 1f)
        return createAnimation(view, startAlpha, 0f, false)
    }

    private fun getStartAlpha(startValues: TransitionValues?, fallbackValue: Float): Float {
        var startAlpha = fallbackValue
        if (startValues != null) {
            val startAlphaFloat = startValues.values.get(PROPNAME_TRANSITION_ALPHA) as Float
            if (startAlphaFloat != null) {
                startAlpha = startAlphaFloat
            }
        }
        return startAlpha
    }

    /**
     * Utility method to handle creating and running the Animator.
     */
    private fun createAnimation(view: View, startAlpha: Float, endAlpha: Float, isAppear: Boolean): Animator? {
        if (startAlpha == endAlpha) {
            return null
        }
        view.transitionAlpha = startAlpha
        val anim = ObjectAnimator.ofFloat(view, "transitionAlpha", endAlpha)
        val listener = FadeAnimatorListener(view)
        anim.addListener(listener)
        addListener(object : TransitionListenerAdapter() {
            override fun onTransitionEnd(transition: Transition) {
                view.transitionAlpha = 1f
                transition.removeListener(this)
            }
        })
        val fileIcon = (view as? ViewGroup)?.findViewById<View>(com.filemanager.common.R.id.file_list_item_icon)
        fileIcon?.let { icon ->
            if (isAppear) {
                //将新item布局的初始值修改为不透明，使文件图标和视频时长在动画期间一直保持不透明，再往其他子 textview 添加淡入动画
                view.transitionAlpha = endAlpha
                runTextViewFadeInAnimationOnAppear(view, startAlpha, endAlpha)
            } else {
                //将旧item的文件图标和视频时长设置为完全透明，其他子 textview 保持淡出动画
                icon.transitionAlpha = endAlpha
                val durationView = (view as? ViewGroup)?.findViewById<View>(com.filemanager.common.R.id.file_duration_tv)
                durationView?.transitionAlpha = endAlpha
            }
        }
        return anim
    }

    private fun runTextViewFadeInAnimationOnAppear(view: ViewGroup, startAlpha: Float, endAlpha: Float) {
        for (child in view.children) {
            if (child.id == com.filemanager.common.R.id.file_duration_tv) {
                //视频时长保持不透明，不添加淡入动画
                continue
            } else if (child is Button) {
                continue
            } else if (child is ViewGroup) {
                runTextViewFadeInAnimationOnAppear(child, startAlpha, endAlpha)
            } else if (child is TextView) {
                child.transitionAlpha = startAlpha
                val anim = ObjectAnimator.ofFloat(child, "transitionAlpha", endAlpha)
                anim.duration = ANIMATION_EASE_TIME
                anim.addListener(object : Animator.AnimatorListener {
                    override fun onAnimationStart(animator: Animator) {}

                    override fun onAnimationEnd(animator: Animator) {
                        child.transitionAlpha = 1f
                        anim.removeListener(this)
                    }

                    override fun onAnimationCancel(animator: Animator) {}

                    override fun onAnimationRepeat(animator: Animator) {}
                })
                anim.start()
            }
        }
    }

    private class FadeAnimatorListener(view: View) : AnimatorListenerAdapter() {
        private var view: View? = null
        private var layerTypeChanged = false

        init {
            this.view = view
        }

        override fun onAnimationStart(animator: Animator) {
            if (view?.hasOverlappingRendering() == true && view?.layerType == View.LAYER_TYPE_NONE) {
                layerTypeChanged = true
                view?.setLayerType(View.LAYER_TYPE_HARDWARE, null)
            }
        }

        override fun onAnimationEnd(animator: Animator) {
            view?.transitionAlpha = 1.0f
            if (layerTypeChanged) {
                view?.setLayerType(View.LAYER_TYPE_NONE, null)
            }
            animator.removeAllListeners()
            view = null
        }
    }
}