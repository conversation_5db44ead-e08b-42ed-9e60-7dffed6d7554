/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - MediaFrameLayout.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2024/06/05
 ** Author: V01528827
 **
 ** ---------------------Revision History: ---------------------
 **  <author>          <data>        <version>      <desc>
 **  kangshengsheng     2024/06/05       1.0          media layout
 *******************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.graphics.RectF
import android.util.AttributeSet
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.core.view.forEach
import com.filemanager.common.R

class MediaFrameLayout : FrameLayout {

    companion object {
        private const val NUM_THREE = 3f
        private const val MEDIA_VIEW_RATIO = 2.07f
    }
    private var mediaViewRatio = NUM_THREE / 2f

    private var isLargeLayout = false
    private var radius = 0f

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr, 0) {
        if (attrs == null) {
            return
        }
        var attrArrays = context.obtainStyledAttributes(attrs, R.styleable.mediaframelayout, 0, 0)
        attrArrays?.let {
            isLargeLayout = it.getBoolean(R.styleable.mediaframelayout_isLargeLayout, false)
            radius = it.getDimensionPixelSize(R.styleable.mediaframelayout_layout_radius, 0).toFloat()
            it.recycle()
        }

        if (isLargeLayout) {
            mediaViewRatio = MEDIA_VIEW_RATIO
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        if (childCount == 0) {
            return
        }
        var width = 0
        var height = 0
        if (isLargeLayout) {
            width = MeasureSpec.getSize(widthMeasureSpec)
            height = (width.toFloat() / mediaViewRatio).toInt()

            setMeasuredDimension(width, height)
        } else {
            var childWidth = 0
            height = MeasureSpec.getSize(heightMeasureSpec)
            width = (mediaViewRatio * height.toFloat()).toInt()

            forEach {
                if (it is ViewGroup) {
                    measureChild(it, widthMeasureSpec, heightMeasureSpec)
                    val curViewWidth = it.measuredWidth
                    if (childWidth < curViewWidth) {
                        childWidth = curViewWidth
                    }
                }
            }

            if (childWidth < width) {
                width = childWidth
            }
            setMeasuredDimension(width, height)
        }

        forEach {
            val widthSpec = MeasureSpec.makeMeasureSpec(width, MeasureSpec.EXACTLY)
            val heightSpec = MeasureSpec.makeMeasureSpec(height, MeasureSpec.EXACTLY)
            it.measure(widthSpec, heightSpec)

            if (it is ViewGroup) {
                changeImageScaleType(it)
            }
        }
    }

    private fun changeImageScaleType(view: View) {
        if (view is ImageView) {
            view.scaleType = ImageView.ScaleType.CENTER_CROP
        }
        if (view is ViewGroup) {
            view.forEach {
                changeImageScaleType(it)
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        clipLayout(canvas)
        super.onDraw(canvas)
    }

    override fun drawChild(canvas: Canvas, child: View?, drawingTime: Long): Boolean {
        clipLayout(canvas)
        return super.drawChild(canvas, child, drawingTime)
    }

    private fun clipLayout(canvas: Canvas) {
        if (radius > 0) {
            val radiusArray = floatArrayOf(radius, radius, radius, radius, radius, radius, radius, radius)

            val path = Path()
            path.addRoundRect(RectF(0f, 0f, measuredWidth.toFloat(), measuredHeight.toFloat()),
                radiusArray, Path.Direction.CW)
            canvas.clipPath(path)
        }
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        if (childCount > 0) {
            forEach {
                it.layout(0, 0, measuredWidth, measuredHeight)
            }
        }
    }
}