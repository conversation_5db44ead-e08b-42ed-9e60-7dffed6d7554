/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - DefaultSelectDelegate.kt
 ** Description: Default SelectDelegate for file and Uri
 ** Version: 1.0
 ** Date : 2020/06/04
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/04    1.0     create
 ****************************************************************/

package com.filemanager.common.dragselection

import com.oplus.dropdrag.SelectionDelegate
import com.oplus.dropdrag.SelectionTracker
import com.filemanager.common.base.SelectionViewModel

class DefaultSelectDelegate(private val mViewModel: SelectionViewModel<*, *>?)
    : SelectionDelegate<Int>() {

    override fun enterSelectionMode(key: Int): Boolean {
        return mViewModel?.enterSelectionMode(key) ?: return false
    }

    override fun isInSelectMode(): Boolean {
        return mViewModel?.isInSelectMode() ?: return false
    }

    override fun selectItems(keys: ArrayList<Int>): Boolean {
        return mViewModel?.selectItems(keys) ?: return false
    }

    override fun deselectItems(key: ArrayList<Int>): Boolean {
        return mViewModel?.deselectItems(key) ?: return false
    }

    override fun isItemSelected(key: Int): Boolean {
        return mViewModel?.isItemSelected(key) ?: return false
    }

    override fun getSelectionList(): List<Int> {
        return mViewModel?.getSelectionKeyList() ?: ArrayList<Int>()
    }

    override fun canDragDrop(): Boolean {
        return mViewModel?.canDragDrop() ?: return false
    }

    override fun getLayoutType(): SelectionTracker.LAYOUT_TYPE {
        return mViewModel?.getRecyclerViewScanMode() ?: return SelectionTracker.LAYOUT_TYPE.LIST
    }

}