/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/25, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.content.Intent
import android.view.DragEvent
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.R
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.DELAY_LOAD_DATA_TIME
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.action.DropDispatchAction.PROHIBIT_STATE
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import com.oplus.filemanager.interfaze.main.IMain
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object DropRecycleBinAction : DropListener {

    private const val TAG = "DropRecycleBinAction"

    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem $dropSideItem categoryType $targetCategoryType")

        val recycleBinFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_RECYCLE_BIN
        if (recycleBinFragmentTag == dragTag) {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }
        if (DragUtils.hasAndroidDataFile) {
            CustomToast.showShort(R.string.drag_cloud_not_support_position)
            Log.w(TAG, "handleDropAction hasAndroidDataFile")
            return false
        }
        val parseData = DropUtil.parseDragFile(dragEvent)
        val delayReloadData = dropSideItem && DropUtil.checkIsMediaFragmentTag(dragTag)

        //分布式文管文件，直接删除
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dfmFragmentTag == dragTag) {
            deleteFiles(activity, parseData, isDfsFiles = true, delayReloadData = delayReloadData, true)
            return true
        }

        //最近页面拖拽删除，执行最近页面的删除逻辑
        val recentFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_RECENT
        if (recentFragmentTag == dragTag) {
            if (Injector.injectFactory<IMain>()?.dragFileInRecentOperate(activity, IFileOperate.OP_DELETE_TO_RECYCLE, null) == true) return true
        }

        //内部文件，删除到回收站
        if (dragTag != null) {
            deleteFiles(activity, parseData, isDfsFiles = false, delayReloadData = delayReloadData, true)
            return true
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty()) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
                Log.d(TAG, "drag out media and private files, return!")
                return true
            }

            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                deleteFiles(activity, data, isDfsFiles = false, delayReloadData = false, false)
                return true
            }
            return false
        }

        val privacyFileCallback = {
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            false
        }
        //外部的媒体库文件，删除到回收站
        val mediaFileCallback = {
            deleteFiles(activity, parseData, isDfsFiles = false, delayReloadData = false, false)
            true
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem $dropSideItem categoryType $targetCategoryType")

        val recycleBinFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_RECYCLE_BIN
        if (recycleBinFragmentTag == dragTag) {
            return DropDispatchAction.PROHIBIT_STATE
        }
        if (DragUtils.hasAndroidDataFile) {
            Log.w(TAG, "getCornerMarkStateCode hasAndroidDataFile")
            return PROHIBIT_STATE
        }
        val parseData = DropUtil.parseDragFile(dragEvent)

        //分布式文管文件，直接删除
        val dfmFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DFM
        if (dfmFragmentTag == dragTag) {
            return DropDispatchAction.NORMAL_STATE
        }

        //最近页面拖拽删除，执行最近页面的删除逻辑
        val recentFragmentTag = CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_RECENT
        if (recentFragmentTag == dragTag) {
            return DropDispatchAction.NORMAL_STATE
        }

        //内部文件，删除到回收站
        if (dragTag != null) {
            return DropDispatchAction.NORMAL_STATE
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty()) {
                return DropDispatchAction.PROHIBIT_STATE
            }

            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.NORMAL_STATE
            }
            return DropDispatchAction.PROHIBIT_STATE
        }

        return DropDispatchAction.NORMAL_STATE
    }

    private fun deleteFiles(activity: Activity, parseData: DragParseData, isDfsFiles: Boolean, delayReloadData: Boolean, dragInternal: Boolean) {
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val fileOperateApi = Injector.injectFactory<IFileOperateApi>()
            val fileBeanList = fileOperateApi?.getFileBeanList(activity, parseData, isMediaFiles = true) ?: ArrayList()
            val finalFileBeanList = fileOperateApi?.findMinLayerFolderListBeans(fileBeanList) ?: fileBeanList
            withContext(Dispatchers.Main) {
                val viewModel = DropViewModel(finalFileBeanList)
                viewModel.loadData()
                val lifecycle = activity.lifecycle
                val categoryType = if (isDfsFiles) {
                    CategoryHelper.CATEGORY_DFM
                } else {
                    CategoryHelper.CATEGORY_RECENT
                }
                val fileOperate = Injector.injectFactory<IFileOperateApi>()?.createNormalFileOperate(lifecycle, categoryType, viewModel)
                setResultListener(activity, fileOperate, delayReloadData, dragInternal)
                fileOperate?.onDelete(activity)
            }
        }
    }

    private fun setResultListener(activity: BaseVMActivity, fileOperate: IFileOperate?, delayReloadData: Boolean, dragInternal: Boolean) {
        fileOperate?.setResultListener(object : IFileOperate.OperateResultListener {
            override fun onActionDone(opType: Int, result: Boolean, data: Any?) {
                super.onActionDone(opType, result, data)
                if (delayReloadData) {
                    activity.lifecycleScope.launch(Dispatchers.Main) {
                        delay(DELAY_LOAD_DATA_TIME)
                        activity.onRefreshData()
                    }
                }
                //外部拖入的文件，删除后，需要通知拖入方刷新
                if (!dragInternal) {
                    val intent = Intent(KtConstants.ACTION_DRAG_FILE_CHANGED)
                    intent.putExtra(KtConstants.DRAG_APP_PACKAGE, activity.packageName)
                    activity.sendBroadcast(intent, KtConstants.PROTECT_PERMISSION)
                }
            }
        })
    }
}