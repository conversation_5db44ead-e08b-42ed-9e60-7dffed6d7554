/**********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  ApplicationInfoLoader.java
 *  * * Description : ApplicationInfoLoader.java
 *  * * Version     : 1.0
 *  * * Date        : 19-9-20 下午4:58
 *  * * Author      : <EMAIL>
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */
package com.filemanager.common.imageloader.application;

import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;

import androidx.annotation.Nullable;
import androidx.collection.LruCache;

import com.filemanager.common.DiskLruCache;
import com.filemanager.common.MyApplication;
import com.filemanager.common.base.BaseFileBean;
import com.filemanager.common.helper.MimeTypeHelper;
import com.filemanager.common.imageloader.BitmapCacheManager;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.Utils;

import java.io.File;
import java.io.IOException;
import java.lang.ref.WeakReference;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;


public class ApplicationInfoLoader {
    protected static final String TAG = "ApplicationInfoLoader";
    private static final int CACHE_SIZE = 1024;
    private static final int DEFAULT_CACHE_SIZE = 1024 * 3;
    private static final String CACHE_DIR_NAME = "application_info";
    private static ApplicationInfoLoader sInstance;
    private volatile boolean mStarted;
    private Object mLock = new Object();
    private ApplicationInfoLoader.LoadThread mLoadThread;
    private Handler mHandler = new Handler(Looper.getMainLooper());
    private BlockingQueue<ApplicationInfoLoader.ImageViewName> mImageQueue = new LinkedBlockingQueue<ApplicationInfoLoader.ImageViewName>();

    private volatile LruCache<String, ApplicationInfoDetail> mLruCache = new LruCache<String, ApplicationInfoDetail>(DEFAULT_CACHE_SIZE) {
        @Override
        protected int sizeOf(String key, ApplicationInfoDetail applicationInfoDetail) {
            return (applicationInfoDetail.getSize() / CACHE_SIZE);
        }

        @Override
        protected void entryRemoved(boolean evicted, String key, ApplicationInfoDetail oldValue, ApplicationInfoDetail newValue) {
            // modify for bug 693901, may use a recycled bitmap
            if ((oldValue == null) || (newValue == null)) {
                return;
            }
            if (oldValue.mBitmap == newValue.mBitmap) {
                return;
            }
            if ((null != oldValue.mBitmap) && (!oldValue.mBitmap.isRecycled())) {
                oldValue.mBitmap.recycle();
                oldValue.mBitmap = null;
            }
        }
    };

    private ApplicationInfoLoader() {
        loadThread();
    }

    public static synchronized ApplicationInfoLoader getInstance() {
        if (sInstance == null) {
            synchronized (ApplicationInfoLoader.class) {
                if (sInstance == null) {
                    sInstance = new ApplicationInfoLoader();
                    sInstance.start();
                }
            }
        }
        return sInstance;
    }

    private String getTag(BaseFileBean baseFileBean, ImageView imageView) {
        boolean nightFlag = Utils.isNightMode(MyApplication.getSAppContext());
        String tag = baseFileBean.getMData() + Long.toHexString(baseFileBean.getMSize()) + nightFlag;

        if (baseFileBean.getMDateModified() > 0) {
            tag += Long.toHexString(baseFileBean.getMDateModified());
        } else {
            File file = new File(baseFileBean.getMData());
            if (file.exists()) {
                tag += Long.toHexString(0);
            } else {
                Object oldTag = imageView.getTag();
                if ((oldTag instanceof String) && ((String) oldTag).startsWith(tag)) {
                    Log.d(TAG, " load old tag = " + oldTag);
                    return (String) oldTag;
                }
            }
        }
        return tag;
    }

    public void load(final BaseFileBean baseFileBean, final ImageView imageView, final ApplicationThumbnailLoaderListener loaderListener) {
        String tag = getTag(baseFileBean, imageView);
        if (TextUtils.isEmpty(tag)) {
            Log.d(TAG, " load tag has null and path:" + baseFileBean.getMData());
            return;
        }

        imageView.setTag(tag);
        ApplicationInfoDetail applicationInfoDetail = mLruCache.get(tag);
        if (null == applicationInfoDetail) {
            try {
                mImageQueue.put(new ApplicationInfoLoader.ImageViewName(tag, baseFileBean.getMData(), imageView, loaderListener));
            } catch (InterruptedException e) {
                Log.w(TAG, "load put image view be interrupted.");
            }
        } else {
            Log.v(TAG, " load applicationInfoDetail = " + applicationInfoDetail.mApkName + " " + applicationInfoDetail.mApkVersion);
            if (checkTag(tag, imageView)) {
                loaderListener.onLoad(imageView, applicationInfoDetail.mBitmap, tag);
                loaderListener.onLoadTextView(applicationInfoDetail.mApkName, applicationInfoDetail.mApkVersion, baseFileBean.getMData());
                loaderListener.clear();
            }
        }
    }

    private boolean checkTag(final String tag, final ImageView imageView) {
        return Objects.equals(tag, imageView.getTag());
    }

    private void loadThread() {
        if (mLoadThread == null) {
            mLoadThread = new ApplicationInfoLoader.LoadThread(this);
            mLoadThread.start();
        }
    }

    private static class LoadThread extends Thread {
        private boolean mClosed = false;
        private WeakReference<ApplicationInfoLoader> mAsyncApplicationInfoWeakReference;

        public LoadThread(ApplicationInfoLoader asyncApplicationInfo) {
            mAsyncApplicationInfoWeakReference = new WeakReference<>(asyncApplicationInfo);
        }


        @Override
        public void run() {
            while (!mClosed) {
                if (mAsyncApplicationInfoWeakReference.get() != null) {
                    mAsyncApplicationInfoWeakReference.get().realLoad();
                }
            }
            if (mAsyncApplicationInfoWeakReference.get() != null) {
                mAsyncApplicationInfoWeakReference.get().clearHeapCache();
            }
        }

        public void close() {
            mClosed = true;
        }
    }

    public static void stopApplicationInfoLoader() {
        if (null != sInstance) {
            sInstance.stopLoadThread();
            sInstance = null;
        }
    }

    private void stopLoadThread() {
        if (mLoadThread != null) {
            mLoadThread.interrupt();
            mLoadThread.close();
            mLoadThread = null;
        }
    }

    private void realLoad() {
        try {
            ApplicationInfoLoader.ImageViewName ivn = mImageQueue.take();
            if (ivn != null) {
                final String tag = ivn.mTag;
                final String path = ivn.mPath;
                final ImageView imageView = ivn.mView;
                if (imageView.getVisibility() != View.VISIBLE) {
                    return;
                }
                if ((path != null) && checkTag(tag, imageView)) {
                    final ApplicationThumbnailLoaderListener loader = ivn.mLoader;
                    DiskLruCache diskLruCache = BitmapCacheManager.getDiskLruCache(MyApplication.getSAppContext(), CACHE_DIR_NAME);
                    ApplicationInfoDetail appDiskCache = BitmapCacheManager.getApplicationDiskCache(tag, diskLruCache);
                    if ((appDiskCache == null) || (appDiskCache.mBitmap == null)) {
                        appDiskCache = loader.fetchImage(path, MimeTypeHelper.APPLICATION_TYPE);
                        if (appDiskCache == null) {
                            loader.clear();
                            try {
                                if (diskLruCache != null) {
                                    diskLruCache.close();
                                }
                            } catch (Exception e) {
                                Log.e(TAG, e.getMessage());
                            }
                            return;
                        }
                        if ((diskLruCache != null) && (appDiskCache.mBitmap != null)) {
                            BitmapCacheManager.addApplicationToDisk(tag, appDiskCache, diskLruCache);
                        }
                    }
                    try {
                        if (diskLruCache != null) {
                            diskLruCache.close();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, e.getMessage());
                    }
                    final Bitmap fetchImage = appDiskCache.mBitmap;
                    final String title = appDiskCache.mApkName;
                    final String version = appDiskCache.mApkVersion;
                    if (checkTag(tag, imageView) && (fetchImage != null)) {
                        final ApplicationInfoDetail finalAppDiskCache = appDiskCache;
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                mLruCache.put(tag, finalAppDiskCache);
                                Log.d(TAG, "realLoad put cache = " + tag + " " + finalAppDiskCache.mApkName + " " + finalAppDiskCache.mApkVersion);
                                loader.onLoad(imageView, fetchImage, tag);
                                loader.onLoadTextView(title, version, path);
                                loader.clear();
                            }
                        });
                    } else if (fetchImage == null) {
                        mHandler.post(new Runnable() {
                            @Override
                            public void run() {
                                loader.onLoad(imageView, null, tag);
                                loader.onLoadTextView(title, version, path);
                                loader.clear();
                            }
                        });
                    } else {
                        loader.clear();
                    }
                }
            }

            if (!mStarted) {
                synchronized (mLock) {
                    while (!mStarted) {
                        mLock.wait();
                    }
                }
            }
        } catch (NullPointerException e) {
            clearHeapCache();
            Log.w(TAG, "load thumb image failed ");
        } catch (InterruptedException e) {
            clearHeapCache();
            Log.w(TAG, "asynchronous thumbnail loader thread interrupted");
        }
    }

    public void clearHeapCache() {
        resetInstance();
        if (null != mLruCache) {
            mLruCache.evictAll();
        }
    }

    private static void resetInstance() {
        sInstance = null;
    }

    /**
     * clear heap cache and disk cache
     */
    public void clearAllCache() {
        if (null != mLruCache) {
            mLruCache.evictAll();
        }
        DiskLruCache diskLruCache = BitmapCacheManager.getDiskLruCache(MyApplication.getSAppContext(), CACHE_DIR_NAME);
        if (diskLruCache != null) {
            try {
                diskLruCache.delete();
            } catch (IOException e) {
                Log.e(TAG, "clearAllCache has error:" + e.getMessage());
            }
        }
    }

    public void clearCache(@Nullable List<? extends BaseFileBean> selectFiles) {
        if ((null != mLruCache) && (selectFiles != null)) {
            Log.d(TAG, "clearCache " + selectFiles.size());
            for (BaseFileBean file : selectFiles) {
                if (file != null) {
                    String key = file.getMData() + file.getMDateModified();
                    Log.d(TAG, " clearCache key = " + key);
                    mLruCache.remove(key);
                    DiskLruCache diskLruCache = BitmapCacheManager.getDiskLruCache(MyApplication.getSAppContext(), CACHE_DIR_NAME);
                    BitmapCacheManager.clearApplicationDiskCache(key, diskLruCache);
                    try {
                        if (diskLruCache != null) {
                            diskLruCache.close();
                        }
                    } catch (IOException e) {
                        Log.e("clearCache  " + e.getMessage());
                    }
                }
            }
        }
    }

    public void start() {
        mStarted = true;
        synchronized (mLock) {
            mLock.notifyAll();
        }
    }

    public void stop() {
        mStarted = false;
        synchronized (mLock) {
            mLock.notifyAll();
        }
    }

    private static class ImageViewName {
        private String mTag;
        private String mPath;
        private ImageView mView;
        private ApplicationThumbnailLoaderListener mLoader;

        public ImageViewName(String tag, String path, ImageView view,
                             ApplicationThumbnailLoaderListener loader) {
            this.mTag = tag;
            this.mPath = path;
            this.mView = view;
            this.mLoader = loader;
        }
    }
}
