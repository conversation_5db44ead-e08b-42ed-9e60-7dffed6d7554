/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: PreviewParentChildLayout
 ** Description: Preview Parent Child Layout
 ** Version: 1.0
 ** Date : 2024/10/10
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Configuration
import android.transition.Transition
import android.transition.TransitionManager
import android.transition.TransitionSet
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.core.view.ViewCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentContainerView
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.animation.COUIEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WindowUtils
import com.support.appcompat.R
import kotlin.math.min

class PreviewParentChildLayout : FrameLayout {

    companion object {
        private const val TAG = "PreviewParentChildLayout"
        private const val ANIMATION_EASE_TIME = 133L
        private const val ANIMATION_TIME = 350L
    }

    private var previewListContainer: FragmentContainerView? = null
    private var previewContentContainer: FragmentContainerView? = null

    private var divider: View? = null
    private var dividerColor = 0
    private var gapWidth = 0

    private var topAppBar: View? = null
    private val listDefaultWidth = MyApplication.sAppContext.resources.getDimensionPixelSize(com.filemanager.common.R.dimen.dimen_280dp)
    private var previewListWidth = listDefaultWidth
    private var isShowPreviewContent = false
    private var isDoingHidePreviewContentAnim = false
    private var isDoingShowPreviewContentAnim = false

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    ) {
        init(context)
    }

    private fun init(context: Context) {
        previewListContainer = FragmentContainerView(context)
        previewContentContainer = FragmentContainerView(context)
        divider = View(context)
        addView(previewListContainer)
        addView(divider)
        addView(previewContentContainer)
        previewListContainer?.id = com.filemanager.common.R.id.preview_list_container
        previewContentContainer?.id = com.filemanager.common.R.id.preview_content_container
        val previewContentBackgroudColor = COUIContextUtil.getAttrColor(getContext(), R.attr.couiColorBackgroundWithCard)
        previewContentContainer?.setBackgroundColor(previewContentBackgroudColor)
        dividerColor = COUIContextUtil.getAttrColor(getContext(), R.attr.couiColorDivider)
        setDividerColor(dividerColor)
        COUIDarkModeUtil.setForceDarkAllow(divider, false)
        gapWidth = context.resources.getDimensionPixelSize(com.support.grid.R.dimen.coui_fragment_gap_width)
    }

    fun bindTopAppBar(topAppBar: View?) {
        this.topAppBar = topAppBar
        applyTopBarInsetsForContent()
    }

    fun setPreviewList(fragmentManager: FragmentManager, fragment: Fragment, tag: String) {
        previewListContainer?.let {
            fragmentManager.beginTransaction()
                .replace(it.id, fragment, tag)
                .commitAllowingStateLoss()
        }
    }

    fun updatePreviewContent(fragmentManager: FragmentManager, fragment: Fragment) {
        previewContentContainer?.let {
            fragmentManager.beginTransaction()
                .replace(it.id, fragment)
                .commitAllowingStateLoss()
        }
    }

    fun getPreviewContentFragment(fragmentManager: FragmentManager): Fragment? =
        previewContentContainer?.let {
            fragmentManager.findFragmentById(it.id)
        }

    private fun setDividerColor(color: Int) {
        dividerColor = color
        divider?.setBackgroundColor(dividerColor)
    }

    fun isDoingAnim(): Boolean {
        return isDoingShowPreviewContentAnim || isDoingHidePreviewContentAnim
    }

    fun hidePreviewContent(animation: Boolean, recyclerView: RecyclerView? = null, isEmptyList: Boolean = false) {
        if (!isShowPreviewContent) {
            return
        }
        isDoingHidePreviewContentAnim = animation
        previewListWidth = measuredWidth
        if (isDoingHidePreviewContentAnim) {
            doHidePreviewContentAnim(recyclerView, isEmptyList)
        }
        requestLayout()
        updateListTabLayout()
        isShowPreviewContent = false
    }

    fun showPreviewContent(anim: Boolean = false, recyclerView: RecyclerView? = null, isEmptyList: Boolean = false) {
        isShowPreviewContent = true
        previewListWidth = listDefaultWidth
        isDoingShowPreviewContentAnim = anim
        if (isDoingShowPreviewContentAnim) {
            doShowPreviewContentAnim(recyclerView, isEmptyList)
        }
        requestLayout()
    }

    private fun doShowPreviewContentAnim(recyclerView: RecyclerView?, isEmptyList: Boolean) {
        Log.d(TAG, "doShowPreviewContentAnim isEmptyList: $isEmptyList")
        var emptyListAnimator: Animator? = null
        if (isEmptyList) {
            emptyListAnimator = createEmptyListAnim(false)
        } else {
            createRecycleViewFadeTransition(recyclerView)
        }

        val screenWidth = getScreenWidth()
        var dividerToX = 0f
        var previewContentToX = 0f
        if (isRTL()) {
            divider?.translationX = -screenWidth.toFloat()
            previewContentContainer?.translationX = -(screenWidth.toFloat() + gapWidth)
            dividerToX = -listDefaultWidth.toFloat()
            previewContentToX = -(listDefaultWidth.toFloat() + gapWidth.toFloat())
        } else {
            divider?.translationX = screenWidth.toFloat()
            previewContentContainer?.translationX = screenWidth.toFloat() + gapWidth
            dividerToX = listDefaultWidth.toFloat()
            previewContentToX = listDefaultWidth.toFloat() + gapWidth.toFloat()
        }
        val moveAnimSet = createMoveAnimSet(dividerToX, previewContentToX, emptyListAnimator)
        moveAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animation: Animator) {
                super.onAnimationStart(animation)
                Log.d(TAG, "doShowPreviewContentAnim onAnimationStart")
            }
            override fun onAnimationEnd(animation: Animator) {
                Log.d(TAG, "doShowPreviewContentAnim onAnimationEnd")
                super.onAnimationEnd(animation)
                moveAnimSet.removeAllListeners()
                isDoingShowPreviewContentAnim = false
                divider?.translationX = 0f
                previewContentContainer?.translationX = 0f
                requestLayout()
            }
        })
        moveAnimSet.start()
    }

    private fun doHidePreviewContentAnim(recyclerView: RecyclerView?, isEmptyList: Boolean) {
        Log.d(TAG, "doHidePreviewContentAnim isEmptyList: $isEmptyList")
        var emptyListAnimator: Animator? = null
        if (isEmptyList) {
            emptyListAnimator = createEmptyListAnim(true)
        } else {
            createRecycleViewFadeTransition(recyclerView)
        }

        val screenWidth = getScreenWidth()
        var dividerToX = 0f
        var previewContentToX = 0f
        if (isRTL()) {
            divider?.translationX = -listDefaultWidth.toFloat()
            previewContentContainer?.translationX = -(listDefaultWidth + gapWidth.toFloat())
            dividerToX = -screenWidth.toFloat()
            previewContentToX = -(screenWidth.toFloat() + gapWidth.toFloat())
        } else {
            divider?.translationX = listDefaultWidth.toFloat()
            previewContentContainer?.translationX = listDefaultWidth.toFloat() + gapWidth.toFloat()
            dividerToX = screenWidth.toFloat()
            previewContentToX = screenWidth.toFloat() + gapWidth
        }
        val moveAnimSet = createMoveAnimSet(dividerToX, previewContentToX, emptyListAnimator)
        moveAnimSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                Log.d(TAG, "doHidePreviewContentAnim onAnimationEnd")
                super.onAnimationEnd(animation)
                moveAnimSet.removeAllListeners()
                isDoingHidePreviewContentAnim = false
                divider?.translationX = 0f
                previewContentContainer?.translationX = 0f
                requestLayout()
            }
        })
        moveAnimSet.start()
    }

    private fun createEmptyListAnim(isHide: Boolean): Animator? {
        var emptyListAnimator: ValueAnimator? = null
        val startWidth = if (isHide) listDefaultWidth else measuredWidth
        val endWidth = if (isHide) measuredWidth else listDefaultWidth
        previewListContainer?.let {
            emptyListAnimator = ValueAnimator.ofInt(startWidth, endWidth)
            emptyListAnimator?.apply {
                duration = ANIMATION_TIME
                interpolator = COUIMoveEaseInterpolator()
                addUpdateListener { animation ->
                    previewListWidth = animation.animatedValue as Int
                    <EMAIL>()
                }
                addListener(object : AnimatorListenerAdapter() {
                    override fun onAnimationEnd(animation: Animator) {
                        super.onAnimationEnd(animation)
                        emptyListAnimator?.removeAllUpdateListeners()
                        emptyListAnimator?.removeAllListeners()
                    }
                })
            }
        }
        return emptyListAnimator
    }

    private fun createMoveAnimSet(dividerToX: Float, previewContentToX: Float, emptyListAnimator: Animator?): Animator {
        val moveInterpolator = COUIMoveEaseInterpolator()
        val dividerAnim = divider?.let {
            ObjectAnimator.ofFloat(
                it,
                "translationX",
                it.translationX,
                dividerToX
            ).apply {
                interpolator = moveInterpolator
            }
        }

        val previewContentAnim = previewContentContainer?.let {
            ObjectAnimator.ofFloat(
                it,
                "translationX",
                it.translationX,
                previewContentToX
            ).apply {
                interpolator = moveInterpolator
            }
        }
        val moveAnimSet = AnimatorSet().apply {
            interpolator = moveInterpolator
            duration = ANIMATION_TIME
        }
        if (emptyListAnimator != null) {
            moveAnimSet.playTogether(emptyListAnimator, dividerAnim, previewContentAnim)
        } else {
            moveAnimSet.playTogether(dividerAnim, previewContentAnim)
        }
        return moveAnimSet
    }

    private fun createRecycleViewFadeTransition(recyclerView: RecyclerView?) {
        if (recyclerView == null) {
            Log.d(TAG, "createRecycleViewFadeAnim recyclerView is null")
            return
        }

        val currentItemAnimator = recyclerView.itemAnimator
        val fade = ListItemFadeTransition().apply {
            duration = ANIMATION_EASE_TIME
            interpolator = COUIEaseInterpolator()
        }
        val transitionSet = TransitionSet()
        transitionSet.addTransition(fade)
        transitionSet.addListener(object : Transition.TransitionListener {
            override fun onTransitionStart(p0: Transition?) {
                Log.d(TAG, "createRecycleViewFadeAnim onTransitionStart")
            }

            override fun onTransitionEnd(p0: Transition?) {
                Log.d(TAG, "createRecycleViewFadeAnim onTransitionEnd 1")
                recyclerView.itemAnimator = currentItemAnimator
                transitionSet.removeListener(this)
            }

            override fun onTransitionCancel(p0: Transition?) {
                Log.d(TAG, "createRecycleViewFadeAnim onTransitionCancel")
            }

            override fun onTransitionPause(p0: Transition?) {
                Log.d(TAG, "createRecycleViewFadeAnim onTransitionPause")
            }

            override fun onTransitionResume(p0: Transition?) {
                Log.d(TAG, "createRecycleViewFadeAnim onTransitionResume")
            }
        })

        recyclerView.itemAnimator = null
        TransitionManager.beginDelayedTransition(recyclerView, transitionSet)
        val firstVisibleItemPosition = (recyclerView.layoutManager as? LinearLayoutManager)?.findFirstVisibleItemPosition() ?: 0
        Log.d(TAG, "createRecycleViewFadeAnim firstVisibleItemPosition=$firstVisibleItemPosition")
        recyclerView.adapter = recyclerView.adapter
        recyclerView.scrollToPosition(firstVisibleItemPosition)
    }

    private fun updateListTabLayout() {
        val listFragment = previewListContainer?.getFragment<Fragment>()
        val tabLayout = listFragment?.view?.findViewById<View>(com.filemanager.common.R.id.tab_layout)
        tabLayout?.post {
            tabLayout.requestLayout()
        }
    }

    private fun isRTL(): Boolean = ViewCompat.getLayoutDirection(this) == ViewCompat.LAYOUT_DIRECTION_RTL

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        dividerColor = COUIContextUtil.getAttrColor(context, R.attr.couiColorDivider)
        setDividerColor(dividerColor)
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val width = measuredWidth
        val parentListWidth = getCurrentListWidth()
        val childContentWidth = getCurrentChildContentWidth()
        val gapWidth = this.gapWidth
        var childWidthSpec = getChildMeasureSpec(
            widthMeasureSpec, 0,
            min(width.toDouble(), parentListWidth.toDouble()).toInt()
        )
        measureChild(previewListContainer, childWidthSpec, heightMeasureSpec)
        childWidthSpec = getChildMeasureSpec(widthMeasureSpec, 0, childContentWidth)
        measureChild(previewContentContainer, childWidthSpec, heightMeasureSpec)
        childWidthSpec = getChildMeasureSpec(widthMeasureSpec, 0, gapWidth)
        measureChild(divider, childWidthSpec, heightMeasureSpec)
    }

    private fun getCurrentListWidth(): Int {
        return if (isShowPreviewContent && isDoingShowPreviewContentAnim) {
            previewListWidth
        } else if (isShowPreviewContent && !isDoingShowPreviewContentAnim) {
            listDefaultWidth
        } else if (!isShowPreviewContent && !isDoingHidePreviewContentAnim) {
            measuredWidth
        } else {
            previewListWidth
        }
    }

    private fun getCurrentChildContentWidth(): Int {
        val screenWidth = getScreenWidth()
        return screenWidth - listDefaultWidth
    }

    private fun getScreenWidth(): Int {
        val screenWidth = resources?.displayMetrics?.widthPixels ?: 0
        if (screenWidth < 1) {
            Log.w(TAG, "getScreenWidth, width is less than 1")
        }
        return screenWidth
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        if (!isDoingShowPreviewContentAnim && !isDoingHidePreviewContentAnim) {
            val width = measuredWidth
            val listWidth = previewListContainer?.width ?: return
            val listHeight = previewListContainer?.height ?: return
            val dividerWidth = divider?.width ?: return
            val dividerHeight = divider?.height ?: return
            val contentWidth = previewContentContainer?.width ?: return
            val contentHeight = previewContentContainer?.height ?: return
            if (isRTL()) {
                previewListContainer?.layout(
                    width - listWidth,
                    0,
                    width,
                    listHeight
                )
                divider?.layout(
                    width - listWidth - dividerWidth,
                    0,
                    width - listWidth,
                    dividerHeight
                )

                previewContentContainer?.layout(
                    width - listWidth - dividerWidth - contentWidth,
                    0,
                    width - listWidth - dividerWidth,
                    contentHeight
                )
            } else {
                previewListContainer?.layout(
                    0,
                    0,
                    listWidth,
                    listHeight
                )
                divider?.layout(
                    listWidth,
                    0,
                    listWidth + dividerWidth,
                    dividerHeight
                )
                previewContentContainer?.layout(
                    listWidth + dividerWidth,
                    0,
                    listWidth + dividerWidth + contentWidth,
                    contentHeight
                )
            }
        }
        applyTopBarInsetsForContent()
    }

    private fun applyTopBarInsetsForContent() {
        val topBarHeight = topAppBar?.height ?: return
        previewContentContainer?.run {
            if (paddingTop == topBarHeight) {
                return
            }
            setPadding(0, topBarHeight, 0, 0)
        }
    }
}