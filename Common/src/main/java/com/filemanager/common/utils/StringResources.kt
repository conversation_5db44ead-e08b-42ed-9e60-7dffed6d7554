/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : StringResources
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/2/28 17:36
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/2/28       1.0      create
 ***********************************************************************/
@file:JvmName("StringResourcesKt")
package com.filemanager.common.utils

import android.content.res.Resources
import androidx.annotation.ArrayRes
import androidx.annotation.StringRes
import com.filemanager.common.MyApplication

/**
 * Load a string resource.
 *
 * @param id the resource identifier
 * @return the string data associated with the resource
 */
fun stringResource(@StringRes id: Int): String {
    val resources = resources()
    return resources.getString(id)
}

/**
 * Load a string resource with formatting.
 *
 * @param id the resource identifier
 * @param formatArgs the format arguments
 * @return the string data associated with the resource
 */
fun stringResource(@StringRes id: Int, vararg formatArgs: Any): String {
    val resources = resources()
    return resources.getString(id, *formatArgs)
}

/**
 * Load a string resource.
 *
 * @param id the resource identifier
 * @return the string data associated with the resource
 */
fun stringArrayResource(@ArrayRes id: Int): Array<String> {
    val resources = resources()
    return resources.getStringArray(id)
}

internal fun resources(): Resources {
    return MyApplication.sAppContext.resources
}