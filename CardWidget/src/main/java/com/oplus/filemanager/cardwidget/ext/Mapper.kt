/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Mapper
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/9
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/9      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.cardwidget.ext

import com.filemanager.common.helper.MimeTypeHelper
import com.oplus.filemanager.cardwidget.data.RecentFileCardItemData
import com.oplus.filemanager.recent.entity.recent.RecentFileEntity

// background drawable
const val PIC_ITEM_BG = "@drawable/pic_item_bg"
const val VIDEO_ITEM_BG = "@drawable/video_item_bg"
const val AUDIO_ITEM_BG = "@drawable/audio_item_bg"
const val DOC_ITEM_BG = "@drawable/doc_item_bg"
const val XLS_ITEM_BG = "@drawable/xls_item_bg"
const val PPT_ITEM_BG = "@drawable/ppt_item_bg"
const val PDF_ITEM_BG = "@drawable/pdf_item_bg"
const val TXT_ITEM_BG = "@drawable/txt_item_bg"
const val APK_ITEM_BG = "@drawable/apk_item_bg"
const val ARCHIVE_ITEM_BG = "@drawable/archive_item_bg"
const val CALENDAR_ITEM_BG = "@drawable/calendar_item_bg"
const val UNKNOWN_ITEM_BG = "@drawable/unknown_item_bg"
const val CONTACT_ITEM_BG = "@drawable/contact_item_bg"
const val THEME_ITEM_BG = "@drawable/theme_item_bg"
const val EBOOK_ITEM_BG = "@drawable/ebook_item_bg"
const val HTML_ITEM_BG = "@drawable/html_item_bg"
const val LYRIC_ITEM_BG = "@drawable/lyric_item_bg"
const val SMS_ITEM_BG = "@drawable/sms_item_bg"

// thumb drawable
const val IC_PIC = "@drawable/ic_file_image"
const val IC_VIDEO = "@drawable/ic_file_video"
const val IC_AUDIO = "@drawable/ic_file_audio"
const val IC_DOC = "@drawable/ic_file_doc"
const val IC_XLS = "@drawable/ic_file_excl"
const val IC_PPT = "@drawable/ic_file_ppt"
const val IC_PDF = "@drawable/ic_file_pdf"
const val IC_OFD = "@drawable/ic_file_ofd"
const val IC_TXT = "@drawable/ic_file_txt"
const val IC_APK = "@drawable/ic_file_apk"
const val IC_7Z = "@drawable/ic_file_compress_7z"
const val IC_RAR = "@drawable/ic_file_compress_rar"
const val IC_ZIP = "@drawable/ic_file_compress_zip"
const val IC_JAR = "@drawable/ic_file_compress_jar"
const val IC_CALENDAR = "@drawable/ic_file_calendar_icon"
const val IC_UNKNOWN = "@drawable/ic_file_other_icon"
const val IC_CONTACT = "@drawable/ic_file_vcard_icon"
const val IC_THEME = "@drawable/ic_file_theme_icon"
const val IC_EBOOK = "@drawable/ic_file_txt"
const val IC_HTML = "@drawable/ic_file_html_icon"
const val IC_LYRIC = "@drawable/ic_file_lrc_icon"
const val IC_VMSG = "@drawable/ic_file_sms_icon"
const val IC_FOLDER = "@drawable/ic_file_folder_icon"
const val IC_KEYNOTE = "@drawable/ic_file_keynote"
const val IC_PAGES = "@drawable/ic_file_pages"
const val IC_NUMBERS = "@drawable/ic_file_numbers"
const val IC_MARKDOWN = "@drawable/ic_file_markdown"
const val IC_CAD = "@drawable/ic_file_dwg"
const val IC_XMIND = "@drawable/ic_file_xmind"
const val IC_PSD = "@drawable/ic_file_photoshop"
const val IC_AI = "@drawable/ic_file_ai"
const val IC_VISIO = "@drawable/ic_file_visio"
const val IC_JS = "@drawable/ic_file_js"
const val IC_EXE = "@drawable/ic_file_exe"
const val IC_DMG = "@drawable/ic_file_dmg"

// 最近文件泛在卡使用的.png图标
const val IC_PIC_PNG = "@drawable/ic_file_image"
const val IC_VIDEO_PNG = "@drawable/ic_file_video"
const val IC_AUDIO_PNG = "@drawable/ic_file_audio"
const val IC_DOC_PNG = "@drawable/ic_file_doc"
const val IC_XLS_PNG = "@drawable/ic_file_excl"
const val IC_PPT_PNG = "@drawable/ic_file_ppt"
const val IC_PDF_PNG = "@drawable/ic_file_pdf"
const val IC_OFD_PNG = "@drawable/ic_file_ofd"
const val IC_TXT_PNG = "@drawable/ic_file_txt"
const val IC_APK_PNG = "@drawable/ic_file_apk"
const val IC_7Z_PNG = "@drawable/ic_file_compress_7z"
const val IC_RAR_PNG = "@drawable/ic_file_compress_rar"
const val IC_ZIP_PNG = "@drawable/ic_file_compress_zip"
const val IC_JAR_PNG = "@drawable/ic_file_compress_jar"
const val IC_CALENDAR_PNG = "@drawable/ic_file_calendar_icon"
const val IC_UNKNOWN_PNG = "@drawable/ic_file_other_icon"
const val IC_CONTACT_PNG = "@drawable/ic_file_vcard_icon"
const val IC_THEME_PNG = "@drawable/ic_file_theme_icon"
const val IC_EBOOK_PNG = "@drawable/ic_file_txt"
const val IC_HTML_PNG = "@drawable/ic_file_html_icon"
const val IC_LYRIC_PNG = "@drawable/ic_file_lrc_icon"
const val IC_VMSG_PNG = "@drawable/ic_file_sms_icon"
const val IC_KEYNOTE_PNG = "@drawable/ic_file_keynote"
const val IC_PAGES_PNG = "@drawable/ic_file_pages"
const val IC_NUMBERS_PNG = "@drawable/ic_file_numbers"
const val IC_MARKDOWN_PNG = "@drawable/ic_file_markdown"
const val IC_CAD_PNG = "@drawable/ic_file_dwg"
const val IC_XMIND_PNG = "@drawable/ic_file_xmind"
const val IC_PSD_PNG = "@drawable/ic_file_photoshop"
const val IC_AI_PNG = "@drawable/ic_file_ai"
const val IC_VISIO_PNG = "@drawable/ic_file_visio"
const val IC_JS_PNG = "@drawable/ic_file_js"
const val IC_EXE_PNG = "@drawable/ic_file_exe"
const val IC_DMG_PNG = "@drawable/ic_file_dmg"

val THUMB_MAP = mutableMapOf<String, Int>().apply {
    this[IC_PIC] = com.filemanager.common.R.drawable.ic_file_image
    this[IC_VIDEO] = com.filemanager.common.R.drawable.ic_file_video
    this[IC_AUDIO] = com.filemanager.common.R.drawable.ic_file_audio
    this[IC_DOC] = com.filemanager.common.R.drawable.ic_file_doc
    this[IC_XLS] = com.filemanager.common.R.drawable.ic_file_excl
    this[IC_PPT] = com.filemanager.common.R.drawable.ic_file_ppt
    this[IC_PDF] = com.filemanager.common.R.drawable.ic_file_pdf
    this[IC_OFD] = com.filemanager.common.R.drawable.ic_file_ofd
    this[IC_TXT] = com.filemanager.common.R.drawable.ic_file_txt
    this[IC_APK] = com.filemanager.common.R.drawable.ic_file_apk
    this[IC_7Z] = com.filemanager.common.R.drawable.ic_file_compress_7z
    this[IC_RAR] = com.filemanager.common.R.drawable.ic_file_compress_rar
    this[IC_ZIP] = com.filemanager.common.R.drawable.ic_file_compress_zip
    this[IC_JAR] = com.filemanager.common.R.drawable.ic_file_compress_jar
    this[IC_CALENDAR] = com.filemanager.common.R.drawable.ic_file_calendar_icon
    this[IC_UNKNOWN] = com.filemanager.common.R.drawable.ic_file_other_icon
    this[IC_CONTACT] = com.filemanager.common.R.drawable.ic_file_vcard_icon
    this[IC_THEME] = com.filemanager.common.R.drawable.ic_file_theme_icon
    this[IC_EBOOK] = com.filemanager.common.R.drawable.ic_file_txt
    this[IC_HTML] = com.filemanager.common.R.drawable.ic_file_html_icon
    this[IC_LYRIC] = com.filemanager.common.R.drawable.ic_file_lrc_icon
    this[IC_VMSG] = com.filemanager.common.R.drawable.ic_file_sms_icon
    this[IC_FOLDER] = com.filemanager.common.R.drawable.ic_file_folder_icon
    this[IC_MARKDOWN] = com.filemanager.common.R.drawable.ic_file_markdown
    this[IC_CAD] = com.filemanager.common.R.drawable.ic_file_dwg
    this[IC_PAGES] = com.filemanager.common.R.drawable.ic_file_pages
    this[IC_KEYNOTE] = com.filemanager.common.R.drawable.ic_file_keynote
    this[IC_NUMBERS] = com.filemanager.common.R.drawable.ic_file_numbers
    this[IC_XMIND] = com.filemanager.common.R.drawable.ic_file_xmind
    this[IC_PSD] = com.filemanager.common.R.drawable.ic_file_photoshop
    this[IC_AI] = com.filemanager.common.R.drawable.ic_file_ai
    this[IC_VISIO] = com.filemanager.common.R.drawable.ic_file_visio
    this[IC_JS] = com.filemanager.common.R.drawable.ic_file_js
    this[IC_EXE] = com.filemanager.common.R.drawable.ic_file_exe
    this[IC_DMG] = com.filemanager.common.R.drawable.ic_file_dmg
}

fun RecentFileEntity.mapperToRecentFileCardItemData(): RecentFileCardItemData {
    val result = RecentFileCardItemData()
    result.type = this.mLocalType
    result.path = this.mAbsolutePath
    result.name = this.mDisplayName ?: ""
    result.dateModified = this.mDateModified
    result.duration = this.mDuration
    result.size = this.mSize
    result.iconResId = getIconFromType(this.mLocalType)
    result.bgResId = getBgBackgroundFromType(this.mLocalType)
    return result
}

fun getBgBackgroundFromType(type: Int): String {
    when (type) {
        MimeTypeHelper.IMAGE_TYPE -> return PIC_ITEM_BG
        MimeTypeHelper.VIDEO_TYPE -> return VIDEO_ITEM_BG
        MimeTypeHelper.AUDIO_TYPE -> return AUDIO_ITEM_BG
        MimeTypeHelper.DOCX_TYPE, MimeTypeHelper.DOC_TYPE -> return DOC_ITEM_BG
        MimeTypeHelper.XLSX_TYPE, MimeTypeHelper.XLS_TYPE -> return XLS_ITEM_BG
        MimeTypeHelper.PPTX_TYPE, MimeTypeHelper.PPT_TYPE -> return PPT_ITEM_BG
        MimeTypeHelper.PDF_TYPE, MimeTypeHelper.OFD_TYPE -> return PDF_ITEM_BG
        MimeTypeHelper.TXT_TYPE -> return TXT_ITEM_BG
        MimeTypeHelper.APPLICATION_TYPE -> return APK_ITEM_BG
        MimeTypeHelper.P7ZIP_TYPE, MimeTypeHelper.RAR_TYPE, MimeTypeHelper.ZIP_TYPE, MimeTypeHelper.JAR_TYPE -> return ARCHIVE_ITEM_BG
        MimeTypeHelper.VCS_TYPE, MimeTypeHelper.ICS_TYPE -> return CALENDAR_ITEM_BG
        MimeTypeHelper.UNKNOWN_TYPE -> return UNKNOWN_ITEM_BG
        MimeTypeHelper.VCF_TYPE, MimeTypeHelper.CSV_TYPE -> return CONTACT_ITEM_BG
        MimeTypeHelper.THEME_TYPE -> return THEME_ITEM_BG
        MimeTypeHelper.EPUB_TYPE, MimeTypeHelper.EBK_TYPE, MimeTypeHelper.CHM_TYPE -> return EBOOK_ITEM_BG
        MimeTypeHelper.HTML_TYPE -> return HTML_ITEM_BG
        MimeTypeHelper.LRC_TYPE -> return LYRIC_ITEM_BG
        MimeTypeHelper.VMSG_TYPE -> return SMS_ITEM_BG
        else -> return UNKNOWN_ITEM_BG
    }
}

fun getIconFromType(type: Int): String {
    when (type) {
        MimeTypeHelper.IMAGE_TYPE -> return IC_PIC
        MimeTypeHelper.VIDEO_TYPE -> return IC_VIDEO
        MimeTypeHelper.AUDIO_TYPE -> return IC_AUDIO
        MimeTypeHelper.DOCX_TYPE, MimeTypeHelper.DOC_TYPE -> return IC_DOC
        MimeTypeHelper.XLSX_TYPE, MimeTypeHelper.XLS_TYPE -> return IC_XLS
        MimeTypeHelper.PPTX_TYPE, MimeTypeHelper.PPT_TYPE -> return IC_PPT
        MimeTypeHelper.PDF_TYPE -> return IC_PDF
        MimeTypeHelper.OFD_TYPE -> return IC_OFD
        MimeTypeHelper.TXT_TYPE -> return IC_TXT
        MimeTypeHelper.APPLICATION_TYPE -> return IC_APK
        MimeTypeHelper.P7ZIP_TYPE -> return IC_7Z
        MimeTypeHelper.RAR_TYPE -> return IC_RAR
        MimeTypeHelper.ZIP_TYPE -> return IC_ZIP
        MimeTypeHelper.JAR_TYPE -> return IC_JAR
        MimeTypeHelper.VCS_TYPE, MimeTypeHelper.ICS_TYPE -> return IC_CALENDAR
        MimeTypeHelper.UNKNOWN_TYPE -> return IC_UNKNOWN
        MimeTypeHelper.VCF_TYPE, MimeTypeHelper.CSV_TYPE -> return IC_CONTACT
        MimeTypeHelper.THEME_TYPE -> return IC_THEME
        MimeTypeHelper.EPUB_TYPE, MimeTypeHelper.EBK_TYPE, MimeTypeHelper.CHM_TYPE -> return IC_EBOOK
        MimeTypeHelper.HTML_TYPE -> return IC_HTML
        MimeTypeHelper.LRC_TYPE -> return IC_LYRIC
        MimeTypeHelper.VMSG_TYPE -> return IC_VMSG
        MimeTypeHelper.DIRECTORY_TYPE -> return IC_FOLDER
        MimeTypeHelper.KEYNOTE_TYPE -> return IC_KEYNOTE
        MimeTypeHelper.PAGES_TYPE -> return IC_PAGES
        MimeTypeHelper.NUMBERS_TYPE -> return IC_NUMBERS
        MimeTypeHelper.MARKDOWN_TYPE -> return IC_MARKDOWN
        MimeTypeHelper.DWG_TYPE, MimeTypeHelper.DWT_TYPE, MimeTypeHelper.DXF_TYPE -> return IC_CAD
        MimeTypeHelper.XMIND_TYPE -> return IC_XMIND
        MimeTypeHelper.PSD_TYPE -> return IC_PSD
        MimeTypeHelper.AI_TYPE -> return IC_AI
        MimeTypeHelper.VSDX_TYPE, MimeTypeHelper.VSDM_TYPE, MimeTypeHelper.VSTX_TYPE, MimeTypeHelper.VSTM_TYPE, MimeTypeHelper.VSSX_TYPE,
        MimeTypeHelper.VSSM_TYPE, MimeTypeHelper.VSD_TYPE, MimeTypeHelper.VSS_TYPE, MimeTypeHelper.VST_TYPE, MimeTypeHelper.VDW_TYPE ->
            return IC_VISIO
        MimeTypeHelper.JS_TYPE -> return IC_JS
        MimeTypeHelper.EXE_TYPE -> return IC_EXE
        MimeTypeHelper.DMG_TYPE -> return IC_DMG
        else -> return IC_UNKNOWN
    }
}

fun getIconResFromType(type: Int): Int {
    val name = getIconFromType(type)
    return THUMB_MAP[name] ?: com.filemanager.common.R.drawable.ic_file_other_icon
}

fun getIconPNGByType(type: Int): String {
    when (type) {
        MimeTypeHelper.IMAGE_TYPE -> return IC_PIC_PNG
        MimeTypeHelper.VIDEO_TYPE -> return IC_VIDEO_PNG
        MimeTypeHelper.AUDIO_TYPE -> return IC_AUDIO_PNG
        MimeTypeHelper.DOCX_TYPE, MimeTypeHelper.DOC_TYPE -> return IC_DOC_PNG
        MimeTypeHelper.XLSX_TYPE, MimeTypeHelper.XLS_TYPE -> return IC_XLS_PNG
        MimeTypeHelper.PPTX_TYPE, MimeTypeHelper.PPT_TYPE -> return IC_PPT_PNG
        MimeTypeHelper.PDF_TYPE -> return IC_PDF_PNG
        MimeTypeHelper.OFD_TYPE -> return IC_OFD_PNG
        MimeTypeHelper.TXT_TYPE -> return IC_TXT_PNG
        MimeTypeHelper.APPLICATION_TYPE -> return IC_APK_PNG
        MimeTypeHelper.P7ZIP_TYPE -> return IC_7Z_PNG
        MimeTypeHelper.RAR_TYPE -> return IC_RAR_PNG
        MimeTypeHelper.ZIP_TYPE -> return IC_ZIP_PNG
        MimeTypeHelper.JAR_TYPE -> return IC_JAR_PNG
        MimeTypeHelper.VCS_TYPE, MimeTypeHelper.ICS_TYPE -> return IC_CALENDAR_PNG
        MimeTypeHelper.UNKNOWN_TYPE -> return IC_UNKNOWN_PNG
        MimeTypeHelper.VCF_TYPE, MimeTypeHelper.CSV_TYPE -> return IC_CONTACT_PNG
        MimeTypeHelper.THEME_TYPE -> return IC_THEME_PNG
        MimeTypeHelper.EPUB_TYPE, MimeTypeHelper.EBK_TYPE, MimeTypeHelper.CHM_TYPE -> return IC_EBOOK_PNG
        MimeTypeHelper.HTML_TYPE -> return IC_HTML_PNG
        MimeTypeHelper.LRC_TYPE -> return IC_LYRIC_PNG
        MimeTypeHelper.VMSG_TYPE -> return IC_VMSG_PNG
        MimeTypeHelper.DIRECTORY_TYPE -> return IC_FOLDER
        MimeTypeHelper.KEYNOTE_TYPE -> return IC_KEYNOTE_PNG
        MimeTypeHelper.PAGES_TYPE -> return IC_PAGES_PNG
        MimeTypeHelper.NUMBERS_TYPE -> return IC_NUMBERS_PNG
        MimeTypeHelper.MARKDOWN_TYPE -> return IC_MARKDOWN_PNG
        MimeTypeHelper.DWG_TYPE, MimeTypeHelper.DWT_TYPE, MimeTypeHelper.DXF_TYPE -> return IC_CAD_PNG
        MimeTypeHelper.XMIND_TYPE -> return IC_XMIND_PNG
        MimeTypeHelper.PSD_TYPE -> return IC_PSD_PNG
        MimeTypeHelper.AI_TYPE -> return IC_AI_PNG
        MimeTypeHelper.VSDX_TYPE, MimeTypeHelper.VSDM_TYPE, MimeTypeHelper.VSTX_TYPE, MimeTypeHelper.VSTM_TYPE, MimeTypeHelper.VSSX_TYPE,
        MimeTypeHelper.VSSM_TYPE, MimeTypeHelper.VSD_TYPE, MimeTypeHelper.VSS_TYPE, MimeTypeHelper.VST_TYPE, MimeTypeHelper.VDW_TYPE ->
            return IC_VISIO_PNG
        MimeTypeHelper.JS_TYPE -> return IC_JS_PNG
        MimeTypeHelper.EXE_TYPE -> return IC_EXE_PNG
        MimeTypeHelper.DMG_TYPE -> return IC_DMG_PNG
        else -> return IC_UNKNOWN_PNG
    }
}

private const val SINGLE_FILE = 1
private const val DOUBLE_FILES = 2

// 流体云 2或者多个文件的堆叠图
const val IC_DOUBLE_FILE_OVERLAP = "@drawable/ic_double_file_overlap"
const val IC_MANY_FILE_OVERLAP = "@drawable/ic_many_file_overlap"


/**
 * 获取文件Icon
 * @param paths 文件路径
 * @return 文件icon的名字，类型于 “@drawable/xxxx”,只支持png和jpg。
 */
fun getFileIconPNG(paths: List<String>): String {
    val size = paths.size
    return when (size) {
        SINGLE_FILE -> getFileIconPNG(paths.get(0))
        DOUBLE_FILES -> IC_DOUBLE_FILE_OVERLAP
        else -> IC_MANY_FILE_OVERLAP
    }
}

/**
 * 获取文件Icon
 * @param path 文件路径
 * @return 文件icon的名字，类型于 “@drawable/xxxx”,只支持png和jpg。
 */
private fun getFileIconPNG(path: String): String {
    val mimeType = MimeTypeHelper.getTypeFromPath(path)
    return getIconPNGByType(mimeType)
}
