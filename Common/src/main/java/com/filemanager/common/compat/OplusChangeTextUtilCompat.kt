/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OplusChangeTextUtilCompat
 ** Description : OplusChangeTextUtilCompat
 ** Version     : 1.0
 ** Date        : 2024/09/12 17:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/12     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

object OplusChangeTextUtilCompat {

    const val G1: Int = 1
    const val G2: Int = 2
    const val G3: Int = 3
    const val G4: Int = 4
    const val G5: Int = 5
    const val GN: Int = 6

    @JvmStatic
    fun getSuitableFontSize(textSize: Float, scale: Float, level: Int): Float {
        return CompatUtils.compactSApi({
            com.oplus.util.OplusChangeTextUtil.getSuitableFontSize(textSize, scale, level)
        }, {
            com.heytap.addon.util.OplusChangeTextUtil.getSuitableFontSize(textSize, scale, level)
        })
    }
}