/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/2/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thread

class ThreadTask<T> : BaseThreadTask<T> {


    private var mPriority: ThreadPriority = ThreadPriority.BACKGROUND
    private var mThreadType: ThreadType = ThreadType.NORMAL_THREAD
    private var mUniqueFlag: String = ""

    constructor(runnable: FileRunnable, threadCancelInterface: ThreadCancelInterface?, result: T?, removeWorkInterface: RemoveWorkInterface?) : super(runnable, result, removeWorkInterface) {
        mUniqueFlag = System.currentTimeMillis().toString() + hashCode().toString() + runnable.mTag
        runnable.mRunnableName = mUniqueFlag
        super.mThreadCancelInterface = threadCancelInterface
    }

    fun setPriority(priority: ThreadPriority) {
        mPriority = priority
    }

    fun setThreadType(threadType: ThreadType) {
        mThreadType = threadType
    }

    override fun getUniqueFlag(): String {
        return mUniqueFlag
    }

    override fun getThreadType(): ThreadType {
        return mThreadType
    }

    override fun getPriority(): ThreadPriority {
        return mPriority
    }

}