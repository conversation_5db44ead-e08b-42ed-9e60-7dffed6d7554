/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:COUIToolTipsView
 ** Description:
 ** Version: 1.0
 ** Date: 2023/7/12
 ** Author:wanghonglei
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.view

import android.app.Activity
import android.content.Context
import android.view.View
import android.view.ViewGroup
import com.coui.appcompat.tooltips.COUIToolTips
import org.jetbrains.annotations.VisibleForTesting
import java.util.Objects

class COUIToolTipsView(
    private val context: Context,
) : COUIToolTips(context) {

    companion object {
        /**
         * 用于记录Tips在哪些页面显示
         */
        @VisibleForTesting
        val activityList: MutableList<String> = mutableListOf()

        fun pushActivity(context: Context) {
            val name = context.javaClass.name
            activityList.add(name)
        }

        fun pollActivity(context: Context) {
            val name = context.javaClass.name
            activityList.remove(name)
        }

        /**
         * 当前页面是否已经显示了Tips
         */
        fun isShowTips(context: Context): Boolean {
            val name = context.javaClass.name
            return activityList.filter {
                Objects.equals(name, it)
            }.isNotEmpty()
        }
    }

    var anchor: View? = null

    override fun refreshWhileLayoutChange() {
        //此处控制在onUIConfigChange后让tips消失
        dismiss()
        (contentView as? ViewGroup)?.removeAllViews()
    }

    override fun showWithDirection(anchor: View?, direction: Int, hasIndicator: Boolean) {
        (context as? Activity)?.let { activity ->
            if (activity.isFinishing || activity.isDestroyed) {
                return
            }
        }
        this.anchor = anchor
        super.showWithDirection(anchor, direction, hasIndicator)
    }

    override fun showWithDirection(
        anchor: View?,
        direction: Int,
        hasIndicator: Boolean,
        offsetX: Int,
        offsetY: Int
    ) {
        (context as? Activity)?.let { activity ->
            if (activity.isFinishing || activity.isDestroyed) {
                return
            }
        }
        this.anchor = anchor
        pushActivity(context)
        super.showWithDirection(anchor, direction, hasIndicator, offsetX, offsetY)
    }

    override fun dismiss() {
        pollActivity(context)
        super.dismiss()
    }

    /**
     * 点击关闭按钮
     */
    fun clickCloseIcon() {
        dismissIv?.performClick()
    }
}