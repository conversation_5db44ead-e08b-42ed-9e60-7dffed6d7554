/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DriveCache
 ** Description : 用于记录被重命名+被删除的云文档的临时内存缓存
 ** Version     : 1.0
 ** Date        : 2024/04/24 11:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/24       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.os.SystemClock
import com.filemanager.common.utils.DriveCache.CacheBean.Companion.BEAN_TYPE_DELETE
import com.filemanager.common.utils.DriveCache.CacheBean.Companion.BEAN_TYPE_RENAME
import java.util.concurrent.ConcurrentHashMap

object DriveCache {

    const val TAG = "DriveCache"
    const val RENAME_CACHE_TIME_INTERNAL = 2 * 60 * 1000
    const val DELETE_CACHE_TIME_INTERNAL = 2 * 60 * 1000


    private var renameCache: ConcurrentHashMap<String, CacheBean> = ConcurrentHashMap()
    private var deleteCache: ConcurrentHashMap<String, CacheBean> = ConcurrentHashMap()

    fun putDeleteCache(id: String) {
        recycleExpireDeleteCache()
        val timeStamp = SystemClock.elapsedRealtime()
        val deleteCacheBean = CacheBean(id, timeStamp, BEAN_TYPE_DELETE)
        deleteCache[id] = deleteCacheBean
        Log.i(TAG, "putDeleteCache $id, result $deleteCacheBean")
    }

    fun getDeleteBean(id: String): CacheBean? {
        recycleExpireDeleteCache()
        val result = deleteCache[id]
        return result
    }

    private fun recycleExpireDeleteCache() {
        val currentTime = SystemClock.elapsedRealtime()
        val iterator = deleteCache.iterator()
        while (iterator.hasNext()) {
            val item = iterator.next()
            val cacheBean = item.value
            if (currentTime - cacheBean.timeStamp > DELETE_CACHE_TIME_INTERNAL) {
                iterator.remove()
            }
        }
    }

    fun putRenameCache(id: String, originalName: String, afterName: String) {
        recycleExpireRenameCache()
        val timeStamp = SystemClock.elapsedRealtime()
        val renameCacheBean = CacheBean(id, timeStamp, BEAN_TYPE_RENAME, originalName, afterName)
        renameCache[id] = renameCacheBean
        Log.i(TAG, "putRenameCache $id, result $renameCacheBean")
    }


    fun getRenameBean(id: String): CacheBean? {
        recycleExpireRenameCache()
        val result = renameCache[id]
        return result
    }

    private fun recycleExpireRenameCache() {
        val currentTime = SystemClock.elapsedRealtime()
        val iterator = renameCache.iterator()
        while (iterator.hasNext()) {
            val item = iterator.next()
            val cacheBean = item.value
            if (currentTime - cacheBean.timeStamp > RENAME_CACHE_TIME_INTERNAL) {
                iterator.remove()
            }
        }
    }

    data class CacheBean(
        var id: String,
        var timeStamp: Long,
        var beanType: Int,
        var originalName: String? = null,
        var afterName: String? = null
    ) {
        companion object {
            const val BEAN_TYPE_DELETE = 0
            const val BEAN_TYPE_RENAME = 1
        }

        fun isDeleteType(): Boolean {
            return beanType == BEAN_TYPE_DELETE
        }

        fun isRenameType(): Boolean {
            return beanType == BEAN_TYPE_RENAME
        }
    }
}