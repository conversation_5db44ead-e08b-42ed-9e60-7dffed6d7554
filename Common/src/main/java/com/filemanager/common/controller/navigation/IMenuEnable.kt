/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IMenuEnable
 ** Description : 控制在选择模式下底部工具栏中各个menu的enable和disable状态的接口定义
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.filemanager.common.controller.navigation

interface IMenuEnable {

    /**
     * 子类实现，告知当前menuId的对应的menu是正常enble状态还是置灰disable状态
     */
    fun isMenuEnable(navigationType: NavigationType, menuId: Int): Boolean
}