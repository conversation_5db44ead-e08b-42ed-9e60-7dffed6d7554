/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File: VolumeEnvironment.java
 * * Description:Get the mount information of the file
 * * Version:1.0
 * * Date :20180802
 * * Author:dengzhicheng
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * dengzhicheng  20180802  1.0  Get the mount information of the file
 ****************************************************************/
package com.filemanager.common.helper;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.os.Environment;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.filemanager.common.MyApplication;
import com.filemanager.common.base.BaseFileBean;
import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.compat.OplusUsbEnvironmentCompat;
import com.filemanager.common.constants.KtConstants;
import com.filemanager.common.thread.FileRunnable;
import com.filemanager.common.thread.ThreadManager;
import com.filemanager.common.utils.KtAppUtils;
import com.filemanager.common.utils.KtUtils;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.SdkUtils;
import com.filemanager.common.utils.Utils;
import com.filemanager.common.wrapper.PathFileWrapper;
import com.oplus.compat.os.storage.StorageVolumeNative;
import com.oplus.compat.utils.util.UnSupportedApiVersionException;
import com.oplus.filemanager.dfm.DFMManager;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import kotlin.Pair;

public class VolumeEnvironment {
    private static final String TAG = "VolumeEnvironment";
    public static final String ANDROID_STORAGE_HEADER = "/storage";
    private static final String SELF_PACKAGENAME = "com.coloros.filemanager";
    private static final String DATA_PATH_HEADER = "/data/user/";
    private static final int MORE_COUNT_MOUNTED = 2;
    private static String sInternalSdDir;
    private static String sExternalSdDir;
    private static String sApplicationDataDir;
    private static StorageManager sStorageManager;
    private static boolean sIsOtgVersion = false;
    private static boolean sIsNeedCheckOTGVersion = true;

    public static String getDataDirPath(Context context) {
        try {
            if (sApplicationDataDir == null) {
                String packageName = context.getPackageName();
                ApplicationInfo appInfo = context.getPackageManager().getApplicationInfo(packageName, 0);
                if (appInfo != null) {
                    sApplicationDataDir = appInfo.dataDir;
                    return sApplicationDataDir;
                }
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, e.getMessage());
        }
        return DATA_PATH_HEADER + Utils.getUserId() + File.separator
                + KtAppUtils.INSTANCE.getApplicationId(MyApplication.getFlavorBrand(), MyApplication.getFlavorRegion());
    }

    public static String getDataDirPath(Context context, String... paths) {
        if ((paths == null) || (paths.length == 0) || (paths[0] == null)) {
            return "";
        }

        StringBuilder pathBuilder = new StringBuilder(getDataDirPath(context));
        for (String path : paths) {
            if (path == null) {
                continue;
            }
            pathBuilder.append(File.separator).append(path);
        }
        Log.d(TAG, "getDataDirPath pathBuilder: " + pathBuilder.toString());
        return pathBuilder.toString();
    }

    public static boolean isSingleSdcard(Context context) {
        int volumeCount = VolumeEnvironment.getVolumeCount(context);
        // 2 means two volume
        return volumeCount < 2;
    }

    public static boolean isInternalSdMounted(Context context) {
        String mount = getInternalSdState(context);
        return (null != mount) && (mount.equals(Environment.MEDIA_MOUNTED) || mount.equals(Environment.MEDIA_MOUNTED_READ_ONLY));
    }

    public static boolean isExternalSdMounted(Context context) {
        String mount = getExternalSdState(context);
        return (null != mount) && (mount.equals(Environment.MEDIA_MOUNTED) || mount.equals(Environment.MEDIA_MOUNTED_READ_ONLY));
    }

    private static void update(Context context) {
        if (!checkStorageManager(context)) {
            return;
        }
        List<StorageVolume> volumes = sStorageManager.getStorageVolumes();

        if (volumes.isEmpty()) {
            return;
        }
        Log.i(TAG, "the length of volumes[] is: " + volumes.size() + " ,expected is 1 or 2 !");
        try {
            for (int i = 0; i < volumes.size(); i++) {
                if (volumes.get(i).isRemovable()) {
                    sExternalSdDir = getPathFromStorageVolume(volumes.get(i));
                } else {
                    sInternalSdDir = getPathFromStorageVolume(volumes.get(i));
                }
            }
        } catch (UnSupportedApiVersionException e) {
            Log.e(TAG, "update getPathFromStorageVolume error " + e.getMessage());
        }
        Log.v(TAG, "update sExternalSdDir " + sInternalSdDir);

    }

    private static int getVolumeCount(Context context) {
        if (FeatureCompat.getSIsNotSupportSD() || (context == null)) {
            return 1;
        }
        if (!checkStorageManager(context)) {
            return 1;
        }
        List<StorageVolume> volumes = sStorageManager.getStorageVolumes();
        if (null != volumes) {
            boolean moreMounted = false;
            try {
                for (StorageVolume volume : volumes) {
                    if (volume.isRemovable()) {
                        String path = getPathFromStorageVolume(volume);
                        if ((null != path) && (Environment.MEDIA_MOUNTED.equals(getVolumeState(volume))
                                || Environment.MEDIA_CHECKING.equals(getVolumeState(volume)))) {
                            moreMounted = true;
                            break;
                        }
                    }
                }
            } catch (UnSupportedApiVersionException e) {
                Log.e(TAG, "getVolumeCount error " + e.getMessage());
            }
            if (moreMounted) {
                return MORE_COUNT_MOUNTED;
            } else {
                return 1;
            }
        } else {
            return 1;
        }
    }

    /**
     * check sStorageManager ,if empty execute  {@link #getStorageManager}
     *
     * @param context
     * @return if sStorageManager not null,return true ,else return false
     */
    private static boolean checkStorageManager(Context context) {
        if ((context != null) && (sStorageManager == null)) {
            Log.d(TAG, "checkStorageManager getStorageManager");
            sStorageManager = getStorageManager(context);
        }
        return sStorageManager != null;
    }

    private static String getVolumeState(StorageVolume vol) {
        if (vol != null) {
            return vol.getState();
        } else {
            return Environment.MEDIA_UNKNOWN;
        }
    }

    private static String getVolumeState(StorageManager sStorageManager, String mountPoint) {
        final StorageVolume vol = sStorageManager.getStorageVolume(new File(mountPoint));
        if (vol != null) {
            return vol.getState();
        } else {
            return Environment.MEDIA_UNKNOWN;
        }
    }

    @Nullable
    public static ArrayList<BaseFileBean> getVolumeList() {
        if (!checkStorageManager(MyApplication.getSAppContext())) {
            return null;
        }
        List<StorageVolume> volumes = sStorageManager.getStorageVolumes();
        if ((null != volumes) && (volumes.size() > 0)) {
            ArrayList<BaseFileBean> list = new ArrayList<BaseFileBean>();
            try {
                for (StorageVolume volume : volumes) {
                    String path = getPathFromStorageVolume(volume);

                    if (FeatureCompat.getSIsNotSupportSD()) {
                        String string = getInternalSdPath(MyApplication.getSAppContext());
                        if ((!TextUtils.isEmpty(string)) && string.equals(path) && (null != path)
                                && (Environment.MEDIA_MOUNTED.equals(getVolumeState(volume))
                                || Environment.MEDIA_CHECKING.equals(getVolumeState(volume)))) {
                            BaseFileBean file = new PathFileWrapper(path);
                            list.add(file);
                        }
                    } else {
                        if ((null != path) && (Environment.MEDIA_MOUNTED.equals(getVolumeState(volume)))
                                || Environment.MEDIA_CHECKING.equals(getVolumeState(volume))) {
                            BaseFileBean file = new PathFileWrapper(path);
                            list.add(file);
                        }
                    }
                }
            } catch (UnSupportedApiVersionException e) {
                Log.e(TAG, "getVolumeList error " + e.getMessage());
            }
            return list;
        } else {
            return null;
        }
    }

    public static ArrayList<String> getVolumePathList() {
        return getVolumePathList(true);
    }

    public static ArrayList<String> getVolumePathList(boolean includeChecking) {
        if (!checkStorageManager(MyApplication.getSAppContext())) {
            return null;
        }
        List<StorageVolume> volumes = sStorageManager.getStorageVolumes();
        if ((null != volumes) && (volumes.size() > 0)) {
            ArrayList<String> list = new ArrayList<String>();
            try {
                for (StorageVolume volume : volumes) {
                    String path = getPathFromStorageVolume(volume);

                    boolean stateEq = Environment.MEDIA_MOUNTED.equals(getVolumeState(volume));
                    if (includeChecking) {
                        stateEq |= Environment.MEDIA_CHECKING.equals(getVolumeState(volume));
                    }
                    if (FeatureCompat.getSIsNotSupportSD()) {
                        String string = getInternalSdPath(MyApplication.getSAppContext());
                        boolean pathEq = !TextUtils.isEmpty(string) && string.equals(path);
                        if (pathEq && stateEq) {
                            list.add(path);
                        }
                    } else {
                        if ((null != path) && stateEq) {
                            list.add(path);
                        }
                    }
                }
            } catch (UnSupportedApiVersionException e) {
                Log.e(TAG, "getVolumePathList error " + e.getMessage());
            }
            return list;
        } else {
            return null;
        }
    }

    public static boolean isNeedLoadPath(Context context, String path) {
        return !isMultiAppVolume(path) && !isMountedByPath(context, path);
    }

    private static boolean isMultiAppVolume(String path) {
        if (TextUtils.isEmpty(path)) {
            Log.e(TAG, "isMultiAppVolume  path is empty");
            return false;
        }
        return KtConstants.LOCAL_VOLUME_MULTI_APP_PATH.startsWith(path);
    }

    public static boolean isMountedByPath(Context context, String path) {
        if ((context == null) || (TextUtils.isEmpty(path))) {
            return false;
        }
        if (!checkStorageManager(context)) {
            return false;
        }
        try {
            return Environment.MEDIA_MOUNTED.equals(getVolumeState(sStorageManager, path))
                    || Environment.MEDIA_CHECKING.equals(getVolumeState(sStorageManager, path));
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public static String getInternalSdPath(Context context) {
        if (null == context) {
            return null;
        }
        sInternalSdDir = OplusUsbEnvironmentCompat.getInternalPath(context);
        if (TextUtils.isEmpty(sInternalSdDir)) {
            update(context);
        }

        return sInternalSdDir;
    }

    public static String getExternalSdPath(Context context) {
        if (null == context) {
            return null;
        }
        if (isOTGVersion(context)) {
            sExternalSdDir = OplusUsbEnvironmentCompat.getExternalPath(context);
            Log.d(TAG, "getExternalSdPath isOTGVersion  sExternalSdDir");
            checkStorageManager(context);
        } else {
            update(context);
        }
        return sExternalSdDir;
    }

    public static String getInternalSdState(Context context) {
        update(context);
        return (null == sInternalSdDir) ? null : getVolumeState(sStorageManager, sInternalSdDir);
    }

    public static String getExternalSdState(Context context) {
        if (isOTGVersion(context)) {
            sExternalSdDir = OplusUsbEnvironmentCompat.getExternalPath(context);
            checkStorageManager(context);
        } else {
            update(context);
        }
        return (null == sExternalSdDir) ? null : getVolumeState(sStorageManager, sExternalSdDir);
    }

    private static StorageManager getStorageManager(Context context) {
        Context applicationContext = context.getApplicationContext();
        return (StorageManager) applicationContext.getSystemService(Context.STORAGE_SERVICE);
    }

    public static Pair<Long, Long> getOtgTotalAndAvailableSize(Context context) {
        long totalSize = 0;
        long availableSize = 0;
        List<String> otgPaths = getOTGPath(context);
        if ((otgPaths != null) && ((otgPaths.size()) > 0)) {
            int size = otgPaths.size();
            for (int i = 0; i < size; i++) {
                String tempString = otgPaths.get(i);
                if (!TextUtils.isEmpty(tempString) && isMountedByPath(context, tempString)) {
                    long tempSize = Utils.getStorageAvailableSize(tempString);
                    if (tempSize > 0) {
                        availableSize += tempSize;
                    }
                    long tempTotal = Utils.getStorageTotalSize(tempString);
                    if (tempTotal > 0) {
                        totalSize += tempTotal;
                    }
                }
            }
        }
        return new Pair<>(totalSize, availableSize);
    }

    public static List<String> getOTGPath(Context context) {
        if (null == context) {
            return null;
        }
        return OplusUsbEnvironmentCompat.getOtgPath(context);
    }

    public static boolean getExternalOTGState(Context context, boolean isRealMounted) {
        List<String> otgPaths = getOTGPath(context);
        if (otgPaths == null) {
            return false;
        }
        int size = otgPaths.size();
        if (size > 0) {
            if (isRealMounted) {
                for (int i = 0; i < size; i++) {
                    String tempString = otgPaths.get(i);
                    if (!TextUtils.isEmpty(tempString) && isMounted(context, tempString)) {
                        return true;
                    }
                }
            } else {
                for (int i = 0; i < size; i++) {
                    String tempString = otgPaths.get(i);
                    if (!TextUtils.isEmpty(tempString) && isMountedByPath(context, tempString)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    public static boolean isMounted(Context context, String path) {
        if ((context == null) || (TextUtils.isEmpty(path))) {
            return false;
        }
        if (!checkStorageManager(context)) {
            return false;
        }
        if (path.startsWith(KtConstants.LOCAL_VOLUME_MULTI_APP_PATH)) {
            return true;
        }
        if (KtUtils.checkIsDfmPath(path)) {
            return DFMManager.INSTANCE.getDFSDevice() != null;
        }
        try {
            return Environment.MEDIA_MOUNTED.equals(getVolumeState(sStorageManager, path));
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public static boolean isOTGPath(Context context, String path) {
        List<String> otgPaths = getOTGPath(context);
        if ((otgPaths == null) || (otgPaths.size() == 0) || TextUtils.isEmpty(path)) {
            return false;
        }
        for (String tempString : otgPaths) {
            if (!TextUtils.isEmpty(tempString)) {
                if (path.startsWith(tempString)) {
                    return true;
                }
            }
        }
        return false;
    }

    public static boolean isSdcardPath(Context context, String path) {
        String string = OplusUsbEnvironmentCompat.getExternalPath(context);
        return !TextUtils.isEmpty(string) && !TextUtils.isEmpty(path) && path.startsWith(string);
    }

    public static boolean isOTGVersion(Context context) {
        if (sIsNeedCheckOTGVersion && (context != null)) {
            Log.v(TAG, "isOTGVersion isNeedCheckOTGVersion ");
            ThreadManager.getSThreadManager().execute(new FileRunnable(new Runnable() {
                @Override
                public void run() {
                    setOtgVersion(FeatureCompat.getSIsSupportOTG());
                    Log.i(TAG, "isOTGVersion saved --> " + sIsOtgVersion);
                    sIsNeedCheckOTGVersion = false;
                }
            }, TAG, null));
        }
        Log.d(TAG, "isOTGVersion " + sIsOtgVersion);
        return sIsOtgVersion;
    }

    private static void setOtgVersion(boolean isOtgVersion) {
        sIsOtgVersion = isOtgVersion;
    }

    public static String getPathFromStorageVolume(StorageVolume volume) throws UnSupportedApiVersionException {
        if (SdkUtils.isAtLeastR()) {
            if (volume.getDirectory() != null) {
                return volume.getDirectory().getAbsolutePath();
            }
        } else  {
            return StorageVolumeNative.getPath(volume);
        }
        return "";
    }

    /*
    * use test clear
    * clearValue
    * */
    public static void clearValue() {
        sInternalSdDir = null;
        sExternalSdDir = null;
        sStorageManager = null;
    }
}
