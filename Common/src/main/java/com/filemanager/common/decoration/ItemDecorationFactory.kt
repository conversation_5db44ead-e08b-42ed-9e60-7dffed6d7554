/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/9/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.decoration

import android.app.Activity
import android.content.Context
import androidx.annotation.VisibleForTesting
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseItemDecoration
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import kotlin.math.ceil
import kotlin.math.floor


class ItemDecorationFactory {
    companion object {
        private const val TAG = "ItemDecorationFactory"
        const val GRID_ITEM_COUNT_1 = 1
        const val GRID_ITEM_COUNT_3 = 3
        const val GRID_ITEM_COUNT_4 = 4
        const val GRID_ITEM_COUNT_5 = 5
        const val GRID_ITEM_COUNT_6 = 6
        const val GRID_ITEM_COUNT_7 = 7
        const val GRID_ITEM_COUNT_8 = 8
        const val GRID_ITEM_COUNT_9 = 9
        const val GRID_ITEM_COUNT_10 = 10
        const val GRID_ITEM_COUNT_11 = 11
        const val GRID_ITEM_COUNT_12 = 12
        const val GRID_ITEM_COUNT_13 = 13
        const val GRID_ITEM_COUNT_14 = 14

        const val GRID_ITEM_DECORATION_ALBUM_SET = 1
        const val GRID_ITEM_DECORATION_ALBUM = 2
        const val GRID_ITEM_DECORATION_FILE_BROWSER = 3
        const val GRID_ITEM_DECORATION_SUPER = 4
        const val GRID_ITEM_DECORATION_APK = 5
        const val GRID_ITEM_DECORATION_COMPRESS = 6
        const val GRID_ITEM_DECORATION_DOC = 7
        const val GRID_ITEM_DECORATION_AUDIO = 8
        const val GRID_ITEM_DECORATION_RECENT = 10
        const val GRID_ITEM_DECORATION_MAIN_CATEGORY = 11
        const val GRID_ITEM_DECORATION_MAIN_LABEL = 12
        const val GRID_ITEM_DECORATION_LIST_LABEL = 13

        const val WIDTH_IN_DP_355 = 355
        const val WIDTH_IN_DP_360 = 360
        const val WIDTH_IN_DP_431 = 431
        const val WIDTH_IN_DP_455 = 455
        const val WIDTH_IN_DP_529 = 529
        const val WIDTH_IN_DP_580 = 580
        const val WIDTH_IN_DP_566 = 566
        const val WIDTH_IN_DP_640 = 640
        const val WIDTH_IN_DP_652 = 652
        const val WIDTH_IN_DP_677 = 677
        const val WIDTH_IN_DP_712 = 712
        const val WIDTH_IN_DP_840 = 840
        const val WIDTH_IN_DP_841 = 841
        const val WIDTH_IN_DP_914 = 914
        const val WIDTH_IN_DP_980 = 980
        const val WIDTH_IN_DP_1080 = 1080
        const val WIDTH_IN_DP_1138 = 1138
        const val WIDTH_IN_DP_1200 = 1200

        private const val GRID_IMAGE_ITEM_WIDTH_360_MIN = 74
        private const val GRID_IMAGE_ITEM_WIDTH_360_MAX = 99
        private const val GRID_IMAGE_ITEM_WIDTH_MORE_MIN = 89
        private const val GRID_IMAGE_ITEM_WIDTH_MORE_MAX = 119
        @VisibleForTesting
        var recentItemCount = 0
        @VisibleForTesting
        var recentImgWidth = 0

        fun getGridItemCount(activity: Activity?, scanMode: Int, category: Int, tabPosition: Int = 0): Int {
            return if ((activity == null) || (scanMode == KtConstants.SCAN_MODE_LIST)) {
                GRID_ITEM_COUNT_1
            } else {
                val widthDp = ViewHelper.px2dip(activity, getWindowWidth(activity, category))
                when (category) {
                    GRID_ITEM_DECORATION_RECENT -> {
                        return getRecentGridCount(widthDp)
                    }
                    GRID_ITEM_DECORATION_ALBUM_SET -> {
                        return getAlumSetColumnByScreenWidth(widthDp)
                    }
                    GRID_ITEM_DECORATION_ALBUM -> {
                        return getAlumOrVideoColumnByScreenWidth(widthDp)
                    }
                    GRID_ITEM_DECORATION_SUPER -> {
                        return when (tabPosition) {
                            Constants.TAB_IMAGE, Constants.TAB_VIDEO -> getAlumOrVideoColumnByScreenWidth(widthDp)
                            else -> getDefaultColumnByScreenWidth(widthDp)
                        }
                    }
                    GRID_ITEM_DECORATION_AUDIO -> {
                        return when (tabPosition) {
                            CategoryHelper.CATEGORY_VIDEO -> getAlumOrVideoColumnByScreenWidth(widthDp)
                            else -> getDefaultColumnByScreenWidth(widthDp)
                        }
                    }
                    GRID_ITEM_DECORATION_MAIN_CATEGORY -> {
                        return getMainCategoryGridCount(widthDp)
                    }
                    GRID_ITEM_DECORATION_MAIN_LABEL -> return getMainLabelFileGridCount(widthDp)
                    else -> {
                        return getDefaultColumnByScreenWidth(widthDp)
                    }
                }
            }
        }

        @VisibleForTesting
        fun getMainLabelFileGridCount(widthDp: Int): Int {
            return when (widthDp) {
                in 0 until WIDTH_IN_DP_355 -> GRID_ITEM_COUNT_3
                in WIDTH_IN_DP_355 until WIDTH_IN_DP_431 -> GRID_ITEM_COUNT_4
                in WIDTH_IN_DP_431 until WIDTH_IN_DP_529 -> GRID_ITEM_COUNT_5
                in WIDTH_IN_DP_529 until WIDTH_IN_DP_580 -> GRID_ITEM_COUNT_6
                in WIDTH_IN_DP_580 until WIDTH_IN_DP_652 -> GRID_ITEM_COUNT_7
                in WIDTH_IN_DP_652 until WIDTH_IN_DP_712 -> GRID_ITEM_COUNT_8
                in WIDTH_IN_DP_712 until WIDTH_IN_DP_840 -> GRID_ITEM_COUNT_9
                in WIDTH_IN_DP_840 until WIDTH_IN_DP_914 -> GRID_ITEM_COUNT_10
                in WIDTH_IN_DP_914 until WIDTH_IN_DP_980 -> GRID_ITEM_COUNT_11
                in WIDTH_IN_DP_980 until WIDTH_IN_DP_1138 -> GRID_ITEM_COUNT_12
                else -> GRID_ITEM_COUNT_13
            }
        }

        @VisibleForTesting
        fun getMainCategoryGridCount(widthDp: Int): Int {
            return when (widthDp) {
                in 0 until WIDTH_IN_DP_455 -> GRID_ITEM_COUNT_3
                in WIDTH_IN_DP_455 until WIDTH_IN_DP_566 -> GRID_ITEM_COUNT_4
                else -> GRID_ITEM_COUNT_6
            }
        }

        fun getDefaultColumnByScreenWidth(widthDp: Int): Int {
            return when (widthDp) {
                in 0 until WIDTH_IN_DP_455 -> GRID_ITEM_COUNT_3
                in WIDTH_IN_DP_455 until WIDTH_IN_DP_566 -> GRID_ITEM_COUNT_4
                in WIDTH_IN_DP_566 until WIDTH_IN_DP_677 -> GRID_ITEM_COUNT_5
                in WIDTH_IN_DP_677 until WIDTH_IN_DP_840 -> GRID_ITEM_COUNT_6
                in WIDTH_IN_DP_840 until WIDTH_IN_DP_914 -> GRID_ITEM_COUNT_6
                else -> GRID_ITEM_COUNT_8
            }
        }

        fun getAlumSetColumnByScreenWidth(widthDp: Int): Int {
            return when (widthDp) {
                in 0 until WIDTH_IN_DP_640 -> GRID_ITEM_COUNT_3
                in WIDTH_IN_DP_640 until WIDTH_IN_DP_840 -> GRID_ITEM_COUNT_5
                else -> GRID_ITEM_COUNT_6
            }
        }

        fun getAlumOrVideoColumnByScreenWidth(screenWidth: Int): Int {
            //屏幕宽：300~360 item宽范围：77~99   屏幕宽：361以上 item宽范围:89~119
            val itemWidthRange = if (screenWidth <= WIDTH_IN_DP_360) {
                GRID_IMAGE_ITEM_WIDTH_360_MIN..GRID_IMAGE_ITEM_WIDTH_360_MAX
            } else {
                GRID_IMAGE_ITEM_WIDTH_MORE_MIN..GRID_IMAGE_ITEM_WIDTH_MORE_MAX
            }
            val itemGap = 1.0f
            val edge = 0f
            val columnMin = floor(getItemColumn(screenWidth, edge, itemGap, itemWidthRange.last.toFloat())).toInt()
            val columnMax = ceil(getItemColumn(screenWidth, edge, itemGap, itemWidthRange.first.toFloat())).toInt()
            //通过以上列数计算出item的宽度
            //从最小列数开始查找，itemWidth 在宽度范围内，或 itemWidth 比 范围的最小值还小的，取此列数
            return (columnMin..columnMax).first {
                val itemWidth = getItemViewWidth(screenWidth, edge, itemGap, it)
                (itemWidth in itemWidthRange) || (itemWidth < itemWidthRange.first)
            }
        }

        /**
         * 获取列表item的宽
         * @param screenWidth 列表的宽 :减去左右页边距
         * @param edge 边距
         * @param itemGap 元素间距 : 单个元素间距 通过resource.getDimension()获取
         * @param column 列数： 列表要显示几列
         */
        private fun getItemViewWidth(screenWidth: Int, edge: Float, itemGap: Float = 0f, column: Int): Int {
            return ((screenWidth - edge + itemGap) / column - itemGap).toInt()
        }

        /**
         * 获取列数
         * @param screenWidth 列表的宽 :减去左右页边距
         * @param edge 边距
         * @param itemGap 元素间距 : 单个元素间距 通过resource.getDimension()获取
         * @param itemWidth item的宽度
         */
        private fun getItemColumn(screenWidth: Int, edge: Float, itemGap: Float, itemWidth: Float): Float {
            return (screenWidth - edge + itemGap) / (itemWidth + itemGap)
        }

        fun getRecentGridCount(widthInDp: Int): Int {
            return getDefaultColumnByScreenWidth(widthInDp)
        }

        fun getWidthInDp(activity: Activity): Int {
            val widthInPx = KtViewUtils.getWindowSize(activity).x
            return ViewHelper.px2dip(activity, widthInPx)
        }

        private fun getFileBrowserItemDecoration(): BaseItemDecoration {
            return FileBrowserSpacesItemDecoration(GRID_ITEM_COUNT_3,
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_browser_grid_item_margin_top),
                false,
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_padding_left),
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_browser_grid_item_margin_top))
        }

        private fun getImgGridItemDecoration(): BaseItemDecoration {
            return SpacesItemDecoration(GRID_ITEM_COUNT_3, MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.album_grid_space), false)
        }

        private fun getFileDocItemDecoration(): BaseItemDecoration {
            return FileBrowserSpacesItemDecoration(GRID_ITEM_COUNT_3,
                MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_margin_left),
                false,
                edgeSpace = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_padding_left),
                vertical = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_margin_top),
                verticalFirst = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_padding_top))
        }

        fun getGridItemDecoration(category: Int, tabPosition: Int = 0, activity: Activity? = null): BaseItemDecoration {
            return when (category) {
                GRID_ITEM_DECORATION_ALBUM_SET -> {
                    return AlbumSetItemDecoration(getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, GRID_ITEM_DECORATION_ALBUM_SET))
                }
                GRID_ITEM_DECORATION_ALBUM -> {
                    getImgGridItemDecoration()
                }
                GRID_ITEM_DECORATION_FILE_BROWSER -> {
                    return getFileBrowserItemDecoration()
                }
                GRID_ITEM_DECORATION_SUPER -> {
                    return when (tabPosition) {
                        Constants.TAB_ALL -> {
                            return getFileBrowserItemDecoration()
                        }
                        Constants.TAB_IMAGE -> {
                            getImgGridItemDecoration()
                        }
                        Constants.TAB_VIDEO -> {
                            getImgGridItemDecoration()
                        }
                        Constants.TAB_DOCUMENT -> {
                            getFileBrowserItemDecoration()
                        }
                        else -> {
                            return getFileBrowserItemDecoration()
                        }
                    }
                }
                GRID_ITEM_DECORATION_APK -> {
                    return getFileBrowserItemDecoration()
                }
                GRID_ITEM_DECORATION_COMPRESS -> {
                    return getFileBrowserItemDecoration()
                }
                GRID_ITEM_DECORATION_DOC -> {
                    return getFileBrowserItemDecoration()
                }
                GRID_ITEM_DECORATION_AUDIO -> {
                    return when (tabPosition) {
                        CategoryHelper.CATEGORY_AUDIO -> {
                            return getFileBrowserItemDecoration()
                        }
                        CategoryHelper.CATEGORY_VIDEO -> {
                            getImgGridItemDecoration()
                        }
                        else -> {
                            getImgGridItemDecoration()
                        }
                    }
                }
                GRID_ITEM_DECORATION_LIST_LABEL -> return getFileBrowserItemDecoration()
                else -> {
                    return getFileBrowserItemDecoration()
                }
            }
        }

        fun getGridItemWidth(activity: Activity, category: Int): Int {
            val spanCount = getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, category)
            val itemSpace = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.weixin_grid_vertical_spacing)
            return KtViewUtils.getGridItemWidth(activity, itemSpace, spanCount, 0, getWindowWidth(activity, category))
        }

        /**
         * 文档宫格缩略图，显示Wps缩略图的item宽度
         */
        fun getGridItemWidthForDocWithWps(activity: Activity): Int {
            val spanCount = getGridItemCount(activity, KtConstants.SCAN_MODE_GRID, GRID_ITEM_DECORATION_DOC)
            val itemSpace = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_margin_left)
            val leftRightMargin = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.file_doc_grid_padding_left) * 2
            return KtViewUtils.getGridItemWidth(activity, itemSpace, spanCount, leftRightMargin)
        }

        fun setRecentGridCount(activity: Activity?) {
            if (activity == null) {
                recentItemCount = 1
                return
            }
            val widthDp = ViewHelper.px2dip(activity, getWindowWidth(activity, GRID_ITEM_DECORATION_RECENT))
            recentItemCount = getRecentGridCount(widthDp)
            Log.e(TAG, "setRecentGridCount width:$widthDp count:$recentItemCount")
        }

        fun setRecentItemImageWidth(activity: Activity?) {
            if (activity == null) {
                recentImgWidth = 0
                return
            }
            recentImgWidth = KtViewUtils.getGridItemWidth(
                activity,
                activity.resources.getDimensionPixelSize(R.dimen.recent_grid_item_space_horizontal),
                recentItemCount,
                getHorizontalMargin(activity) * 2 +
                        activity.resources.getDimensionPixelSize(R.dimen.dimen_16dp) * 2,
            )
        }

        @VisibleForTesting
        fun getHorizontalMargin(context: Context): Int {
            return when (UIConfigMonitor.getWindowType()) {
                UIConfig.WindowType.SMALL -> context.resources.getDimensionPixelSize(R.dimen.dimen_16dp)
                UIConfig.WindowType.MEDIUM -> context.resources.getDimensionPixelSize(R.dimen.dimen_24dp)
                else -> context.resources.getDimensionPixelSize(R.dimen.dimen_40dp)
            }
        }

        fun getRecentItemImageWidth(activity: Activity?): Int {
            if (recentImgWidth == 0) {
                setRecentItemImageWidth(activity)
            }
            return recentImgWidth
        }

        fun getRecentGridCount(activity: Activity?): Int {
            if (recentItemCount == 0) {
                setRecentGridCount(activity)
            }
            return recentItemCount
        }

        fun getWindowWidth(activity: Activity?, category: Int): Int {
            var width = 0
            if (activity is BaseVMActivity) {
                width = activity.getWindowWidth(category)
            }
            if (width == 0) {
                width = KtViewUtils.getWindowSize(activity).x
            }
            return width
        }
    }
}