/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : CompatUtils
 ** Description : CompatUtils
 ** Version     : 1.0
 ** Date        : 2023/07/03 10:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2023/07/03     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

import android.os.Build
import com.filemanager.common.utils.SdkUtils
import java.util.function.Supplier

object CompatUtils {

    // 不适配 osdk
    private const val DISABLE_COMPACT_OSDK = false

    const val OS_14 = OplusBuildCompat.OS_14_0

    /**
     * OS 14 第二位版本号
     */
    const val OS_14_SUB_V = 0

    /**
     * 判断 os版本
     */
    @JvmStatic
    fun checkOSVersion(api: Int, subApi: Int): Boolean {
        if (DISABLE_COMPACT_OSDK) {
            return false
        }
        // 版本号为两位，从android-T 13.0 以上开始定义，13.0 以下无法获取
        val osVersion = SdkUtils.getOSVersion()
        if (osVersion < OplusBuildCompat.OS_13_0) {
            return false
        }
        val sdkVersion = SdkUtils.getOSSdkVersion()
        if (sdkVersion > api) {
            return true
        }
        val subVersion = SdkUtils.getOSSdkSubVersion()
        if (sdkVersion == api) {
            return subVersion >= subApi
        }
        return false
    }

    /**
     * 判断是否是 android U版本
     */
    @JvmStatic
    fun checkAndroidUVersion(): Boolean {
        if (DISABLE_COMPACT_OSDK) {
            return false
        }
        return SdkUtils.getSDKVersion() > Build.VERSION_CODES.TIRAMISU
    }


    /**
     * 根据os版本适配 api 接口
     */
    @JvmStatic
    fun <R> compactApi(apiVersion: Int, subApiVersion: Int, newApi: Supplier<R>, oldApi: Supplier<R>): R {
        return if (checkOSVersion(apiVersion, subApiVersion)) {
            newApi.get()
        } else {
            oldApi.get()
        }
    }

    /**
     * 根据Android U版本适配接口
     */
    @JvmStatic
    fun <R> compactApi(newApi: Supplier<R>, oldApi: Supplier<R>): R {
        return if (checkAndroidUVersion()) {
            newApi.get()
        } else {
            oldApi.get()
        }
    }

    /**
     * 根据Android S版本适配接口 【OS12】
     * 【暂时主要用来适配addon接口】
     */
    @JvmStatic
    fun <R> compactSApi(newApi: Supplier<R>, oldApi: Supplier<R>): R {
        return if (SdkUtils.isAtLeastS()) {
            newApi.get()
        } else {
            oldApi.get()
        }
    }
}