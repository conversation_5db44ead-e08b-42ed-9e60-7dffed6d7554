/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : BaseFragmentAdapter
 ** Description : Base Fragment Adapter for ViewPager
 ** Version     : 1.0
 ** Date        : 2023/3/7
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2023/3/7       1.0        create
 ***********************************************************************/
package com.filemanager.common.base

import androidx.annotation.NonNull
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentStatePagerAdapter

abstract class BaseFragmentAdapter : FragmentStatePagerAdapter {

    constructor(
        @NonNull fragmentActivity: FragmentActivity,
        behavior: Int = BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
    ) : super(fragmentActivity.supportFragmentManager, behavior)

    constructor(@NonNull fragment: Fragment, behavior: Int = BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT) : super(fragment.childFragmentManager, behavior)

    override fun getCount(): Int {
        return getItemCount()
    }

    override fun getItem(position: Int): Fragment {
        return createFragment(position)
    }

    /**
     * Return the number of views available.
     */
    abstract fun getItemCount(): Int

    /**
     * Return the Fragment associated with a specified position.
     */
    abstract fun createFragment(position: Int): Fragment
}