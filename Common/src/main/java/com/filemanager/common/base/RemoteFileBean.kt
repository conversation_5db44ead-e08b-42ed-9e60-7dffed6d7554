/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileBean
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.net.Uri
import com.filemanager.common.bean.remotedevice.RemoteFileData
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.Constants.REMOTE_RECENT_DIR_PATH
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.PathUtils
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date

class RemoteFileBean() : BaseFileBean(), IRemotePath {

    companion object {
        const val TAG = "RemoteFileBean"
    }

    var remoteId: String? = null

    var originalPath: String = ""

    var createTime: Long = -1


    var folderFileNum: Int = -1

    var remotePicWidth: Int = -1
    var remotePicHeight: Int = -1

    var remoteAudioChannels: Int = -1

    var remotePicUri: Uri? = null

    var alternativeName: String? = null

    constructor(data: RemoteFileData) : this() {
        this.mDisplayName = data.fileName
        this.originalPath = data.path
        val currentRootPath = PathUtils.getCurrentRemoteMacRootPath()
        if (isRecentFolder() && currentRootPath != null) {
            this.mData = Constants.REMOTE_PATH_PREFIX + currentRootPath + File.separator + originalPath
        } else {
            this.mData = Constants.REMOTE_PATH_PREFIX + originalPath
        }
        this.alternativeName = data.alternativeName
        this.mIsDirectory = data.isDir
        this.mSize = data.size
        //最近使用没有最后修改时间，只有最后打开时间，用最后打开时间代替
        this.mDateModified = if (isRecentFolder()) data.lastOpenDate else data.modifyDate
        this.lastOpenTime = data.lastOpenDate
        this.mLocalType = if (data.isDir) {
            MimeTypeHelper.DIRECTORY_TYPE
        } else {
            MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName))
                ?: MimeTypeHelper.UNKNOWN_TYPE
        }
        this.mMimeType = MimeTypeHelper.getMimeTypeFromPath(this.mDisplayName)

        this.mMediaDuration = data.remoteVideoDuration


        //这些是特有的
        this.remoteId = data.remoteId
        //最近使用没有创建时间，只有最后打开时间，用最后打开时间代替
        this.createTime = if (isRecentFolder()) data.lastOpenDate else data.createDate
        this.folderFileNum = data.fileNum

        this.remotePicWidth = data.remoteImageWidth
        this.remotePicHeight = data.remoteImageHeight
        this.remoteAudioChannels = data.remoteAudioChannel
    }


    fun isRecentFolder(): Boolean {
        return this.originalPath == REMOTE_RECENT_DIR_PATH
    }

    fun shouldShowAlternativeName(): Boolean {
        return this.alternativeName != null && (this.alternativeName != this.mDisplayName)
    }

    override fun getRemotePath(): String {
        return originalPath
    }

    override fun toString(): String {
        return "RemoteFileBean(originalPath='$originalPath', remoteId: $remoteId" +
                ", mDisplayName=$mDisplayName, alias=$alternativeName, mData: $mData" +
                ", modifyTime=$mDateModified" + ": " + getDateFormateString(mDateModified) +
                ", lastOpenTime=$lastOpenTime" + ": " + getDateFormateString(lastOpenTime) +
                ", createTime=$createTime, " + ": " + getDateFormateString(createTime) +
                "folderFileNum=$folderFileNum, " +
                "remotePicWidth=$remotePicWidth, " +
                "remotePicHeight=$remotePicHeight, " +
                "remoteAudioChannels=$remoteAudioChannels)"
    }

    private fun getDateFormateString(timeStamp: Long): String {
        val date = Date(timeStamp)
        val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val result = simpleDateFormat.format(date)
        return result
    }
}

interface IRemotePath {
    fun getRemotePath(): String
}