/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : abstract Base VMActivity
 * * Version     : 1.0
 * * Date        : 2020/1/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.lifecycle.MutableLiveData
import com.coui.appcompat.sidenavigation.COUISideNavigationBar
import com.coui.appcompat.statusbar.COUIStatusBarResponseUtil
import com.coui.appcompat.theme.COUIThemeOverlay
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.base.edge.EdgeToEdgeActivity
import com.filemanager.common.compat.OplusIntentCompat
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.KtConstants.ACTION_DRAG_FILE_CHANGED
import com.filemanager.common.constants.KtConstants.PROTECT_PERMISSION
import com.filemanager.common.controller.PermissionController
import com.filemanager.common.controller.PermissionController.OnRequestPermissionListener
import com.filemanager.common.controller.PrivacyPolicyController
import com.filemanager.common.dragselection.DefaultDropListener
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.action.DropDispatchAction
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.UIConfigMonitor.OnUIConfigChangeListener
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenFoldConfig
import com.filemanager.common.helper.uiconfig.type.ScreenOrientationConfig
import com.filemanager.common.helper.uiconfig.type.ZoomWindowConfig
import com.filemanager.common.imageloader.application.ApplicationInfoLoader
import com.filemanager.common.thread.BaseThreadTask
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.thread.ThreadPriority
import com.filemanager.common.thread.ThreadType
import com.filemanager.common.utils.AppPlatformController
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.registerExportedReceiver
import com.oplus.statistics.OplusTrack
import com.oplus.view.OplusView
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import java.util.function.Consumer

abstract class BaseVMActivity : EdgeToEdgeActivity(), CoroutineScope by CoroutineScope(Dispatchers.Default),
        OnRequestPermissionListener, COUIStatusBarResponseUtil.StatusBarClickListener, OnUIConfigChangeListener {
    companion object {
        private const val TAG = "BaseVMActivity"
        private const val ACTION_SKIN_CHANGED = "android.intent.action.SKIN_CHANGED"
        private const val ACTION_ENCRYPT_FILE_CHANGED = "com.oplus.encryption.action.ENCRYPT_FILE_CHANGED"
    }
    private var mStatusBarResponse: COUIStatusBarResponseUtil? = null
    private var mPermissionController: PermissionController? = null
    private val mAppPlatformController by lazy {
        AppPlatformController(this)
    }
    var sideNavigationStatus = MutableLiveData<Int>(KtConstants.SIDE_NAVIGATION_OPEN)
    var sideNavigationContainer: COUISideNavigationBar? = null
    private var requestPermissionCallback: Consumer<Boolean>? = null
    private val permissionLauncher: ActivityResultLauncher<String> = registerForActivityResult(ActivityResultContracts.RequestPermission()) { grant ->
        Log.d(TAG, "request permission result:$grant")
        requestPermissionCallback?.accept(grant)
    }

    private var activityResultCallback: Consumer<ActivityResult>? = null
    private val activityResultLauncher: ActivityResultLauncher<Intent> = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        Log.d(TAG, "activity result:$it")
        activityResultCallback?.accept(it)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        registerUIConfigChange()
        mStatusBarResponse = COUIStatusBarResponseUtil(this)
        mStatusBarResponse?.setStatusBarClickListener(this)
        //should be called before setContentView
        StatusBarUtil.setStatusBarTransparentAndBlackFont(this)
        applyThemeOverlays()
        setContentView(getLayoutResId())
        initView()
        initNavigationBar()
        startObserve()
        initData()
        if (isNotMainActivity()) {
            registerOTGReceiver()
        }
        registerCornerUpdateReceiver()
        registerDragPrivateSafeReceiver()
        registerDragFileChangeReceiver()
        window.decorView.setOnDragListener(DefaultDropListener(this))
        PredictiveBackUtils.registerOnBackInvokedCallback(this) {
            onBackPressed()
        }
    }

    /**
    #7838609 体验单，某些界面不需要跟随系统主题色
     */
    private fun applyThemeOverlays() {
        if (needApplyThemeOverlays()) {
            COUIThemeOverlay.getInstance().applyThemeOverlays(this)
        }
    }

    open fun needApplyThemeOverlays(): Boolean {
        return true
    }

    protected open fun initNavigationBar() {
        StatusBarUtil.setNavigationBarColor(this)
    }

    /** 是否适配OS12的底部导航栏，底部有Tab（如MainActivity）的不需要适配
     * 适配的效果为，当是手势导航并且是上下滚动页面时，最底部的导航条后面可看到页面内容 */
    open fun isAdaptNavigationBar() = true

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        UIConfigMonitor.instance.onActivityConfigChanged(newConfig)
    }

    override fun onMultiWindowModeChanged(isInMultiWindowMode: Boolean, newConfig: Configuration) {
        super.onMultiWindowModeChanged(isInMultiWindowMode, newConfig)
        UIConfigMonitor.instance.onMultiWindowModeChanged(isInMultiWindowMode)
    }

    abstract fun getLayoutResId(): Int
    abstract fun initView()
    abstract fun startObserve()
    abstract fun initData()
    abstract fun refreshCurrentPage(action: String? = null, data: String? = null)
    private val mMountReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val action = intent.action
                Log.d(TAG, "onReceive action = $action ")
                if ((Intent.ACTION_MEDIA_SCANNER_FINISHED == action) || (Intent.ACTION_MEDIA_BAD_REMOVAL == action)
                        || (Intent.ACTION_MEDIA_REMOVED == action) || (Intent.ACTION_MEDIA_UNMOUNTED == action)
                        || (Intent.ACTION_MEDIA_EJECT == action)) {
                    if (PrivacyPolicyController.hasAgreePrivacy()) {
                        refreshCurrentPage(action, intent.dataString)
                    } else {
                        Log.w(TAG, "onReceive $action , refresh Page， but user not agree statement")
                    }
                }
            }
        }
    }

    private val mCornerMarkReceiver by lazy {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                if (intent.action == KtConstants.COM_OPLUS_VIEW_DRAGBADGE) {
                    if (!SdkUtils.isAtLeastOS16()) return
                    var status = intent.getIntExtra(KtConstants.BADGE_STATUS, DragUtils.CORNER_MARK_STATE_INVALID)
                    val shadowBuild = DragUtils.getShadowBuild()
                    if (status != -1 && shadowBuild != null) {
                        shadowBuild.getCornerMarkPosition()?.let {
                            if (it.size <= 1) return
                            val count = shadowBuild.dropCount ?: 0
                            var display = count.toString()
                            if (count == 1) {
                                //普通状态下如果只选中1个则不显示角标
                                if (status == DropDispatchAction.NORMAL_STATE) {
                                    status = -1
                                }
                                display = ""
                            }
                            val bundle = Bundle().apply {
                                putBoolean(KtConstants.BADGE_SKIP_ANIMA, false)
                            }
                            if (status != DragUtils.lastCornerMarkStateCode) {
                                DragUtils.lastCornerMarkStateCode = status
                            }
                            OplusView(window.decorView).updateDragShadowBadge(
                                shadowBuild,
                                it[0], it[1],
                                display, status, bundle
                            )
                        }
                    }
                }
            }
        }
    }


    private val mVMChangedReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            Log.d(TAG, "mVMChangedReceiver onReceive action = $action")
            onVMChangedReceiver(action, intent)
            if ((ACTION_SKIN_CHANGED == action) || (OplusIntentCompat.ACTION_SKIN_CHANGED == action)) {
                onSkinChangerReceiver(action, intent)
            }
        }
    }

    private val dragPrivateSafeReceiver = object : BroadcastReceiver() {

        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            Log.d(TAG, "dragPrivateSafeReceiver onReceive action = $action")
            if (action == ACTION_ENCRYPT_FILE_CHANGED) {
                onRefreshData()
            }
        }
    }

    private val dragFileChangeReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            val action = intent?.action
            Log.d(TAG, "dragFileChangeReceiver onReceive action = $action")
            if (action == ACTION_DRAG_FILE_CHANGED) {
                val dragApp = intent.getStringExtra(KtConstants.DRAG_APP_PACKAGE)
                Log.d(TAG, "dragFileChangeReceiver dragApp $dragApp")
                if (dragApp != appContext.packageName) {
                    handleDragFileChange(intent)
                }
            }
        }
    }

    open fun handleDragFileChange(intent: Intent?) {
        onRefreshData()
    }

    protected open fun onSkinChangerReceiver(action: String?, intent: Intent) {
        if (ThreadManager.sThreadManager.containThread(ACTION_SKIN_CHANGED).not()) {
            ThreadManager.sThreadManager.execute(ClearAllCacheTask())
        }
    }

    internal class ClearAllCacheTask: BaseThreadTask<Void>(FileRunnable({
        //change shin need to clear apk icon cache
        ApplicationInfoLoader.getInstance().clearAllCache()
    }, ACTION_SKIN_CHANGED), null, null) {

        override fun getUniqueFlag(): String {
            return ACTION_SKIN_CHANGED
        }

        override fun getThreadType(): ThreadType {
            return ThreadType.NORMAL_THREAD
        }

        override fun getPriority(): ThreadPriority {
            return ThreadPriority.BACKGROUND
        }
    }

    protected open fun onVMChangedReceiver(action: String?, intent: Intent) {
    }

    private fun registerOTGReceiver() {
        val iFilter = IntentFilter()
        iFilter.addAction(Intent.ACTION_MEDIA_BAD_REMOVAL)
        iFilter.addAction(Intent.ACTION_MEDIA_SCANNER_FINISHED)
        iFilter.addAction(Intent.ACTION_MEDIA_REMOVED)
        iFilter.addAction(Intent.ACTION_MEDIA_UNMOUNTED)
        iFilter.addAction(Intent.ACTION_MEDIA_EJECT)
        iFilter.addDataScheme("file")
        try {
            appContext.registerExportedReceiver(mMountReceiver, iFilter)
        } catch (e: Exception) {
            Log.e(TAG, "registerOTGReceiver " + e.message)
        }
    }

    private fun registerCornerUpdateReceiver() {
        val cornerFilter = IntentFilter()
        cornerFilter.addAction(KtConstants.COM_OPLUS_VIEW_DRAGBADGE)
        runCatching {
            appContext.registerExportedReceiver(mCornerMarkReceiver, cornerFilter)
        }.onFailure {
            Log.e(TAG, "registerCornerUpdateReceiver onFailure")
        }
    }

    protected fun registerVmChangedReceiver(actions: Array<String>?) {
        val intentFilter = IntentFilter()
        intentFilter.addAction(OplusIntentCompat.ACTION_SKIN_CHANGED)
        intentFilter.addAction(ACTION_SKIN_CHANGED)
        if (actions != null) {
            for (action in actions) {
                intentFilter.addAction(action)
            }
        }
        try {
            appContext.registerExportedReceiver(mVMChangedReceiver, intentFilter)
        } catch (e: Exception) {
            Log.e(TAG, "registerVmChangedReceiver " + e.message)
        }
    }

    protected fun unregisterVmChangedReceiver() {
        try {
            appContext.unregisterReceiver(mVMChangedReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "unregisterVmChangedReceiver " + e.message)
        }

    }

    protected open fun registerUIConfigChange() {
        UIConfigMonitor.instance.also {
            it.attachActivity(this)
            it.addOnUIConfigChangeListener(this)
            KtViewUtils.updateUIOrientation(this, getScreenOrientation())
        }
    }

    protected open fun getScreenOrientation(): Int? {
        return null
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        configList.forEach {
            if ((it is ScreenFoldConfig) || (it is ScreenOrientationConfig)) {
                KtViewUtils.updateUIOrientation(this, getScreenOrientation())
                return
            } else if (it is ZoomWindowConfig && !it.windowShow) {
                KtViewUtils.resetTabletUIOrientation(this)
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        permissionLauncher.unregister()
        activityResultLauncher.unregister()
        mPermissionController = null
        try {
            if (isNotMainActivity()) {
                appContext.unregisterReceiver(mMountReceiver)
            }
            cancel()
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
        runCatching {
            appContext.unregisterReceiver(mCornerMarkReceiver)
        }.onFailure {
            Log.e(TAG, "registerCornerUpdateReceiver onFailure")
        }
        unregisterDragPrivateSafeReceiver()
        unregisterDragFileChangeReceiver()
    }

    override fun onResume() {
        OplusTrack.onResume(this)
        super.onResume()
        PCConnectAction.onActivityResume()
        try {
            mStatusBarResponse?.onResume()
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
        window.decorView.post {
            checkStoragePermission()
        }
    }

    override fun onPause() {
        OplusTrack.onPause(this)
        super.onPause()
        try {
            mStatusBarResponse?.onPause()
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
    }

    override fun onPermissionSuccess(isInstallPermission: Boolean?) {
        mPermissionController?.removePermissionEmptyView(getRootView())

        mAppPlatformController.checkAppPlatformEnabled(this)
    }

    open fun checkStoragePermission() {
        if (mPermissionController == null) {
            mPermissionController = PermissionController.create(this.lifecycle, this)
        }
        if (mPermissionController!!.getWaitPermissionGrantResult()) {
            mPermissionController!!.setWaitPermissionGrantResult(false)
        } else {
            mPermissionController!!.checkPermission(this)
        }
    }
    interface PermissonCallBack {
        fun handleNoStoragePermission()
    }

    override fun onPermissionReject(alwaysReject: Boolean) {
        if (alwaysReject) { // If always reject, show a full screen permission request UI
            Log.d(TAG, "onPermissionReject: permission always rejected")
        } else {
            Log.d(TAG, "onPermissionReject: activity finished after permission reject")
            finish()
        }
    }


    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        mPermissionController?.onPermissionsResultReturn(this, requestCode, permissions, grantResults)
    }

    protected open fun getRootView(): ViewGroup? {
        val view = findViewById<View>(R.id.coordinator_layout)
        return if (view is ViewGroup) view else null
    }

    override fun onStatusBarClicked() {
        backtoTop()
    }

    protected open fun backtoTop() {
        Log.d(TAG, "backtoTop")
    }

    protected open fun isNotMainActivity(): Boolean {
        return true
    }

    open fun onRefreshData() {
        Log.d(TAG, "onRefreshData")
    }

    open fun onRefreshDataAfterFileOperation(operationType: Int, operationFiles: List<BaseFileBean>) {
        Log.i(TAG, "onRefreshDataAfterFileOperation ")
    }


    open fun getWindowWidth(category: Int): Int {
        return 0
    }

    @Suppress("TooGenericExceptionCaught")
    private fun registerDragPrivateSafeReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(ACTION_ENCRYPT_FILE_CHANGED)
        try {
            appContext.registerExportedReceiver(dragPrivateSafeReceiver, intentFilter)
        } catch (e: Exception) {
            Log.e(TAG, "registerDragPrivateSafeReceiver " + e.message)
        }
    }

    @Suppress("TooGenericExceptionCaught")
    private fun unregisterDragPrivateSafeReceiver() {
        try {
            appContext.unregisterReceiver(dragPrivateSafeReceiver)
        } catch (e: Exception) {
            Log.e(TAG, "unregisterDragPrivateSafeReceiver " + e.message)
        }
    }

    private fun registerDragFileChangeReceiver() {
        val intentFilter = IntentFilter()
        intentFilter.addAction(ACTION_DRAG_FILE_CHANGED)
        runCatching {
            if (SdkUtils.isAtLeastT()) {
                appContext.registerReceiver(dragFileChangeReceiver, intentFilter, PROTECT_PERMISSION, null, RECEIVER_EXPORTED)
            } else {
                appContext.registerExportedReceiver(dragPrivateSafeReceiver, intentFilter)
            }
        }.onFailure {
            Log.e(TAG, "registerDragFileChangeReceiver $it")
        }
    }

    private fun unregisterDragFileChangeReceiver() {
        runCatching {
            appContext.unregisterReceiver(dragFileChangeReceiver)
        }.onFailure {
            Log.e(TAG, "registerDragFileChangeReceiver $it")
        }
    }

    fun showSettingGuildDialog() {
        if (mPermissionController == null) {
            mPermissionController = PermissionController.create(this.lifecycle, this)
        }
        mPermissionController?.showSettingGuildDialog(activity = this)
    }

    /**
     * forceShow 点击所有文件弹窗取消按钮时  需要判断显示应用列表弹窗
     */
    open fun checkGetInstalledAppsPermission(forceShow: Boolean = false, isMainShow: Boolean = false, checkCallBack: CheckCallBack? = null) {
        Log.d(TAG, "checkGetInstalledAppsPermission forceShow $forceShow isMainShow $isMainShow")
        if (PrivacyPolicyController.hasAgreePrivacy().not()) {
            return
        }
        if (mPermissionController == null) {
            mPermissionController = PermissionController.create(this.lifecycle, this)
        }
        Log.d(TAG, "checkGetInstalledAppsPermission isDialogShow ${mPermissionController?.isDialogShow}")
        //如果当前显示所有文件弹窗，就不弹读取应用列表弹窗
        if (mPermissionController?.isDialogShow?.not() != false || forceShow) {
            checkCallBack?.handleAfterCheck()
            mPermissionController?.checkGetInstalledAppsPermission(activity = this, isMainShow)
        }
    }

    /**
     * 请求权限
     * @param permission 权限
     * @param callback 权限结果的回调
     */
    fun launchPermission(permission: String, callback: Consumer<Boolean>) {
        Log.d(TAG, "launchPermission $permission")
        requestPermissionCallback = callback
        permissionLauncher.launch(permission)
    }

    /**
     * 跳转页面，类似startActivityForResult
     * @param intent 跳转界面的intent
     * @param callback 从跳转界面回到当前界面后的结果回调
     */
    fun launchActivity(intent: Intent, callback: Consumer<ActivityResult>) {
        activityResultCallback = callback
        activityResultLauncher.launch(intent)
    }

    interface CheckCallBack {
        fun handleAfterCheck()
    }
}