/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OplusFeatureConfigManagerCompat
 ** Description : OplusFeatureConfigManagerCompat
 ** Version     : 1.0
 ** Date        : 2024/09/13 14:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/13     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

import android.content.Context

class OplusFeatureConfigManagerCompat(private val featureConfigMgr: Any) {

    companion object {

        const val FEATURE_LMVIBRATOR_SUPPORT: String = "oplus.software.vibrator_lmvibrator"
        const val FEATURE_MULTI_APP_DISABLED: String = "oplus.software.multi_app_disabled"

        private var instance: OplusFeatureConfigManagerCompat? = null

        @JvmStatic
        fun getInstance(context: Context): OplusFeatureConfigManagerCompat {
            if (instance == null) {
                val mgr = CompatUtils.compactSApi({
                    com.oplus.content.OplusFeatureConfigManager.getInstance()
                }, {
                    com.heytap.addon.content.OplusFeatureConfigManager.getInstance(context)
                })
                instance = OplusFeatureConfigManagerCompat(mgr)
            }
            return instance!!
        }
    }

    fun hasFeature(feature: String): Boolean {
        return CompatUtils.compactSApi({
            val manager = featureConfigMgr as com.oplus.content.OplusFeatureConfigManager
            manager.hasFeature(feature)
        }, {
            val manager = featureConfigMgr as com.heytap.addon.content.OplusFeatureConfigManager
            manager.hasFeature(feature)
        })
    }
}