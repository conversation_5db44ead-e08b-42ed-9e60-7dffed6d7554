/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ClipboardUtils
 ** Description : 剪切板的工具类
 ** Version     : 1.0
 ** Date        : 2024/03/18 11:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/03/18       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context

object ClipboardUtils {

    /**
     * 复制到剪切板
     */
    @SuppressLint("ClipboardManagerDetector")
    @JvmStatic
    fun copyToClipboard(context: Context, text: String) {
        val clipMgr = context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        val clipData = ClipData.newPlainText("", text)
        clipMgr.setPrimaryClip(clipData)
    }
}