/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.interfaces.PCConsumeOnGenericMotionListener
 * * Description : 安装包，全局搜索，文档，压缩包，文件来源等有ViewPager2的页面，PC互联的时候，有滚动滑动事件，会导致ViewPager2滑动卡在中间不动
 *                 在ViewPager2的子View中（目前加在RecyclerView）上添加此监听，消费掉滚轮事件，即可解决此问题
 * * Version     : 1.0
 * * Date        : 2022/4/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.interfaces

import android.view.MotionEvent
import android.view.View

class PCConsumeOnGenericMotionListener : View.OnGenericMotionListener {
    override fun onGenericMotion(v: View?, event: MotionEvent?): Boolean {
        return true
    }
}