/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MediaFileCompat
 ** Description : MediaFileCompat
 ** Version     : 1.0
 ** Date        : 2024-9-13 17:17:08
 ** Author      : W9069469
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9069469        2024/09/13     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

import android.annotation.SuppressLint
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.SdkUtils

class AddonSdkCompat {

    companion object {

        private const val LINEARMOTORVIBRATOR_SERVICE = "linearmotor"

        @JvmStatic
        fun hasFeature(featureKey: String): Boolean {
            return CompatUtils.compactSApi({
                com.oplus.content.OplusFeatureConfigManager.getInstance().hasFeature(featureKey)
            }, {
                com.heytap.addon.content.OplusFeatureConfigManager.getInstance(MyApplication.sAppContext)
                    .hasFeature(featureKey)
            })
        }

        @SuppressLint("WrongConstant")
        @JvmStatic
        fun getLinearmotorVibrator(): Any? {
            return CompatUtils.compactSApi({
                val mVibratorOplus = MyApplication.sAppContext.getSystemService(LINEARMOTORVIBRATOR_SERVICE) as? (com.oplus.os.LinearmotorVibrator)
                mVibratorOplus
            }, {
                val mVibratorHeytap = MyApplication.sAppContext.getSystemService(LINEARMOTORVIBRATOR_SERVICE) as? (com.heytap.addon.os.LinearmotorVibrator)
                mVibratorHeytap
            })
        }

        @JvmStatic
        fun <R,T> vibrator(vibrator: R, we: T) {
            CompatUtils.compactSApi({
                val weOplus = we as com.oplus.os.WaveformEffect
                val mVibratorOplus = vibrator as (com.oplus.os.LinearmotorVibrator)
                mVibratorOplus.vibrate(weOplus)
            }, {
                val weHeytap = we as com.heytap.addon.os.WaveformEffect
                val mVibratorHeytap = vibrator as (com.heytap.addon.os.LinearmotorVibrator)
                mVibratorHeytap.vibrate(weHeytap)
            })
        }

        @JvmStatic
        fun getWaveformEffect(effectType: Int): Any {
            return CompatUtils.compactSApi({
                val weOplus = com.oplus.os.WaveformEffect.Builder()
                    .setEffectType(effectType)
                    .setAsynchronous(true)
                    .build()
                weOplus
            }, {
                val weHeytap = com.heytap.addon.os.WaveformEffect.Builder()
                    .setEffectType(effectType)
                    .build()
                weHeytap
            })
        }

        @JvmStatic
        fun isColorZoomWindowInfoSupported(): Boolean {
            return SdkUtils.isAtLeastR()
        }
    }
}