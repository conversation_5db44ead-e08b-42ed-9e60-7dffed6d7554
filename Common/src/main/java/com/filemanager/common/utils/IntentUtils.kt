/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.utils.IntentUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2020/12/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import android.text.TextUtils
import android.webkit.MimeTypeMap
import com.filemanager.common.constants.Constants
import java.io.Serializable

object IntentUtils {
    private const val TAG = "IntentUtils"

    @JvmStatic
    fun processIntent(intent: Intent, uri: Uri?, path: String?, defaultType: String?) {
        if (uri == null || TextUtils.isEmpty(path)) {
            intent.setDataAndType(uri, defaultType)
            return
        }
        val ext = FileTypeUtils.getExtension(path)
        val type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(ext)
        if (TextUtils.isEmpty(type)) {
            intent.setDataAndType(uri, defaultType)
            return
        }
        Log.d(TAG, "processIntent mimetype = $type")
        intent.setDataAndType(uri, type)
    }

    @JvmStatic
    fun getString(intent: Intent?, key: String?): String? {
        intent?.let {
            try {
                return it.getStringExtra(key)
            } catch (e: Exception) {
                Log.e(TAG, "getString: ${e.message}")
            }
        }
        return ""
    }

    @JvmStatic
    fun getInt(intent: Intent?, key: String?, defaultValue: Int): Int {
        intent?.let {
            try {
                return it.getIntExtra(key, defaultValue)
            } catch (e: Exception) {
                Log.e(TAG, "getInt: ${e.message}")
            }
        }
        return defaultValue
    }

    @JvmStatic
    fun getLong(intent: Intent?, key: String?, defaultValue: Long): Long {
        intent?.let {
            try {
                return it.getLongExtra(key, defaultValue)
            } catch (e: Exception) {
                Log.e(TAG, "getLong: ${e.message}")
            }
        }
        return defaultValue
    }

    @JvmStatic
    fun getBoolean(intent: Intent?, key: String?, defaultValue: Boolean): Boolean {
        intent?.let {
            try {
                return it.getBooleanExtra(key, defaultValue)
            } catch (e: Exception) {
                Log.e(TAG, "getBoolean: ${e.message}")
            }
        }
        return defaultValue
    }

    @JvmStatic
    fun getParcelableArrayList(intent: Intent?, key: String?): ArrayList<Parcelable>? {
        intent?.let {
            try {
                return it.getParcelableArrayListExtra<Parcelable>(key)
            } catch (e: Exception) {
                Log.e(TAG, "getParcelableArrayList: ${e.message}")
            }
        }
        return null
    }

    @JvmStatic
    fun getParcelable(intent: Intent?, key: String?): Parcelable? {
        intent?.let {
            try {
                return it.getParcelableExtra<Parcelable>(key)
            } catch (e: Exception) {
                Log.e(TAG, "getParcelableArrayList: ${e.message}")
            }
        }
        return null
    }

    @JvmStatic
    fun getStringArrayList(intent: Intent?, key: String?): ArrayList<String>? {
        intent?.let {
            try {
                return it.getStringArrayListExtra(key)
            } catch (e: Exception) {
                Log.e(TAG, "getStringArrayList: ${e.message}")
            }
        }
        return null
    }

    @JvmStatic
    fun getIntegerArrayList(intent: Intent?, key: String?): ArrayList<Int>? {
        intent?.let {
            try {
                return it.getIntegerArrayListExtra(key)
            } catch (e: Exception) {
                Log.e(TAG, "getIntegerArrayList: ${e.message}")
            }
        }
        return null
    }

    @JvmStatic
    fun getSerializableExtra(intent: Intent?, key: String?): Serializable? {
        intent?.let {
            runCatching {
                return it.getSerializableExtra(key)
            }.onFailure {
                Log.e(TAG, "getSerializableExtra -> error cause ${it.message}")
            }
        }
        return null
    }

    /**
     * 从Bundle中获取title,优先获取titleId，如果没有再获取title
     */
    @JvmStatic
    fun getTitleFromBundle(context: Context, bundle: Bundle): String {
        val resId = bundle.getInt(Constants.TITLE_RES_ID, -1)
        if (resId != -1) {
            return context.getString(resId)
        }
        return bundle.getString(Constants.TITLE) ?: ""
    }
}