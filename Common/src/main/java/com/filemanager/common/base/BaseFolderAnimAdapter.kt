/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.adapter
 * * Version     : 1.0
 * * Date        : 2020/7/29
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.content.Context
import android.os.Handler
import android.os.Looper
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.animation.FolderTransformAnimator
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.noMoreAction

/**
 * use for Folder transform with animate when RecyclerView has Folder View , such as FileBrowserAdapter
 */
abstract class BaseFolderAnimAdapter<V : RecyclerView.ViewHolder, T>(protected val context: Context) : BaseSelectionRecycleAdapter<V, T>(context) {
    companion object {
        const val FILE_BROWSER_FOLDER_ANIM_DELAY = 0L
        //notifyItemRangeChanged will make ANR when data size is very huge
        private const val MAX_ITEM_CHANGED_SIZE = 5000
    }
    var mScanViewModel = KtConstants.SCAN_MODE_GRID
        get() = field.noMoreAction()
    protected val mUiHandler: Handler = Handler(Looper.getMainLooper())
    private var mFolderTransformAnimator: FolderTransformAnimator? = null
    private var mRunnable: Runnable? = null
    //When FileBrowserActivity sort by the type with item animateRemove(),we can not set outRect margin from recycleView.ItemDecoration getItemOffsets()
    //because of spanSizeLookup getSpanSize() will return null(mFiles are clear),so we need store oldFiles when it remove,and clear when added
    @VisibleForTesting(otherwise = VisibleForTesting.PROTECTED)
    var mOldFiles: MutableList<T> = mutableListOf()
        get() = field.noMoreAction()
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        if (recyclerView.itemAnimator is FolderTransformAnimator) {
            mFolderTransformAnimator = recyclerView.itemAnimator as FolderTransformAnimator
        }
    }

    fun setData(
        data: MutableList<T>,
        selectionArray: MutableList<Int> = mutableListOf(),
        doAnimation: Boolean? = false,
        notifyData: Boolean = true
    ) {
        mFolderTransformAnimator?.mSkipAddRemoveAnimation = (doAnimation != true) || (mFiles.isNullOrEmpty())
        if (doAnimation == true) {
            mSelectionArray = selectionArray
            val previousSize = mFiles.size
            if (mScanViewModel == KtConstants.SCAN_MODE_GRID) {
                mOldFiles.clear()
                mOldFiles.addAll(mFiles)
            }
            mFiles.clear()
            notifyItemRangeRemoved(0, previousSize)
            mRunnable = Runnable {
                mOldFiles.clear()
                mFiles = data
                notifyItemRangeInserted(0, data.size)
                if (data.size < MAX_ITEM_CHANGED_SIZE) {
                    notifyItemRangeChanged(0, data.size)
                }
            }
            mRunnable?.apply {
                mUiHandler.postDelayed(this, FILE_BROWSER_FOLDER_ANIM_DELAY)
            }
        } else {
            mRunnable?.apply {
                mUiHandler.removeCallbacks(this)
            }
            mSelectionArray = selectionArray
            mFiles = data
            if (notifyData) {
                notifyDataSetChanged()
            }
        }
    }

    protected fun onRemoveCallBack() {
        mUiHandler.removeCallbacksAndMessages(null)
    }
}