/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -RecycleFileWrapper.kt
 * * Description : com.coloros.filemanager.filerefactor.wrapper
 * * Version     : 1.0
 * * Date        : 2020/2/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.wrapper

import android.database.Cursor
import android.net.Uri
import com.filemanager.common.RecycleStore
import com.filemanager.common.base.UriFileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTypeUtils
import org.apache.commons.io.FilenameUtils

open class RecycleFileWrapper : UriFileWrapper {
    var mOrientation: Int = 0
    var mHeight: Int? = null
    var mWidth: Int? = null

    var mRecycleId: String? = null
    var mRecyclePath: String? = null
    var mRecycelDate: Long = 0L
    var mOriginPath: String? = null

    /**
     * image 1, vedio 2, audio 3,
     * documents 4, apk 5, zip 6,
     * folder 1000
     */
    var mMediaType = 0
    var mIsDrm: Boolean? = false
    var mRecycleParent: Int? = null
    var mRecycleBucketId: String? = null
    var mVolumeName: String? = null

    companion object {
        private const val TAG = "RecycleFileWrapper"
        val RECYCLEBIN_PROJECTION = arrayOf(
                RecycleStore.Files.FileColumns.RECYCLE_PATH,
                RecycleStore.Files.FileColumns.DISPLAY_NAME,
                RecycleStore.Files.FileColumns.RECYCLE_DATE,
                RecycleStore.Files.FileColumns.SIZE,
                RecycleStore.Files.FileColumns.ORIGIN_PATH,
                RecycleStore.Files.FileColumns.MEDIA_TYPE,
                RecycleStore.Files.FileColumns.MIME_TYPE,
                RecycleStore.Files.FileColumns.IS_DRM,
                RecycleStore.Files.FileColumns.RECYCLE_ID,
                RecycleStore.Files.FileColumns.DATE_MODIFIED
//                FileColumns.RECYCLE_PARENT,
//                FileColumns.RECYCLE_BUCKET_ID
        )
        const val INDEX_COLUMN_RECYCLE_PATH = 0
        const val INDEX_COLUMN_DISPLAY_NAME = 1
        const val INDEX_COLUMN_RECYCLE_DATE = 2
        const val INDEX_COLUMN_SIZE = 3
        const val INDEX_COLUMN_ORIGIN_PATH = 4
        const val INDEX_COLUMN_MEDIA_TYPE = 5
        const val INDEX_COLUMN_MIME_TYPE = 6
        const val INDEX_COLUMN_IS_DRM = 7
        const val INDEX_COLUMN_RECYCLE_ID = 8
        const val INDEX_COLUMN_DATE_MODIFIED = 9
//        private const val INDEX_COLUMN_RECYCLE_PARENT = 11
//        private const val INDEX_COLUMN_RECYCLE_BUCKET_ID = 12


        fun getIndexDataFromCursor(): Int {
            return INDEX_COLUMN_RECYCLE_PATH
        }
    }

    constructor()

    constructor(cursor: Cursor, uri: Uri?) : super(cursor, uri)
    constructor(path: String, label: Int, viewType: Int, typeNum: Int) {
        mData = path
        mOriginPath = path
        mFileWrapperLabel = label
        mFileWrapperViewType = (viewType)
        mFileWrapperTypeNum = typeNum
    }

    override fun createData(cursor: Cursor, uri: Uri?) {
        mId = cursor.getInt(INDEX_COLUMN_RECYCLE_ID)
        mRecycleId = cursor.getString(INDEX_COLUMN_RECYCLE_ID)
        mData = cursor.getString(INDEX_COLUMN_RECYCLE_PATH)
        mDisplayName = cursor.getString(INDEX_COLUMN_DISPLAY_NAME)
        mSize = cursor.getLong(INDEX_COLUMN_SIZE)
        mDateModified = cursor.getLong(INDEX_COLUMN_DATE_MODIFIED) * 1000
        mMimeType = cursor.getString(INDEX_COLUMN_MIME_TYPE)
        mRecyclePath = cursor.getString(INDEX_COLUMN_RECYCLE_PATH)
        mRecycelDate = cursor.getLong(INDEX_COLUMN_RECYCLE_DATE)
        mOriginPath = cursor.getString(INDEX_COLUMN_ORIGIN_PATH)
        mMediaType = cursor.getInt(INDEX_COLUMN_MEDIA_TYPE)
        mIsDrm = cursor.getInt(INDEX_COLUMN_IS_DRM) == 1
        mLocalFileUri = uri?.buildUpon()?.appendPath(mId.toString())?.build()
        mData?.let {
            val file = PathFileWrapper(it)
            //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
            if (mSize <= 0L) {
                mSize = file.mSize
                mDateModified = file.mDateModified
            }
            //@Notice The file name of the recycle bin file has special treatment,
            // the real file name cannot be used directly, and must used the mDislpayName queried from the database
            mLocalType = if (file.mIsDirectory) {
                MimeTypeHelper.DIRECTORY_TYPE
            } else {
                MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName))
                        ?: MimeTypeHelper.UNKNOWN_TYPE
            }
        }
    }

}