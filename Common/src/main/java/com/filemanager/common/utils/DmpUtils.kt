/*********************************************************************
 ** Copyright (C), 2010-2024 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : Utils.kt
 ** Description : Utils
 ** Version     : 1.0
 ** Date        : 2024/04/28
 ** Author      : W9059186
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9059186      2024/04/28       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.text.TextUtils
import java.nio.ByteBuffer

object DmpUtils {
    private const val INT_BYTE_SIZE = 4
    private const val TAG = "Utils"
    @JvmStatic
    fun toIntArray(byteArray: ByteArray?): IntArray {
        if (byteArray == null) {
            return IntArray(0)
        }
        val buffer = ByteBuffer.wrap(byteArray)
        val intArray = IntArray(byteArray.size / INT_BYTE_SIZE)
        for (i in intArray.indices) {
            intArray[i] = buffer.int
        }
        return intArray
    }

    /**
     * 判断是否是无意义字符 A-Za-z0-9_.-
     */
    @JvmStatic
    fun getSimpleWordValue(mSearchKey: String?): Boolean {
        var isSimpleWord = false
        if (TextUtils.isEmpty(mSearchKey)) {
            return true
        }
        val regex = Regex("^[A-Za-z0-9_.()~!@#$%^&*+=-]")
        mSearchKey?.let {
            if (it.length == 1 && regex.matches(it)) {
                isSimpleWord = true
            }
            if (it.trim() == "") {
                isSimpleWord = true
            }
        }
        Log.d(TAG, "isSimpleWord $isSimpleWord")
        return isSimpleWord
    }
}