/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: DropCloudDiskAction
 ** Description: Drop Cloud Disk Action
 ** Version: 1.0
 ** Date : 2024/10/31
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.net.Uri
import android.view.DragEvent
import android.view.Gravity
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.MyApplication
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.getBottomAlertDialogWindowAnimStyle
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.clouddrive.ICloudDrive
import com.oplus.filemanager.interfaze.heytapaccount.IHeytapAccount
import com.support.appcompat.R
import java.io.File

object DropCloudDiskAction : DropListener {

    private const val TAG = "DropCloudDiskAction"

    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem $dropSideItem")
        if (!isAccountLogin(activity)) {
            return false
        }
        val fileUrls: ArrayList<Uri>?
        val parseData = DropUtil.parseDragFile(dragEvent)
        val listUris = parseData.uriList
        if (parseData.noneMediaPathList.isNotEmpty()) {
            listUris.addAll(handleNoneMediaFile(parseData.noneMediaPathList))
        }
        Log.d(TAG, "handleDropAction uris = ${listUris.toArray().contentToString()}")
        if (DropUtil.isExistPrivateDirectory(listUris, activity.applicationContext)) {
            Log.d(TAG, "handleDropAction exist private directory")
            CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
            return false
        }
        fileUrls = DropUtil.handleDocumentsUIFile(listUris)

        if (fileUrls.isEmpty()) {
            Log.d(TAG, "handleDropAction fileUrls is empty")
            CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_emptry_folder)
            return false
        }
        startUpload(activity, fileUrls)
        return true
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        //永远是复制逻辑
        return DropDispatchAction.COPY_STATE
    }

    private fun isAccountLogin(context: Context): Boolean {
        val isAccountLogin = Injector.injectFactory<IHeytapAccount>()?.isLogin(context) ?: false
        Log.d(TAG, "isLogin: $isAccountLogin")
        if (!isAccountLogin) {
            showLoginTipDialog(context)
        }
        return isAccountLogin
    }

    private fun showLoginTipDialog(context: Context) {
        val confirmListener = LoginConfirmDialogListener()
        COUIAlertDialogBuilder(context, com.support.dialog.R.style.COUIAlertDialog_Bottom)
            .setWindowGravity(Gravity.BOTTOM)
            .setWindowAnimStyle(getBottomAlertDialogWindowAnimStyle(context))
            .setTitle(com.filemanager.common.R.string.drag_cloud_not_login)
            .setMessage(com.filemanager.common.R.string.drag_cloud_login_to_upload)
            .setOnCancelListener(confirmListener)
            .setPositiveButton(com.filemanager.common.R.string.drag_cloud_to_login, confirmListener)
            .setNegativeButton(com.filemanager.common.R.string.alert_dialog_no, confirmListener).show()
    }

    private fun handleNoneMediaFile(pathList: ArrayList<String>): ArrayList<Uri> {
        val urls = arrayListOf<Uri>()
        for (path in pathList) {
            urls.add(Uri.fromFile(File(path)))
        }
        return urls
    }

    private fun startUpload(activity: Activity, fileUrls: ArrayList<Uri>) {
        if (isSupportUpload(activity.applicationContext)) {
            Injector.injectFactory<ICloudDrive>()?.uploadFileInBackground(activity.applicationContext, fileUrls, null)
        } else {
            Injector.injectFactory<ICloudDrive>()?.uploadFiles(activity, fileUrls)
        }
    }

    private fun isSupportUpload(context: Context): Boolean {
        val isSupportBackgroundUpload = Injector.injectFactory<ICloudDrive>()?.isSupportBackgroundUpload(context) ?: false
        Log.d(TAG, "isSupportUpload: $isSupportBackgroundUpload")
        return isSupportBackgroundUpload
    }

    private class LoginConfirmDialogListener : DialogInterface.OnClickListener, DialogInterface.OnCancelListener {
        override fun onClick(dialog: DialogInterface?, which: Int) {
            if (which == DialogInterface.BUTTON_POSITIVE) {
                val heytapAccountAction: IHeytapAccount? = Injector.injectFactory<IHeytapAccount>()
                heytapAccountAction?.login(MyApplication.appContext, true) {
                    Log.d(TAG, "login result = $it")
                }
            }
            dialog?.dismiss()
        }

        override fun onCancel(dialog: DialogInterface?) {
            dialog?.dismiss()
        }
    }
}