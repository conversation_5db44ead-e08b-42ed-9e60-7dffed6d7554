/***********************************************************
 ** Copyright (C), 2024-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: IRefreshDataForCreateDir.kt
 ** Description: Refresh all files page from the activity
 ** Version: 1.0
 ** Date: 2024/12/27
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.interfaces

import com.filemanager.common.base.BaseFileBean

interface IRefreshActivityDataForCreateDir {
    fun onRefreshDataForDir(path: String)

    fun renameToShortCutFolder(newName: String, file: BaseFileBean)

    fun renameToLabel(newName: String, labelId: Long)
}