/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: IPreviewOperate
 ** Description: Preview Operate
 ** Version: 1.0
 ** Date : 2024/10/04
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview

import android.view.DragEvent
import android.view.Menu
import androidx.lifecycle.MutableLiveData
import com.filemanager.common.base.BaseFileBean

interface IPreviewOperate {

    fun isSupportPreview(): Boolean

    fun onToolbarMenuUpdated(menu: Menu)

    /**
     * 在非文件选择模式(非编辑模式)下的文件预览，调用这个接口预览文件
     */
    fun previewClickedFile(file: BaseFileBean?, clickFileLiveData: MutableLiveData<BaseFileBean?>?): Boolean

    /**
     * 在文件选择模式(编辑模式)下的文件预览，调用这个接口预览文件
     */
    fun previewEditedFiles(files: List<BaseFileBean>?): Boolean

    /**
     * 当列表的文件数据为空时，调用这个接口把预览内容设置成空页面
     */
    fun listEmptyFile()

    /**
     * 当前是否开启了预览
     */
    fun isPreviewOpen(): Boolean

    /**
     * 关闭预览
     */
    fun closePreview()

    /**
     * 启动页面滚动
     */
    fun handleDragEvent(event: DragEvent): Boolean?

    /**
     * 停止页面滚动
     */
    fun resetDragStatus()
}