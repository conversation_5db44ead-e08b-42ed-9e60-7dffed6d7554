/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils;

import android.content.Context;
import android.content.Intent;
import android.os.Build;

import com.filemanager.common.constants.SafeConstants;
import com.filemanager.common.helper.FileWrapper;
import com.filemanager.common.helper.MimeTypeHelper;
import com.filemanager.common.helper.VolumeEnvironment;
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils;

import java.util.ArrayList;

public class SafeUtils {
    private static final String TAG = "SafeUtils";
    private static final String SYNC_DATA_ALL = "ALL";
    private static final String SYNC_TYPE_START_FILE_SAFE = "sync_type_start_file_safe";
    private static final String ACTION_OLD_CLOUD_ALBUM_DATA_CHANGE = "com.coloros.cloud.action.ALBUM_DATA_CHANGED";
    private static final String ACTION_NEW_CLOUD_ALBUM_DATA_CHANGE = "coloros.intent.action.gallery3d.ALBUM_DATA_CHANGED";
    private static final String PACKAGE_GALLERY3D_APP = "com.coloros.gallery3d";
    private static final String PACKAGE_CLOUD_APP = "com.coloros.cloud";
    private static final int VERSION_Q = 29;
    private static ArrayList<FileWrapper> sFileList;

    public static void cleanEncryptionFileList() {
        if (null != sFileList) {
            sFileList.clear();
        }
    }

    public static void saveEncryptionFileList(ArrayList<FileWrapper> list) {
        sFileList = list;
    }

    public static ArrayList<FileWrapper> getEncryptionFileList() {
        return sFileList;
    }

    public static String getRootPath(Context context) {
        String rootPath = VolumeEnvironment.getInternalSdPath(context);
        if (null == rootPath) {
            rootPath = VolumeEnvironment.getExternalSdPath(context);
        }
        return rootPath;
    }

    public static boolean isStorageEnable(int type, long length, String rootPath) {
        if (null != rootPath) {
            long storageSize = Utils.getStorageAvailableSize(rootPath);
            if ((SafeUtils.isDirectlyEncryption(type) && (storageSize < SafeConstants.THRESHOLD))
                    || ((!SafeUtils.isDirectlyEncryption(type) && (storageSize < (length + SafeConstants.THRESHOLD))))) {
                return false;
            }
        }
        return true;
    }

    public static boolean isDirectlyEncryption(int type) {
        if ((type == MimeTypeHelper.IMAGE_TYPE) || (type == MimeTypeHelper.VIDEO_TYPE) || (type == MimeTypeHelper.ZIP_TYPE)
                || (type == MimeTypeHelper.RAR_TYPE)) {
            return true;
        }
        return false;
    }

    public static void notifySyncGalleryDB(Context context) {
        Log.i(TAG, "notifySyncGalleryDB");
        if (context != null) {
            Intent intent = new Intent();
            if (SdkUtils.getSDKVersion() >= VERSION_Q) {
                intent.setPackage(PACKAGE_GALLERY3D_APP);
                CollectPrivacyUtils.collectInstalledAppList(PACKAGE_GALLERY3D_APP);
                intent.setAction(ACTION_NEW_CLOUD_ALBUM_DATA_CHANGE);
            } else {
                if (Utils.isNeededSdk27()) {
                    intent.setPackage(PACKAGE_CLOUD_APP);
                    CollectPrivacyUtils.collectInstalledAppList(PACKAGE_CLOUD_APP);
                }
                intent.setAction(ACTION_OLD_CLOUD_ALBUM_DATA_CHANGE);
            }
            intent.putExtra("DATA", SYNC_DATA_ALL);
            intent.putExtra("TYPE", SYNC_TYPE_START_FILE_SAFE);
            context.sendBroadcast(intent);
        }
    }
}
