/*********************************************************************
 * * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2020/9/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.graphics.Point
import android.text.Layout
import android.text.StaticLayout
import android.text.TextDirectionHeuristics
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView
import com.filemanager.common.utils.Utils
import java.util.*
import kotlin.math.max
import kotlin.math.min


class MiddleMultilineTextView @JvmOverloads constructor(context: Context,
                                                        attrs: AttributeSet? = null,
                                                        defStyleAttr: Int = 0) :
    AppCompatTextView(context, attrs, defStyleAttr) {
    companion object {
        private const val SYMBOL = "\u2026" // HORIZONTAL ELLIPSIS (…)
    }

    fun setMultiText(text: String) {
        handleStartDrawable()
        super.setText(if (maxLines > 1) {
            ellipsizeString(text, width)
        } else {
            text
        })
    }

    private fun getDrawableSpaceWidth(): Int {
        val drawableStart = getDrawableStart()
        val drawableStartWidth = drawableStart?.intrinsicWidth ?: 0
        val drawablePaddingWidth = if (drawableStartWidth > 0) compoundDrawablePadding else 0
        return drawableStartWidth + drawablePaddingWidth
    }

    /**
     * 利用StaticLayout获取第一行文本
     * 再计算第一行文本的宽度
     * 将标签Icon显示在第一行文本左（或右侧）
     */
    private fun handleStartDrawable() {
        text ?: return
        layout ?: return
        val drawableStart = getDrawableStart()
        drawableStart ?: return
        val firstLineText = text.substring(0, getStaticLayout(text, getAvailableWidth(width)).getLineEnd(0))
        val textWidth = paint.measureText(firstLineText)
        val lineCount = min(lineCount, maxLines)
        val vSpace = bottom - top - compoundPaddingBottom - compoundPaddingTop
        val verticalOffset = (-1 * (vSpace * (1 - 1.0f / lineCount)) / 2).toInt()
        val horizontalOffset = ((width - textWidth - drawableStart.intrinsicWidth) / 2).toInt()
        val left = max(0, horizontalOffset)
        if (Utils.isRtl()) {
            drawableStart.setBounds(-left, verticalOffset, -left + drawableStart.intrinsicWidth, drawableStart.intrinsicHeight + verticalOffset)
        } else {
            drawableStart.setBounds(left, verticalOffset, left + drawableStart.intrinsicWidth, drawableStart.intrinsicHeight + verticalOffset)
        }
    }

    private fun getDrawableStart() = if (Utils.isRtl()) compoundDrawables[2] else compoundDrawables[0]

    /**
     * 给定文字，返回静态布局
     * 即当前文字，会显示的布局，用于获取当前显示的文字的行数，有几行，每一行的文字
     */
    private fun getStaticLayout(text: CharSequence, width: Int): StaticLayout {
        return StaticLayout.Builder.obtain(text, 0, text.length, paint, width)
            .setAlignment(Layout.Alignment.ALIGN_NORMAL)
            .setTextDirection(TextDirectionHeuristics.FIRSTSTRONG_LTR)
            .setLineSpacing(lineSpacingExtra, lineSpacingMultiplier)
            .setIncludePad(includeFontPadding).setBreakStrategy(breakStrategy)
            .setHyphenationFrequency(hyphenationFrequency)
            .setMaxLines(if (maxLines == -1) Integer.MAX_VALUE else maxLines)
            .setJustificationMode(justificationMode).build()
    }

    private fun ellipsizeString(content: String, width: Int): String {
        try {
            val availableWidth: Int = getAvailableWidth(width)
            val linesStart = getLineStartAndEnd(content, availableWidth)
            if (linesStart.size <= maxLines) { // The number of rows does not exceed the limit, no processing
                return content
            }
            val middleLineStart = 0
            val point = linesStart[middleLineStart]
            val startEllipsize: Int = point.y // Append an ellipsis at the end of the middle line
            val substringStart = content.substring(0, startEllipsize) // The text before the ellipsis
            val middleLineEnd = linesStart.size - 1
            val pointEnd = linesStart[middleLineEnd]
            var substringEndStart = pointEnd.x
            val extPointStart = content.indexOfLast { it == '.' }
            if (extPointStart > startEllipsize) {
                substringEndStart = min(pointEnd.x, extPointStart)
            }
            var substringEnd = content.substring(substringEndStart)
            // Crop the text after the ellipsis until the whole can be displayed within the range of the number of lines
            while (getStaticLayout(createEllipsizedText(substringStart, substringEnd),
                    availableWidth).lineCount > maxLines) {
                val firstSpace = substringEnd.indexOf(' ')
                substringEnd = if ((firstSpace == -1) || (firstSpace >= substringEnd.length - 1)) {
                    substringEnd.substring(1)
                } else {
                    substringEnd.substring(firstSpace + 1)
                }
            }
            return createEllipsizedText(substringStart, substringEnd)
        } catch (e: Exception) {
            return content
        }
    }

    private fun getAvailableWidth(width: Int): Int {
        val availableWidth = width - paddingLeft - paddingRight - getDrawableSpaceWidth()
        return if (availableWidth < 0) 0 else availableWidth
    }

    /**
     * 创建包含省略号的文本
     * 带上换行，第一行使用第一次获取到的行文本
     * 因为若不带上换行，会出现以下情况：
     * abc acdaaaaaaaaaaaa.mp3 此文件名，系统默认会分成3行，即：
     * abc （当第一段单词较短）
     * acdaaaaaaaaaaaa
     * .mp3
     * 取第一、三两行，中间加三点，得到字符串abc....mp3 最终变成一行显示
     */
    private fun createEllipsizedText(substringStart: String, substringEnd: String) = "$substringStart\n$SYMBOL$substringEnd"

    /**
     * Calculate the start character position and end character position of each line
     *
     * @return List.size() is the total number of rows．
     * point.x is the starting character position of the current line，
     * point.y is the end character position of the current line
     */
    private fun getLineStartAndEnd(cs: CharSequence, lineWidth: Int): List<Point> {
        val layout = getStaticLayout(cs, lineWidth)
        val count = layout.lineCount
        val list: MutableList<Point> = ArrayList<Point>()
        for (i in 0 until count) {
            list.add(Point(layout.getLineStart(i), layout.getLineEnd(i)))
        }
        return list
    }
}