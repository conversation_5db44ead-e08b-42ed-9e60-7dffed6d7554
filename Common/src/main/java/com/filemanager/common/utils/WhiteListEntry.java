/***********************************************************
 * * Copyright (C), 2008-2019 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:WhiteListEntry.java
 * * Description:
 * * Version:1.0
 * * Date :2019/10/12
 * * Author:W9000843
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.common.utils;

public class WhiteListEntry {
    String mData;
    int mOrder = -1;
    public WhiteListEntry(String data, int order) {
        this.mData = data;
        this.mOrder = order;
    }
    @Override
    public String toString() {
        return "mData: " + mData + ", mOrder: " + mOrder;
    }
    public int getOrder() {
        return mOrder;
    }
    public String getData() {
        return mData;
    }
}
