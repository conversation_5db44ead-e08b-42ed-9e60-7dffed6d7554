/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MediaFileCompat
 ** Description : MediaFileCompat
 ** Version     : 1.0
 ** Date        : 2024/09/11 10:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/11     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

class MediaFileCompat {

    companion object {

        const val MEDIA_TYPE_COMPRESS: Int = 10001
        const val MEDIA_TYPE_APK: Int = 10002
        const val MEDIA_TYPE_DOC: Int = 10003

        @JvmStatic
        fun getMimeTypeForFile(filePath: String?): String {
            return CompatUtils.compactSApi({
                com.oplus.media.MediaFile.getMimeTypeForFile(filePath)
            }, {
                com.heytap.addon.media.MediaFile.getMimeTypeForFile(filePath)
            })
        }

        @JvmStatic
        fun getFileType(filePath: String?): MediaFileType? {
            return CompatUtils.compactSApi({
                val type = com.oplus.media.MediaFile.getFileType(filePath)
                if (type != null) {
                    MediaFileType(type.fileType, type.mimeType)
                } else {
                    null
                }
            }, {
                val type = com.heytap.addon.media.MediaFile.getFileType(filePath)
                if (type != null) {
                    MediaFileType(type.fileType, type.mimeType)
                } else {
                    null
                }
            })
        }

        @JvmStatic
        fun isImageFileType(fileType: Int): Boolean {
            return CompatUtils.compactSApi({
                com.oplus.media.MediaFile.isImageFileType(fileType)
            }, {
                com.heytap.addon.media.MediaFile.isImageFileType(fileType)
            })
        }

        @JvmStatic
        fun isAudioFileType(fileType: Int): Boolean {
            return CompatUtils.compactSApi({
                com.oplus.media.MediaFile.isAudioFileType(fileType)
            }, {
                com.heytap.addon.media.MediaFile.isAudioFileType(fileType)
            })
        }

        @JvmStatic
        fun isVideoFileType(fileType: Int): Boolean {
            return CompatUtils.compactSApi({
                com.oplus.media.MediaFile.isVideoFileType(fileType)
            }, {
                com.heytap.addon.media.MediaFile.isVideoFileType(fileType)
            })
        }

        @JvmStatic
        fun isDocFileType(fileType: Int): Boolean {
            return CompatUtils.compactSApi({
                com.oplus.media.MediaFile.isDocFileType(fileType)
            }, {
                com.heytap.addon.media.MediaFile.isDocFileType(fileType)
            })
        }

        @JvmStatic
        fun isApkMimeType(mimeType: String?): Boolean {
            return CompatUtils.compactSApi({
                com.oplus.media.MediaFile.isApkMimeType(mimeType)
            }, {
                com.heytap.addon.media.MediaFile.isApkMimeType(mimeType)
            })
        }
    }

    data class MediaFileType(val fileType: Int, val mimeType: String?)
}