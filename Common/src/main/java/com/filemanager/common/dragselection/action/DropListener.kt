/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/24, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.view.DragEvent

interface DropListener {
    /**
     * 处理拖拽结果
     * @param dragEvent 拖拽事件，包含过拽文件
     * @param dragTag 给每个页面设置的标识，若不为空，为内部拖拽；若为空，为外部拖拽
     * @param targetCategoryType 拖拽到某个页面或tab唯一对应的类别
     * @param activity 当前所处的activity
     * @param dropSideItem 是否拖拽到侧导或小屏的某个项，否则是拖拽到某个页面
     */
    fun handleDropAction(dragEvent: DragEvent, dragTag: String?, targetCategoryType: Int, activity: Activity, dropSideItem: Boolean): Boolean

    fun getCornerMarkStateCode(dragEvent: DragEvent, dragTag: String?, targetCategoryType: Int, activity: Activity, dropSideItem: Boolean): Int
}