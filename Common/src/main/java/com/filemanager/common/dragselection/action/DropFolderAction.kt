/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/29, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.view.DragEvent
import com.filemanager.common.R
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.Companion.parseFiles
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi

object DropFolderAction {

    private const val TAG = "DropFolderAction"

    fun handleDropFolderAction(activity: Activity, dragEvent: DragEvent?, destPath: String?): Boolean {
        val dragTag = dragEvent?.localState as? String
        Log.d(TAG, "handleDropFolderAction dragTag $dragTag dragEvent $dragEvent destPath $destPath")
        if (dragEvent == null || destPath == null) {
            return false
        }
        runCatching {
            //内部拖拽接收不需要获取uri授权
            if (dragTag == null) {
                activity.requestDragAndDropPermissions(dragEvent)
            }
        }.onFailure {
            Log.e(TAG, "handleDragDrop $it")
        }
        if (DragUtils.hasAndroidDataFile || AndroidDataHelper.isAndroidDataPath(destPath)) {
            CustomToast.showShort(R.string.drag_cloud_not_support_position)
            Log.w(TAG, "handleDropFolderAction hasAndroidDataFile")
            return true
        }
        val parseData = DropUtil.parseDragFile(dragEvent)
        val categoryType = DropUtil.getCategoryTypeByDragTag(dragTag)
        val isInternal = (dragTag != null)
        OptimizeStatisticsUtil.dragFolderResult(activity, dragEvent, destPath, isInternal.toString())
        //解决mac只能拖到全部文件目录，点击全部文件进入全部文件，选择目录提示“出现异常，无法移动”
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            macDropFolderAction(dragEvent, activity, destPath)
            return true
        }
        //内部文件，移动文件
        if (isInternal) {
            Injector.injectFactory<IFileOperateApi>()?.cutMediaFile(activity, parseData, destPath, categoryType, true)
            return true
        }
        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
                Log.d(TAG, "drag out media and private files, return!")
                return true
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
                return true
            }
            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                Injector.injectFactory<IFileOperateApi>()?.cutMediaFile(activity, data, destPath, categoryType, false)
                return true
            }
            return true
        }

        //外部拖拽文本，保存逻辑
        if (parseData.isExternalTextFiles()) {
            Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
            return true
        }

        //外部私有文件，执行保存操作
        val privacyFileCallback: () -> Boolean = {
            Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
            true
        }
        //外部公共媒体库文件，执行移动操作
        val mediaFileCallback = {
            Injector.injectFactory<IFileOperateApi>()?.cutMediaFile(activity, parseData, destPath, categoryType, false)
            true
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    private fun macDropFolderAction(
        dragEvent: DragEvent,
        activity: Activity,
        destPath: String
    ) {
        dragEvent.clipData?.apply {
            val onMacDownloadCallBack: (Boolean, ArrayList<RemoteFileBean>) -> Unit =
                { result, _ ->
                    if (result) {
                        COUISnackBarUtils.show(
                            activity, R.string.drag_in_success
                        ) {
                            val fileBrowser = Injector.injectFactory<IFileBrowser>()
                            fileBrowser?.toFileBrowserActivity(activity, destPath)
                        }
                        Log.d(TAG, "onDragMacResult true")
                    } else {
                        //处理下载失败
                        Log.d(TAG, "onDragMacResult false")
                    }
                }
            parseFiles(activity, destPath, onMacDownloadCallBack, description)
        }
    }

    fun getCornerMarkStateCode(activity: Activity, dragEvent: DragEvent?, destPath: String?, targetCategoryType: DropTag.Type?): Int {
        val dragTag = dragEvent?.localState as? String
        Log.d(TAG, "getCornerMarkStateCode dragTag $dragTag dragEvent $dragEvent destPath $destPath")
        if (dragEvent == null || destPath == null) {
            return DropDispatchAction.NORMAL_STATE
        }

        if (targetCategoryType == DropTag.Type.ITEM_VIEW_NOTRESPONSE) {
            return DropDispatchAction.PROHIBIT_STATE
        }

        runCatching {
            //内部拖拽接收不需要获取uri授权
            if (dragTag == null) {
                activity.requestDragAndDropPermissions(dragEvent)
            }
        }.onFailure {
            Log.e(TAG, "handleDragDrop $it")
        }

        val parseData = DropUtil.parseDragFile(dragEvent)
        val categoryType = DropUtil.getCategoryTypeByDragTag(dragTag)
        val isInternal = (dragTag != null)
        //解决mac只能拖到全部文件目录，点击全部文件进入全部文件，选择目录提示“出现异常，无法移动”
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            return DropDispatchAction.COPY_STATE
        }
        //内部文件，移动文件
        if (isInternal) {
            Injector.injectFactory<IFileOperateApi>()?.cutMediaFile(activity, parseData, destPath, categoryType, true)
            return DropDispatchAction.NORMAL_STATE
        }
        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                return DropDispatchAction.COPY_STATE
            }
            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.NORMAL_STATE
            }
            return DropDispatchAction.PROHIBIT_STATE
        }

        //外部拖拽文本，保存逻辑
        if (parseData.isExternalTextFiles()) {
            return DropDispatchAction.COPY_STATE
        }

        return DropDispatchAction.COPY_STATE
    }
}