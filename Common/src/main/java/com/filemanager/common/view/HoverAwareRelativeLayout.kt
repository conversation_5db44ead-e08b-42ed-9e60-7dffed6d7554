/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : HoverAwareFrameLayout
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/8/18
 * * Author      : W9086652
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   W9086652                2025/8/18       1
 ***********************************************************************/
package com.filemanager.common.view

import android.animation.ValueAnimator
import android.content.Context
import android.content.res.Configuration
import android.graphics.Canvas
import android.graphics.Paint
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.animation.LinearInterpolator
import android.widget.RelativeLayout
import androidx.core.content.ContextCompat
import androidx.core.content.withStyledAttributes
import com.filemanager.common.R
import com.filemanager.common.imageloader.glide.RoundRectUtil
import com.filemanager.common.utils.Log

/**
 * 自定义FrameLayout实现悬停状态持久化
 * 解决子视图(如CheckBox)悬停时父布局状态丢失的问题
 */
class HoverAwareRelativeLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : RelativeLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "HoverAwareRelativeLayout"
        private const val FADE_IN_DURATION = 100L
        private const val FADE_OUT_DURATION = 100L
        private const val DARK_MODE_ALPHA = 50
        private const val LIGHT_MODE_ALPHA = 40
    }

    // 悬停状态跟踪标志
    private var hoverTracking = false
    private var currentAlpha = 0
    private var currentMaxAlpha = 0 // 动态最大透明度值
    private val hoverPaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.hover_color)
        isAntiAlias = true
    }
    private lateinit var fadeInAnimator: ValueAnimator
    private lateinit var fadeOutAnimator: ValueAnimator

    private var cornerRadius: Float = 0f

    init {
        context.withStyledAttributes(attrs, R.styleable.HoverAwareLayout, defStyleAttr, 0) {
            cornerRadius = getDimension(R.styleable.HoverAwareLayout_hover_radius, cornerRadius)
        }
        updateHoverColorForTheme()
        initHover()
    }

    private fun updateHoverColorForTheme() {
        val isDarkMode =
            (resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES

        currentMaxAlpha = if (isDarkMode) {
            DARK_MODE_ALPHA
        } else {
            LIGHT_MODE_ALPHA
        }

        // 初始化动画器
        initAnimators()
    }

    private fun initAnimators() {
        fadeInAnimator = ValueAnimator.ofInt(0, currentMaxAlpha).apply {
            duration = FADE_IN_DURATION
            interpolator = LinearInterpolator()
            addUpdateListener { animation ->
                val alpha = animation.animatedValue as Int
                currentAlpha = alpha
                hoverPaint.alpha = currentAlpha
                invalidate()
            }
        }

        fadeOutAnimator = ValueAnimator.ofInt(currentMaxAlpha, 0).apply {
            duration = FADE_OUT_DURATION
            interpolator = LinearInterpolator()
            addUpdateListener { animation ->
                val alpha = animation.animatedValue as Int
                currentAlpha = alpha
                hoverPaint.alpha = currentAlpha
                invalidate()
            }
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        // 取消所有正在运行的动画
        fadeInAnimator.cancel()
        fadeOutAnimator.cancel()

        // 安全更新主题和动画器
        updateHoverColorForTheme()

        // 根据当前悬停状态重置动画状态
        if (isHovered) {
            hoverPaint.alpha = currentMaxAlpha
        } else {
            hoverPaint.alpha = 0
        }
        invalidate()
    }

    private fun initHover() {
        isHovered = false
        isFocusable = true
        isFocusableInTouchMode = true
    }

    override fun onHoverEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_HOVER_ENTER -> {
                isForceDarkAllowed = false
                isHovered = true
                fadeOutAnimator.cancel()
                fadeInAnimator.start()
                return true
            }

            MotionEvent.ACTION_HOVER_EXIT -> {
                isForceDarkAllowed = true
                isHovered = false
                fadeInAnimator.cancel()
                fadeOutAnimator.start()
                return true
            }
        }
        return super.onHoverEvent(event)
    }

    override fun dispatchDraw(canvas: Canvas) {
        super.dispatchDraw(canvas)
        if (currentAlpha > 0) {
            val path = RoundRectUtil.getPath(
                0f,
                0f,
                width.toFloat(),
                height.toFloat(),
                cornerRadius,
                tl = true,
                tr = true,
                bl = true,
                br = true
            )
            canvas.drawPath(path, hoverPaint)
        }
    }

    /**
     * 处理悬停事件分发
     * @param event 运动事件对象
     * @return Boolean 表示事件是否被消费
     */
    override fun dispatchHoverEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_HOVER_ENTER -> {
                // 进入悬停状态时激活跟踪
                hoverTracking = true
                isHovered = true
                Log.d(TAG, "${this.hashCode()} Hover enter triggered")
            }

            MotionEvent.ACTION_HOVER_MOVE -> {
                // 移动时保持悬停状态
                if (hoverTracking) {
                    isHovered = true
                }
            }

            MotionEvent.ACTION_HOVER_EXIT -> {
                if (!isPointInView(this, event.x, event.y)) {
                    // 当坐标确实离开视图范围时才释放状态
                    Log.d(TAG, "${this.hashCode()} release hover")
                    hoverTracking = false
                    isHovered = false
                } else {
                    // 坐标仍在视图内则保持状态
                    Log.d(TAG, "${this.hashCode()} Keep hovering")
                }
            }
        }
        return super.dispatchHoverEvent(event)
    }

    /**
     * 拦截悬停事件确保父布局优先处理
     * @param event 运动事件对象
     * @return Boolean 始终返回true表示拦截事件
     */
    override fun onInterceptHoverEvent(event: MotionEvent): Boolean {
        return true
    }

    /**
     * 坐标点是否在视图范围内的检测方法
     * @param view 待检测的目标视图
     * @param x 当前X轴坐标(绝对坐标)
     * @param y 当前Y轴坐标(绝对坐标)
     * @return Boolean 坐标是否在视图范围内
     */
    private fun isPointInView(view: View, x: Float, y: Float): Boolean {
        return (x < (view.width) && y < (view.height)) && (y >= 0)
    }
}
