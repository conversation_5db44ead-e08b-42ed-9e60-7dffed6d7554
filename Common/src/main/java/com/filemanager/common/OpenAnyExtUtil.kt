/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : WpsExtUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/7/21       1      create
 ***********************************************************************/
package com.filemanager.common

object OpenAnyExtUtil {

    private val wps1 = arrayListOf(
        "ppt", "pot", "pps", "dps", "dpss",
        "dpt", "pptx", "potx", "ppsx", "pptm",
        "potm", "ppsm"
    )

    private val wps2 = arrayListOf(
        "doc", "dot", "wps", "wpss", "wpt",
        "docx", "dotx", "docm", "dotm", "rtf",
        "txt", "log", "lrc", "c", "cpp",
        "h", "asm", "s", "java", "asp",
        "bat", "bas", "prg", "cmd"
    )

    private val wps3 = arrayListOf(
        "xls", "xlt", "et", "ets", "ett",
        "xlsx", "xltx", "csv", "xlsb", "xlsm",
        "xltm", "xml", "htm", "mht", "mhtm",
        "mhtml"
    )

    private val wps4 = arrayListOf("pdf")

    private val iwork = arrayListOf("key", "pages", "numbers")
    private val cad = arrayListOf("dwg", "dxf")
    private val md = arrayListOf("md")
    private val xmind = arrayListOf("xmind")
    private val psd = arrayListOf("psd")
    private val ai = arrayListOf("ai")
    private val visio = arrayListOf("vsdx", "vsdm", "vstx", "vstm", "vssx", "vssm", "vsd", "vss", "vst", "vdw")

    val openAnyExt by lazy {
        arrayListOf<String>().apply {
            addAll(wps1)
            addAll(wps2)
            addAll(wps3)
            addAll(wps4)
            addAll(iwork)
            addAll(cad)
            addAll(md)
            addAll(xmind)
            addAll(psd)
            addAll(ai)
            addAll(visio)
        }
    }
}