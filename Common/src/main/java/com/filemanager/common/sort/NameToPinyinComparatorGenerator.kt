/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.module.operation.sort
 * * Version     : 1.0
 * * Date        : 2024/3/13
 * * Author      : w9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.HanziToPinyin
import com.filemanager.common.wrapper.RecycleFileWrapper

open class NameToPinyinComparatorGenerator : NameComparatorGenerator() {

    override fun compare1(o1: BaseFileBean, o2: BaseFileBean, isDesc: Boolean): Int {
        return comparePinyin(o1, o2, isDesc) { s1, s2 ->
            return@comparePinyin compareString(s1, s2)
        }
    }

    override fun compareRecycleFileWrapper(o1: RecycleFileWrapper, o2: RecycleFileWrapper, isDesc: Boolean): Int {
        return comparePinyin(o1, o2, isDesc) { s1, s2 ->
            return@comparePinyin mCollator.compare(s1, s2)
        }
    }

    private fun comparePinyin(
        o1: BaseFileBean,
        o2: BaseFileBean,
        isDesc: Boolean,
        reCompare: (String, String) -> Int
    ): Int {
        if (o1 === o2) {
            return 0
        }
        if ((o1.mDisplayName == null) || (o2.mDisplayName == null)) {
            return 0
        }
        o1.mDisplayName?.let { s1 ->
            o2.mDisplayName?.let { s2 ->
                val o1Letter = o1.letter ?: (HanziToPinyin.getPinYinFirstLetter(s1).firstOrNull()?.toString() ?: "#")
                val o2Letter = o2.letter ?: (HanziToPinyin.getPinYinFirstLetter(s2).firstOrNull()?.toString() ?: "#")
                o1.letter = o1Letter
                o2.letter = o2Letter
                var result = 0
                // 是否是英文字母
                val o1IsLetter = o1Letter.first().isLetter()
                val o2IsLetter = o2Letter.first().isLetter()
                if (o1IsLetter && o2IsLetter) {
                    //同样的首字母
                    result = mCollator.compare(o1Letter, o2Letter)
                } else if (o1IsLetter.not() && o2IsLetter.not()) {
                    //都不是字母
                    result = 0
                } else if (o1IsLetter.not()) {
                    result = if (isDesc) {
                        1
                    } else {
                        -1
                    }
                } else if (o2IsLetter.not()) {
                    result = if (isDesc) {
                        -1
                    } else {
                        1
                    }
                }
                if (result == 0) {
                    return reCompare.invoke(s1, s2)
                }
                return result
            }
        }
        return mCollator.compare(o1.mDisplayName, o2.mDisplayName)
    }

    companion object {
        val comparator = NameToPinyinComparatorGenerator()
        val COMPARATOR = comparator.genComparator()
        val COMPARATOR_CATEGORY = comparator.genCategoryComparator()
        val SEARCH_ALL_COMPARATOR = comparator.genSearchAllComparator()
        val COMPARATOR_RECYCLEBIN = comparator.genRecycleFileComparator()
        fun getNameComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return if (isDesc) {
                comparator.genComparator()
            } else {
                comparator.genReverseComparator()
            }
        }

        fun getCategoryComparator(isDesc: Boolean): Comparator<BaseFileBean> {
            return comparator.genCategoryComparator(isDesc)
        }

        fun getRecycleBinComparator(isDesc: Boolean): Comparator<RecycleFileWrapper> {
            return comparator.genRecycleFileComparator(isDesc)
        }
    }
}