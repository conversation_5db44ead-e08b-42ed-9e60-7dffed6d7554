/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DocFormatUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/7/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/7/19       1      create
 ***********************************************************************/
package com.filemanager.common.utils

object DocFormatUtil {

    /**
     * 文件管理不支持的格式，需要用wps打开的文件格式
     */
    private val OTHER_DOC = arrayOf(".dot", ".wps", ".wpt", ".dotx", ".docm", ".dotm", ".rtf",
        ".xlt", ".et", ".ett", ".xltx", ".csv", ".xlsm", ".xltm", ".xlsb",
        ".ppsm", ".pot", ".potx", ".pps", ".ppsx", ".dps", ".dpt", ".pptm", ".potm",
        ".log", ".lrc", ".c", ".cpp", ".h", ".js", ".asm", ".s", ".java", ".asp", ".bat",
        ".bas", ".prg", ".cmd", ".xml", ".htm", ".chm", ".mht", ".mhtml", ".mhtm", ".svg", ".mm",
        ".py", ".c", ".cpp", ".h", ".js", ".asm", ".s", ".java",
        ".asp", ".htm", ".chm", ".mht", ".mhtml", ".mhtm", ".html")

    @JvmStatic
    fun needWpsOpenFile(path: String): Boolean {
        val index = path.lastIndexOf(".")
        if (index > 0) {
            val ext = path.subSequence(path.lastIndexOf("."), path.length)
            for (item in OTHER_DOC) {
                if (ext == item) {
                    return true
                }
            }
        }
        return false
    }
}