/*********************************************************************
 * * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : StickyFooterDelegation.kt
 * * Description : Calculate FootView top offset
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>        <data>       <version>       <desc>
 * *  ********       2020/9/21       1.0           create
 ***********************************************************************/
package com.filemanager.common.decoration

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.view.FootViewOperation
import java.lang.Integer.min
import kotlin.math.ceil

class StickyFooterDelegation {

    companion object {
        /**
         * Top offset to completely hide footer from the screen and therefore avoid noticeable blink
         * during changing position of the footer.
         * Update for Bug:1409658
         * Notice: This value will affect the [RecyclerView.computeVerticalScrollRange] to obtain the height of the RecyclerView content,
         * and therefore affect the [com.coloros.filemanager.filerefactor.base.FastScrollerBar.checkContentTooShort] display
         */
        private val OFF_SCREEN_OFFSET = WindowUtils.getScreenHeight(MyApplication.sAppContext) * 3
    }

    fun getItemOffsets(spacing: Int = 0, outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        //For the first time, each view doesn't contain any parameters related to its size,
        //hence we can't calculate the appropriate offset.
        //In this case, set a big top offset and notify adapter to update footer one more time.
        //Also, we shouldn't do it if footer became visible after scrolling.
        if (view.height <= 0 && state.didStructureChange()) {
            hideFooterAndUpdate(outRect, view, parent)
        } else {
            outRect.set(0, calculateTopOffsetOther(spacing, parent, view), 0, 0)
            parent.postInvalidate()
        }
    }

    private fun hideFooterAndUpdate(outRect: Rect, footerView: View, parent: RecyclerView) {
        outRect.set(0, OFF_SCREEN_OFFSET, 0, 0)
        footerView.post {
            parent.adapter?.notifyDataSetChanged()
        }
    }

    private fun calculateTopOffsetOther(spacing: Int, parent: RecyclerView, footerView: View): Int {
        val blankHeight = visibleChildHeightWithFooter(spacing, parent, footerView)
        val topOffset = parent.height - parent.paddingTop - parent.paddingBottom - blankHeight

        return if (topOffset < 0) {
            0
        } else {
            topOffset
        }
    }

    private fun visibleChildHeightWithFooter(spacing: Int, parent: RecyclerView, footerView: View): Int {
        var totalHeight = 0
        val onScreenItemCount = parent.childCount.let {
            if (it <= 1) {
                // if use scrollTo method in RecyclerView, getChildCount will get value(1) incorrectly
                parent.adapter?.itemCount ?: 0
            } else {
                min(parent.childCount, parent.adapter?.itemCount ?: 0)
            }
        }
        val layoutManager = parent.layoutManager as? GridLayoutManager
        val span = layoutManager?.spanCount ?: 1
        val rowsCount = ceil((onScreenItemCount - 1) / span.toDouble()).toInt()

        for (i in 0 until rowsCount) {
            totalHeight += (parent.getChildAt(0).height + spacing)
        }

        return totalHeight + footerView.height + spacing
    }

    fun isFootView(parent: RecyclerView, view: View): Boolean {
        return if (view.visibility == View.VISIBLE) {
            val footViewOperation = parent.adapter as? FootViewOperation
            footViewOperation?.isFootView(parent.getChildAdapterPosition(view))
                    ?: false
        } else {
            false
        }
    }
}