/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/5/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.helper.VolumeEnvironment
import org.apache.commons.io.FilenameUtils
import java.io.File
import java.util.*

private val TAG = "PathHelper"
const val PREVIEW_ROOT_PATH = ""

@Deprecated("废弃")
class FileBrowPathHelper {

    private var mRootExternalPath: String? = null
    private var mRootInternalPath: String? = null
    private var mRootOtgPath: List<String>? = null
    private var mRootPath: String? = null
    private var mRootPathInfo: PathInfo? = null
    private val mPathStack = LinkedList<PathInfo>()

    constructor (currentPath: String) {
        updateRootPath(currentPath)
        Log.d(TAG, "initRootPath mRootPathInfo=${mRootPathInfo}")
    }

    fun updateRootPath(currentPath: String) {
        mPathStack.clear()
        mRootInternalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
        mRootExternalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
        mRootOtgPath = VolumeEnvironment.getOTGPath(MyApplication.sAppContext)
        if ((mRootInternalPath != null) && (currentPath.startsWith(mRootInternalPath!!))) {
            mRootPathInfo = PathInfo(mRootInternalPath!!)
            mRootPath = mRootInternalPath
        } else if ((mRootExternalPath != null) && (currentPath.startsWith(mRootExternalPath!!))) {
            mRootPathInfo = PathInfo(mRootExternalPath!!)
            mRootPath = mRootExternalPath
        } else if (KtUtils.checkIsMultiAppPath(currentPath)) {
            mRootPathInfo = PathInfo(KtConstants.LOCAL_VOLUME_MULTI_APP_PATH)
            mRootPath = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
        } else {
            mRootOtgPath?.let {
                it.forEach {
                    if (currentPath.startsWith(it)) {
                        mRootPathInfo = PathInfo(it)
                        mRootPath = it
                    }
                }
            }
        }
        if (mRootPath == null) {
            mRootPathInfo = PathInfo(PREVIEW_ROOT_PATH)
            mRootPath = currentPath
        }
        Log.d(TAG, "updateRootPath mRootPathInfo=${mRootPathInfo}")
        mPathStack.push(mRootPathInfo)
    }

    fun getExternalPath(): String? {
        return mRootExternalPath
    }

    fun getInternalPath(): String? {
        return mRootInternalPath
    }

    fun getRootPath(): String? {
        return mRootPath
    }

    fun getCurrentDirectoryName(currentPath: String): String? {
        var currentPath = currentPath
        if (TextUtils.isEmpty(currentPath)) {
            return null
        }
        val res = MyApplication.sAppContext.getResources()

        if ((currentPath == mRootInternalPath)) {
            currentPath = res.getString(R.string.string_all_files)
        } else if ((currentPath == KtConstants.LOCAL_VOLUME_MULTI_APP_PATH)) {
            currentPath = res.getString(R.string.string_all_files)
        } else if (currentPath.equals(mRootExternalPath)) {
            currentPath = res.getString(R.string.storage_external)
        } else if (isRootOtgPath(currentPath)) {
            currentPath = res.getString(R.string.storage_otg)
        } else {
            val index = currentPath.lastIndexOf(File.separator)
            if (index != -1) {
                currentPath = currentPath.substring(index)
            }
            currentPath = currentPath.replaceFirst(File.separator.toRegex(), "")
        }
        return currentPath
    }

    fun pop(): PathInfo? {
        if (hasUp()) {
            return mPathStack.pop() ?: return mRootPathInfo
        } else {
            return null
        }
    }

    /**
     * Push a path info to stack, and the specified path info's position will be
     * set to the last path's position.
     *
     * @param path
     * @return how many path info in stack.
     */
    fun push(path: PathInfo?): Int {
        if (path != null) {
            // Updating the location of the last directory.
            val tempPath = mPathStack.peek()
            if (null != tempPath) {
                tempPath.position = path.position
                tempPath.y = path.y
            }
            if (!mPathStack.contains(path)) {
                mPathStack.push(path)
            }
        }
        return mPathStack.size
    }

    fun pushTo(path: String) {
        if ((mRootPath != null) && !isRootPath(path) && path.startsWith(mRootPath!!)) {
            mPathStack.clear()
            mPathStack.addLast(PathInfo(path))
            if (isRootExternalPath(path) || isRootInternalPath(path) || isRootMultiAppPath(path)) {
                mPathStack.addLast(PathInfo(mRootPath!!))
                return
            }
            var parent: File? = File(path).parentFile

            var flag = false
            while (!isRootPath(parent!!.absolutePath)) {
                mPathStack.addLast(PathInfo(parent.absolutePath))
                if (isRootExternalPath(parent.absolutePath) || isRootInternalPath(parent.absolutePath) || isRootMultiAppPath(parent.absolutePath)) {
                    if (isRootPath(parent.absolutePath)) {
                        break
                    } else {
                        parent = File(mRootPath!!)
                    }
                } else {
                    parent = parent.parentFile
                }
                if (null == parent) {
                    flag = true
                    break
                }
            }
            if (!flag && isRootPath(parent.absolutePath)) {
                mPathStack.addLast(PathInfo(parent.absolutePath))
            }
        } else if (isRootPath(path)) {
            mPathStack.clear()
            mPathStack.addLast(PathInfo(path))
        }
    }

    /**
     * Search the path info of the specified index, and remove all path info
     * before it.
     *
     * @param index
     * @return The path info of set, or null if the path it's not found in the
     * path stack.
     */
    fun setTopPath(index: Int): PathInfo? {
        var index = index
        val size = mPathStack.size
        if (index != -1 && index < size) {
            while (index-- > 0) {
                mPathStack.removeAt(0)
            }
            return mPathStack.first
        }
        return null
    }

    /**
     * Search the path info of the specified index, and remove all path info
     * after it.
     *
     * @param index
     * @return The path info of set, or null if the path it's not found in the
     * path stack.
     */
    fun setCurrentPathIndex(index: Int) {
        val size = mPathStack.size
        var decreaseCount = size - (index + 1)
        if (decreaseCount > 0 && decreaseCount < size) {
            while (decreaseCount-- > 0) {
                mPathStack.removeAt(0)
            }
        }
    }

    /**
     * Fetch the specified index path info.
     *
     * @param index
     * @return the specified index path info, or null if the index great than or
     * equals the stack size.
     */
    fun getPath(index: Int): PathInfo? {
        val size = mPathStack.size
        return if (index >= 0 && index < size) {
            mPathStack[index]
        } else {
            null
        }
    }

    fun getOtgPath(): List<String>? {
        return mRootOtgPath
    }


    fun getCount(): Int {
        return mPathStack.size
    }

    fun getPathLeft(): Int {
        return mPathStack.size
    }

    /**
     * Retrieves, but does not remove, the head (first path info) of this list.
     *
     * @return the top path info of the path stack, if no path in stack ,return
     * the root path.
     */
    fun getTopPathInfo(): PathInfo? {
        return mPathStack.peek() ?: return getRootPathInfo()
    }

    fun getRootPathInfo(): PathInfo? {
        return mRootPathInfo
    }

    /**
     * Whether have path info in stack, exclude the root path.
     *
     * @return true if has, otherwise return false.
     */
    fun hasUp(): Boolean {
        // Decrease the root path elements.
        return mPathStack.size - 1 > 0
    }

    fun isRootPath(path: String): Boolean {
        return mRootPath?.equals(path) ?: false
    }

    fun isRootExternalPath(path: String): Boolean {
        return path.equals(mRootExternalPath)

    }

    fun isAtExternalSD(path: String): Boolean {
        return if (null == mRootExternalPath) {
            false
        } else {
            path.startsWith(mRootExternalPath + File.separator)
        }
    }

    fun isRootInternalPath(path: String): Boolean {
        return path.equals(mRootInternalPath)
    }

    fun isRootMultiAppPath(path: String): Boolean {
        return path == KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
    }

    fun isRootOtgPath(path: String): Boolean {
        for (otgPath in mRootOtgPath!!) {
            return otgPath.equals(path, ignoreCase = true)
        }
        return false
    }

    fun isAtInternalSD(path: String): Boolean {
        return if (null == mRootInternalPath) false else path.startsWith(mRootInternalPath!! + File.separator)
    }


    class PathInfo(private var mPath: String) {
        /**
         * The last list view position.
         */
        /**
         * Return the last list view position.
         */
        var position: Int = 0
        var name: String? = null
            private set
        var y = 0

        var path: String
            get() = mPath
            set(path) {
                this.mPath = path
                name = FilenameUtils.getName(this.mPath)
            }

        init {
            name = FilenameUtils.getName(this.mPath)
        }

        constructor(path: String, position: Int, y: Int) : this(path) {
            this.position = position
            this.y = y
        }

        override fun hashCode(): Int {
            val prime = 31
            var result = 1
            result = prime * result + if (mPath == null) 0 else mPath!!.hashCode()
            result = prime * result + position
            return result
        }

        override fun equals(obj: Any?): Boolean {
            if (this === obj) {
                return true
            }
            if (obj == null) {
                return false
            }
            if (javaClass != obj.javaClass) {
                return false
            }
            val other = obj as PathInfo?
            if (mPath == null) {
                if (other?.mPath != null) {
                    return false
                }
            } else if (mPath != other?.mPath) {
                return false
            }
            return true
        }

        override fun toString(): String {
            return "PathInfo(mPath='$mPath', position=$position, name=$name, y=$y)"
        }

    }

}