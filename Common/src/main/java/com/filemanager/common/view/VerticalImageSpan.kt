/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : VerticalImageSpan
 ** Description : 文字和图片居中对齐，自定义图片相对文字的左右边距的自定义ImageSpan，用于来源显示
 ** Version     : 1.0
 ** Date        : 2024/8/14 15:51
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue      2024/8/14       1.0      create
 ***********************************************************************/
package com.filemanager.common.view

import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.Drawable
import android.text.style.ImageSpan

class VerticalImageSpan(var inputDrawable: Drawable) : ImageSpan(inputDrawable) {

    companion object {
        const val TAG = "VerticalImageSpan"
    }

    private var marginLeft = 0
    private var marginRight = 0


    constructor(inputDrawable: Drawable, marginLeft: Int, marginRight: Int) : this(inputDrawable) {
        this.marginLeft = marginLeft
        this.marginRight = marginRight
    }

    /**
     * Returns the width of the span. Extending classes can set the height of the span by updating attributes of Paint.FontMetricsInt. If the span covers the whole text, and the height is not set, draw(Canvas, CharSequence, int, int, float, int, int, int, Paint) will not be called for the span.
     *
     * Params:
     * paint – Paint instance.
     * text – Current text.
     * start – Start character index for span.
     * end – End character index for span.
     * fm – Font metrics, can be null.
     *
     * Returns: ImageSpan中图片渲染的的整体宽度大小
     */
    override fun getSize(
        paint: Paint,
        text: CharSequence?,
        start: Int,
        end: Int,
        fontMetricsInt: Paint.FontMetricsInt?
    ): Int {
        //Log.d(TAG, "getSize begin text $text start $start, end $end, ")
        val drawable =  inputDrawable
        //图片高度和宽度
        val drWidth = drawable.intrinsicWidth
        val drHeight = drawable.intrinsicHeight
        //Log.i(TAG, "getSize step1 drWidth $drWidth, drHeight $drHeight")
        if (null != fontMetricsInt) {
            val fmPaint: Paint.FontMetricsInt = paint.getFontMetricsInt()
            //文字高度
            val fontHeight: Int = fmPaint.descent - fmPaint.ascent
            //Log.i(TAG, "getSize step2 descent ${fmPaint.descent}, ascent ${fmPaint.ascent}, fontHeight $fontHeight")
            val centerY: Int = fmPaint.ascent + fontHeight / 2
            //这里将字体内部的数据设置到
            fontMetricsInt.ascent = centerY - drHeight / 2
            fontMetricsInt.top = fontMetricsInt.ascent
            fontMetricsInt.bottom = centerY + drHeight / 2
            fontMetricsInt.descent = fontMetricsInt.bottom
        }
        val result = marginLeft + drWidth + marginRight
        //Log.d(TAG, "getSize end result $result")
        return result
    }

    /**
     * 绘制Drawable到Canvas中.
     *
     * Params:
     * canvas – Canvas into which the span should be rendered.
     * text – Current text.
     * start – Start character index for span.
     * end – End character index for span.
     * x – Edge of the replacement closest to the leading margin.
     * top – Top of the line.
     * y – Baseline.
     * bottom – Bottom of the line.
     * paint – Paint instance.
     */
    override fun draw(
        canvas: Canvas,
        text: CharSequence?,
        start: Int,
        end: Int,
        x: Float,
        top: Int,
        y: Int,
        bottom: Int,
        paint: Paint
    ) {
        val drawable = inputDrawable
        val drWidth = drawable.intrinsicWidth
        val drHeight = drawable.intrinsicHeight
        //这里必须要setBounds，不然下面drawable.draw(canvas)方法中调用之后，无法绘制出相应的drawable
        drawable.setBounds(0, 0, drWidth, drHeight)
        //Log.d(TAG, "drawStart text $text start $start, end $end, x $x, top $top, y $y, bottom $bottom, drWidth $drWidth, drHeight $drHeight")
        canvas.save()
        val fmPaint: Paint.FontMetricsInt = paint.getFontMetricsInt()
        val newX = marginLeft + x
        val fontHeight: Int = fmPaint.descent - fmPaint.ascent
        val centerY: Int = y + fmPaint.descent - fontHeight / 2
        val transY = centerY - drHeight / 2
        canvas.translate(newX, transY.toFloat())
        drawable.draw(canvas)
        canvas.restore()
        //Log.d(TAG, "drawEnd text $text newX $newX, transY $transY")
    }
}