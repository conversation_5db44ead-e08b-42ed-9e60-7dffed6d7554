/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteFileData
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.bean.remotedevice

class RemoteFileData {
    var remoteId: String? = null

    var fileName: String = ""
    var path: String = ""
    var lastFilePath: String = ""
    //名称，有些文件在mac端访达上面显示的名称和实际的文件名不一样，这个是访达的名称
    var alternativeName: String? = null

    var size: Long = 0

    var createDate: Long = 0
    var modifyDate: Long = 0
    var lastOpenDate: Long = 0

    var isDir: Boolean = false
    var fileNum: Int = -1

    var remoteImageWidth: Int = -1
    var remoteImageHeight: Int = -1

    var remoteVideoDuration: Long = -1

    var remoteAudioChannel: Int = -1
}