/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : GsonUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/21
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2022/12/21       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import com.google.gson.Gson
import java.lang.reflect.ParameterizedType
import java.lang.reflect.Type

object GsonUtil {

    private const val TAG = "GsonUtil"

    fun <T> toJson(t: T): String {
        val result = Gson().toJson(t)
        Log.d(TAG, "toJson:$result")
        return result
    }

    fun <T> fromJson(json: String, cls: Class<T>): T {
        return Gson().fromJson(json, cls)
    }

    fun <T> toList(json: String?, clazz: Class<*>): List<T> {
        val type: Type = ParameterizedTypeImpl(clazz)
        val result: List<T> = Gson().fromJson(json, type)
        Log.w(TAG, "toList:$result")
        return result
    }

    @JvmStatic
    fun <T> toJsonSafeCall(t: T): String? {
        runCatching {
            val result = Gson().toJson(t)
            Log.d(TAG, "toJson:$result")
            return result
        }.onFailure { err ->
            Log.e(TAG, "toJsonSafeCall: ERROR!", err)
            return null
        }
        return null
    }

    @JvmStatic
    fun <T> toListSafeCall(json: String?, clazz: Class<*>): List<T> {
        runCatching {
            val type: Type = ParameterizedTypeImpl(clazz)
            val result: List<T> = Gson().fromJson(json, type)
            Log.w(TAG, "toListSafeCall:$result")
            return result
        }.onFailure { err ->
            Log.e(TAG, "toListSafeCall: ERROR!", err)
            return listOf()
        }
        return listOf()
    }
}

class ParameterizedTypeImpl(val clazz: Class<*>) : ParameterizedType {

    override fun getActualTypeArguments(): Array<Type> {
        return arrayOf(clazz)
    }

    override fun getRawType(): Type {
        return MutableList::class.java
    }

    override fun getOwnerType(): Type? {
        return null
    }
}