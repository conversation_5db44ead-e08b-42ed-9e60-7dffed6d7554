/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: DragDropSelectionViewModel.kt
 ** Description: drag drop action
 ** Version: 1.0
 ** Date: 2023/6/13
 ** Author: hank.zhou(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.dragselection

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.utils.Log
import com.oplus.dropdrag.SelectionTracker

class DragDropSelectionViewModel : SelectionViewModel<BaseFileBean, BaseUiModel<BaseFileBean>>() {
    companion object {
        const val TAG = "DragDropSelectionViewModel"
    }
    override fun getRealFileSize(): Int {
        return 0
    }

    override fun loadData() {
        Log.d(TAG, "loadData")
    }

    override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE {
        return SelectionTracker.LAYOUT_TYPE.LIST
    }
}