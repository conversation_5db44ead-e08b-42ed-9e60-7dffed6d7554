/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MediaHelper
 ** Description : Media Helper
 ** Version     : 1.0
 ** Date        : 2015/01/14
 ** Author      : pengbo
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  pengbo        2015/01/14       1.0      create
 ***********************************************************************/
package com.filemanager.common.helper;

import android.content.ContentResolver;
import android.content.ContentUris;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.mtp.MtpConstants;
import android.net.Uri;
import android.os.Bundle;
import android.os.storage.StorageManager;
import android.os.storage.StorageVolume;
import android.provider.MediaStore;
import android.provider.MediaStore.Files;
import android.text.TextUtils;

import com.filemanager.common.MyApplication;
import com.filemanager.common.compat.MediaFileCompat;
import com.filemanager.common.compat.MediaScannerCompat;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.SdkUtils;
import com.filemanager.common.utils.Utils;

import java.io.File;
import java.util.Locale;

import static com.filemanager.common.fileutils.FileUtilsKt.contains;

public class MediaHelper {
    public static final Uri VIDEO_MEDIA_URI = MediaStore.Video.Media.EXTERNAL_CONTENT_URI;
    public static final Uri AUDIO_MEDIA_URI = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI;
    public static final Uri IMAGES_MEDIA_URI = MediaStore.Images.Media.EXTERNAL_CONTENT_URI;
    public static final Uri FILE_URI = MediaStore.Files.getContentUri("external");
    public static final int MEDIA_TYPE_IMAGE = 1;
    public static final int MEDIA_TYPE_AUDIO = 2;
    public static final int MEDIA_TYPE_VEDIO = 3;
    public static final int MEDIA_TYPE_DOC_ON_R = 6;
    public static final int MEDIA_TYPE_DOC = SdkUtils.isAtLeastR() ? MEDIA_TYPE_DOC_ON_R : MediaFileCompat.MEDIA_TYPE_DOC;
    public static final int MEDIA_TYPE_APK = MediaFileCompat.MEDIA_TYPE_APK;
    public static final int MEDIA_FORMAT_DIR = MtpConstants.FORMAT_ASSOCIATION;
    public static final String COLUMN_MEDIA_FORMAT = "format";
    /**
     * format for folder and directory
     */
    public static final int FORMAT_ASSOCIATION = 0X3001;
    public static final int COLUMNS_0 = 0;
    public static final int COLUMNS_1 = 1;
    public static final int COLUMNS_2 = 2;
    public static final int COLUMNS_3 = 3;
    public static final int COLUMNS_4 = 4;
    public static final int COLUMNS_5 = 5;
    public static final int COLUMNS_6 = 6;
    public static final int COLUMNS_7 = 7;
    public static final int COLUMNS_8 = 8;

    public static final int COLUMN_INDEX_IMAGES_LIST_DATA = 0;
    public static final int COLUMN_INDEX_IMAGES_LIST_DATE_TAKEN = 1;
    public static final int COLUMN_INDEX_IMAGES_LIST_DATE_ORIENTATION = 2;
    public static final int COLUMN_INDEX_IMAGES_LIST_ID = 3;
    public static final int COLUMN_INDEX_IMAGES_LIST_CSHOT_ID = 4;
    public static final String[] IMAGE_PROJECTION = new String[]{
            MediaStore.Images.Media.DATA,
            MediaStore.Images.Media.DATE_TAKEN,
            MediaStore.Images.Media.ORIENTATION,
            MediaStore.Images.Media._ID,
            "cshot_id"
    };
    private static final String TAG = "MediaHelper";
    private static final String PARAM_DELETE_DATA = "deletedata";
    private static final String SLOW_MOTION_CHECK = "oppo_0slow_motion_hsr";
    private static final String NEW_SLOW_MOTION_CHECK = "oppo_0slow_motion_hfr";

    public static Uri getUriByPath(Context context, String path) {
        Uri uri = null;
        ContentResolver cr = context.getContentResolver();
        String[] projectStrings = new String[]{"_id"};
        String where = "_data = ?";
        Uri dbUri = FILE_URI;
        Cursor cursor = null;
        try {
            cursor = cr.query(dbUri, projectStrings, where, new String[]{path}, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                long rowId = cursor.getLong(0);
                uri = ContentUris.withAppendedId(dbUri, rowId);
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != cursor) {
                cursor.close();
                cursor = null;
            }
        }
        return uri;
    }

    public static Uri getUri(Context context, FileWrapper file) {
        Uri uri = null;
        ContentResolver cr = context.getContentResolver();
        String[] projectStrings = new String[]{"_id"};
        String where = "_data = ?";
        Uri dbUri = getUri(file.getType(), file.getAbsolutePath());

        if (dbUri != null) {
            Cursor cursor = null;
            try {
                cursor = cr.query(dbUri, projectStrings, where, new String[]{file.getAbsolutePath()}, null);
                if ((cursor != null) && (cursor.getCount() > 0)) {
                    cursor.moveToFirst();
                    long rowId = cursor.getLong(0);
                    uri = ContentUris.withAppendedId(dbUri, rowId);
                }
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            } finally {
                if (null != cursor) {
                    cursor.close();
                    cursor = null;
                }
            }
        }
        return uri;
    }

    public static String getAudioFilePath(Context context, Uri uri) {
        if ((null == uri) || (null == context)) {
            return null;
        }

        String path = null;
        String[] column = new String[]{MediaStore.Audio.Media.DATA};
        Cursor c = null;
        try {
            c = context.getContentResolver().query(uri, column, null, null, null);
            if ((null != c) && c.moveToFirst()) {
                path = c.getString(0);
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != c) {
                c.close();
                c = null;
            }
        }

        return path;
    }

    private static Uri getUri(int type, String path) {
        return getUri(type, path, false);
    }

    private static Uri getUri(int type, String path, boolean ignoreExist) {
        if (TextUtils.isEmpty(path)) {
            return null;
        }
        Uri uri = null;
        File file = new File(path);
        if (ignoreExist || file.exists()) {
            String volumeName = getVolumeName(file);
            if (MimeTypeHelper.AUDIO_TYPE == type) {
                uri = MediaStore.Audio.Media.getContentUri(volumeName);
            } else if (MimeTypeHelper.VIDEO_TYPE == type) {
                uri = MediaStore.Video.Media.getContentUri(volumeName);
            } else if (MimeTypeHelper.IMAGE_TYPE == type) {
                uri = MediaStore.Images.Media.getContentUri(volumeName);
            } else {
                uri = MediaStore.Files.getContentUri(volumeName);
            }
        }
        return uri;
    }

    private static String getVolumeName(File path) {
        if (contains(VolumeEnvironment.ANDROID_STORAGE_HEADER, path.getAbsolutePath())) {
            final StorageManager sm = (StorageManager) MyApplication.getSAppContext().getSystemService(Context.STORAGE_SERVICE);
            if (sm != null) {
                final StorageVolume sv = sm.getStorageVolume(path);
                if (sv != null) {
                    if (sv.isPrimary()) {
                        return MediaStore.VOLUME_EXTERNAL_PRIMARY;
                    } else {
                        String fsUuid = sv.getUuid();
                        String normalizeUuid = (fsUuid != null) ? fsUuid.toLowerCase(Locale.US) : null;
                        return checkArgumentVolumeName(normalizeUuid);
                    }
                }
                Log.w(TAG, "getVolumeName Unknown volume at " + path);
            }
        }
        return MediaStore.VOLUME_INTERNAL;
    }

    /**
     * Copy MediaStore.checkArgumentVolumeName(String volumeName)
     *
     * @param volumeName
     * @return
     */
    private static String checkArgumentVolumeName(String volumeName) {
        if (TextUtils.isEmpty(volumeName)) {
            Log.d(TAG, "checkArgumentVolumeName  volume is " + volumeName);
            return MediaStore.VOLUME_INTERNAL;
        }

        if (MediaStore.VOLUME_INTERNAL.equals(volumeName)) {
            return volumeName;
        } else if (MediaStore.VOLUME_EXTERNAL.equals(volumeName)) {
            return volumeName;
        } else if (MediaStore.VOLUME_EXTERNAL_PRIMARY.equals(volumeName)) {
            return volumeName;
        }

        // When not one of the well-known values above, it must be a hex UUID
        for (int i = 0; i < volumeName.length(); i++) {
            final char c = volumeName.charAt(i);
            if ((('a' <= c) && (c <= 'f')) || (('0' <= c) && (c <= '9')) || (c == '-')) {
                continue;
            } else {
                Log.d(TAG, "checkArgumentVolumeName Invalid volume: " + volumeName);
                return MediaStore.VOLUME_INTERNAL;
            }
        }
        return volumeName;
    }

    public static boolean deleteMediaDBFile(Context context, FileWrapper file) {
        if (null == file) {
            return false;
        }

        if (!Utils.isOperateDatabase(context, file.getAbsolutePath())) {
            return true;
        }

        Uri uri = Files.getContentUri("external");
        if (null == uri) {
            return false;
        }

        Uri.Builder builder = uri.buildUpon();
        builder.appendQueryParameter(PARAM_DELETE_DATA, "false");
        uri = builder.build();

        String where = "";
        String[] argsStrings = null;
        if (file.getType() == MimeTypeHelper.DIRECTORY_TYPE) {
            where = "_data like ?";
            argsStrings = new String[]{file.getAbsolutePath() + File.separator + "%"};
        } else {
            where = "_data = ?";
            argsStrings = new String[]{file.getAbsolutePath()};
        }
        Cursor cursor = null;
        try {
            Log.v(TAG, "uri=" + uri + "|where =" + where + argsStrings[0]);
            if (file.getType() == MimeTypeHelper.DIRECTORY_TYPE) {
                context.getContentResolver().delete(uri, "_data = ?", new String[]{file.getAbsolutePath()});
            }
            int count = context.getContentResolver().delete(uri, where, argsStrings);
            Log.v(TAG, "count =" + count);
            if (count <= 0) {
                cursor = context.getContentResolver().query(uri, new String[]{"_id"}, where, argsStrings,
                        null);
                if ((null == cursor) || (cursor.getCount() == 0)) {
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
            Log.v(TAG, e.toString());
            return false;
        } finally {
            if (null != cursor) {
                cursor.close();
            }
        }
        return true;
    }

    private static String getFileTitleInMediaDB(Context context, FileWrapper file) {
        String result = null;
        String where = "_data = ?";
        Cursor cursor = null;
        try {
            cursor = context.getContentResolver()
                    .query(FILE_URI, new String[]{MediaStore.MediaColumns.TITLE}, where, new String[]{file.getAbsolutePath()}, null);
            if ((cursor != null) && (cursor.getCount() > 0)) {
                cursor.moveToFirst();
                result = cursor.getString(0);
                Log.d(TAG, "title =" + result);
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != cursor) {
                cursor.close();
            }
        }
        return result;
    }

    private static boolean updateFileNameInMediaDB(Context context, FileWrapper oldFile, FileWrapper newFile) {
        if ((null == oldFile) || (null == newFile)) {
            return false;
        }
        Log.d(TAG, "updateFileNameInMediaDB");
        Uri uri = getUri(oldFile.getType(), oldFile.getAbsolutePath(), true);
        Log.v(TAG, "updateFileNameInMediaDB: oldPath=" + oldFile.getAbsolutePath()
                + ", newPath=" + newFile.getAbsolutePath() + ", oldUri=" + uri);
        if (null == uri) {
            return false;
        }
        String where = "_data = ?";
        String[] argsStrings = new String[]{oldFile.getAbsolutePath()};
        ContentValues cv = new ContentValues();
        String newName = newFile.getName();
        cv.put(MediaStore.MediaColumns.DATA, newFile.getAbsolutePath());
        cv.put(MediaStore.MediaColumns.DISPLAY_NAME, newName);
        // Update new file's mime type
        String oldMimeType = MediaFileCompat.getMimeTypeForFile(oldFile.getAbsolutePath());
        String newMimeType = MediaFileCompat.getMimeTypeForFile(newFile.getAbsolutePath());
        if (!TextUtils.equals(oldMimeType, newMimeType)) {
            Log.d(TAG, "updateFileNameInMediaDB: oldMimeType=" + oldMimeType + ", newMimeType=" + newMimeType);
            cv.put(MediaStore.MediaColumns.MIME_TYPE, newMimeType);
        }
        String withoutExtName = getNameWithoutExt(newName);
        if (withoutExtName != null) {
            String title = getFileTitleInMediaDB(context, oldFile);
            title = (title == null) ? null : title.toLowerCase();
            if ((title == null) || (!title.contains(SLOW_MOTION_CHECK) && !title.contains(NEW_SLOW_MOTION_CHECK))) {
                cv.put(MediaStore.MediaColumns.TITLE, withoutExtName);
            }
        }
        String parentName = null;
        String bucketId = null;
        FileWrapper parentFile = oldFile.getParentFile();
        if ((null != parentFile) && parentFile.exists()) {
            parentName = parentFile.getName();
            bucketId = getBucketId(parentFile.getAbsolutePath());
        }

        if ((null != parentName) && (null != bucketId)
                && (uri.equals(VIDEO_MEDIA_URI) || uri.equals(IMAGES_MEDIA_URI))) {
            cv.put("bucket_id", newFile.getAbsolutePath());
            cv.put("bucket_display_name", newFile.getName());
        }
        try {
            final int count = context.getContentResolver().update(uri, cv, where, argsStrings);
            // maybe uri is wrong for audio,video or image,do again with file uri
            if (count <= 0) {
                Log.d(TAG, "updateFileNameInMediaDB, Uri is wrong for audio/video/image, do again with file uri!");
                return doAgainWithFileUri(context, cv, where, argsStrings);
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
            return doAgainWithFileUri(context, cv, where, argsStrings);
        }
        return true;
    }

    private static String getNameWithoutExt(String name) {
        int position = -1;
        if (name != null) {
            position = name.lastIndexOf(".");
        }
        String withoutExtName = name;
        if (position > 0) {
            withoutExtName = name.substring(0, position);
        }
        return withoutExtName;
    }

    private static boolean doAgainWithFileUri(Context context, ContentValues cv, String where, String[] argsStrings) {
        int count = 0;
        try {
            count = context.getContentResolver().update(FILE_URI, cv, where, argsStrings);
        } catch (Exception e) {
            Log.w(TAG, "doAgainWithFileUri, but still error!");
            Log.e(TAG, e.getMessage());
            return false;
        }
        return count >= 0;
    }

    private static String getBucketId(String path) {
        if (null != path) {
            return String.valueOf(path.toLowerCase(Locale.getDefault()).hashCode());
        } else {
            return null;
        }
    }


    public static boolean updateFolderNameInMediaDB(Context context, FileWrapper oldFile, FileWrapper newFile) {
        if ((null == oldFile) || (null == newFile)) {
            return false;
        }
        if (!Utils.isOperateDatabase(context, newFile.getAbsolutePath())) {
            return true;
        }
        if (oldFile.getType() == MimeTypeHelper.DIRECTORY_TYPE) {
            Log.v(TAG, "rom platform");
            Bundle bundle = new Bundle();
            bundle.putString("oldPath", oldFile.getAbsolutePath());
            bundle.putString("newPath", newFile.getAbsolutePath());
            Uri uri = getUri(oldFile.getType(), oldFile.getAbsolutePath());

            if (uri != null) {
                context.getContentResolver().call(uri, "oppoFileManagerRenameDir", null, bundle);
                return true;
            }
        } else {
            return updateFileNameInMediaDB(context, oldFile, newFile);
        }
        return false;
    }

    public static void insertFileMediaDB(Context context, FileWrapper file, String scanScene) {
        if ((null == file) || (!file.exists()) || (!Utils.isOperateDatabase(context, file.getAbsolutePath()))) {
            return;
        }
        Log.v(TAG, "insertFileMediaDB");
        MediaScannerCompat.sendMediaScanner(file.getAbsolutePath(), scanScene);
    }

    /**
     * Add For Android R.
     * On Android R, the file interface method is time-consuming,
     * so it will not judge and return true directly on R.
     *
     * @param path file path
     * @return return true on R, else return file.exists()
     */
    public static boolean checkFileExist(String path) {
        if (!SdkUtils.isAtLeastR()) {
            File file = new File(path);
            return file.exists();
        } else {
            return true;
        }
    }

}
