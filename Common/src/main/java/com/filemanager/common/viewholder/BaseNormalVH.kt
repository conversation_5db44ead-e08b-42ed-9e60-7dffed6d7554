/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.interfaces
 * * Version     : 1.0
 * * Date        : 2020/9/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.content.Context
import android.view.View
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.Log
import java.util.*

private const val TAG = "BaseNormalVH"

abstract class BaseNormalVH(convertView: View, canLongPressOrClick: Boolean = true) : BaseSelectionViewHolder(convertView, canLongPressOrClick) {
    open fun loadData(
        context: Context,
        key: Int?,
        file: BaseFileBean,
        choiceMode: Boolean,
        selectionArray: MutableList<Int>,
        sizeCache: HashMap<String, String>,
        threadManager: ThreadManager,
        adapter: BaseSelectionRecycleAdapter<*, *>
    ) {
        this.itemCount = adapter.getRealFileItemCount()
        updateKey(key)
        Log.v(TAG, "onBindViewHolder: holder.mSelectionKey = ${getItemDetails()?.selectionKey}")
        updateViewHolder(context, key, file, choiceMode, selectionArray, sizeCache, threadManager, adapter)
    }

    abstract fun updateViewHolder(context: Context, key: Int?, data: BaseFileBean, choiceMode: Boolean, selectionArray: MutableList<Int>
                                  , sizeCache: HashMap<String, String>, threadManager: ThreadManager, adapter: BaseSelectionRecycleAdapter<*, *>)
}