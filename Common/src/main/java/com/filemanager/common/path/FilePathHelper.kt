/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : IFilePath
 ** Description : 文件的路径接口
 ** Version     : 1.0
 ** Date        : 2023/12/14 15:50
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2023/12/14       1.0      create
 ***********************************************************************/
package com.filemanager.common.path

import android.text.TextUtils
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.back.PredictiveBackPathChecker
import com.filemanager.common.back.PredictiveBackUtils
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import java.io.File
import java.util.LinkedList

abstract class FilePathHelper(currentPath: String, private val isFromShortcutFolder: Boolean = false, shortcutRootPath: String? = null) {
    companion object {
        private const val TAG = "FilePathHelper"
        const val PREVIEW_ROOT_PATH = ""
    }

    private var mRootPath: String? = null
    private var mRootPathInfo: PathInfo? = null
    private val mPathStack = LinkedList<PathInfo>()
    private var showRoot: Boolean = true
    private var checker: PredictiveBackPathChecker? = null

    init {
        updateRootPath(currentPath, shortcutRootPath)
        Log.d(TAG, "initRootPath mRootPathInfo=$mRootPathInfo")
    }

    /**
     * @param shortcutRootPath 若是快捷文件夹，则需要传 shortcutRootPath 作为上方显示文件夹路径的首位
     */
    fun updateRootPath(currentPath: String, shortcutRootPath: String? = null) {
        Log.d(TAG, "updateRootPath currentPath=$currentPath  shortcutRootPath=$shortcutRootPath")
        mPathStack.clear()
        val rootInternalPath = getInternalPath()
        val rootExternalPath = getExternalPath()
        val rootOtgPath = getOtgPath()
        val rootDfmPath = getDfmRootPath()
        val remoteMacPath = getRemoteMacRootPath()
        var isSetRoot = false
        if (isFromShortcutFolder) {
            val rootPath = shortcutRootPath ?: rootInternalPath ?: currentPath
            Log.d(TAG, "rootPath=$rootPath")
            mRootPathInfo = PathInfo(rootPath)
            mRootPath = rootPath
            isSetRoot = true
        } else if ((rootInternalPath != null) && (currentPath.startsWith(rootInternalPath))) {
            mRootPathInfo = PathInfo(rootInternalPath)
            mRootPath = rootInternalPath
            isSetRoot = true
        } else if ((rootExternalPath != null) && (currentPath.startsWith(rootExternalPath))) {
            mRootPathInfo = PathInfo(rootExternalPath)
            mRootPath = rootExternalPath
            isSetRoot = true
        } else if (KtUtils.checkIsMultiAppPath(currentPath)) {
            mRootPathInfo = PathInfo(KtConstants.LOCAL_VOLUME_MULTI_APP_PATH)
            mRootPath = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
            isSetRoot = true
        } else if (rootDfmPath != null && KtUtils.checkIsDfmPath(currentPath)) {
            mRootPathInfo = PathInfo(rootDfmPath)
            mRootPath = rootDfmPath
            isSetRoot = true
        } else if (remoteMacPath != null && currentPath.startsWith(remoteMacPath)) {
            Log.d(TAG, "updateRootPath remoteMacPath $remoteMacPath")
            mRootPathInfo = PathInfo(remoteMacPath)
            mRootPath = remoteMacPath
            isSetRoot = true
        } else {
            rootOtgPath?.let {
                it.forEach {
                    if (currentPath.startsWith(it)) {
                        mRootPathInfo = PathInfo(it)
                        mRootPath = it
                        isSetRoot = true
                    }
                }
            }
        }
        if (!isSetRoot) {
            mRootPathInfo = PathInfo(PREVIEW_ROOT_PATH)
            mRootPath = currentPath
        }
        mPathStack.push(mRootPathInfo)
        showRoot = isRootPath(currentPath)
        Log.d(TAG, "updateRootPath mRootPathInfo=$mRootPathInfo showRoot:$showRoot mRootPath=$mRootPath, mPathStack $mPathStack")
    }

    /**
     * 判断是否用于本地文件
     */
    open fun isLocalFile(): Boolean = true

    /**
     * 获取外部存储sd卡的路径
     */
    abstract fun getExternalPath(): String?

    /**
     * 获取内部存储sd卡的路径
     */
    abstract fun getInternalPath(): String?

    /**
     * 获取otg的路径
     */
    abstract fun getOtgPath(): List<String>?

    /**
     * 获取分布式文管DFM的路径
     */
    abstract fun getDfmRootPath(): String?

    /**
     * 获取远端mac文件的根目录路径
     */
    open fun getRemoteMacRootPath(): String? {
        return null
    }

    /**
     * 最左侧的rootbutton是否需要disable
     */
    open fun shouldDisableRootButton(): Boolean {
        return false
    }

    /**
     * 获取分布式文管DFM的设备名称
     */
    abstract fun getDfmDeviceName(): String?

    /**
     * 获取远端mac的设备名称
     */
    open fun getRemoteMacDeviceName(): String? {
        return null
    }

    /**
     * 判断是否是外部存储sd卡的根路径
     */
    abstract fun isRootExternalPath(path: String): Boolean

    /**
     * 判断是否是内部存储sd卡的根路径
     */
    abstract fun isRootInternalPath(path: String): Boolean

    /**
     * 判断是否是otg的根路径
     */
    abstract fun isRootOtgPath(path: String): Boolean

    /**
     * 判断是否是DFM的根路径
     */
    abstract fun isRootDfmPath(path: String): Boolean

    fun getRootPath(): String? {
        return mRootPath
    }

    fun getCurrentDirectoryName(currentPath: String): String? {
        var currentPathName = currentPath
        if (TextUtils.isEmpty(currentPathName)) {
            return null
        }
        val res = MyApplication.sAppContext.getResources()

        if ((currentPath == getInternalPath())) {
            currentPathName = res.getString(R.string.string_all_files)
        } else if ((currentPath == KtConstants.LOCAL_VOLUME_MULTI_APP_PATH)) {
            currentPathName = res.getString(R.string.string_all_files)
        } else if (currentPath.equals(getExternalPath())) {
            currentPathName = res.getString(R.string.storage_external)
        } else if (isRootOtgPath(currentPath)) {
            currentPathName = res.getString(R.string.storage_otg)
        } else if (isRootDfmPath(currentPath) && getDfmDeviceName() != null) {
            currentPathName = getDfmDeviceName()!!
        } else {
            val index = currentPathName.lastIndexOf(File.separator)
            if (index != -1) {
                currentPathName = currentPathName.substring(index)
            }
            currentPathName = currentPathName.replaceFirst(File.separator.toRegex(), "")
        }
        return currentPathName
    }

    fun pop(): PathInfo? {
        //Log.d(TAG, "pop START mPathStack $mPathStack")
        val top = if (hasUp()) {
            mPathStack.pop() ?: mRootPathInfo
        } else {
            null
        }
        if (top != null) {
            syncPredictiveBackState()
        }
        return top
    }

    private fun syncPredictiveBackState() {
        if (checker == null) {
            checker = PredictiveBackPathChecker(this, showRoot)
        }
        checker?.let {
            PredictiveBackUtils.checkEnableBackAnim(it)
        }
    }

    /**
     * Push a path info to stack, and the specified path info's position will be
     * set to the last path's position.
     *
     * @param path
     * @return how many path info in stack.
     */
    fun push(path: PathInfo?): Int {
        //Log.d(TAG, "push inputInfo $path, before stack $mPathStack")
        if (path != null) {
            // Updating the location of the last directory.
            val tempPath = mPathStack.peek()
            if (null != tempPath) {
                tempPath.position = path.position
                tempPath.y = path.y
            }
            if (!mPathStack.contains(path)) {
                mPathStack.push(path)
                syncPredictiveBackState()
            }
        }
        //Log.d(TAG, "push inputInfo $path, after stack $mPathStack")
        return mPathStack.size
    }

    /**
     * 获取上一级的路径
     */
    abstract fun getParentPath(path: String): String

    /**
     * 页面初始化调用，页面创建之后只会调用一次
     */
    fun pushTo(path: String, extra: String = "") {
        //Log.d(TAG, "pushTo START path $path, extra $extra, mRootPath $mRootPath, mPathStack $mPathStack ")
        if ((mRootPath != null) && !isRootPath(path) && path.startsWith(mRootPath!!)) {
            mPathStack.clear()
            mPathStack.addLast(PathInfo(path, extra))
            Log.d(TAG, "pushTo path $path, mRootPath $mRootPath START")
            if (isRootExternalPath(path) || isRootInternalPath(path) || isRootMultiAppPath(path)) {
                mPathStack.addLast(PathInfo(mRootPath!!))
                return
            }
            var parent = getParentPath(path)

            var flag = false
            while (!isRootPath(parent) && parent.isNotEmpty()) {
                Log.d(TAG, "pushTo while parent $parent")
                mPathStack.addLast(PathInfo(parent))
                if (isRootExternalPath(parent) || isRootInternalPath(parent) || isRootMultiAppPath(parent)) {
                    if (isRootPath(parent)) {
                        break
                    } else {
                        parent = getParentPath(mRootPath!!)
                    }
                } else {
                    parent = getParentPath(parent)
                }
                if (parent.isEmpty()) {
                    flag = true
                    break
                }
            }
            if (!flag && isRootPath(parent)) {
                mPathStack.addLast(PathInfo(parent))
            }
            Log.d(TAG, "pushTo A END path $path, mRootPath $mRootPath, mPathStack $mPathStack ")
        } else if (isRootPath(path)) {
            mPathStack.clear()
            mPathStack.addLast(PathInfo(path, extra))
            Log.d(TAG, "pushTo B END path $path, mRootPath $mRootPath, mPathStack $mPathStack ")
        }
    }

    /**
     * Search the path info of the specified index, and remove all path info
     * before it.
     *
     * @param index
     * @return The path info of set, or null if the path it's not found in the
     * path stack.
     */
    fun setTopPath(index: Int): PathInfo? {
        //Log.d(TAG, "setTopPath START index $index, mPathStack $mPathStack")
        var index = index
        val size = mPathStack.size
        if (index != -1 && index < size) {
            while (index-- > 0) {
                mPathStack.removeAt(0)
            }
            syncPredictiveBackState()
            return mPathStack.first
        }
        return null
    }

    /**
     * Search the path info of the specified index, and remove all path info
     * after it.
     *
     * @param index
     * @return The path info of set, or null if the path it's not found in the
     * path stack.
     */
    fun setCurrentPathIndex(index: Int) {
        val size = mPathStack.size
        var decreaseCount = size - (index + 1)
        if (decreaseCount > 0 && decreaseCount < size) {
            while (decreaseCount-- > 0) {
                mPathStack.removeAt(0)
            }
        }
    }

    /**
     * Fetch the specified index path info.
     *
     * @param index
     * @return the specified index path info, or null if the index great than or
     * equals the stack size.
     */
    fun getPath(index: Int): PathInfo? {
        val size = mPathStack.size
        return if (index >= 0 && index < size) {
            mPathStack[index]
        } else {
            null
        }
    }


    fun getCount(): Int {
        return mPathStack.size
    }

    fun getPathLeft(): Int {
        return mPathStack.size
    }

    /**
     * Retrieves, but does not remove, the head (first path info) of this list.
     *
     * @return the top path info of the path stack, if no path in stack ,return
     * the root path.
     */
    fun getTopPathInfo(): PathInfo? {
        return mPathStack.peek() ?: return getRootPathInfo()
    }

    fun getRootPathInfo(): PathInfo? {
        return mRootPathInfo
    }

    /**
     * Whether have path info in stack, exclude the root path.
     *
     * @return true if has, otherwise return false.
     */
    fun hasUp(): Boolean {
        // Decrease the root path elements.
        return mPathStack.size - 1 > 0
    }

    fun isRootPath(path: String): Boolean {
        return mRootPath?.equals(path) ?: false
    }

    fun getCurrentShowPath(): String? {
        return getTopPathInfo()?.path
    }

    fun isRootMultiAppPath(path: String): Boolean {
        return path == KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
    }


    open class PathInfo(private var mPath: String) {
        /**
         * The last list view position.
         */
        /**
         * Return the last list view position.
         */
        var position: Int = 0
        var y = 0

        /**
         * 额外的信息
         */
        var extra: String = ""

        var path: String
            get() = mPath
            set(path) {
                this.mPath = path
            }

        constructor(path: String, position: Int, y: Int) : this(path) {
            this.position = position
            this.y = y
        }

        constructor(path: String, extra: String, position: Int, y: Int) : this(path) {
            this.extra = extra
            this.position = position
            this.y = y
        }

        constructor(path: String, extra: String) : this(path) {
            this.extra = extra
        }

        override fun hashCode(): Int {
            val prime = 31
            var result = 1
            result = prime * result + mPath.hashCode()
            result = prime * result + position
            return result
        }

        override fun equals(obj: Any?): Boolean {
            if (this === obj) {
                return true
            }
            if (obj == null) {
                return false
            }
            if (javaClass != obj.javaClass) {
                return false
            }
            val other = obj as? PathInfo
            if (mPath == null) {
                if (other?.mPath != null) {
                    return false
                }
            } else if (mPath != other?.mPath) {
                return false
            }
            return true
        }

        override fun toString(): String {
            return "PathInfo(mPath='$mPath', extra=$extra, position=$position, y=$y)"
        }
    }
}