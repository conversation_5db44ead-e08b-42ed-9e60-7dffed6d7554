/*********************************************************************
 * * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : GridSpanAnimationHelper.kt
 * * Description : Provide grid and list switching animation function
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>        <data>       <version>       <desc>
 * *  ********       2020/9/21       1.0           create
 ***********************************************************************/
package com.filemanager.common.helper

import android.view.View
import android.view.animation.Animation
import android.view.animation.AnimationSet
import android.view.animation.GridLayoutAnimationController
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.view.FootViewOperation
import com.filemanager.common.animation.CustomOrderLayoutAnimationController
import com.filemanager.common.utils.KtAnimationUtil

private const val DELAY_LINE: Long = 30L
private const val DELAY_TIME_100: Long = 100L

class GridSpanAnimationHelper(recyclerView: RecyclerView) {
    private var mIsInFadeOutState = false
    private val mRecyclerView: RecyclerView = recyclerView

    private val mFootViewOperation: FootViewOperation? by lazy {
        mRecyclerView.adapter as? FootViewOperation
    }

    private val mSlideInAnimationController: CustomOrderLayoutAnimationController by lazy {
        val layoutInAnimationSet = AnimationSet(false).apply {
            addAnimation(KtAnimationUtil.getSlideInAnimation())
            addAnimation(KtAnimationUtil.getFadeInAlphaAnimation())
            fillAfter = true
        }

        CustomOrderLayoutAnimationController(layoutInAnimationSet, DELAY_LINE).apply {
            order = GridLayoutAnimationController.ORDER_NORMAL
            directionPriority = CustomOrderLayoutAnimationController.PRIORITY_LINE
            direction = GridLayoutAnimationController.DIRECTION_TOP_TO_BOTTOM
        }
    }

    private val mFadeOutAnimationController: GridLayoutAnimationController by lazy {
        val listOutAnimationSet = AnimationSet(false).apply {
            addAnimation(KtAnimationUtil.getFadeOutAlphaAnimation())
            fillAfter = true
        }

        GridLayoutAnimationController(listOutAnimationSet).apply {
            columnDelay = 0.0f
            rowDelay = 0.0f
            order = GridLayoutAnimationController.ORDER_NORMAL
            directionPriority = GridLayoutAnimationController.PRIORITY_NONE
            direction = GridLayoutAnimationController.DIRECTION_VERTICAL_MASK
        }
    }

    private val listSortSlideInAnimationController by lazy {
        val listOutAnimationSet = AnimationSet(false).apply {
            addAnimation(KtAnimationUtil.getSlideInAnimation())
            addAnimation(KtAnimationUtil.getFadeInAlphaAnimation())
            startOffset = DELAY_TIME_100
            fillAfter = true
        }
        CustomOrderLayoutAnimationController(listOutAnimationSet, DELAY_LINE).apply {
            order = GridLayoutAnimationController.ORDER_NORMAL
            directionPriority = CustomOrderLayoutAnimationController.PRIORITY_LINE
            direction = GridLayoutAnimationController.DIRECTION_TOP_TO_BOTTOM
        }
    }

    fun startLayoutAnimation(onSpanChangeCallback: OnSpanChangeCallback?
                             , onAnimatorEndListener: OnAnimatorEndListener? = null) {
        onSpanChangeCallback?.let {
            mIsInFadeOutState = true
            mRecyclerView.layoutAnimation = mFadeOutAnimationController
            mRecyclerView.layoutAnimationListener = getLayoutAnimationListener(it, onAnimatorEndListener)
            mRecyclerView.startLayoutAnimation()
        }
    }

    fun startListSortAnimation(onRefreshDataCallback: OnRefreshDataCallback?, onAnimatorEndListener: OnAnimatorEndListener?) {
        mIsInFadeOutState = true
        mRecyclerView.layoutAnimation = mFadeOutAnimationController
        mRecyclerView.layoutAnimationListener = object : Animation.AnimationListener {
            override fun onAnimationStart(animation: Animation?) {
                runFootViewAnimation()
            }

            override fun onAnimationEnd(animation: Animation?) {
                if (mIsInFadeOutState) {
                    onRefreshDataCallback?.onRefreshDataCallback()
                    mRecyclerView.layoutAnimation = listSortSlideInAnimationController
                } else {
                    onAnimatorEndListener?.onAnimatorEnd()
                }
                mIsInFadeOutState = false
            }

            override fun onAnimationRepeat(animation: Animation?) {}
        }
        mRecyclerView.startLayoutAnimation()
    }

    private fun getLayoutAnimationListener(onSpanChangeCallback: OnSpanChangeCallback
                                           , onAnimatorEndListener: OnAnimatorEndListener?)
            : Animation.AnimationListener = object : Animation.AnimationListener {
        override fun onAnimationStart(animation: Animation) {
            runFootViewAnimation()
        }

        override fun onAnimationEnd(animation: Animation) {
            onLayoutOutAnimationFinished(onSpanChangeCallback, onAnimatorEndListener)
        }

        override fun onAnimationRepeat(animation: Animation) {}
    }

    private fun onLayoutOutAnimationFinished(onSpanChangeCallback: OnSpanChangeCallback
                                             , onAnimatorEndListener: OnAnimatorEndListener?) {
        if (mIsInFadeOutState) {
            mRecyclerView.layoutAnimation = mSlideInAnimationController
            onSpanChangeCallback.onSpanChangeCallback()
        } else {
            onAnimatorEndListener?.onAnimatorEnd()
        }
        mIsInFadeOutState = false
    }

    private fun runFootViewAnimation() {
        mFootViewOperation?.run {
            if (hasFootView()) {
                val footView: View? = mRecyclerView.getChildAt(getFootViewPosition())
                if (mIsInFadeOutState) {
                    //FootView does not need to run SlideInAnimation
                    footView?.visibility = View.GONE
                } else {
                    if (needShowFootView()) {
                        footView?.startAnimation(KtAnimationUtil.getFadeInAlphaAnimation())
                    }
                }
            }
        }
    }
}

interface OnSpanChangeCallback {
    fun onSpanChangeCallback()
}

interface OnAnimatorEndListener {
    fun onAnimatorEnd()
}

interface OnRefreshDataCallback {
    fun onRefreshDataCallback()
}