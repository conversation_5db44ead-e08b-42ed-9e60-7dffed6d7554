/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:
 * * Description:
 * * Version:
 * * Date :
 * * Author:
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils;

import static com.filemanager.common.fileutils.FileUtilsKt.MIN_RECYCLER_SIZE;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import android.content.res.Configuration;
import android.icu.text.DateFormat;
import android.net.Uri;
import android.os.BatteryManager;
import android.os.Build;
import android.os.IBinder;
import android.os.RemoteException;
import android.os.StatFs;
import android.os.SystemClock;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.TextUtils;
import android.text.format.DateUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.VisibleForTesting;

import com.coui.appcompat.contextutil.COUIContextUtil;
import com.coui.appcompat.unitconversionutil.COUIUnitConversionUtils;
import com.filemanager.common.MyApplication;
import com.filemanager.common.R;
import com.filemanager.common.compat.CompatUtils;
import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.compat.OplusUsbEnvironmentCompat;
import com.filemanager.common.constants.Constants;
import com.filemanager.common.constants.KtConstants;
import com.filemanager.common.helper.FileWrapper;
import com.filemanager.common.helper.PathHelper;
import com.filemanager.common.helper.VolumeEnvironment;
import com.oplus.compat.app.ActivityManagerNative;
import com.oplus.compat.os.UserHandleNative;
import com.oplus.compat.utils.util.UnSupportedApiVersionException;
import com.oplus.filemanager.dfm.DFMManager;
import com.oplus.wrapper.app.IActivityManager;
import com.oplus.wrapper.os.ServiceManager;
import com.oplus.wrapper.os.UserHandle;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.security.AccessController;
import java.security.PrivilegedAction;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.Objects;
import java.util.function.Supplier;

public class Utils {
    private static final String TAG = "Utils";
    /**
     * The number of bytes in a kilobyte.
     */
    public static final float ONE_KB = 1024;

    /**
     * The number of bytes in a megabyte.
     */
    public static final float ONE_MB = ONE_KB * ONE_KB;

    /**
     * The number of bytes in a gigabyte.
     */
    public static final float ONE_GB = ONE_KB * ONE_MB;
    public static final int RTL_POSITION_HEAD = 1;
    public static final int RTL_POSITION_TAIL = 2;
    public static final int RTL_POSITION_DOUBLE = 3;
    public static final int ACTIVITY_QUICK_CLICK = 100;
    public static final int FRAGMENT_QUICK_CLICK = 101;
    public static final String OPEN_FLAG = "oppo_filemanager_openflag";
    public static final String MEDIA_FROM_FILEMANAGER = "from_file_manager";
    public static final String MEDIA_SCAN_SCENE = "scanScene";
    public static final String MEDIA_SCAN_COMPRESS = "_compress";
    public static final String MEDIA_SCAN_DECOMPRESS = "_decompress";
    public static final String MEDIA_SCAN_RENAME = "_rename";
    public static final String MEDIA_SCAN_COPY = "_copy";
    public static final String MEDIA_SCAN_MOVE = "_move";
    public static final String MEDIA_SCAN_ENCRYPTION = "_encryption";
    public static final String MEDIA_SCAN_AKEYTOMOVE = "_akeytomove";
    public static final String MEDIA_SCAN_DOWNLOAD = "_download";

    public static final String MEDIA_SCAN_SHARE = "_share";

    public static final String MEDIA_SCAN_SAVE = "_save";
    public static final String MEDIA_SCAN_CREATE_FOLDER = "_create_folder";
    public static final String MEDIA_SCAN_UPDATE_MAIN = "_update_main";
    public static final String MEDIA_SCAN_UPDATE_TENCENT = "_update_tencent";
    public static final String MEDIA_SCAN_RECYCLE_RESTORE = "_recycle_restore";
    public static final String MEDIA_SCAN_RECYCLE_DELETE = "_recycle_delete";
    public static final String ONE_SPACE = " ";
    public static final String TWO_SPACE = "  ";
    public static final String DIVIDER_WITH_SPACE = " ｜ ";
    public static final String RTL_CHARACTER_PREVIOUS = "\u200F";
    public static final String RTL_CHARACTER_AFTER_D = "\u202D";
    public static final String RTL_CHARACTER_AFTER_C = "\u202C";
    private static final String CONTAINER_MANAGER_CLASS = "com.thundersoft.security.ContainerManager";
    private static final String CONTAINER_MANAGER_GET_METHOD = "get";
    private static final String CONTAINER_MANAGER_ISCURRENTCONTAINERUSER_METHOD = "isCurrentContainerUser";
    private static final String NAVIGATION_BAR_ID_NAME = "navigationBarBackground";
    private static final String RTL_SYMBOL = "\u200E";
    /**
     * add for Cshot
     */
    private static final String CAMERA_CSHOT_RELATIVE_PATH = "/DCIM/Camera/";
    private static final String MYALBUM_CSHOT_RELATIVE_PATH = "/DCIM/MyAlbums/";
    private static final String OPPO = "oppo";
    private static final String REALME = "realme";

    private static final String ONEPLUS = "oneplus";

    private static final int STORAGE_INIT_IN_THOUSAND = 1000 * 1000 * 1000;
    private static final int STORAGE_INIT_IN_NORMAL = 1024 * 1024 * 1024;
    private static final int STORAGE_NORMAL_SIZE = 1024;
    private static final int STORAGE_THOUSAND_SIZE = 1000;
    private static final int STORAGE_CHECKED_SIZE = 999;
    private static final int MATH_LOG_TWO = 2;
    private static long sActivityClickTime = -300L;
    private static long sFragmentClickTime = -300L;
    private static long sItemClickTime = -300L;
    private static long sChangeModelClickTime = -300L;
    private static boolean sOperating = false;
    private static boolean sIsNeedFilter = true;
    private static ArrayList<String> sSourcePath = new ArrayList<String>();
    private static ArrayList<Uri> sSourceUri = new ArrayList<Uri>();

    public static int getBatteryLeft() {
        Intent batteryIntent = ExtFunctionKt.registerExportedReceiver(MyApplication.getSAppContext(), null,
                new IntentFilter(Intent.ACTION_BATTERY_CHANGED), true);
        int rawLevel = IntentUtils.getInt(batteryIntent, BatteryManager.EXTRA_LEVEL, -1);
        int scale = IntentUtils.getInt(batteryIntent, BatteryManager.EXTRA_SCALE, -1);
        int batteryLeft = (((rawLevel > 0) && (scale > 0)) ? (rawLevel * Constants.MAX_PROGRESS / scale) : 0);
        Log.v(TAG, "getBatteryLeft batteryLeft: " + batteryLeft);
        return batteryLeft;
    }

    public static boolean getFilterState() {
        return sIsNeedFilter;
    }

    public static void setFilterState(boolean filter) {
        sIsNeedFilter = filter;
    }

    public static String byteCountToDisplaySize(long size) {
        String displaySize = "";
        COUIUnitConversionUtils colorUnitConversionUtils = new COUIUnitConversionUtils(MyApplication.getSAppContext());
        try {
            displaySize = colorUnitConversionUtils.getUnitValue(size);
        } catch (Exception e) {
            Log.e("byteCountToDisplaySize e:" + e);
        }
        return Utils.formatMessage(displaySize, RTL_POSITION_DOUBLE);
    }

    /**
     * Just total size and free size need to unit in 1000 or 1024
     *
     * @param size
     * @return
     */
    public static String byteCountToDisplaySizeForUnit(long size) {
        if (FeatureCompat.getSIsStorageUnitNormal()) {
            return byteCountToDisplaySize(size);
        } else {
            return byteCountToDisplayUnitInThousand(MyApplication.getSAppContext(), size);
        }
    }

    @VisibleForTesting
    public static String byteCountToDisplayUnitInThousand(Context context, long size) {
        String displaySize = "";
        if (context != null) {
            COUIUnitConversionUtils colorUnitConversionUtils = new COUIUnitConversionUtils(context);
            try {
                // format the size by 1000;
                if (size >= (long) STORAGE_CHECKED_SIZE * STORAGE_INIT_IN_THOUSAND) {
                    displaySize = colorUnitConversionUtils.getUnitValue(size / STORAGE_INIT_IN_THOUSAND * STORAGE_INIT_IN_NORMAL);
                } else {
                    displaySize = colorUnitConversionUtils.getUnitThousandValue(size);
                }
            } catch (Exception e) {
                Log.e("byteCountToDisplaySize e:" + e);
            }
        }
        return Utils.formatMessage(displaySize, RTL_POSITION_DOUBLE);
    }

    @VisibleForTesting
    public static int formatInThousand(double size) {
        int formatSize = 0;
        int n = (int) (Math.log(size) / Math.log(MATH_LOG_TWO));
        formatSize = (int) Math.pow(MATH_LOG_TWO, n + 1);
        return formatSize;
    }

    public static String formatStorageDetail(Context context, String path) {
        // Only internal path need to format like 8G 16G 64G 128G...
        boolean isInternalPath = OplusUsbEnvironmentCompat.getInternalPath(context).equals(path);
        long totalSize = isInternalPath ? Utils.getStorageTotalSizeAfterFormat(path) : Utils.getStorageTotalSize(path);
        String availableSizeStr = Utils.byteCountToDisplaySizeForUnit(Utils.getStorageAvailableSize(path));
        String totalSizeStr = Utils.byteCountToDisplaySizeForUnit(totalSize);
        return context.getResources().getString(R.string.storage_space_size, availableSizeStr, totalSizeStr);
    }

    public static String formatSize(final FileWrapper file) {
        if ((file == null) || (!file.exists())) {
            return "";
        } else {
            return (file.isDirectory()) ? "" : Utils.formatMessage(Utils.byteCountToDisplaySize(file.length()), RTL_POSITION_DOUBLE);
        }
    }

    public static long getStorageAvailableSize(String path) {
        long availableSize = 0;
        try {
            if (KtUtils.checkIsDfmPath(path)) {
                Long dfmAvailableSize = DFMManager.getDFSAvailableSize();
                availableSize = (dfmAvailableSize != null) ? dfmAvailableSize : 0;
            } else {
                StatFs statFs = new StatFs(path);
                availableSize = statFs.getFreeBytes();
            }
            Log.v(TAG, "getStorageAvailableSize availableSize = " + availableSize);
        } catch (Exception e) {
            Log.e(TAG, "getStorageAvailableSize exception catch");
            Log.e(TAG, e.getMessage());
            availableSize = 0;
        }
        return availableSize;
    }

    public static Boolean storageAvailableSizeCanOperate(String path) {
        return getStorageAvailableSize(path) <  MIN_RECYCLER_SIZE * 2;
    }

    public static long getStorageTotalSize(String path) {
        long totalSize = 0;
        try {
            if (KtUtils.checkIsDfmPath(path)) {
                Long dfmAvailableSize = DFMManager.getDFSTotalSize();
                totalSize = (dfmAvailableSize != null) ? dfmAvailableSize : 0;
            } else {
                StatFs statFs = new StatFs(path);
                totalSize = statFs.getTotalBytes();
            }
            Log.d(TAG, "getStorageTotalSize = " + totalSize);
        } catch (Exception e) {
            Log.e(TAG, "getStorageTotalSize exception catch");
        }
        return totalSize;
    }

    public static long getStorageTotalSizeAfterFormat(String path) {
        boolean hasUnitInNormalFeature = FeatureCompat.getSIsStorageUnitNormal();
        Log.d(TAG, "getStorageTotalSizeAfterFormat normal = " + hasUnitInNormalFeature);
        if (hasUnitInNormalFeature) {
            return getStorageTotalSize(path);
        } else {
            return getStorageTotalSizeUnitInThousand(path);
        }
    }

    private static long getStorageTotalSizeUnitInThousand(String path) {
        long formatDisplaySize = 0;
        long totalSize = 0;
        try {
            if (KtUtils.checkIsDfmPath(path)) {
                Long dfmAvailableSize = DFMManager.INSTANCE.getDFSTotalSize();
                totalSize = (dfmAvailableSize != null) ? dfmAvailableSize : 0;
            } else {
                StatFs statFs = new StatFs(path);
                totalSize = statFs.getTotalBytes();
            }
        } catch (Exception e) {
            Log.e(TAG, "getStorageTotalSize exception catch");
            Log.e(TAG, e.getMessage());
            totalSize = 0;
        }
        long displaySizeInGb = totalSize / STORAGE_INIT_IN_THOUSAND;
        //if total size smaller than 1GB,return this.
        if (displaySizeInGb < 1) {
            return totalSize;
        } else {
            formatDisplaySize = formatInThousand(displaySizeInGb);
            return (formatDisplaySize * STORAGE_INIT_IN_THOUSAND);
        }
    }

    public static String getVirtualPathString(Context context, String path) {
        if ((null == context) || TextUtils.isEmpty(path)) {
            Log.e(TAG, "getVirtualPathString context or path is empty");
            return "";
        }
        String internalPath = VolumeEnvironment.getInternalSdPath(context);
        String externalPath = VolumeEnvironment.getExternalSdPath(context);
        Log.v(TAG, "internalPath = " + internalPath + ",externalPath = (" + externalPath + ")");
        String result = null;
        boolean flagI = path.startsWith(internalPath);
        boolean flagE = ((!TextUtils.isEmpty(externalPath)) && path.startsWith(externalPath));
        boolean flagD = KtUtils.checkIsDfmPath(path);
        String dfmDevice = DFMManager.INSTANCE.getDFSDeviceName();
        String dfmMountPath = DFMManager.INSTANCE.getDFSMountPath();
        Log.v(TAG, "flagI = " + flagI + ",flagE = " + flagE + ",flagD = " + flagD);
        if (flagI && flagE) {
            if (internalPath.startsWith(externalPath)) {
                result = path.replace(internalPath, context.getResources().getString(R.string.string_all_files));
            } else {
                result = path.replace(externalPath, context.getResources().getString(R.string.storage_external));
            }
        } else if (flagI) {
            result = path.replace(internalPath, context.getResources().getString(R.string.string_all_files));
        } else if (flagE) {
            result = path.replace(externalPath, context.getResources().getString(R.string.storage_external));
        } else if (flagD) {
            if (dfmMountPath != null && dfmDevice != null && path.contains(dfmMountPath)) {
                result = path.replace(dfmMountPath, dfmDevice);
            }
            if (dfmMountPath == null || dfmDevice == null || !path.contains(dfmMountPath)) {
                result = path.replace(KtConstants.DFM_MOUNT_PATH_SUFFIX, "");
            }
        } else {
            if (FeatureCompat.getSIsSupportMultiApp()) {
                String internalPath999 = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH;
                if (!TextUtils.isEmpty(internalPath999) && path.startsWith(internalPath999)) {
                    return path;
                }
            }
            result = path.substring(new PathHelper(context).getRootPath().length() + 1);
        }
        return result;
    }

    public static long getCategoryItemLastRecord(Context context, String key) {
        return PreferencesUtils.getLong(Constants.FILEMANAGER_RECORD, key, 0);
    }

    public static boolean isStorageMounted(Context context) {
        return VolumeEnvironment.isInternalSdMounted(context);
    }

    public static boolean checkAppStateEnable(Context context, String pkgName) {
        try {
            final PackageManager pm = context.getPackageManager();
            int state = pm.getApplicationEnabledSetting(pkgName);
            Log.d(TAG, "checkAppStateEnable state = " + state);
            return (state != PackageManager.COMPONENT_ENABLED_STATE_DISABLED);
        } catch (Exception e) {
            Log.e(TAG, "checkAppStateEnable error e = " + e.getMessage());
        }
        return false;
    }

    public static void setOperatingFlag(boolean flag) {
        sOperating = flag;
    }

    public static boolean isOperating() {
        return sOperating;
    }

    public static boolean isOperateDatabase(Context context, String path) {
        Log.v(TAG, "isOperateDatabase path = " + path);
        if ((null == path) || (context == null)) {
            return false;
        }
        int storageType = KtUtils.INSTANCE.getStorageByPath(context, path);
        switch (storageType) {
            case KtUtils.STORAGE_INTERNAL:
            case KtUtils.STORAGE_EXTERNAL:
            case KtUtils.STORAGE_OTG:
            case KtUtils.STORAGE_DMF:
                return true;
            case KtUtils.STORAGE_INTERNAL_MULTI_APP:
                return KtUtils.INSTANCE.checkIsMultiAppPath(path);
            default:
                return false;
        }
    }

    public static void setSourcePath(ArrayList<String> paths) {
        if (sSourcePath == null) {
            sSourcePath = new ArrayList<>();
        } else {
            sSourcePath.clear();
        }
        if ((paths != null) && (paths.size() > 0)) {
            sSourcePath.addAll(paths);
        }
    }

    public static ArrayList<String> getSourcePath() {
        return sSourcePath;
    }

    public static void releaseSourcePath() {
        if (sSourcePath != null) {
            sSourcePath.clear();
            sSourcePath = null;
        }
    }

    public static ArrayList<Uri> getSourceUri() {
        return sSourceUri;
    }

    public static void releaseSourceUri() {
        if (sSourceUri != null) {
            sSourceUri.clear();
            sSourceUri = null;
        }
    }

    public static void setSourceUri(ArrayList<Uri> uris) {
        if (null == sSourceUri) {
            sSourceUri = new ArrayList<>();
        } else {
            sSourceUri.clear();
        }
        if ((uris != null) && (uris.size() > 0)) {
            sSourceUri.addAll(uris);
        }
    }

    /**
     * @param detailStr
     * @param dateAndTime will change the order if the string has blank. PLEASE PAY ATTENTION
     */
    public static CharSequence formatDetail(Context context, String detailStr, String dateAndTime) {
        if ((dateAndTime == null) || (detailStr == null)) {
            return "";
        }
        String detailString = formatMessage(detailStr, RTL_POSITION_DOUBLE);
        String detail = "";
        if (isRtl()) {
            detail = RTL_CHARACTER_PREVIOUS + detailString + DIVIDER_WITH_SPACE + RTL_CHARACTER_PREVIOUS + dateAndTime;
        } else {
            detail = detailString + DIVIDER_WITH_SPACE + dateAndTime;
        }
        SpannableString spanString = new SpannableString(detail);
        int colorInt = 0;
        //这里context需要是activity的context，如果用application的context获取出来的色值会为0
        if (context != null) {
            colorInt = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary);
        } else {
            colorInt = MyApplication.getSAppContext().getColor(R.color.black_30_percent);
        }
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(colorInt);
        int index = detailString.length() + ONE_SPACE.length();
        if (isRtl()) {
            index += RTL_CHARACTER_PREVIOUS.length();
        }
        spanString.setSpan(colorSpan, index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spanString;
    }

    public static CharSequence formatDetail(Context context, String detailStr, String dateAndTime, String space) {
        if ((dateAndTime == null) || (detailStr == null)) {
            return "";
        }
        String detailString = formatMessage(detailStr, RTL_POSITION_DOUBLE);
        String detail = "";
        if (isRtl()) {
            detail = RTL_CHARACTER_PREVIOUS + detailString + space + RTL_CHARACTER_PREVIOUS + dateAndTime;
        } else {
            detail = detailString + space + dateAndTime;
        }
        SpannableString spanString = new SpannableString(detail);
        int colorInt = 0;
        //这里context需要是activity的context，如果用application的context获取出来的色值会为0
        if (context != null) {
            colorInt = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary);
        } else {
            colorInt = MyApplication.getSAppContext().getColor(R.color.black_30_percent);
        }
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(colorInt);
        int index = detailString.length();
        if (isRtl()) {
            index += RTL_CHARACTER_PREVIOUS.length();
        }
        spanString.setSpan(colorSpan, index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spanString;
    }

    public static String formatDetailDrag(String detailStr, String dateAndTime) {
        if ((dateAndTime == null) || (detailStr == null)) {
            return "";
        }
        String detailString = formatMessage(detailStr, RTL_POSITION_DOUBLE);
        String detail = "";
        if (isRtl()) {
            detail = RTL_CHARACTER_PREVIOUS + detailString + DIVIDER_WITH_SPACE + RTL_CHARACTER_PREVIOUS + dateAndTime;
        } else {
            detail = detailString + DIVIDER_WITH_SPACE + dateAndTime;

        }
        return detail;
    }

    public static CharSequence formatRecentGroup(String timeDisplay,String countDisplay) {
        if ((timeDisplay == null) || (countDisplay == null)) {
            return "";
        }
        String detailString = formatMessage(countDisplay, RTL_POSITION_DOUBLE);
        String detail = "";
        if (isRtl()) {
            detail = RTL_CHARACTER_PREVIOUS + timeDisplay + DIVIDER_WITH_SPACE + RTL_CHARACTER_PREVIOUS + detailString;
        } else {
            detail = timeDisplay + DIVIDER_WITH_SPACE + detailString;

        }
        SpannableString spanString = new SpannableString(detail);
        ForegroundColorSpan colorSpan =
                new ForegroundColorSpan(MyApplication.getAppContext().getColor(com.filemanager.common.R.color.black_30_percent));
        var index = timeDisplay.length() + ONE_SPACE.length();
        if (Utils.isRtl()) {
            index += RTL_CHARACTER_PREVIOUS.length();
        }
        spanString.setSpan(colorSpan, index, index + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spanString;
    }

    public static String formatMessage(String msg, int position) {
        String message = msg;
        if (message == null) {
            message = "";
        }
        if (isRtl()) {
            switch (position) {
                case RTL_POSITION_HEAD:
                    message = RTL_SYMBOL + message;
                    break;
                case RTL_POSITION_TAIL:
                    message = message + RTL_SYMBOL;
                    break;
                case RTL_POSITION_DOUBLE:
                    message = RTL_SYMBOL + message + RTL_SYMBOL;
                    break;
                default:
                    break;
            }
        }
        return message;
    }

    public static String formatPathWithRTL(String path) {
        if (path == null) {
            return null;
        }
        if (isRtl()) {
            return "\u200F" + path.replaceAll("/", "/\u200F");
        } else {
            return path;
        }
    }

    public static String formatFileNameWithRTL(String name) {
        if (name == null) {
            return null;
        }
        if (isRtl()) {
            return RTL_SYMBOL + name.replace("_", RTL_SYMBOL + "_") + RTL_SYMBOL;
        } else {
            return name;
        }
    }

    public static String getDateAndTimeFormat(Context context, long time) {
        final Date date = new Date(time);
        String formatDate = getDashDate(date);
        String formatTime = getTime(context, date);
        final String dateAndTime = formatDate + " " + formatTime;
        return dateAndTime;
    }

    public static String getDateAndTimeFormatLocal(Context context, long time) {
        final Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(time);
        String formatDate = getDateFormat(context, time);
        String formatTime = getTime(context, calendar.getTime());
        return formatDate + " " + formatTime;
    }

    public static String getDateFormat(Context context, long time) {
        return DateUtils.formatDateTime(context, time, DateUtils.FORMAT_SHOW_DATE);
    }

    public static String getShortDateFormat(Context context, long time) {
        return DateUtils.formatDateTime(context, time, DateUtils.FORMAT_NUMERIC_DATE);
    }

    public static String getShortDateFormat(long time) {
        DateFormat shortDateFormat = DateFormat.getDateInstance(DateFormat.SHORT, Locale.getDefault());
        return shortDateFormat.format(new Date(time));
    }

    public static String getDateMDFormat(Context context, long time) {
        final Date date = new Date(time);
        String formatDate = getMDDate(context, date);
        final String dateAndTime = formatDate;
        return dateAndTime;
    }

    /**
     * is need target language
     *
     * @param targetLanguage
     * @return if localeLanguage equals targetLanguage return true,else return false.
     */
    public static boolean isNeededTargetLanguage(String targetLanguage) {
        boolean isNeeded = false;
        Configuration config = CompatUtils.compactApi(CompatUtils.OS_14, CompatUtils.OS_14_SUB_V, () -> {
            try {
                IBinder activityService = ServiceManager.getService(Context.ACTIVITY_SERVICE);
                IActivityManager activityMgr = IActivityManager.Stub.asInterface(activityService);
                return activityMgr.getConfiguration();
            } catch (RemoteException e) {
                Log.e(TAG, "isNeededTargetLanguage error " + e.getMessage());
            }
            return null;
        }, (Supplier<Configuration>) () -> {
            try {
                return ActivityManagerNative.getConfiguration();
            } catch (UnSupportedApiVersionException e) {
                Log.e(TAG, "isNeededTargetLanguage " + e.getMessage());
            }
            return null;
        });
        if (config == null) {
            Log.e(TAG, "isNeededTargetLanguage config = null");
            return false;
        }
        String localeLanguage = config.locale.getLanguage().toLowerCase(Locale.getDefault());
        Log.d(TAG, "locLanguage = " + localeLanguage);
        isNeeded = (targetLanguage.equals(localeLanguage));
        return isNeeded;
    }

    public static boolean isNeededSdk24() {
        return SdkUtils.getSDKVersion() >= Build.VERSION_CODES.N;
    }

    public static boolean isNeededSdk27() {
        return SdkUtils.getSDKVersion() >= Build.VERSION_CODES.O_MR1;
    }

    public static boolean isNeededSdk28() {
        return SdkUtils.getSDKVersion() > Build.VERSION_CODES.O_MR1;
    }

    public static boolean isContainerUser(Context context) {
        try {
            Class<?> clazz = Class.forName(CONTAINER_MANAGER_CLASS);
            Method getManagerMethod = clazz.getMethod(CONTAINER_MANAGER_GET_METHOD, Context.class);
            Object containerManager = getManagerMethod.invoke(null, context);
            Method isContainerUserMethod = clazz.getMethod(CONTAINER_MANAGER_ISCURRENTCONTAINERUSER_METHOD);
            Object isContainerUser = isContainerUserMethod.invoke(containerManager);
            if (isContainerUser instanceof Boolean) {
                return (Boolean) isContainerUser;
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        }
        return false;
    }

    public static boolean isRtl() {
        return TextUtils.getLayoutDirectionFromLocale(Locale.getDefault()) == View.LAYOUT_DIRECTION_RTL;
    }

    /**
     * check System is in Night Mode
     *
     * @param ctx
     * @return
     */
    public static boolean isNightMode(Context ctx) {
        Context context = ctx;
        if (context == null) {
            context = MyApplication.getSAppContext();
        }
        Configuration configuration = context.getResources().getConfiguration();
        int currentMode = configuration.uiMode & Configuration.UI_MODE_NIGHT_MASK;
        return Configuration.UI_MODE_NIGHT_YES == currentMode;
    }

    public static int getNavigationBarHeight(Activity activity) {
        if ((activity != null) && (activity.getWindow() != null)) {
            ViewGroup viewGroup = (ViewGroup) activity.getWindow().getDecorView();
            if (viewGroup != null) {
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    View childView = viewGroup.getChildAt(i);
                    try {
                        if ((childView.getId() != View.NO_ID)
                                && (NAVIGATION_BAR_ID_NAME.equals(activity.getResources().getResourceEntryName(childView.getId())))
                                && (childView.getVisibility() == View.VISIBLE)) {
                            return childView.getMeasuredHeight();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, e.getMessage());
                    }
                }
            }
        }
        return 0;
    }

    public static String getYMDDate(Date date) {
        DateFormat longDateFormat = DateFormat.getDateInstance(DateFormat.LONG);
        return longDateFormat.format(date);
    }

    public static String getDashDate(Date date) {
        DateFormat shortDateFormat = DateFormat.getDateInstance(java.text.DateFormat.SHORT);
        return shortDateFormat.format(date);
    }

    public static String getTimeToSeconds(Date date) {
        return java.text.DateFormat.getTimeInstance(java.text.DateFormat.MEDIUM).format(date);
    }

    public static String getTime(Context context, Date date) {
        if (context == null) {
            return null;
        }
        return android.text.format.DateFormat.getTimeFormat(context).format(date);
    }

    public static String getMDDate(Context context, Date date) {
        if (context == null) {
            return null;
        }
        return DateUtils.formatDateTime(context, date.getTime(), DateUtils.FORMAT_ABBREV_ALL);
    }

    public static CharSequence getApplicationDetailFormat(Context context, String apkName, String size, String version) {
        if (context == null) {
            return "";
        }
        final boolean isRtl = Utils.isRtl();
        StringBuilder stringBuilder = new StringBuilder();
        int indexFirst = 0;
        int indexSecoond = 0;
        if (!TextUtils.isEmpty(apkName)) {
            indexFirst = apkName.length() + ONE_SPACE.length();
            if (isRtl) {
                stringBuilder.append(RTL_CHARACTER_PREVIOUS);
                indexFirst += RTL_CHARACTER_PREVIOUS.length();
            }
            stringBuilder.append(apkName);
            stringBuilder.append(DIVIDER_WITH_SPACE);
            indexSecoond = indexFirst + ONE_SPACE.length();
        }
        if (!TextUtils.isEmpty(size)) {
            stringBuilder.append(size);
            stringBuilder.append(DIVIDER_WITH_SPACE);
            indexSecoond += (size.length() + ONE_SPACE.length());
        }
        if (!TextUtils.isEmpty(version)) {
            stringBuilder.append(context.getResources().getString(R.string.apk_version_name));
            if (isRtl) {
                stringBuilder.append(RTL_CHARACTER_AFTER_D);
            }
            stringBuilder.append(version);
            if (isRtl) {
                stringBuilder.append(RTL_CHARACTER_AFTER_C);
            }
        }
        //这里context需要是activity的context，如果用application的context获取出来的色值会为0
        int colorCharacter = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary);
        SpannableString spanString = new SpannableString(stringBuilder.toString());
        ForegroundColorSpan colorSpan = new ForegroundColorSpan(colorCharacter);
        ForegroundColorSpan colorSpan2 = new ForegroundColorSpan(colorCharacter);
        if (indexFirst > 0) {
            spanString.setSpan(colorSpan, indexFirst, indexFirst + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        spanString.setSpan(colorSpan2, indexSecoond, indexSecoond + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
        return spanString;
    }

    public static CharSequence getApplicationDetailFormat(Context context, String title, String detail, FileWrapper file) {
        if (context == null) {
            return "";
        }
        String apkDetail = Utils.formatSize(file);
        if (!TextUtils.isEmpty(apkDetail) && !TextUtils.isEmpty(detail)) {
            final boolean isRtl = Utils.isRtl();
            StringBuilder stringBuilder = new StringBuilder();
            int indexFirst = 0;
            int indexSecoond = 0;
            if (!TextUtils.isEmpty(title)) {
                indexFirst = title.length() + ONE_SPACE.length();
                if (isRtl) {
                    stringBuilder.append(RTL_CHARACTER_PREVIOUS);
                    indexFirst += RTL_CHARACTER_PREVIOUS.length();
                }
                stringBuilder.append(title);
                stringBuilder.append(DIVIDER_WITH_SPACE);
            }
            indexSecoond = indexFirst + ONE_SPACE.length() + 1;
            stringBuilder.append(apkDetail);
            stringBuilder.append(DIVIDER_WITH_SPACE);
            indexSecoond += (apkDetail.length() + ONE_SPACE.length());
            stringBuilder.append(context.getResources().getString(R.string.apk_version_name));
            if (isRtl) {
                stringBuilder.append(RTL_CHARACTER_AFTER_D);
            }
            stringBuilder.append(detail);
            if (isRtl) {
                stringBuilder.append(RTL_CHARACTER_AFTER_C);
            }
            //这里context需要是activity的context，如果用application的context获取出来的色值会为0
            int colorCharacter = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary);
            SpannableString spanString = new SpannableString(stringBuilder.toString());
            ForegroundColorSpan colorSpan = new ForegroundColorSpan(colorCharacter);
            ForegroundColorSpan colorSpan2 = new ForegroundColorSpan(colorCharacter);
            if (indexFirst > 0) {
                spanString.setSpan(colorSpan, indexFirst, indexFirst + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            }
            spanString.setSpan(colorSpan2, indexSecoond, indexSecoond + 1, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);
            return spanString;
        }
        return formatDetail(context, apkDetail, getDateFormat(context, file.lastModified()));
    }

    public static boolean isOppoPhone() {
        return OPPO.equalsIgnoreCase(Build.BRAND);
    }

    public static boolean isRealmePhone() {
        return REALME.equalsIgnoreCase(Build.BRAND);
    }

    public static boolean isOnePlusPhone() {
        return ONEPLUS.equalsIgnoreCase(Build.BRAND);
    }

    public static int getUserId() {
        int userId = CompatUtils.compactApi(CompatUtils.OS_14, CompatUtils.OS_14_SUB_V, new Supplier<Integer>() {
            @Override
            public Integer get() {
                return UserHandle.myUserId();
            }
        }, new Supplier<Integer>() {
            @Override
            public Integer get() {
                try {
                    return UserHandleNative.myUserId();
                } catch (Exception ex) {
                    Log.e(TAG, "getHandleNative error: " + ex);
                }
                return -1;
            }
        });
        return userId;
    }

    public static boolean isQuickClick() {
        return isQuickClick(0);
    }

    /**
     * @param clickType Default: 0, value in:{@link #ACTIVITY_QUICK_CLICK}, {@link #FRAGMENT_QUICK_CLICK}
     * @return
     */
    public static boolean isQuickClick(int clickType) {
        boolean result = false;
        long curTime = SystemClock.elapsedRealtime();
        long lastClickTime = sItemClickTime;
        switch (clickType) {
            case ACTIVITY_QUICK_CLICK:
                lastClickTime = sActivityClickTime;
                break;
            case FRAGMENT_QUICK_CLICK:
                lastClickTime = sFragmentClickTime;
                break;
            default:
                break;
        }
        if ((curTime - lastClickTime) <= Constants.QUICK_CLICK_TIME_GAP) {
            Log.d(TAG, "you click too fast " + clickType);
            result = true;
        } else {
            switch (clickType) {
                case ACTIVITY_QUICK_CLICK:
                    sActivityClickTime = curTime;
                    break;
                case FRAGMENT_QUICK_CLICK:
                    sFragmentClickTime = curTime;
                    break;
                default:
                    sItemClickTime = curTime;
                    break;
            }
        }
        return result;
    }

    public static boolean isQuickChangeModel() {
        boolean result = false;
        long curTime = SystemClock.elapsedRealtime();
        long lastClickTime = sChangeModelClickTime;
        if ((curTime - lastClickTime) <= Constants.QUICK_CLICK_TIME_GAP) {
            Log.d(TAG, "you ChangeModel too fast");
            result = true;
        } else {
            sChangeModelClickTime = curTime;
        }
        return result;
    }

    public static NavigationBarInfo getNavigationBarInfo(Activity activity) {
        if ((activity != null) && (activity.getWindow() != null)) {
            ViewGroup viewGroup = (ViewGroup) activity.getWindow().getDecorView();
            if (viewGroup != null) {
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    View childView = viewGroup.getChildAt(i);
                    try {
                        if ((childView.getId() != View.NO_ID)
                                && (NAVIGATION_BAR_ID_NAME.equals(activity.getResources().getResourceEntryName(childView.getId())))
                                && (childView.getVisibility() == View.VISIBLE)) {
                            NavigationBarInfo navigationBarInfo = new NavigationBarInfo();
                            if (i == viewGroup.getChildCount() - 1) {
                                navigationBarInfo.mIsTopView = true;
                            }
                            navigationBarInfo.mNavigationBarHeight = childView.getMeasuredHeight();
                            return navigationBarInfo;
                        }
                    } catch (Exception e) {
                        Log.e(TAG, e.getMessage());
                    }
                }
            }
        }
        NavigationBarInfo navigationBarInfo = new NavigationBarInfo();
        navigationBarInfo.mNavigationBarHeight = 0;
        return navigationBarInfo;
    }

    /**
     * 计算空间容量的占比值
     *
     * @param availableSize 可用的容量
     * @param totalSize     总容量
     * @return 返回千分比的值
     */
    public static int calcSpaceSizeRatio(long availableSize, long totalSize) {
        if (totalSize == 0L) {
            return 0;
        }
        long usedSpaceSize = totalSize - availableSize;
        return (int) (KtConstants.MAX_PROGRESS_1000 * usedSpaceSize / totalSize);
    }

    /**
     * 获取启动Activity 的包名
     * @param activity 被启动的Activity
     * @return
     */
    public static String getLauncherPkg(Activity activity) {
        return AccessController.doPrivileged(new PrivilegedAction<Object>() {
            @Override
            public Object run() {
                try {
                    Class<?> activityClass = Class.forName("android.app.Activity");
                    Field refererField = activityClass.getDeclaredField("mReferrer");
                    refererField.setAccessible(true);
                    Object pkg = refererField.get(activity);
                    if (Objects.isNull(pkg)) {
                        return "";
                    }
                    return pkg;
                } catch (ClassNotFoundException | NoSuchFieldException | IllegalArgumentException | IllegalAccessException e) {
                    Log.e(TAG, "getLauncherPkg error", e);
                } catch (Exception e) {
                    Log.e(TAG, "getLauncherPkg error", e);
                }
                return "";
            }
        }).toString();
    }
}
