/*********************************************************************************
 * Copyright (C), 2008-2022, Oplus, All rights reserved.
 *
 * File: - BundleExtends.kt
 * Description:
 *     The extend util methods for bundle
 *
 * Version: 1.0
 * Date: 2024-04-09
 * Author: <EMAIL>
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * <PERSON><PERSON><PERSON><PERSON>.<PERSON>@ROM.SysApp.Screenshot    2024-04-09   1.0    Create this module
 *********************************************************************************/
package com.filemanager.common.utils

import android.os.Bundle

fun Bundle.getBooleanOrNull(key: String): Boolean? {
    if (containsKey(key)) {
        return getBoolean(key)
    }
    return null
}

fun Bundle.getLongOrNull(key: String): Long? {
    if (containsKey(key)) {
        return getLong(key)
    }
    return null
}

/**
 * log 输出bundle的内容
 */
fun Bundle?.log(): String {
    if (this == null) {
        return "null"
    }
    val builder = StringBuilder()
    builder.append("Bundle[{")
    val keySet = this.keySet()
    val keyCount = keySet.size
    keySet.forEachIndexed { index, key ->
        builder.append("$key=")
        builder.append(this.get(key))
        if (index < keyCount - 1) {
            builder.append(", ")
        }
    }
    builder.append("}]")
    return builder.toString()
}