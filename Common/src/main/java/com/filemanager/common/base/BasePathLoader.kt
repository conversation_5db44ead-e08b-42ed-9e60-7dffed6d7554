/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.factor
 * * Version     : 1.0
 * * Date        : 2020/2/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.content.Context
import com.filemanager.common.MyApplication
import com.filemanager.common.fileutils.HiddenFileHelper.isHiddenFile
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.RecycleBinUtils.isRecycleDirPath
import java.io.File

data class PathLoadResult<K, T : BaseFileBean>(val mResultList: List<T>, val mResultMap: HashMap<K, T>)

abstract class BasePathLoader<K, T : BaseFileBean> : FileTaskLoader<PathLoadResult<K, T>> {
    companion object {
        const val TAG = "BasePathLoader"
    }

    var mFilterList: List<Int>? = null
    var mPath: Array<String>? = null
    var mVolume: List<String>? = null

    constructor(context: Context) : super(context)

    protected fun initData() {
        mFilterList = getFilterList()
    }

    override fun loadInBackground(): PathLoadResult<K, T>? {
        prepareHandleBackground()
        var allFileList = ArrayList<T>()
        val keyMap = HashMap<K, T>()
        var key: K? = null
        var itemList: List<T>? = null
        try {
            mVolume = getVolume()
            mPath = getPath()
            Log.d(TAG, "loadInBackground() mVolume.size = ${mVolume?.size},mPath.size=${mPath?.size}")
            if (mVolume == null) {
                for (i in 0 until mPath!!.size) {
                    val path = mPath!![i]
                    val file = File(AndroidDataHelper.replacePath(path))
                    Log.d(TAG, "loadInBackground() path=${file.absolutePath}")
                    if (file.isDirectory) {
                        var ss: Array<String>?
                        try {
                            Log.d(TAG, "loadInBackground() begin list")
                            ss = JavaFileHelper.list(file)
                            Log.d(TAG, "loadInBackground() end list")
                        } catch (e: Exception) {
                            Log.e(TAG, e.message)
                            continue
                        }
                        if (ss == null) {
                            Log.v("BasePathLoader", "file system can not fetch data")
                            return continue
                        }
                        val tempSize = ss.size
                        Log.d(TAG, "loadInBackground() tempSize=${tempSize}")
                        for (j in 0 until tempSize) {
                            if (!isNeedShowHiddenFile() && isHiddenFile(ss[j])) {
                                continue
                            }
                            itemList = createFromPath("", path, ss[j])
                            if (itemList.isNullOrEmpty()) {
                                continue
                            }

                            for (item in itemList) {
                                if (isRecycleDirPath(item.mData)) {
                                    continue
                                }
                                if (item.mDateModified == 0L) {
                                    Log.d(TAG, "mDateModified is 0")
                                    item.mDateModified = FileTimeUtil.getFileTime(item.mData) ?: 0
                                }
                                allFileList.add(item)
                                key = getItemKey(item) ?: continue
                                keyMap.put(key, item)
                            }
                        }
                        Log.d(TAG, "loadInBackground() keyMap.size=${keyMap.size}")
                    }
                }
            } else {
                for (volume in mVolume!!) {
                    if (VolumeEnvironment.isNeedLoadPath(MyApplication.sAppContext, volume)) {
                        continue
                    }
                    Log.d(TAG, "loadInBackground() mVolume=${volume}")
                    for (i in 0 until mPath!!.size) {
                        val path = mPath!![i]
                        val file = File(volume, AndroidDataHelper.replacePath(path))
                        Log.d(TAG, "loadInBackground() path=${file.absolutePath}")
                        if (file.isDirectory) {
                            var ss: Array<String>?
                            try {
                                Log.d(TAG, "loadInBackground() begin list")
                                ss = JavaFileHelper.list(file)
                                Log.d(TAG, "loadInBackground() end list")
                            } catch (e: Exception) {
                                Log.e(TAG, e.message)
                                continue
                            }
                            if (ss == null) {
                                Log.v("BasePathLoader", "file system can not fetch data")
                                return continue
                            }
                            val tempSize = ss.size
                            Log.d(TAG, "loadInBackground() tempSize=${tempSize}")
                            for (j in 0 until tempSize) {
                                if (!isNeedShowHiddenFile() && isHiddenFile(ss[j])) {
                                    continue
                                }

                                itemList = createFromPath(volume, path, ss[j])
                                if (itemList.isNullOrEmpty()) {
                                    continue
                                }

                                for (item in itemList) {
                                    if (isRecycleDirPath(item.mData)) {
                                        continue
                                    }
                                    if (item.mDateModified == 0L) {
                                        Log.d(TAG, "mDateModified is 0")
                                        item.mDateModified = FileTimeUtil.getFileTime(item.mData) ?: 0
                                    }
                                    allFileList.add(item)
                                    key = getItemKey(item) ?: continue
                                    keyMap.put(key, item)
                                }
                            }
                            Log.d(TAG, "loadInBackground() itemList.size=${itemList?.size},keyMap.size=${keyMap.size}")
                        }
                    }
                }
            }
            mFilterList = getFilterList()
            mFilterList?.let {
                if (!it.contains(MimeTypeHelper.UNKNOWN_TYPE)) {
                    val filterLists = ArrayList<T>()
                    keyMap.clear()
                    var tempkey: K? = null
                    for (tempFile in allFileList) {
                        if (tempFile.mDateModified == 0L) {
                            Log.d(TAG, "mDateModified is 0")
                            tempFile.mDateModified = FileTimeUtil.getFileTime(tempFile.mData) ?: 0
                        }
                        if (it.contains(tempFile.mLocalType)) {
                            filterLists.add(tempFile)
                            tempkey = getItemKey(tempFile) ?: continue
                            keyMap.put(tempkey, tempFile)
                        }
                    }
                    allFileList = filterLists
                    Log.d(TAG, "loadInBackground() allFileList.size=${allFileList?.size}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, e.message)
        }
        val sortItems = preHandleResultBackground(allFileList)
        Log.d(TAG, "loadInBackground() sortItems.size=${sortItems?.size}")
        return PathLoadResult(sortItems, keyMap)
    }

    abstract fun createFromPath(volume: String, parentPath: String, path: String): List<T>?
    abstract fun getPath(): Array<String>
    abstract fun getVolume(): List<String>?
    abstract fun getFilterList(): List<Int>?
    abstract fun getItemKey(item: T): K?

    open fun prepareHandleBackground() {}

    /**
     * Processing the query result list in background,Subclasses can be replicated
     * @return Processed results
     */
    open fun preHandleResultBackground(list: List<T>): List<T> {
        return list
    }

    override fun onStartLoading() {
        forceLoad()
    }

    override fun onStopLoading() {
        cancelLoad()
    }

    override fun deliverResult(data: PathLoadResult<K, T>?) {
        if (mReset) {
            data?.let {
                (it.mResultList as? ArrayList)?.clear()
                (it.mResultMap as? HashMap)?.clear()
            }
            return
        }
        if (mStarted) {
            super.deliverResult(data)
        }
    }

    override fun onReset() {
        super.onReset()
        onStopLoading()
    }
}