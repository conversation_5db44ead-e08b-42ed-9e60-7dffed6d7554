/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.viewholder
 * * Version     : 1.0
 * * Date        : 2020/9/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.content.Context
import android.os.Environment
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseSelectionViewHolder
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.fileutils.HiddenFileHelper.isNeedShowHiddenFile
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.NewFunctionSwitch
import com.filemanager.common.utils.Utils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

private const val TAG = "BaseFileBrowserVH"

abstract class BaseFileBrowserVH(convertView: View) : BaseNormalVH(convertView) {
    companion object {
        private val DOWNLOAD_PATH by lazy {
            Environment.getExternalStorageDirectory().absolutePath + File.separator + Environment.DIRECTORY_DOWNLOADS
        }
        private val DOCUMENTS_PATH by lazy {
            Environment.getExternalStorageDirectory().absolutePath + File.separator + Environment.DIRECTORY_DOCUMENTS
        }
    }

    protected fun showDetail(context: Context, file: BaseFileBean, detail: TextView, path: String, sizeCache: HashMap<String, String>
                             , threadManager: ThreadManager, isLinearViewHolder: Boolean) {
        var lastModified = file.mDateModified
        val size = sizeCache.get(path + file.mSize + lastModified + isNeedShowHiddenFile())
        if (size != null && !size.isEmpty()) {
            if (isLinearViewHolder && lastModified != 0L) {
                val dateAndTime = Utils.getDateFormat(context, lastModified)
                detail.text = Utils.formatDetail(context, size, dateAndTime)
            } else {
                detail.text = size
            }
        } else {
            //ViewHolder reuse causes display exception,holder.mDetail need to set default display text
            detail.text = ""
             (context as? BaseVMActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
                val formatStorageDetail = if (file.mIsDirectory) {
                    val fileCount = JavaFileHelper.listFilesCount(file, isNeedShowHiddenFile().not())
                    MyApplication.sAppContext.resources.getQuantityString(R.plurals.text_x_items, fileCount, fileCount)
                } else {
                    KtUtils.formatSize(file)
                }
                detail.post {
                    val currentPath = detail.tag as? String
                    val lastModified = file.mDateModified
                    if (path == currentPath) {
                        sizeCache.put(path + file.mSize  + lastModified + isNeedShowHiddenFile(), formatStorageDetail)
                        if (isLinearViewHolder && lastModified != 0L) {
                            val dateAndTime = Utils.getDateFormat(context, lastModified)
                            detail.text = Utils.formatDetail(context, formatStorageDetail, dateAndTime)
                        } else {
                            detail.text = formatStorageDetail
                        }
                    }
                }
            }
        }
    }

    protected fun showApkIconIfNeed(file: BaseFileBean, apkIcon: ImageView, isGridMode: Boolean = false) {
        Log.d(TAG, "showApkIconIfNeed apkIcon = $apkIcon file.mLocalType = ${file.mLocalType} file.originPackage = ${file.originPackage}")
        val isSpecial = isSpecialFolder(file)
        if (NewFunctionSwitch.SUPPORT_FOLDER_LOGO &&
            MimeTypeHelper.DIRECTORY_TYPE == file.mLocalType &&
            (!file.originPackage.isNullOrEmpty() || isSpecial)) {
            apkIcon.visibility = View.VISIBLE
            if (isSpecial) {
                apkIcon.setImageResource(getSpecialFolderIcon(file))
            } else {
                val logoFile = BaseFileBean()
                logoFile.mData = file.originPackage
                FileImageLoader.sInstance.displayApplicationWithDetail(logoFile, apkIcon,
                    {
                        Log.d(TAG, "showApkIconIfNeed")
                    }, isGridMode = isGridMode)
            }
        } else {
            apkIcon.visibility = View.GONE
        }
    }

    protected open fun isSpecialFolder(file: BaseFileBean): Boolean {
        return DOWNLOAD_PATH.equals(file.mData, true) || DOCUMENTS_PATH.equals(file.mData, true)
    }

    protected open fun getSpecialFolderIcon(file: BaseFileBean): Int {
        if (DOWNLOAD_PATH.equals(file.mData, true)) {
            return R.drawable.main_category_download
        } else if (DOCUMENTS_PATH.equals(file.mData, true)) {
            return R.drawable.ic_file_doc
        }
        return 0
    }
}