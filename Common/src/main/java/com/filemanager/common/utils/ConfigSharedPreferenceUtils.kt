/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.utils
 * * Version     : 1.0
 * * Date        : 2020/9/15
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

class ConfigSharedPreferenceUtils {
    companion object {
        private const val SHARED_PREFS_CONFIG = "file_manager_config_preferences"
        fun putInt(key: String, value: Int) {
            PreferencesUtils.put(SHARED_PREFS_CONFIG, key, value)
        }

        fun getInt(key: String, defaultValue: Int): Int {
            return PreferencesUtils.getInt(SHARED_PREFS_CONFIG, key, defaultValue)
        }

        fun putLong(key: String, value: Long) {
            PreferencesUtils.put(SHARED_PREFS_CONFIG, key, value)
        }

        fun getLong(key: String, defaultValue: Long): Long {
            return PreferencesUtils.getLong(SHARED_PREFS_CONFIG, key, defaultValue)
        }
    }
}