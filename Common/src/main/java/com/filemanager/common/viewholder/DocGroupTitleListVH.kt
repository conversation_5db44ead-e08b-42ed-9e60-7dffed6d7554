/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/3/14
 * * Author      : W9037462
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.view.View
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.utils.FileImageVHUtils

class DocGroupTitleListVH(convertView: View) : RecyclerView.ViewHolder(convertView) {

    companion object {
        fun getLayoutId(): Int {
            return R.layout.file_group_title_item
        }
    }

    private val tvGroupTitle: TextView = convertView.findViewById(R.id.tv_group_title)

    fun updateViewHolder(data: BaseFileBean) {
        //当文件时间获取是0（1970/01/01），不显示时间戳
        if (data.mDisplayName == "1970/01/01") {
            tvGroupTitle.visibility = View.GONE
            return
        }
        tvGroupTitle.visibility = View.VISIBLE
        tvGroupTitle.text = data.mDisplayName
        updateLeftRightMargin()
    }

    private fun updateLeftRightMargin() {
        tvGroupTitle.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }
}