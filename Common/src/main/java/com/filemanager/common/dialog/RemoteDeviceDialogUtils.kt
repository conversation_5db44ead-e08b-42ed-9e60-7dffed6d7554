/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : RemoteDeviceDialogUtils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/04/22
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.dialog

import android.content.Context
import android.content.DialogInterface
import android.view.Gravity
import androidx.appcompat.app.AlertDialog

import com.coui.appcompat.dialog.COUIAlertDialogBuilder

object RemoteDeviceDialogUtils {

    /*当前设备已经与其他电脑建立连接*/
    const val PHONE_CONNECTED_TO_ANOTHER = 1
    /*当前设备正在与其他电脑建立连接*/
    const val PHONE_CONNECTING_TO_ANOTHER = 2
    /*该电脑已经与其他设备建立连接*/
    const val PC_CONNECTED_TO_ANOTHER = 3
    /*该电脑正在与其他设备建立连接*/
    const val PC_CONNECTING_TO_ANOTHER = 4
    @JvmStatic
    fun createDialog(
        context: Context,
        title: String,
        message: String?,
        positive: String,
        negative: String? = null,
        positiveListener: DialogInterface.OnClickListener? = DialogInterface.OnClickListener { _, _ -> }
    ): AlertDialog {
        val builder =
            COUIAlertDialogBuilder(context).setWindowGravity(Gravity.BOTTOM).setTitle(title)
                .setMessage(message).setPositiveButton(positive, positiveListener)
                .setNegativeButton(negative, null).setBlurBackgroundDrawable(true)
        return builder.create()
    }

    /**
     * 密码错误
     */
    @JvmStatic
    fun createPasswdErrorDialog(context: Context, listener: DialogInterface.OnClickListener): AlertDialog {
        val title = context.getString(com.filemanager.common.R.string.password_wrong)
        val message =
            context.getString(com.filemanager.common.R.string.please_enter_correct_password)
        val negative = context.getString(com.filemanager.common.R.string.action_close)
        val positive = context.getString(com.filemanager.common.R.string.reinput)
        return createDialog(context, title, message, positive, negative, listener)
    }

    /**
     * 同账号退出，重新输入密码
     */
    @JvmStatic
    fun createExitAccountDialog(context: Context, listener: DialogInterface.OnClickListener): AlertDialog {
        val title = context.getString(com.filemanager.common.R.string.pc_disconnected)
        val message = context.getString(com.filemanager.common.R.string.pc_exited_account_reconnect)
        val negative = context.getString(com.filemanager.common.R.string.action_close)
        val positive = context.getString(com.filemanager.common.R.string.dialog_input_password_title)
        return createDialog(context, title, message, positive, negative, listener)
    }

    /**
     * 无网络连接
     */
    @JvmStatic
    fun createNoInternetDialog(context: Context): AlertDialog {
        val title = context.getString(com.filemanager.common.R.string.no_internet_connection)
        val message =
            context.getString(com.filemanager.common.R.string.check_network_status_and_retry)
        val positive = context.getString(com.filemanager.common.R.string.positive_ok)
        val positiveListener = DialogInterface.OnClickListener { _, _ -> }
        return createDialog(context, title, message, positive, null, positiveListener)
    }

    /**
     * 设备已离线
     */
    @JvmStatic
    fun createDeviceOfflineDialog(context: Context): AlertDialog {
        val title = context.getString(com.filemanager.common.R.string.device_offline)
        val message1 =
            context.getString(com.filemanager.common.R.string.mac_remote_device_make_sure_connect)
        val message2 = context.getString(com.filemanager.common.R.string.oplus_interconnect)
        val message = String.format(message1, message2)
        val positive = context.getString(com.filemanager.common.R.string.positive_ok)
        val positiveListener = DialogInterface.OnClickListener { _, _ -> }
        return createDialog(context, title, message, positive, null, positiveListener)
    }

    /**
     * 远程文件管理不可用
     */
    @JvmStatic
    fun createRemoteFMUnavailableDialog(context: Context): AlertDialog {
        val title =
            context.getString(com.filemanager.common.R.string.remote_fm_function_is_unavailable)
        val message = context.getString(com.filemanager.common.R.string.mac_ver_low_user_notice)
        val positive = context.getString(com.filemanager.common.R.string.positive_ok)
        val positiveListener = DialogInterface.OnClickListener { _, _ -> }
        return createDialog(context, title, message, positive, null, positiveListener)
    }

    /**
     * 连接失败，请稍后重试
     */
    @JvmStatic
    fun createConnectionFailAfterRetryDialog(context: Context): AlertDialog {
        val title = context.getString(com.filemanager.common.R.string.connect_fail_please_retry)
        val positive = context.getString(com.filemanager.common.R.string.positive_ok)
        val positiveListener = DialogInterface.OnClickListener { _, _ -> }
        return createDialog(context, title, null, positive, null, positiveListener)
    }

    /**
     * 连接失败的4种状态
     * @param code 用 code 区分四种弹窗
     */
    @JvmStatic
    fun createConnectionFailDialog(context: Context, code: Int): AlertDialog {
        val title = context.getString(com.filemanager.common.R.string.connection_fail)
        val positive = context.getString(com.filemanager.common.R.string.positive_ok)
        val positiveListener = DialogInterface.OnClickListener { _, _ -> }
        val message = when (code) {
            PHONE_CONNECTED_TO_ANOTHER -> context.getString(com.filemanager.common.R.string.phone_connected_to_another_disconnect_retry)
            PHONE_CONNECTING_TO_ANOTHER -> context.getString(com.filemanager.common.R.string.phone_connecting_to_another_disconnect_retry)
            PC_CONNECTED_TO_ANOTHER -> context.getString(com.filemanager.common.R.string.pc_connected_to_another_disconnect_retry)
            PC_CONNECTING_TO_ANOTHER -> context.getString(com.filemanager.common.R.string.pc_connecting_to_another_disconnect_retry)
            else -> context.getString(com.filemanager.common.R.string.phone_connected_to_another_disconnect_retry)
        }
        return createDialog(context, title, message, positive, null, positiveListener)
    }
}