/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.view
 * * Version     : 1.0
 * * Date        : 2020/5/20
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.animation.Animator
import android.content.Context
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.view.View.OnScrollChangeListener
import android.view.ViewTreeObserver
import android.widget.Button
import android.widget.HorizontalScrollView
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.annotation.Px
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.dragselection.DropTag
import com.filemanager.common.path.FilePathHelper
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ThemeColorUtils
import com.filemanager.common.utils.TypefaceUtil
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.path.OnPathVisibilityListener
import com.filemanager.common.view.path.PathBarConfig
import java.io.File
import kotlin.math.min

class BrowserPathBar(context: Context, attrs: AttributeSet) : RelativeLayout(context, attrs) {

    companion object {
        private const val TAG = "BrowserPathBar"
        private const val SIZE_ONE_PATH_AND_ONE_ARROW = 2
        const val FILE_BROWSER_FOLDER_ANIM_TIME = 100L
    }

    private var mBtnPadding = 0
    private var mMinWidth = 0
    private var imgSize = 0
    private var imgMarginStart = 0
    private var mCanClick = true
    private var mRootView: View? = null
    private var mLeftGradientImg: ImageView? = null
    private var mRightGradientImg: ImageView? = null
    private var mPathLayout: LinearLayout? = null
    private var mRootPathButton: TextView? = null
    private var mPathArrow: ImageView? = null
    private var mInflater: LayoutInflater? = null
    private var mScrollView: HorizontalScrollView? = null
    private var mOnPathClickListener: OnPathClickListener? = null
    private var mOnFocusChangeListener: OnTextFocusColorChangeListener? = null
    private var mOnGlobalLayoutListener: ViewTreeObserver.OnGlobalLayoutListener? = null
    private var mOnPathVisibilityListener: OnPathVisibilityListener? = null
    private var mSettings: PathBarConfig = PathBarConfig.init()
    private var mPathHelper: FilePathHelper? = null
    private var mCurrentPath: String? = null
    private var mCurrentRootPath: String? = null
    private var mLastPath: String? = null
    private var mIsInPop = false
    private var mBrowserContext: Context = context
    private var mIsAddGlobalLayoutListener = false
    private var mGradientEndColor = 0
    private var specialPathProcess: ISpecialPathProcess? = null
    private var normalPathProcess: ISpecialPathProcess = NormalPathProcess()

    private var isFromShortcutFolder: Boolean = false
    private var shortcutFolderRootPath = ""

    private val mOnScrollChangeListener = OnScrollChangeListener { _, x, _, _, _ ->
        //Log.d(TAG, "onScrollchange x $x, scrollViewWidth ${mScrollView?.width}, pathLayoutWidth ${mPathLayout?.width}, isSupportGradient ${mSettings.isSupportGradient}, isNeedScroll $isNeedScroll")
        if (mSettings.isSupportGradient) {
            if (x == 0) {
                if (isNeedScroll) {
                    setVisibility(mLeftGradientImg, false)
                    setVisibility(mRightGradientImg, true)
                }
            } else {
                if (isNeedScroll) {
                    if ((x + (mScrollView?.width ?: 0)) < (mPathLayout?.width ?: 0)) {
                        setVisibility(mRightGradientImg, true)
                    } else {
                        setVisibility(mRightGradientImg, false)
                    }
                }
                if (isNeedScroll) {
                    setVisibility(mLeftGradientImg, true)
                }
            }
        }
    }

    init {
        initViews(context)
    }

    private val isNeedScroll: Boolean
        get() = if ((mPathLayout == null) || (mScrollView == null)) {
            false
        } else {
            mPathLayout!!.width > mScrollView!!.width
        }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        super.onLayout(changed, l, t, r, b)
        if (!changed) {
            return
        }
        mLeftGradientImg?.let(::updateGradientLayout)
        mRightGradientImg?.let(::updateGradientLayout)
    }

    private fun updateGradientLayout(gradientView: ImageView) {
        val lp = gradientView.layoutParams
        val barHeight = height
        if (barHeight != lp.height) {
            lp.height = barHeight
            gradientView.layoutParams = lp
        }
    }

    fun setGradientColor(colorId: Int) {
        mGradientEndColor = colorId
        runCatching {
            if (mGradientEndColor != 0) {
                mLeftGradientImg?.background = GradientDrawable(
                    GradientDrawable.Orientation.LEFT_RIGHT,
                    intArrayOf(
                        mGradientEndColor,
                        android.R.color.transparent
                    )
                )
                mRightGradientImg?.background = GradientDrawable(
                    GradientDrawable.Orientation.LEFT_RIGHT,
                    intArrayOf(
                        android.R.color.transparent,
                        mGradientEndColor
                    )
                )
            }
        }.onFailure {
            Log.e(TAG, it.message)
        }
    }

    fun getCurrentPath(): String? {
        return mCurrentPath
    }

    fun setCurrentPath(path: String, forceRefresh: Boolean = false) {
        if (path.isEmpty()) {
            return
        }
        Log.d(TAG, "setCurrentPath path = $path  CurrentRootPath = $mCurrentRootPath mLastPath = $mLastPath, force $forceRefresh")
        mCurrentPath = path
        if ((mCurrentRootPath != null) && isSameRoot(path, mLastPath) && !forceRefresh) {
            Log.d(TAG, "setCurrentPath A")
            setCurrentPathWhenRootPathNotNull(path)
        } else {
            Log.d(TAG, "setCurrentPath B")
            setCurrentPathWhenRootPathNull(path)
        }
        mLastPath = mCurrentPath
        addOnGlobalLayoutListener()
    }

    fun updateShortcutFolderRootPath(folderRootPath: String) {
        Log.d(TAG, "updateShortcutFolderRootPath shortcutFolderRootPath=$folderRootPath")
        shortcutFolderRootPath = folderRootPath
    }

    fun setIsFromShortcutFolder(fromShortcutFolder: Boolean) {
        Log.d(TAG, "updateShortcutFolderRootPath isFromShortcutFolder=$fromShortcutFolder")
        isFromShortcutFolder = fromShortcutFolder
    }

    private fun setCurrentPathWhenRootPathNull(path: String) {
        showRootPath(path)
        val currentRootPath = mCurrentRootPath
        var pathNames: Array<String>? = null
        var startIndex = 0
        if ((currentRootPath != null) && path.startsWith(currentRootPath)) {
            pathNames = path.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.toTypedArray()
            //计算之前rootPath中的分割之后的
            startIndex = currentRootPath.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.size
            Log.d(TAG, "setCurrentPath B path $path, pathName ${pathNames.toList()}, currentRootPath $currentRootPath, startIndex $startIndex")
        }
        removeViewFromEnd(
            (mPathLayout?.childCount ?: 0) / SIZE_ONE_PATH_AND_ONE_ARROW + 1,
            true,
            false
        )
        if (pathNames != null) {
            //这里将原有的路径path和路径path拆分出来的路径列表转换为UI上显示需要的字符串数组
            val convertPathArray = getConvertPathArray(path, pathNames)
            for (i in startIndex until pathNames.size) {
                val isLast = i == pathNames.size - 1
                val isFirst = i == startIndex
                Log.d(TAG, "setCurrentPathWhenRootPathNull paths$i = ${pathNames[i]} ${convertPathArray[i]}, isLast $isLast, isFirst: $isFirst")
                addChildView(convertPathArray[i], true, !isFirst)
            }
        }
        if (isRootPath(path)) {
            mPathArrow?.visibility = INVISIBLE
        } else {
            mPathArrow?.visibility = VISIBLE
        }
        val childCnt = mPathLayout?.childCount ?: 0
        setFocusColor((childCnt - 1) / SIZE_ONE_PATH_AND_ONE_ARROW)
    }

    private fun setCurrentPathWhenRootPathNotNull(path: String) {
        if (isRootPath(path)) {
            Log.d(TAG, "setCurrentPath A rootPath $path, mCurrentRootPath $mCurrentRootPath")
            removeViewFromEnd((mPathLayout?.childCount ?: 0) / SIZE_ONE_PATH_AND_ONE_ARROW + 1, true, true)
            onFocusTextChangeCallback()
            setTextColorForRootPathButton()
            mPathArrow?.visibility = INVISIBLE
        } else if (path.length > mCurrentRootPath!!.length) {
            if (mPathHelper?.shouldDisableRootButton() == true) {
                setDisableTextColorForView(mRootPathButton)
            } else {
                mRootPathButton?.setTextColor(getColor(mSettings.textNormalColor))
            }
            val lastNames = mLastPath!!.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.toTypedArray()
            val pathNames = path.split(File.separator.toRegex()).dropWhile { it.isEmpty() }.toTypedArray()
            val shortLength = min(lastNames.size, pathNames.size)
            var differentIndex = shortLength
            for (i in 0 until shortLength) {
                if (lastNames[i] != pathNames[i]) {
                    differentIndex = i
                    break
                }
            }
            val currentRootPath = mCurrentRootPath
            val firstIndex = currentRootPath?.split(File.separator.toRegex())?.dropWhile { it.isEmpty() }?.size ?: 0
            Log.d(TAG, "setCurrentPath differentIndex = $differentIndex, lastNames size = ${lastNames.size}, firstIndex $firstIndex")
            //hasAddChildView is used to mark whether to do the removal animation, because the removal animation cannot exist at the same time as the display animation when setFocusColor()
            val hasAddChildView = pathNames.size > differentIndex
            removeViewFromEnd(lastNames.size - differentIndex, false, !hasAddChildView)
            if (hasAddChildView) {
                //这里将原有的路径path和路径path拆分出来的路径列表转换为UI上显示需要的字符串数组
                val convertPathArray = getConvertPathArray(path, pathNames)
                for (i in differentIndex until pathNames.size) {
                    val isFirst = (i == differentIndex) && differentIndex == firstIndex
                    Log.d(TAG, "setCurrentPathWhenRootPathNotNull paths$i = ${pathNames[i]} ${convertPathArray[i]}, isFirst $isFirst")
                    addChildView(convertPathArray[i], true, !isFirst)
                }
                val childCnt = mPathLayout?.childCount ?: 0
                setFocusColor((childCnt - 1) / SIZE_ONE_PATH_AND_ONE_ARROW)
            } else {
                val childCnt = mPathLayout?.childCount ?: 0
                val index = ((childCnt - 1) / SIZE_ONE_PATH_AND_ONE_ARROW) - (lastNames.size - differentIndex)
                setFocusColor(index)
            }
            mPathArrow?.visibility = VISIBLE
        }
    }

    private fun initViews(context: Context) {
        mInflater = context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
        if (mInflater == null) {
            return
        }
        //Log.d(TAG, "initViews mOnScrollChangeListener $mOnScrollChangeListener")
        mInflater!!.inflate(R.layout.path_bar, this)
        mRootView  = findViewById<View>(R.id.root_view)
        mRootPathButton = findViewById<View>(R.id.root_path) as TextView
        mLeftGradientImg = findViewById(R.id.path_bar_left_gradient_img)
        mRightGradientImg = findViewById(R.id.path_bar_right_gradient_img)
        mPathArrow = findViewById(R.id.root_path_mark)
        mPathArrow?.setImageDrawable(getArrowAutoMirrored(mSettings.middleImg))
        setInPopWindow(mIsInPop)
        mRootPathButton?.setTextSize(TypedValue.COMPLEX_UNIT_PX, getDimension(mSettings.rootTextSize))
        mRootPathButton?.setOnClickListener {
            if (mCanClick && !mCurrentRootPath.equals(mCurrentPath, ignoreCase = true)) {
                mOnPathClickListener?.onPathClick(0, mCurrentRootPath)
            }
        }
        mPathLayout = findViewById<View>(R.id.path_layout) as LinearLayout
        mScrollView = findViewById<View>(R.id.path_scroll_view) as HorizontalScrollView
        mOnGlobalLayoutListener = object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                onPathVisibility()
                mPathLayout?.viewTreeObserver?.removeOnGlobalLayoutListener(this)
                mIsAddGlobalLayoutListener = false
            }
        }
        mScrollView?.setOnScrollChangeListener(mOnScrollChangeListener)
        addOnGlobalLayoutListener()
        mBtnPadding = context.resources.getDimensionPixelSize(mSettings.childPadding)
        mMinWidth = context.resources.getDimensionPixelSize(mSettings.pathBarMinWidth)
        imgSize = context.resources.getDimensionPixelSize(R.dimen.dimen_24dp)
        imgMarginStart = context.resources.getDimensionPixelSize(R.dimen.dimen_3dp)
        updateLeftRightMargin()
    }

    /**
     * void
     */
    private fun addOnGlobalLayoutListener() {
        mPathLayout?.viewTreeObserver?.addOnGlobalLayoutListener(mOnGlobalLayoutListener)
    }

    private fun setVisibility(view: ImageView?, visible: Boolean) {
        view?.let {
            if (visible) {
                if (it.visibility == View.GONE) {
                    it.visibility = View.VISIBLE
                }
            } else {
                if (it.visibility == View.VISIBLE) {
                    it.visibility = View.GONE
                }
            }
        }

    }

    fun showRootPathString(currentPath: String) {
        if (currentPath.isNotEmpty()) {
            removeAllViews()
            mLastPath = currentPath
            mCurrentPath = currentPath
            mCurrentRootPath = currentPath
            mRootPathButton?.text = currentPath
            setTextColorForRootPathButton()
            mRootPathButton?.typeface = TypefaceUtil.mediumTypeface
            onFocusTextChangeCallback()
            addOnGlobalLayoutListener()
        }
    }

    private fun showRootPath(currentPath: String): BrowserPathBar {
        Log.d(TAG, "showRootPath currentPath $currentPath")
        mPathHelper?.let {
            var rootText: String
            if (it.isLocalFile()) {
                mCurrentRootPath = it.getInternalPath()
                val storageType = if (isFromShortcutFolder) {
                    KtUtils.SHORTCUT_FOLDER
                } else {
                    KtUtils.getStorageByPath(appContext, currentPath)
                }
                Log.d(TAG, "showRootPath currentPath $currentPath storageType $storageType")
                when (storageType) {
                    KtUtils.STORAGE_INTERNAL -> mCurrentRootPath = it.getInternalPath()
                    KtUtils.STORAGE_INTERNAL_MULTI_APP -> mCurrentRootPath = KtConstants.LOCAL_VOLUME_MULTI_APP_PATH
                    KtUtils.STORAGE_EXTERNAL -> mCurrentRootPath = it.getExternalPath()
                    KtUtils.STORAGE_DMF -> mCurrentRootPath = it.getDfmRootPath()
                    KtUtils.SHORTCUT_FOLDER -> {
                        val rootPath = shortcutFolderRootPath.ifEmpty { it.getInternalPath() }
                        Log.d(TAG, "rootPath=$rootPath")
                        mCurrentRootPath = rootPath
                    }
                    KtUtils.STORAGE_OTG -> {
                        val otgPath = it.getOtgPath()
                        otgPath?.apply {
                            for (i in this.indices) {
                                if (currentPath.startsWith(this[i])) {
                                    mCurrentRootPath = this[i]
                                }
                            }
                        }
                    }
                }
            } else {
                mCurrentRootPath = it.getRootPath()
            }
            Log.d(TAG, "mCurrentRootPath = $mCurrentRootPath currentPath = $currentPath")
            mCurrentRootPath?.let { rootPath ->
                    rootText = if (it.isLocalFile()) {
                        when {
                            isFromShortcutFolder -> File(rootPath).name
                            it.isRootMultiAppPath(rootPath) -> getString(R.string.device_storage)
                            it.isRootInternalPath(rootPath) -> getString(R.string.string_all_files)
                            it.isRootExternalPath(rootPath) -> getString(R.string.storage_external)
                            KtUtils.checkIsDfmPath(rootPath) && it.getDfmDeviceName() != null -> it.getDfmDeviceName()!!
                            else -> getString(R.string.storage_otg)
                    }
                } else {
                    if (it.getRemoteMacDeviceName() != null) {
                        it.getRemoteMacDeviceName() ?: ""
                    } else {
                        rootPath
                    }
                }
                Log.d(TAG, "showRootPath rootText $rootText")
                mRootPathButton?.text = rootText
                mRootPathButton?.tag = DropTag(-1, DropTag.Type.TOOLBAR_MENU)
                setTextColorForRootPathButton()
                mRootPathButton?.typeface = TypefaceUtil.mediumTypeface
                onFocusTextChangeCallback()
                mScrollView?.importantForAccessibility = View.IMPORTANT_FOR_ACCESSIBILITY_NO
            }
        } ?: run {
            Log.i(TAG, "showRootPath pathHelper null")
        }
        return this
    }

    fun setOnPathClickListener(listener: OnPathClickListener?): BrowserPathBar {
        mOnPathClickListener = listener
        return this
    }

    private fun onFocusTextChangeCallback() {
        mOnFocusChangeListener?.onFocusChange(mRootPathButton?.text.toString())
    }


    fun setTextFocusChangeListener(listener: OnTextFocusColorChangeListener): BrowserPathBar {
        mOnFocusChangeListener = listener
        onFocusTextChangeCallback()
        return this
    }

    /**
     * void
     */
    private fun onPathVisibility() {
        Log.d(TAG, "onPathVisibility isNeedScroll $isNeedScroll")
        if (isNeedScroll) {
            if (mSettings.isSupportGradient && mLeftGradientImg != null) {
                setVisibility(mLeftGradientImg, true)
                setVisibility(mRightGradientImg, false)
            }
            if (layoutDirection == View.LAYOUT_DIRECTION_RTL) {
                mScrollView?.scrollBy((mScrollView?.width ?: 0) - (mPathLayout?.width ?: 0), 0)
            } else {
                mScrollView?.scrollBy((mPathLayout?.width ?: 0) - (mScrollView?.width ?: 0), 0)
            }
        } else {
            if (mSettings.isSupportGradient && mLeftGradientImg != null) {
                setVisibility(mLeftGradientImg, false)
            }
        }
        mOnPathVisibilityListener?.onPathVisibility((mPathLayout?.childCount ?: 0) != 0)
    }


    fun setPathHelper(pathHelper: FilePathHelper?) {
        if (pathHelper != null) {
            mPathHelper = pathHelper
        }
    }


    private fun isRootPath(currentPath: String): Boolean {
        if (mCurrentRootPath == null) return true
        return if (TextUtils.isEmpty(currentPath) || (currentPath.length > mCurrentRootPath!!.length)) {
            false
        } else currentPath == mCurrentRootPath
    }


    private fun getString(stringId: Int): String {
        return mBrowserContext.getString(stringId)
    }


    private fun isSameRoot(firstPath: String, otherPath: String?): Boolean {
        return if (TextUtils.isEmpty(firstPath) || TextUtils.isEmpty(otherPath)) {
            false
        } else {
            KtUtils.getStorageByPath(appContext, firstPath) == KtUtils.getStorageByPath(appContext, otherPath)
        }
    }

    private fun getColor(colorId: Int): Int {
        return appContext.resources.getColor(colorId, null)
    }

    private fun getDimension(textSize: Int): Float {
        return appContext.resources.getDimension(textSize)
    }

    private fun setFocusColor(index: Int) {
        Log.d(TAG, "setFocusColor index= $index")
        if (index < 0) {
            setTextColorForRootPathButton()
            onFocusTextChangeCallback()
            return
        }
        var child: Button? = null
        val count = (mPathLayout?.childCount ?: 0)
        for (i in 0 until count) {
            if (i % SIZE_ONE_PATH_AND_ONE_ARROW == 0) {
                try {
                    child = mPathLayout?.getChildAt(i) as? Button
                } catch (e: Exception) {
                    Log.e(TAG, e.message)
                }
                if (i == index * SIZE_ONE_PATH_AND_ONE_ARROW) {
                    if (child != null) {
                        mOnFocusChangeListener?.onFocusChange(child.text.toString())
                        ThemeColorUtils.tintTextView(child)
                    }
                } else {
                    child?.setTextColor(getColor(mSettings.textNormalColor))
                }
            }
        }
    }

    fun show() {
        // do nothing , just a flag
    }

    private fun getArrowAutoMirrored(resId: Int): Drawable {
        val arrow = resources.getDrawable(resId, null)
        arrow.isAutoMirrored = true
        return arrow
    }

    private fun addChildView(name: String, needAnim: Boolean, addImgBefore: Boolean) {
        if (TextUtils.isEmpty(name)) {
            return
        }
        if (mPathHelper?.shouldDisableRootButton() == true) {
            setDisableTextColorForView(mRootPathButton)
        } else {
            mRootPathButton?.setTextColor(getColor(mSettings.textNormalColor))
        }
        //文字前面的箭头
        val childImgBefore = if (addImgBefore) ImageView(mBrowserContext) else null
        val imgParamsBefore = LinearLayout.LayoutParams(imgSize, imgSize)
        imgParamsBefore.marginStart = imgMarginStart
        imgParamsBefore.gravity = Gravity.CENTER_VERTICAL
        childImgBefore?.layoutParams = imgParamsBefore
        childImgBefore?.setImageDrawable(getArrowAutoMirrored(mSettings.middleImg))
        childImgBefore?.isEnabled = false
        //中间的文字
        val childBtn = Button(mBrowserContext, null, 0)
        childBtn.isAllCaps = false
        childBtn.typeface = TypefaceUtil.mediumTypeface
        childBtn.setTextSize(TypedValue.COMPLEX_UNIT_PX, getDimension(mSettings.childTextSize))
        childBtn.setBackgroundColor(getColor(mSettings.childBackgroundColor))
        childBtn.background = null
        childBtn.minWidth = mMinWidth
        childBtn.setPadding(mBtnPadding, 0, mBtnPadding, 0)
        val btnParams = LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        )
        btnParams.gravity = Gravity.CENTER_VERTICAL
        childBtn.layoutParams = btnParams
        childBtn.gravity = Gravity.CENTER
        childBtn.setOnClickListener(OnClickListener {
            if (!mCanClick) {
                return@OnClickListener
            }
            val index = (mPathLayout?.indexOfChild(childBtn) ?: -1)
            if ((index < 0) || (index >= mPathLayout!!.childCount - SIZE_ONE_PATH_AND_ONE_ARROW)) {
                return@OnClickListener
            }
            val pathIndex = index / SIZE_ONE_PATH_AND_ONE_ARROW
            mOnPathClickListener?.onPathClick(pathIndex + 1, getClickPath(pathIndex))
        })
        childBtn.text = name
        if (childImgBefore != null) {
            mPathLayout?.addView(childImgBefore)
        }
        childBtn.tag = DropTag(-1, DropTag.Type.TOOLBAR_MENU)
        mPathLayout?.addView(childBtn)
        if (needAnim) {
            childBtn.alpha = 0F
            childImgBefore?.alpha = 0F
            childBtn.animate().alpha(1F).setDuration(FILE_BROWSER_FOLDER_ANIM_TIME).start()
            childImgBefore?.let {
                it.animate().alpha(1F).setDuration(FILE_BROWSER_FOLDER_ANIM_TIME).start()
            }
        }
    }

    private fun getClickPath(childIndex: Int): String {
        if (TextUtils.isEmpty(mCurrentPath)) {
            return ""
        }
        if (mCurrentPath!!.startsWith(mCurrentRootPath!!) && mCurrentPath!!.length > mCurrentRootPath!!.length) {
            val suffixPath = mCurrentPath!!.replace(mCurrentRootPath!! + File.separator, "")
            val sb = StringBuffer()
            sb.append(mCurrentRootPath).append(File.separator)
            val pathArr = suffixPath.split(File.separator.toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()

            if (childIndex >= pathArr.size) {
                return ""
            }

            for (i in 0..childIndex) {
                sb.append(pathArr[i])
                if (i != childIndex) {
                    sb.append(File.separator)
                }
            }
            Log.d(TAG, "getClickPath = $sb")
            return sb.toString()
        } else {
            return ""
        }
    }

    override fun removeAllViews() {
        mPathLayout?.removeAllViews()
        addOnGlobalLayoutListener()
    }

    private fun removeViewFromEnd(size: Int, removeAll: Boolean = false, needAnim: Boolean) {
        if (size > 0) {
            val count = (mPathLayout?.childCount ?: 0)
            val removeCount = if (count == 1 && size == 1) {
                1
            } else {
                if (removeAll) {
                    count
                } else {
                    (size * SIZE_ONE_PATH_AND_ONE_ARROW).let {
                        if (it > count) count else it
                    }
                }
            }
            Log.d(TAG, "removeViewByEnd count = $count, size = $size, removeCount $removeCount, needAnim=$needAnim")
            if (count >= removeCount) {
                if (needAnim) {
                    for (index in (count - removeCount) until count) {
                        try {
                            val view: View? = mPathLayout?.getChildAt(index)
                            val animation = view?.animate()
                            animation?.alpha(0F)?.setDuration(FILE_BROWSER_FOLDER_ANIM_TIME)?.setListener(
                                    object : Animator.AnimatorListener {
                                        override fun onAnimationRepeat(p0: Animator) {}
                                        override fun onAnimationEnd(p0: Animator) {
                                            animation.setListener(null)
                                            mPathLayout?.removeView(view)
                                            addOnGlobalLayoutListener()
                                        }

                                        override fun onAnimationCancel(p0: Animator) {}
                                        override fun onAnimationStart(p0: Animator) {}
                                    }
                            )?.start()
                        } catch (e: Exception) {
                            Log.e(TAG, e.message)
                        }
                    }
                } else {
                    try {
                        mPathLayout?.removeViews(count - removeCount, removeCount)
                    } catch (e: Exception) {
                        Log.e(TAG, e.message)
                    }
                }
            }
        }
    }

    fun setInPopWindow(isInPop: Boolean) {
        mIsInPop = isInPop
        if (Utils.isNightMode(mBrowserContext) && mIsInPop) {
            mLeftGradientImg?.setBackgroundResource(mSettings.popNightModeLeftGradient)
            mRightGradientImg?.setBackgroundResource(mSettings.popNightModeRightGradient)
        } else {
            mLeftGradientImg?.setBackgroundResource(mSettings.nightModeLeftGradient)
            mRightGradientImg?.setBackgroundResource(mSettings.nightModeRightGradient)
        }
    }

    fun interface OnPathClickListener {
        fun onPathClick(index: Int, path: String?)
    }

    fun updateLeftRightMargin() {
        val margin = FileImageVHUtils.getListLeftMargin()
        updateHorizontalMargin(margin)
    }

    fun updateHorizontalMargin(@Px margin: Int) {
        mRootView?.updateLayoutParams<LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }

    fun interface OnTextFocusColorChangeListener {
        fun onFocusChange(currentFocusText: String)
    }

    fun setSpecailPathProcess(pathProcess: ISpecialPathProcess) {
        specialPathProcess = pathProcess
    }

    interface ISpecialPathProcess {

        /**
         * 判断整体路径是否需要特殊替换处理
         */
        fun shouldProcessSpecial(wholePath: String): Boolean

        /**
         * 对路径进行特殊处理
         */
        fun getVisibleStringForSeparatePath(wholePath: String, separateList: Array<String>, index: Int): String
    }


    class NormalPathProcess : ISpecialPathProcess {
        override fun shouldProcessSpecial(wholePath: String): Boolean {
            return false
        }

        override fun getVisibleStringForSeparatePath(
            wholePath: String,
            separateList: Array<String>,
            index: Int
        ): String {
            return separateList[index]
        }
    }


    /**
     * 根据inputPath，将path中的每条分节都转换为需要显示的字符串
     * @param inputPath 输入的全部路径
     * @param containRoots 是否需要将root路径也拆分
     */
    private fun getConvertPathArray(inputPath: String, orignalPathItems: Array<String>): Array<String> {
        //判断是否需要转换，需要转换的则调用转换函数
        val specailProcess = if (specialPathProcess != null) specialPathProcess else normalPathProcess
        val lastNames = if (specailProcess != null) {
            convertSpecailPath(inputPath, orignalPathItems, specailProcess)
        } else {
            orignalPathItems
        }
        return lastNames
    }


    /**
     * 这个函数完成每个分离的path中变为需要替换的文件名称
     */
    private fun convertSpecailPath(wholePath: String, separateList: Array<String>, delegate: ISpecialPathProcess): Array<String> {
        if (delegate.shouldProcessSpecial(wholePath)) {
            Log.d(TAG, "convertSpecailPath process Specail ${separateList.toList()}")
            val convertList = separateList.mapIndexed { index, _ ->
                val convertItem = delegate.getVisibleStringForSeparatePath(wholePath, separateList, index)
                convertItem
            }
            return convertList.toTypedArray()
        } else {
            return separateList
        }
    }

    /**
     * 远程mac的第一个index中的需要置灰
     */
    private fun setTextColorForRootPathButton() {
        Log.d(TAG, "setTextColorForRootPathButton mCurrentRootPath $mCurrentRootPath")
        if (mPathHelper?.shouldDisableRootButton() == true) {
            Log.d(TAG, "setTextColorForRootPathButton disable color")
            setDisableTextColorForView(mRootPathButton)
        } else {
            ThemeColorUtils.tintTextView(mRootPathButton)
        }
    }


    private fun setDisableTextColorForView(view: TextView?) {
        view?.let {
            val context = it.context
            //这个颜色后续根据UX提供的色值修改，现在只用来区分一下
            it.setTextColor(COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorLabelTertiary))
        }
    }
}