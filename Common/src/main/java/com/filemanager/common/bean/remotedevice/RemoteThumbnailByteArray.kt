/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: RemoteThumbnailByteArray.kt
 ** Description: RemoteThumbnailByteArray
 ** Version: 1.0
 ** Date : 2024/12/11
 ** Author: 80241271
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.bean.remotedevice

import androidx.annotation.Keep
import java.io.Serializable

@Keep
data class RemoteThumbnailByteArray(
    val mBitmapByteArray: ByteArray? = null,
) : Serializable {

    companion object {

        private const val serialVersionUID: Long = -1413074439402870648L

        const val TAG = "RemoteThumbnailByteArray"
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as RemoteThumbnailByteArray

        if (mBitmapByteArray != null) {
            if (other.mBitmapByteArray == null) return false
            if (!mBitmapByteArray.contentEquals(other.mBitmapByteArray)) return false
        } else if (other.mBitmapByteArray != null) return false

        return true
    }

    override fun hashCode(): Int {
        return mBitmapByteArray?.contentHashCode() ?: 0
    }
}