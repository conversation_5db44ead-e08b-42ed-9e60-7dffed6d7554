/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ThirdAppSearchUtil
 ** Description : 判断依赖方是否支持功能的工具类
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <PERSON><EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.content.pm.PackageManager
import android.os.Bundle
import android.util.Log
import com.oplus.viewextract.ViewExtractSDK

object ThirdAppSearchUtil {

    const val TAG = "ThirdAppSearchUtil"
    //场景智能meta-data的key
    const val SUPPORT_APPSWITCH_KEY = "oplus.sceneservice.ability.appswitch.version"

    const val SCENESERVICE_SWITCH_AUTHORITY = "com.oplus.sceneservice.lightprovider"

    const val SCENESERVICE_SWITCH_METHOD = "method_get_service_on_off"

    const val SCENESERVICE_SWITCH_KEY = "get_service_on_off"
    //场景智能包名
    const val SCENESERVICE_PACKAGENAME = "com.coloros.sceneservice"
    //中子dmp包名
    const val DMP_PACKAGENAME = "com.oplus.dmp"
    //场景智能支持通过provider或broadcast方式通知业务方页面切换的版本号
    const val SCENESERVICE_APPSWITCH_SUPPORT_VERSION_PROVIDER = 2
    //场景智能支持通过bind service方式通知业务方页面切换的版本号
    const val SCENESERVICE_APPSWITCH_SUPPORT_VERSION_SERVICE = 1
    //场景智能不支持通知业务方
    const val SCENESERVICE_APPSWITCH_SUPPORT_VERSION_NOSUPPORT = 0

    /**
     * OS13.0上面找不到这个类，这里判定系统是否支持图文提取框架
     */
    @JvmStatic
    fun isViewExtractSupported(): Boolean {
        kotlin.runCatching {
            ViewExtractSDK::class.java.toString()
        }.onSuccess {
            Log.d(TAG, "isViewExtractSupported true")
            return true
        }.onFailure {
            Log.w(TAG, "isViewExtractSupported false")
        }
        return false
    }


    /**
     * 检测场景智能是否具备oplus.sceneservice.ability.appswitch.version = 2，==2明支持contentprovider的方式拉起文管， ！=2表明不支持拉起文管。
     */
    @JvmStatic
    fun checkSmartSceneSupportAppSwitch(context: Context): Boolean {
        var result = false
        try {
            val pm = context.packageManager
            val applicationInfo =
                pm.getApplicationInfo(SCENESERVICE_PACKAGENAME, PackageManager.GET_META_DATA)
            val metaValue = applicationInfo.metaData.getInt(SUPPORT_APPSWITCH_KEY)
            result = (metaValue == SCENESERVICE_APPSWITCH_SUPPORT_VERSION_PROVIDER)
            Log.i(TAG, "checkSmartSceneSupportAppSwitch metaValue $metaValue, result $result")
        } catch (e: PackageManager.NameNotFoundException) {
            Log.e(TAG, "checkSmartSceneSupportAppSwitch error", e)
        }
        return result
    }


    /**
     * 检测 景智能有两种方式可以撤回隐私权限
     * 目前，场景智能有两种方式可以撤回隐私权限：
     * 1、关闭增强服务开关，路径：设置-其他设置-增强服务-智慧数据增强服务；
     * 2、清除场景智能的数据，清除后，在个人信息与偏好界面会拉起场景智能的隐私权限弹窗。
     */
    @JvmStatic
    fun checkSmartSceneSwitchKeyOn(context: Context): Boolean {
        var result = false
        runCatching {
            val bundle = Bundle()
            val resultBundle: Bundle? = context.contentResolver.call(
                SCENESERVICE_SWITCH_AUTHORITY,
                SCENESERVICE_SWITCH_METHOD,
                null,
                bundle
            )
            if (resultBundle != null) {
                val resultString = resultBundle.getString(SCENESERVICE_SWITCH_KEY, "")
                if (resultString.contentEquals("true", true)) {
                    result = true
                }
                Log.i(TAG, "checkSmartSceneSupportSwitchKeyOn resultString $resultString, result $result")
            }
        }.onFailure {
            Log.e(TAG, "checkSmartSceneSupportSwitchKeyOn error", it)
        }
        Log.i(TAG, "checkSmartSceneSupportSwitchKeyOn result: $result")
        return result
    }
}