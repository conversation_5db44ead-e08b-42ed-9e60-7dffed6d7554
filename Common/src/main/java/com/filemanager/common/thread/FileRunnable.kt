/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/4/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thread

open class FileRunnable : Runnable {
    var mRunnableName: String = ""
    var mRunnable: Runnable
    var mTag: String
    var mRecycler: ((key: String) -> Unit)?

    constructor(runnable: Runnable, tag: String, recycle: ((key: String) -> Unit)? = {}) {
        mRunnable = runnable
        mTag = tag
        mRecycler = recycle
    }

    override fun run() {
        Thread.currentThread().name = mRunnableName
        mRunnable.run()
    }
}