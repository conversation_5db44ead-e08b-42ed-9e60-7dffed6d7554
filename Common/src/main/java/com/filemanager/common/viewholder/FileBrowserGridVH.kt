/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.viewholder
 * * Version     : 1.0
 * * Date        : 2020/9/16
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.core.animation.doOnEnd
import com.coui.appcompat.checkbox.COUICheckBox
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.HighlightUtil
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.LabelVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.MiddleMultilineTextView
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.thumbnail.ThumbnailManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

class FileBrowserGridVH(convertView: View) : BaseFileBrowserVH(convertView) {
    companion object {
        private const val TAG = "FileBrowserGridVH"
        private const val SECOND = 1000L
        private const val ALPHA = 31
        private const val ANIM_DURATION = 750L
        private const val RADIUS = 20f
        fun getLayoutId(): Int {
            return R.layout.file_browser_scan_grid_item
        }
    }

    private val mImg: FileThumbView = convertView.findViewById(R.id.file_grid_item_icon)
    private val mTitle: MiddleMultilineTextView = convertView.findViewById(R.id.title_tv)
    private val mDetail: TextView = convertView.findViewById(R.id.detail_tv)
    private val mRootView: RelativeLayout = convertView.findViewById(R.id.file_grid_item_layout)
    private var mDuration: TextView = convertView.findViewById(R.id.file_duration_tv)
    private var apkIcon: ImageView = convertView.findViewById(R.id.apk_icon)
    private var mIconMarginBottom: Int
    private var externalPath: String? = null

    private val mImgRadius = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.scan_grid_bg_radius)
    private val docRadius = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.dimen_4dp)
    var loadDocThumbnail = true

    init {
        mCheckBox = convertView.findViewById(R.id.gridview_scrollchoice_checkbox)
        mIconMarginBottom = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_icon_margin_bottom)
    }

    override fun updateViewHolder(context: Context, key: Int?, file: BaseFileBean, choiceMode: Boolean, selectionArray: MutableList<Int>
                                  , sizeCache: HashMap<String, String>, threadManager: ThreadManager, adapter: BaseSelectionRecycleAdapter<*, *>) {
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        if (choiceMode) {
            mRootView.setBackgroundResource(R.drawable.file_browser_scan_grid_item_bg)
        } else {
            mRootView.background = null
        }
        mTitle.tag = path
        mDetail.tag = path
        file.mDisplayName?.apply {
            mTitle.text = this
        }
        itemView.addOnLayoutChangeListener(object :View.OnLayoutChangeListener{
            override fun onLayoutChange(v: View?, l: Int, t: Int, r: Int, b: Int,
                                        oL: Int, oT: Int, oR: Int, oB: Int
            ) {
                itemView.removeOnLayoutChangeListener(this)
                file.mDisplayName?.apply {
                    mTitle.setMultiText(this)
                }
            }
        })
        LabelVHUtils.displayLabelFlag(file, context, mTitle)
        FileImageVHUtils.updateFileGridImgSize(context, mImg, file)
        val layoutParams = mImg.layoutParams as RelativeLayout.LayoutParams
        layoutParams.bottomMargin = mIconMarginBottom
        mImg.setStrokeStyle(FileThumbView.STROKE_NONE)
        if (type == MimeTypeHelper.DRM_TYPE) {
            mImg.setDrmState(true)
            val typeString = MimeTypeHelper.getDrmMimeType(context, path)
            if (!TextUtils.isEmpty(typeString) && (typeString!!.startsWith("video/") || typeString.startsWith("image/"))) {
                mImg.setStrokeStyle(FileThumbView.STROKE_4DP)
                mImg.scaleType = ImageView.ScaleType.FIT_XY
            } else {
                mImg.scaleType = ImageView.ScaleType.FIT_CENTER
            }
        } else {
            mImg.setDrmState(false)
            when {
                (type == MimeTypeHelper.IMAGE_TYPE) || (type == MimeTypeHelper.VIDEO_TYPE) -> {
                    mImg.setStrokeStyle(FileThumbView.STROKE_4DP)
                    mImg.scaleType = ImageView.ScaleType.FIT_XY
                }
                (MimeTypeHelper.isDocType(type) && ThumbnailManager.isDocThumbnailSupported(context)) -> {
                    if (MimeTypeHelper.PPT_TYPE == type || type == MimeTypeHelper.PPTX_TYPE) {
                        mImg.scaleType = ImageView.ScaleType.FIT_END
                    } else {
                        mImg.scaleType = ImageView.ScaleType.FIT_CENTER
                    }
                }
                (type == MimeTypeHelper.APPLICATION_TYPE) -> {
                    layoutParams.bottomMargin = 0
                    mImg.scaleType = ImageView.ScaleType.FIT_CENTER
                }
                (type == MimeTypeHelper.DIRECTORY_TYPE) -> {
                    layoutParams.bottomMargin = 0
                    mImg.scaleType = ImageView.ScaleType.FIT_END
                }
                else -> {
                    mImg.scaleType = ImageView.ScaleType.FIT_CENTER
                }
            }
        }
        if (type == MimeTypeHelper.APPLICATION_TYPE) {
            FileImageLoader.sInstance.clear(context, mImg)
            mImg.mErrorLoadTimes = 0
            mImg.setCallBack(object : FileThumbView.LoadCallBack {
                override fun onError() {
                    displayApplicationWithDetail(context, file, path, sizeCache, threadManager)
                }
            })
            displayApplicationWithDetail(context, file, path, sizeCache, threadManager)
        } else {
            val radius = ViewUtils.getIconRadius(type, docRadius, mImgRadius)
            FileImageLoader.sInstance.clear(context, mImg)
            FileImageLoader.sInstance.displayDefault(
                file,
                mImg,
                0,
                radius,
                FileImageLoader.THUMBNAIL_TYPE_SQUARE,
                loadDocThumbnail,
                isCoverError = true,
                isSmallDoc = true
            )
            mDetail.visibility = View.VISIBLE
            showDetail(context, file, mDetail, path, sizeCache, threadManager, false)
        }
        mCheckBox?.let {
            if (choiceMode) {
                if (selectionArray.contains(key)) {
                    it.state = COUICheckBox.SELECT_ALL
                    it.visibility = View.VISIBLE
                } else {
                    it.isEnabled = false
                    it.state = COUICheckBox.SELECT_NONE
                    it.visibility = View.VISIBLE
                    it.isEnabled = true
                }
            } else {
                it.state = COUICheckBox.SELECT_NONE
                it.jumpDrawablesToCurrentState()
                it.visibility = View.GONE
            }
        }
        val alpha = HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, adapter.mIsDarkModel)
        mTitle.alpha = alpha
        mDetail.alpha = alpha
        mImg.alpha = alpha
        if (file.mLocalType == MimeTypeHelper.VIDEO_TYPE && (file as? PathFileWrapper)?.mMediaDuration == 0L) {
            GlobalScope.launch(Dispatchers.IO) {
                (file as? PathFileWrapper)?.getMediaDuration()
                withContext(Dispatchers.Main) {
                    showDurationIfNeed(file, mDuration)
                }
            }
        } else {
            showDurationIfNeed(file, mDuration)
        }
        showApkIconIfNeed(file, apkIcon, true)
        HighlightUtil.addItemHighlight(context, this@FileBrowserGridVH, bindingAdapterPosition)
        if (bindingAdapterPosition == 0 && externalPath != null) {
            animateBackground(mRootView)
            externalPath = null
        }
    }

    private fun displayApplicationWithDetail(
        context: Context,
        file: BaseFileBean,
        path: String,
        sizeCache: HashMap<String, String>,
        threadManager: ThreadManager
    ) {
        FileImageLoader.sInstance.displayApplicationWithDetail(file, mImg,
            { applicationInfoDetail ->
                val tagDetail = mDetail.tag as? String
                if (applicationInfoDetail?.mPath == tagDetail && !TextUtils.isEmpty(applicationInfoDetail?.mApkVersion)) {
                    mDetail.visibility = View.VISIBLE
                    mDetail.text = KtUtils.formatSize(file)
                } else {
                    showDetail(context, file, mDetail, path, sizeCache, threadManager, false)
                }
            }, isGridMode = true)
    }

    override fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        val checkBoxRect = Rect()
        itemView.getGlobalVisibleRect(checkBoxRect)
        return checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }

    private fun showDurationIfNeed(file: BaseFileBean, mDuration: TextView) {
        if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType && file.mMediaDuration != 0L) {
            mDuration.visibility = View.VISIBLE
            mDuration.text = KtUtils.formatVideoTime(file.mMediaDuration / SECOND)
        } else {
            mDuration.visibility = View.GONE
        }
    }

    private fun animateBackground(view: View) {
        val originalColor = (view.background as? ColorDrawable)?.color ?: Color.TRANSPARENT
        val targetColor = Color.argb(ALPHA, 0, 0, 0) // #000000 透明度12%
        // 创建圆角矩形形状
        val shape = GradientDrawable().apply {
            cornerRadius = RADIUS // 设置圆角半径为20px
            setColor(originalColor)
        }
        // 设置初始背景
        view.background = shape
        // 创建颜色渐变动画
        ValueAnimator.ofObject(ArgbEvaluator(), originalColor, targetColor).apply {
            duration = ANIM_DURATION
            addUpdateListener { animator ->
                shape.setColor(animator.animatedValue as Int)
                view.background = shape
            }
            doOnEnd {
                // 创建反向颜色渐变动画
                ValueAnimator.ofObject(ArgbEvaluator(), targetColor, originalColor).apply {
                    duration = ANIM_DURATION
                    addUpdateListener { animator ->
                        shape.setColor(animator.animatedValue as Int)
                        view.background = shape
                    }
                }.start()
            }
        }.start()
    }

    fun setExternalPath(externalPath: String?) {
        this.externalPath = externalPath
    }
}