/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : AdSlotConstants
 * * Description : 广告资源常量
 * * Version     : 1.0
 * * Date        : 2025/03/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.constants

import com.filemanager.common.helper.CategoryHelper

object AdResourceConstants {

    /**
     * 广告位的请求id
     * <a href="https://odocs.myoas.com/sheets/NJkbEZd6rvc9VBqR/CGgeU?la">查看</a>
     */
    const val REQUEST_ID_OPPO_HOME = "92863"
    const val REQUEST_ID_REALME_HOME = "160559"
    const val REQUEST_ID_OPPO_SUB = "92865"
    const val REQUEST_ID_REALME_SUB = "160561"
    const val REQUEST_ID_SPLASH_COLD = "1004773" // 冷启动开屏广告
    const val REQUEST_ID_SPLASH_HOT = "1004860" // 热启动的开屏广告

    private const val APK_PAGE_PREFIX = "ApkFragment"
    private const val DOC_PAGE_PREFIX = "DocumentFragment"
    private const val AUDIO_PAGE_PREFIX = "CategoryAudioFragment"
    private const val VIDEO_PAGE_PREFIX = "CategoryVideoFragment"


    fun getPage(type: Int): String {
        if (CategoryHelper.isSuperAppType(type)) {
            return Constants.PAGE_SUPER
        } else if (CategoryHelper.isShortcutFolderType(type)) {
            return Constants.PAGE_SHORTCUT_FOLDER
        } else if (CategoryHelper.isLabelType(type)) {
            return Constants.PAGE_LABEL
        } else if (CategoryHelper.isRemoteMacDeviceType(type)) {
            return Constants.PAGE_REMOTE_FILE_LIST
        }
        return when (type) {
            CategoryHelper.CATEGORY_RECYCLE_BIN -> Constants.PAGE_RECYCLE_BIN
            CategoryHelper.CATEGORY_DFM -> Constants.PAGE_DFM
            CategoryHelper.CATEGORY_FILE_BROWSER -> Constants.PAGE_FILE_BROWSER
            CategoryHelper.CATEGORY_SDCARD_BROWSER -> Constants.PAGE_SD_CARD
            CategoryHelper.CATEGORY_OTG_BROWSER -> Constants.PAGE_OTG
            CategoryHelper.CATEGORY_RECENT -> Constants.PAGE_RECENT
            CategoryHelper.CATEGORY_IMAGE -> Constants.PAGE_IMAGE
            CategoryHelper.CATEGORY_PAGE_ALBUM_SET -> Constants.PAGE_IMAGE_SET
            CategoryHelper.CATEGORY_VIDEO -> Constants.PAGE_VIDEO
            CategoryHelper.CATEGORY_AUDIO -> Constants.PAGE_AUDIO
            CategoryHelper.CATEGORY_DOC -> Constants.PAGE_DOC
            CategoryHelper.CATEGORY_APK -> Constants.PAGE_APK
            CategoryHelper.CATEGORY_COMPRESS -> Constants.PAGE_COMPRESS
            CategoryHelper.CATEGORY_MAIN -> Constants.PAGE_MAIN
            CategoryHelper.CATEGORY_SPLASH -> Constants.PAGE_SPLASH
            else -> ""
        }
    }

    fun getPageId(name: String): Int {
        if (name.startsWith(APK_PAGE_PREFIX)) {
            return CategoryHelper.CATEGORY_APK
        }
        if (name.startsWith(DOC_PAGE_PREFIX)) {
            return CategoryHelper.CATEGORY_DOC
        }
        if (name.startsWith(AUDIO_PAGE_PREFIX)) {
            return CategoryHelper.CATEGORY_AUDIO
        }
        if (name.startsWith(VIDEO_PAGE_PREFIX)) {
            return CategoryHelper.CATEGORY_VIDEO
        }
        return when (name) {
            Constants.PAGE_SUPER -> CategoryHelper.CATEGORY_SOURCE_GROUP
            Constants.PAGE_SHORTCUT_FOLDER -> CategoryHelper.CATEGORY_FOLDER_GROUP
            Constants.PAGE_LABEL -> CategoryHelper.CATEGORY_LABEL_GROUP
            Constants.PAGE_REMOTE_FILE_LIST -> CategoryHelper.CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC
            Constants.PAGE_RECYCLE_BIN -> CategoryHelper.CATEGORY_RECYCLE_BIN
            Constants.PAGE_DFM -> CategoryHelper.CATEGORY_DFM
            Constants.PAGE_FILE_BROWSER -> CategoryHelper.CATEGORY_FILE_BROWSER
            Constants.PAGE_SD_CARD -> CategoryHelper.CATEGORY_SDCARD_BROWSER
            Constants.PAGE_OTG -> CategoryHelper.CATEGORY_OTG_BROWSER
            Constants.PAGE_RECENT -> CategoryHelper.CATEGORY_RECENT
            Constants.PAGE_IMAGE -> CategoryHelper.CATEGORY_IMAGE
            Constants.PAGE_IMAGE_SET -> CategoryHelper.CATEGORY_PAGE_ALBUM_SET
            Constants.PAGE_VIDEO -> CategoryHelper.CATEGORY_VIDEO
            Constants.PAGE_AUDIO -> CategoryHelper.CATEGORY_AUDIO
            Constants.PAGE_DOC -> CategoryHelper.CATEGORY_DOC
            Constants.PAGE_APK -> CategoryHelper.CATEGORY_APK
            Constants.PAGE_COMPRESS -> CategoryHelper.CATEGORY_COMPRESS
            Constants.PAGE_MAIN -> CategoryHelper.CATEGORY_MAIN
            Constants.PAGE_SPLASH -> CategoryHelper.CATEGORY_SPLASH
            Constants.PAGE_SPLASH_HOT -> CategoryHelper.CATEGORY_SPLASH
            else -> 0
        }
    }
}