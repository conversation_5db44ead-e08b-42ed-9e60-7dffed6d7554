/***********************************************************
 * * Copyright (C), 2020 - 2020, Oplus. All rights reserved.
 * * File: GlideLoader
 * * Description: the utils for glide to load image
 * * Version: 1.0
 * * Date : 2020/11/13
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>          <data>      <version >        <desc>
 * * <EMAIL>    2020/11/13       1.0         the loader for glide to load image
 ****************************************************************/
package com.filemanager.common.imageloader.glide

import android.app.Activity
import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.text.TextUtils
import android.widget.ImageView
import androidx.annotation.VisibleForTesting
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.signature.ObjectKey
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.imageloader.ImageLoaderInterface
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageLoader.Companion.VIDEO_FRAME_VALUE
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.view.FileThumbView
import org.apache.commons.io.FilenameUtils
import java.io.File

object GlideLoader : ImageLoaderInterface {
    private const val TAG = "GlideLoader"
    private var mMpgMaxSize = 0
    private var mListImageMaxSize = 0
    private var mGridImageMaxSize = 0
    private var mRealImageMaxSize = 0

    const val ROUND_CONNER_ALL = 0
    const val ROUND_CONNER_LEFT_TOP_ONLY = 1
    const val ROUND_CONNER_RIGHT_TOP_ONLY = 2
    const val ROUND_CONNER_LEFT_BOTTOM_ONLY = 3
    const val ROUND_CONNER_RIGHT_BOTTOM_ONLY = 4
    const val ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY = 5
    const val ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY = 6
    const val ROUND_CONNER_NONE = 7
    const val ROUND_CONNER_NONE_RECENT_IMAGE = 8

    override fun display(mContext: Context?, resourceId: Int?, radiusSize: Int, imageView: ImageView?) {
        if ((mContext == null) || (imageView == null)) {
            return
        }
        var options = RequestOptions()
        var width = imageView.width
        var height = imageView.height
        if ((width <= 0) || (height <= 0)) {
            val layoutParams = imageView.layoutParams
            width = layoutParams.width
            height = layoutParams.height
        }
        options = options.override(width, height)
        imageView.background = KtUtils.getDefaultImageBackground(radiusSize.toFloat())
        options = requestOptionsForRoundConner(options, ROUND_CONNER_ALL, radiusSize)
        Glide.with(mContext).load(resourceId).apply(options).into(imageView)
    }

    @Suppress("LongMethod")
    override fun display(
        mContext: Context?,
        baseFileBean: BaseFileBean?,
        radiusSize: Int,
        orientation: Int,
        imageView: ImageView?,
        isCoverError: Boolean,
        roundConnerType: Int,
        errorImageType: Int
    ) {
        if ((baseFileBean == null) || (mContext == null) || (imageView == null)) {
            return
        } else if (mContext is Activity && (mContext.isFinishing || mContext.isDestroyed)) {
            Log.d(TAG, "display ignore: activity is isDestroyed")
            return
        }
        var options = RequestOptions()
        var stringSignature = baseFileBean.mDateModified.toString() + baseFileBean.mSize + "" + Utils.isRtl() + roundConnerType
        if (orientation != 0) {
            stringSignature += orientation
        }
        options = options.diskCacheStrategy(DiskCacheStrategy.AUTOMATIC).signature(ObjectKey(stringSignature))
        if (mMpgMaxSize == 0) {
            mMpgMaxSize = mContext.resources.getDimensionPixelSize(R.dimen.glide_mpg_max_size)
        }
        if (mListImageMaxSize == 0) {
            mListImageMaxSize = mContext.resources.getDimensionPixelOffset(R.dimen.file_list_video_icon_width)
        }
        if (mGridImageMaxSize == 0) {
            mGridImageMaxSize = mContext.resources.getDimensionPixelSize(R.dimen.weixin_grid_icon_size)
            mRealImageMaxSize = mContext.resources.getDimensionPixelSize(R.dimen.glide_grid_real_max_size)
        }
        var drmPath: String? = null
        if (baseFileBean.mLocalType == MimeTypeHelper.DRM_TYPE) {
            drmPath = baseFileBean.mData
        }
        var extensionString = baseFileBean.mDisplayName
        if (TextUtils.isEmpty(extensionString)) {
            extensionString = baseFileBean.mData
        }
        val path = Uri.fromFile(File(baseFileBean.mData))
        val extension = FilenameUtils.getExtension(extensionString)
        /*
            video of .mpg type will be decode in Glide.Downsampler with BitmapFactory.decodeStream method, then will not be
            decode with MediaMetadataRetriever,To circumvent this problem we can override a large size (decodeFromWrappedStreams will throw and decode Process continues)
            to make program work fine.
        */
        /*
            video of .mpg type will be decode in Glide.Downsampler with BitmapFactory.decodeStream method, then will not be
            decode with MediaMetadataRetriever,To circumvent this problem we can override a large size (decodeFromWrappedStreams will throw and decode Process continues)
            to make program work fine.
         */
        if ((extension != null) && (extension.toLowerCase().endsWith("mpg") || extension.toLowerCase().endsWith("mpeg"))) {
            options = options.override(mMpgMaxSize)
        } else {
            var width = imageView.width
            var height = imageView.height
            if ((width <= 0) || (height <= 0)) {
                val layoutParams = imageView.layoutParams
                width = layoutParams.width
                height = layoutParams.height
            }
            val maxSize = width.coerceAtLeast(height)
            var ignore = true
            //Glide解码bmp格式图片时，解码大小小于ImageView宽高时，解码出的图片显示异常，在此跳过RequestOptions.override()方法调用
            if(baseFileBean.mData != null && baseFileBean.mData!!.endsWith(".bmp",true)){
                ignore = false
            }
            if (maxSize in 1..mListImageMaxSize) {
                options = buildOverride(options, mListImageMaxSize, width, height, false)
            } else if (maxSize in 1..mGridImageMaxSize) {
                options = buildOverride(options, mRealImageMaxSize, width, height, ignore)
            }
        }

        if (radiusSize != 0) {
            var realWidth = imageView.width
            if (realWidth <= 0) {
                realWidth = imageView.layoutParams.width
            }
            val newRadiusSize = if (realWidth > 0 && options.overrideWidth > 0) {
                options.overrideWidth * radiusSize / realWidth
            } else {
                radiusSize
            }
            options = requestOptionsForRoundConner(options, roundConnerType, newRadiusSize)
        } else {
            options = options.transform(CenterCrop())
        }
        imageView.tag = null
        imageView.setTag(R.id.glide_tag_id, path.toString())
        if (isCoverError) {
            val errorResId = when (baseFileBean.mLocalType) {
                MimeTypeHelper.IMAGE_TYPE ->
                    if (errorImageType == FileImageLoader.ERROR_IMAGE_NORMAL_GRID) {
                        R.drawable.error_img
                    } else {
                        R.drawable.ic_file_image
                    }
                MimeTypeHelper.VIDEO_TYPE ->
                    if (errorImageType == FileImageLoader.ERROR_IMAGE_NORMAL_GRID) {
                        R.drawable.error_img
                    } else {
                        R.drawable.ic_file_video
                    }
                MimeTypeHelper.AUDIO_TYPE -> R.drawable.ic_file_audio
                else -> null
            }
            val placeholderResId = R.drawable.thumbnail_load_default_place_holder
            options = if (errorResId == null) {
                options.placeholder(placeholderResId)
            } else {
                (imageView as? FileThumbView)?.setStrokeStyle(FileThumbView.STROKE_NONE)
                options.placeholder(errorResId).error(errorResId)
            }
        } else {
            val placeholderDrawable = if (radiusSize != 0) {
                KtUtils.getDefaultImageBackground(radiusSize.toFloat())
            } else {
                ColorDrawable(mContext.getColor(R.color.color_text_ripple_bg_color))
            }
            options = options.placeholder(placeholderDrawable)
        }
        options.frame(VIDEO_FRAME_VALUE)
        try {
            val loadBuilder = Glide.with(mContext).asBitmap()
            if (TextUtils.isEmpty(drmPath)) {
                loadBuilder.load(path)
            } else {
                loadBuilder.load(DrmCover(drmPath))
            }
            loadBuilder.apply(options).into(BitmapViewTarget(imageView, path.toString()))
        } catch (e: Exception) {
            Log.w(TAG, "display: Glide with " + e.message)
        }
    }

    @VisibleForTesting
    fun requestOptionsForRoundConner(
        options: RequestOptions,
        roundConnerType: Int,
        newRadiusSize: Int
    ): RequestOptions {
        var requestOptions = options
        requestOptions = when (roundConnerType) {
            ROUND_CONNER_LEFT_TOP_ONLY -> {
                requestOptions.transform(RoundedCornersBitmapTransformation(newRadiusSize.toFloat(),  Utils.isRtl(), true, false, false, false))
            }
            ROUND_CONNER_RIGHT_TOP_ONLY -> {
                requestOptions.transform(RoundedCornersBitmapTransformation(newRadiusSize.toFloat(), Utils.isRtl(), false, true, false, false))
            }
            ROUND_CONNER_LEFT_BOTTOM_ONLY -> {
                requestOptions.transform(RoundedCornersBitmapTransformation(newRadiusSize.toFloat(), Utils.isRtl(), false, false, true, false))
            }
            ROUND_CONNER_RIGHT_BOTTOM_ONLY -> {
                requestOptions.transform(RoundedCornersBitmapTransformation(newRadiusSize.toFloat(), Utils.isRtl(), false, false, false, true))
            }
            ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY -> {
                requestOptions.transform(RoundedCornersBitmapTransformation(newRadiusSize.toFloat(), Utils.isRtl(), true, false, true, false))
            }
            ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY -> {
                requestOptions.transform(RoundedCornersBitmapTransformation(newRadiusSize.toFloat(), Utils.isRtl(), false, true, false, true))
            }
            ROUND_CONNER_NONE -> {
                requestOptions.transform(CenterCrop())
            }
            else -> {
                requestOptions.transform(RoundedCornersBitmapTransformation(newRadiusSize.toFloat(), Utils.isRtl(), true, true, true, true))
            }
        }
        return requestOptions
    }

    fun buildOverride(options: RequestOptions, targetLength: Int, width: Int, height: Int, ignore: Boolean): RequestOptions {
        return if (ignore || (width in 1 until targetLength) && (height > 0) && (height < targetLength)) {
            // bugfix 2982810: make sure image width and length is the same
            options.override(targetLength, targetLength)
        } else {
            options
        }
    }

    override fun display(context: Context?, uri: Uri?, imageView: ImageView?) {
        if ((context == null) || (imageView == null) || (uri == null)) {
            return
        }
        var options = RequestOptions()
        val radius =
            context.resources.getDimensionPixelOffset(R.dimen.drag_shadow_layout_background_radius)
        val iconWidth =
            context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_one_height)
        val iconHeight =
            context.resources.getDimensionPixelOffset(R.dimen.img_drag_shadow_one_height)
        options = options.override(iconWidth, iconHeight)
        options = requestOptionsForRoundConner(options, ROUND_CONNER_ALL, radius)
        options.frame(VIDEO_FRAME_VALUE)
        Glide
            .with(context)
            .asBitmap()
            .load(uri)
            .apply(options)
            .into(imageView)
    }
}