/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/28, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.action

import android.app.Activity
import android.os.Environment
import android.view.DragEvent
import com.filemanager.common.R
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.dragselection.MacDragUtil
import com.filemanager.common.dragselection.MacDragUtil.Companion.parseFiles
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.utils.COUISnackBarUtils
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.oplus.filemanager.interfaze.filebrowser.IFileBrowser
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import com.oplus.filemanager.interfaze.main.IMain
import java.io.File

object DropSourceAction : DropListener {

    private const val TAG = "DropSourceAction"

    /**
     * 内外部拖拽数据，都是复制策略
     */
    override fun handleDropAction(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Boolean {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem $dropSideItem categoryType $targetCategoryType")

        val parseData = DropUtil.parseDragFile(dragEvent)
        val destPath = getDestFilePath(activity, targetCategoryType, dropSideItem)
        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            dragEvent.clipData?.apply {
                val onMacDownloadCallBack: (Boolean, ArrayList<RemoteFileBean>) -> Unit =
                    { result, _ ->
                        if (result) {
                            COUISnackBarUtils.show(
                                activity, com.filemanager.common.R.string.drag_in_success
                            ) {
                                val fileBrowser = Injector.injectFactory<IFileBrowser>()
                                fileBrowser?.toFileBrowserActivity(activity, destPath)
                            }
                            Log.d(TAG, "onDragMacResult true")
                        } else {
                            //处理下载失败
                            Log.d(TAG, "onDragMacResult false")
                        }
                    }
                parseFiles(
                    activity = activity,
                    path = destPath,
                    onMacDownloadCallBack = onMacDownloadCallBack,
                    description = description
                )
            }
            return true
        }
        //内部文件，复制
        if (dragTag != null) {
            Injector.injectFactory<IFileOperateApi>()?.copyMediaFile(activity, parseData, destPath)
            return true
        }

        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                CustomToast.showShort(com.filemanager.common.R.string.drag_cloud_not_support_position)
                Log.d(TAG, "drag out media and private files, return!")
                return false
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
                return false
            }
            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                Injector.injectFactory<IFileOperateApi>()?.copyMediaFile(activity, data, destPath)
                return true
            }
            return false
        }

        val privacyFileCallback = {
            Injector.injectFactory<IFileOperateApi>()?.saveFile(activity, parseData, destPath)
            true
        }
        //外部的媒体库文件，复制
        val mediaFileCallback = {
            Injector.injectFactory<IFileOperateApi>()?.copyMediaFile(activity, parseData, destPath)
            true
        }
        return DropUtil.checkIsMediaFiles(parseData, activity, mediaFileCallback, privacyFileCallback)
    }

    override fun getCornerMarkStateCode(
        dragEvent: DragEvent,
        dragTag: String?,
        targetCategoryType: Int,
        activity: Activity,
        dropSideItem: Boolean
    ): Int {
        Log.d(TAG, "handleDropAction dragTag $dragTag dropSideItem $dropSideItem categoryType $targetCategoryType")

        if (MacDragUtil.MacDragObject.isDraggingFromMac) {
            return DropDispatchAction.COPY_STATE
        }
        //内部文件，复制
        if (dragTag != null) {
            return DropDispatchAction.COPY_STATE
        }

        val parseData = DropUtil.parseDragFile(dragEvent)
        //从documentUI拖入
        val uri = parseData.uriList.getOrNull(0)
        if (uri != null && DropUtil.isUriFromDocumentsUI(uri)) {
            val data = DropUtil.parseDocumentUIUris(activity.applicationContext, parseData.uriList)
            //如果同时拖出公有文件和私有文件，提示不支持拖拽
            if (data.uriList.isNotEmpty() && (data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //私有文件
            if (data.uriList.isNotEmpty()) {
                return DropDispatchAction.PROHIBIT_STATE
            }
            //公有媒体库文件
            if ((data.mediaPathList.isNotEmpty() || data.folderList.isNotEmpty())) {
                return DropDispatchAction.COPY_STATE
            }
            return DropDispatchAction.PROHIBIT_STATE
        }

        return DropDispatchAction.COPY_STATE
    }

    private fun getDestFilePath(activity: Activity, categoryType: Int, dropSideItem: Boolean): String {
        val superAppPaths = if (dropSideItem) {
            Injector.injectFactory<IMain>()?.getSuperAppFilePathsByCategoryType(activity, categoryType)
        } else {
            Injector.injectFactory<IMain>()?.getShowingSuperAppFilePaths(activity)
        }
        val downloadFilePath = Environment.DIRECTORY_DOWNLOADS + File.separator
        var downloadSavePath: String? = null
        superAppPaths?.let {
            for (filePath in it) {
                if (filePath.startsWith(downloadFilePath)) {
                    downloadSavePath = filePath
                }
            }
            if (downloadSavePath == null) {
                downloadSavePath = it.getOrNull(0)
            }
        }
        val destPath = if (downloadSavePath != null) {
            DropUtil.STORAGE_ROOT_PATH + File.separator + downloadSavePath
        } else {
            DropUtil.DOWNLOAD_PATH
        }
        Log.d(TAG, "getDestFilePath -> downloadSavePath : $downloadSavePath destPath : $destPath")
        return destPath
    }
}