/*********************************************************************
 * * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : NormalFooterVH.kt
 * * Description : NormalFooterVH
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>        <data>       <version>       <desc>
 * *  W9001165       2020/9/21       1.0           create
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.content.Context
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import com.filemanager.common.view.FootViewOperation
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.thread.ThreadManager
import java.util.*

class NormalFooterVH(convertView: View) : BaseNormalVH(convertView, false) {
    private val mMsg: TextView = convertView.findViewById(R.id.footer_view)
    private val mConvertView: ViewGroup = convertView.findViewById(R.id.footer_layout)

    override fun updateViewHolder(context: Context, key: Int?, data: BaseFileBean, choiceMode: Boolean, selectionArray: MutableList<Int>, sizeCache: HashMap<String, String>, threadManager: ThreadManager, adapter: BaseSelectionRecycleAdapter<*, *>) {
        val footViewOperation = adapter as? FootViewOperation
        if (footViewOperation?.needShowFootView() == true) {
            mConvertView.visibility = View.VISIBLE
            mMsg.text = getString(footViewOperation, adapter)
        } else {
            mConvertView.visibility = View.GONE
        }
    }

    override fun isInDragRegionImpl(event: MotionEvent): Boolean = false

    override fun isInSelectRegionImpl(event: MotionEvent): Boolean = false

    fun getString(footViewOperation: FootViewOperation, adapter: BaseSelectionRecycleAdapter<*, *>): String {
        val count = adapter.itemCount - 1
        return footViewOperation.getFootString(count)
    }
}