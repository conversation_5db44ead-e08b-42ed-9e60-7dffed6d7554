/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/14
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.base

import android.database.Cursor
import android.net.Uri
import android.provider.MediaStore
import com.filemanager.common.helper.MediaHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.utils.FileTimeUtil
import com.filemanager.common.utils.FileTypeUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import java.io.File

open class SearchFileWrapper(cursor: Cursor, uri: Uri?) : UriFileWrapper(cursor, uri) {

    companion object {
        const val INDEX_ID = 0
        const val INDEX_DATA = 1
        const val INDEX_DISPLAY_NAME = 2
        const val INDEX_SIZE = 3
        const val INDEX_DATE_MODIFIED = 4
        const val INDEX_DATE_MIME_TYPE = 5
        const val INDEX_DATE_MEDIA_FORMAT = 6
        val MEDIA_PROJECT = arrayOf(
                MediaStore.Files.FileColumns._ID,
                MediaStore.Files.FileColumns.DATA,
                MediaStore.Files.FileColumns.DISPLAY_NAME,
                MediaStore.Files.FileColumns.SIZE,
                MediaStore.Files.FileColumns.DATE_MODIFIED,
                MediaStore.Files.FileColumns.MIME_TYPE,
                MediaHelper.COLUMN_MEDIA_FORMAT
        )
    }

    override fun createData(cursor: Cursor, uri: Uri?) {
        mId = cursor.getInt(INDEX_ID)
        mData = cursor.getString(INDEX_DATA)
        mDisplayName = cursor.getString(INDEX_DISPLAY_NAME)
        mSize = cursor.getLong(INDEX_SIZE)
        mDateModified = cursor.getLong(INDEX_DATE_MODIFIED) * 1000
        mMimeType = cursor.getString(INDEX_DATE_MIME_TYPE)
        mLocalFileUri = uri?.buildUpon()?.appendPath(mId.toString())?.build()
        mData?.let {
            val file = PathFileWrapper(it)
            //mSize will return 0 sometimes when query from MediaStore, so need check from Java_File_Interface again
            if (mSize == 0L) {
                mSize = file.mSize
                mDateModified = file.mDateModified
            }
        }
        if (mDateModified == 0L) {
            Log.d(TAG, "dateModified is 0")
            mDateModified = FileTimeUtil.getFileTime(mData) ?: 0
        }
        var isDir = false
        if (cursor.isNull(INDEX_DATE_MEDIA_FORMAT)) {
            Log.d(TAG, "createData format is null")
            isDir = File(mData).isDirectory
        } else {
            val format = cursor.getInt(INDEX_DATE_MEDIA_FORMAT)
            isDir = (format == MediaHelper.MEDIA_FORMAT_DIR)
        }
        if (isDir) {
            mLocalType = MimeTypeHelper.DIRECTORY_TYPE
        } else {
            // TODO: Should check the file is directory or not. (Currently we ignore the dir type because of
            //  the file properties interface has performance problem in android R)
            mLocalType = MimeTypeHelper.getTypeFromExtension(FileTypeUtils.getExtension(mDisplayName)) ?: MimeTypeHelper.UNKNOWN_TYPE
        }
    }
}