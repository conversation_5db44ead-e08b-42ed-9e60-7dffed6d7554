/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2021/3/25
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.content.Context
import android.os.Bundle
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.panel.COUIPanelFragment
import com.filemanager.common.R
import com.filemanager.common.dragselection.DefaultSelectDelegate
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.view.FileManagerRecyclerView
import com.google.android.material.appbar.COUIDividerAppBarLayout
import com.oplus.dropdrag.OnItemClickListener
import com.oplus.dropdrag.base.DefaultDetailsLookup
import com.oplus.dropdrag.base.DefaultKeyProvider
import com.oplus.dropdrag.recycleview.ItemDetailsLookup

abstract class BaseVMPanelFragment<VM : BaseViewModel> : COUIPanelFragment(), OnItemClickListener<Int> {
    companion object {
        private const val TAG = "BaseVMFragment"
    }

    protected var mActivity: BaseVMActivity? = null
    protected var mBaseRootView: View? = null
    protected var permissionEmptyView: ConstraintLayout? = null
    var isShowPermissionEmptyView = false
    protected var rootView: ViewGroup? = null
    protected var appBarLayout: COUIDividerAppBarLayout? = null
    protected var fragmentRecyclerView: FileManagerRecyclerView? = null
    protected var fragmentViewModel: SelectionViewModel<*, *>? = null

    override fun onAttach(context: Context) {
        super.onAttach(context)
        mActivity = activity as? BaseVMActivity
    }

    override fun initView(panelView: View?) {
        mActivity?.let { activity ->
            LayoutInflater.from(activity).inflate(getLayoutResId(), null, false)?.let {
                mBaseRootView = it
                (contentView as? ViewGroup)?.addView(it)
                initContentView(it)
                initData()
                startObserve()
                initSelectionTracker()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        PCConnectAction.attachToLifecycle(this)
    }

    private fun initSelectionTracker() {
        val recyclerView = fragmentRecyclerView ?: run {
            Log.w(TAG, "initSelectionTracker error: mRecyclerView is null")
            return
        }
        recyclerView.setMIsSupportDragSlide(false)
        runCatching {
            val keyProvider = DefaultKeyProvider(recyclerView)
            val detailsLookup = DefaultDetailsLookup(recyclerView)
            val trackerBuilder = com.oplus.dropdrag.RecycleSelectionBuilder(
                javaClass.name,
                recyclerView,
                DefaultSelectDelegate(fragmentViewModel),
                keyProvider,
                detailsLookup
            )
            trackerBuilder.withSlideSelection(false)
            trackerBuilder.withOnItemClickListener(this)
            PCConnectAction.getItemTouchInterceptor()?.let {
                trackerBuilder.withOnItemTouchListener(it)
            }
            trackerBuilder.build()
        }.onFailure { ex ->
            Log.w(TAG, "initSelectionTracker error: $ex")
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        PCConnectAction.onFragmentHiddenChanged(this, hidden)
    }

    abstract fun initContentView(view: View)
    abstract fun getLayoutResId(): Int
    abstract fun initData()
    abstract fun startObserve()
    abstract fun onResumeLoadData()

    open fun setPermissionEmptyVisible(visible: Int) {
        Log.d(TAG, "setPermissionEmptyVisible $visible")
        if (View.VISIBLE == visible) {
            isShowPermissionEmptyView = true
            createPermissionEmptyView(rootView)
        } else {
            isShowPermissionEmptyView = false
        }
        handleToolbarEnable(visible)
        bringToFront(visible)
    }

    open fun handleToolbarEnable(permissionEmptyVisible: Int) {}

    open fun bringToFront(visible: Int) {
        permissionEmptyView?.let { layout ->
            layout.visibility = visible
            if (View.VISIBLE == visible) {
                layout.bringToFront()
            }
        }
    }

    open fun createPermissionEmptyView(mRootView: ViewGroup?) {
        if (permissionEmptyView != null || mRootView == null) {
            return
        }
        val viewStub = mRootView.findViewById<ViewStub>(getPermissionEmptyViewStubId())
        val viewRoot = viewStub?.inflate()
        permissionEmptyView = viewRoot?.findViewById(R.id.permission_root_layout)
        viewRoot?.findViewById<TextView>(R.id.button)?.setOnClickListener {
            mActivity?.let { act -> PermissionUtils.openPermissionSetting(act) }
        }
        permissionEmptyView?.setBackgroundColor(
            COUIContextUtil.getAttrColor(
                context,
                com.support.appcompat.R.attr.couiColorBackgroundElevatedWithCard
            )
        )
        updatePermissionEmptyMarginTop()
    }

    open fun getPermissionEmptyViewStubId(): Int {
        return 0
    }

    open fun updatePermissionEmptyMarginTop() {
        if (isShowPermissionEmptyView.not()) {
            return
        }
        appBarLayout?.post {
            permissionEmptyView?.let { emptyLayout ->
                val emptyMarginTop = getEmptyMarginTop()
                val layoutParams = emptyLayout.layoutParams
                if (layoutParams is CoordinatorLayout.LayoutParams) {
                    layoutParams.topMargin = emptyMarginTop
                    emptyLayout.layoutParams = layoutParams
                }
            }
        }
    }

    open fun getEmptyMarginTop(): Int {
        return appBarLayout?.let { bar -> (bar.y + bar.measuredHeight).toInt() - StatusBarUtil.getStatusBarHeight() } ?: 0
    }

    override fun onItemClick(item: ItemDetailsLookup.ItemDetails<Int>, e: MotionEvent): Boolean {
        return false
    }
}