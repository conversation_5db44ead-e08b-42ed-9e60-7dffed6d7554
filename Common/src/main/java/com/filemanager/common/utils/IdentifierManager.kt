/***********************************************************
 * Copyright (C), 2008-2017 Oplus. All rights reserved.
 * VENDOR_EDIT
 * OPPO Coding Static Checking Skip
 * File: IdentifierManager.java
 * Description: Util to get OpenID.
 * Version: V 1.0
 * Date : 2018-11-12
 * Author: <PERSON>
 *
 * ---------------------Revision History: ---------------------
 * <author>    <data>    <version>    <desc>
 * W9000846    20190802     1.1        three to one
</desc></version></data></author> */
package com.filemanager.common.utils

import android.content.Context
import androidx.annotation.WorkerThread
import com.oplus.stdid.sdk.StdIDSDK
import com.sdid.id.IdentifierManager

object IdentifierManager {

    private const val TAG = "IdentifierManager"

    /**
     * The initialization method [initSdk] must be called,
     * and other methods can be called after the initialization is completed. Must be called first.
     * Incoming parameters context
     * @param context
     */
    @JvmStatic
    fun initSdk(context: Context) {
        Log.d(TAG, "initSdk")
        StdIDSDK.init(context)
    }

    /**
     * Unique Device ID / UDID(GUID)
     * The initialization method [initSdk] must be called
     *
     * @param context
     * @return
     */
    @WorkerThread
    @JvmStatic
    fun getUDID(context: Context): String? {
        return if (StdIDSDK.isSupported()) {
            StdIDSDK.getGUID(context)
        } else {
            IdentifierManager.getUDID(context)
        }
    }

    /**
     * Get anonymous device identifier switch status
     * The initialization method [initSdk] must be called
     *
     * @return true : indicates that the user allows personalized advertising tracking;
     * @return false : indicates that the user restricts personalized ad tracking;
     * @return false : Export ColorOS 12 and above, the interface always returns false
     */
    fun getOUIDStatus(context: Context): Boolean {
        return StdIDSDK.getOUIDStatus(context)
    }


    /**
     * Open Anonymous Device ID / OAID(User Dev Id OUID)
     * The initialization method [initSdk] must be called
     *
     * @param context
     * @return null : Export ColorOS 12 and above, the interface always returns null
     */
    @WorkerThread
    @JvmStatic
    fun getOUID(context: Context): String? {
        return if (StdIDSDK.isSupported()) {
            StdIDSDK.getOUID(context)
        } else {
            IdentifierManager.getOAID(context)
        }
    }

    /**
     * Vender Anonymous Device ID / VAID(DUID)
     * The initialization method [initSdk] must be called
     *
     * @param context
     * @return
     */
    @WorkerThread
    @JvmStatic
    fun getVAID(context: Context): String? {
        return if (StdIDSDK.isSupported()) {
            StdIDSDK.getDUID(context)
        } else {
            IdentifierManager.getVAID(context)
        }
    }

    /**
     * Application Anonymous Device ID / AAID(AUID)
     * The initialization method [initSdk] must be called
     *
     * @param context
     * @return
     */
    @WorkerThread
    @JvmStatic
    fun getAAID(context: Context): String? {
        return if (StdIDSDK.isSupported()) {
            StdIDSDK.getAUID(context)
        } else {
            IdentifierManager.getAAID(context)
        }
    }

    /**
     * APID
     * The initialization method [initSdk] must be called
     *
     * @param context
     * @return
     */
    @WorkerThread
    @JvmStatic
    fun getAPID(context: Context?): String {
        return if (StdIDSDK.isSupported()) {
            StdIDSDK.getAPID(context)
        } else "NA"
    }

    /**
     * Unbind the Service connected to StdID
     * Call this method to unbind the Service connected to the StdID.
     * Not allowed to be called on the main thread
     */
    @WorkerThread
    @JvmStatic
    fun clearSdk(context: Context) {
        Log.d(TAG, "clearSdk")
        StdIDSDK.clear(context)
    }
}