/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved.
 ** File:  - CameraCompatR.java
 ** Description: Compat class for Camera on android R
 ** Version: 1.0
 ** Date : 2020/05/07
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  Jiafei.<PERSON>@Apps.FileManager      2020/05/07    1.0     create
 ****************************************************************/

package com.filemanager.common.compat.compat30

import android.content.ContentResolver
import android.os.Bundle
import android.provider.MediaStore
import android.provider.MediaStore.Files.FileColumns
import com.filemanager.common.compat.MediaStoreCompat
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.utils.KtUtils.getParentFilePath
import com.filemanager.common.wrapper.PathFileWrapper
import com.filemanager.common.MyApplication
import com.filemanager.common.utils.Log
import java.util.regex.Pattern

internal object CameraCompatR {
    private const val TAG = "CameraCompatR"

    // regex for all cshot image
    private val PATTERN_CSHOT_RELATIVE_PATH by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        //Pattern.compile("(?i)(DCIM/Camera/Cshot/[1-9][0-9]*/|DCIM/MyAlbums/[^/]+/Cshot/[1-9][0-9]*/)")
        val builder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
        builder.append("(?i)(DCIM/Camera/Cshot/[1-9][0-9]*/|")
        builder.append(MediaStoreCompat.MYALBUM_PATH)
        builder.append("[^/]+/Cshot/[1-9][0-9]*/)")
        Pattern.compile(builder.toString())
    }

    // regex for camera cshot to get cshotId easy
    private val PATTERN_CAMERA_CSHOT_RELATIVE_PATH by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        Pattern.compile("(?i)(DCIM/Camera/Cshot/)([1-9][0-9]*)/")
    }

    // regex for myalbum cshot to get cshotId easy
    private val PATTERN_MYALBUM_CSHOT_RELATIVE_PATH by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
        //Pattern.compile("(?i)(DCIM/MyAlbums/[^/]+/Cshot/)([1-9][0-9]*)/")
        val builder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
        builder.append("(?i)(")
        builder.append(MediaStoreCompat.MYALBUM_PATH)
        builder.append("[^/]+/Cshot/)([1-9][0-9]*)/")
        Pattern.compile(builder.toString())
    }

    /**
     * @return bucket id list of all cshot cover item in mediastore
     */
    fun getBucketIdsForCshotCompatR(): ArrayList<Long> {
        val builder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
        builder.append(FileColumns.RELATIVE_PATH)
        builder.append(" REGEXP '")
        builder.append(PATTERN_CSHOT_RELATIVE_PATH.pattern())
        builder.append("'")

        val bucketIds = ArrayList<Long>()
        val queryArgs = Bundle()
        queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
        queryArgs.putString(ContentResolver.QUERY_ARG_SQL_GROUP_BY, FileColumns.BUCKET_ID)
        try {
            val context = MyApplication.sAppContext
            context.contentResolver.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, arrayOf(FileColumns.BUCKET_ID)
                    , queryArgs, null).use { cursor ->
                if (cursor == null) {
                    return bucketIds
                }
                var bucketId = -1L
                while (cursor.moveToNext()) {
                    bucketId = cursor.getLong(0)
                    bucketIds.add(bucketId)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getBucketIdsForCshotCompatR error: $ex")
        }
        return bucketIds
    }

    /**
     * @return id list of all camera cshot cover item in mediastore
     */
    fun getIdsForCameraCshotCompatR(): ArrayList<Long> {
        val builder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
        builder.append(FileColumns.RELATIVE_PATH)
        builder.append(" REGEXP '")
        builder.append(PATTERN_CAMERA_CSHOT_RELATIVE_PATH.pattern())
        builder.append("'")

        val ids = ArrayList<Long>()
        val queryArgs = Bundle()
        queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
        queryArgs.putString(ContentResolver.QUERY_ARG_SQL_GROUP_BY, FileColumns.BUCKET_ID)
        try {
            val context = MyApplication.sAppContext
            context.contentResolver.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, arrayOf(FileColumns._ID)
                    , queryArgs, null).use { cursor ->
                if (cursor == null) {
                    return ids
                }
                var bucketId = -1L
                while (cursor.moveToNext()) {
                    bucketId = cursor.getLong(0)
                    ids.add(bucketId)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getIdsForCameraCshotCompatR error: $ex")
        }
        return ids
    }

    /**
     * @return id list of all cshot cover item in oppo gallery MyAlbum
     */
    fun getIdsForMyAlbumCshotCompatR(key: String): ArrayList<Long> {
        val ids = ArrayList<Long>()
        if (key.isNullOrEmpty()) {
            return ids
        }

        try {
            // my album cshot pattern like "(?i)(DCIM/MyAlbums/[^/]+/Cshot/)([1-9][0-9]*)/"
            val patternBuilder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
            patternBuilder.append("(?i)(")
            patternBuilder.append(key)
            patternBuilder.append("Cshot/[1-9][0-9]*/)")
            var patternString = patternBuilder.toString()
            val patternCshot = Pattern.compile(patternString)

            val builder = StringBuilder(MediaStoreCompat.DEFAULT_CAPACITY)
            builder.append(FileColumns.RELATIVE_PATH)
            builder.append(" REGEXP '")
            builder.append(patternCshot.pattern())
            builder.append("'")

            Log.d(TAG, "getIdsForMyAlbumCshotCompatR selection: ${builder.toString()}")

            val queryArgs = Bundle()
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_SELECTION, builder.toString())
            queryArgs.putString(ContentResolver.QUERY_ARG_SQL_GROUP_BY, FileColumns.BUCKET_ID)

            val context = MyApplication.sAppContext
            context.contentResolver.query(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, arrayOf(FileColumns._ID)
                    , queryArgs, null).use { cursor ->
                if (cursor == null) {
                    return ids
                }
                var bucketId = -1L
                while (cursor.moveToNext()) {
                    bucketId = cursor.getLong(0)
                    ids.add(bucketId)
                }
            }
        } catch (ex: Exception) {
            Log.d(TAG, "getIdsForMyAlbumCshotCompatR error: $ex")
        }
        return ids
    }

    /**
     * @return cshotId for specify relative Path
     */
    fun getCshotIdCompatR(path: String?): Long {
        Log.d(TAG, "getCshotIdCompatR path: $path")
        var cshotId = 0L
        if (path.isNullOrEmpty()) {
            return cshotId
        }
        try {
            var cshotString: String? = null
            // check my album cshot first
            val matcher = PATTERN_MYALBUM_CSHOT_RELATIVE_PATH.matcher(path)
            if (matcher.matches()) {
                cshotString = matcher.group(2)
                Log.d(TAG, "getCshotIdCompatR cshotString1: $cshotString")
            } else {
                // check camera album cshot
                val matcher1 = PATTERN_CAMERA_CSHOT_RELATIVE_PATH.matcher(path)
                if (matcher1.matches()) {
                    cshotString = matcher1.group(2)
                    Log.d(TAG, "getCshotIdCompatR cshotString2: $cshotString")
                }
            }

            cshotString?.let {
                cshotId = it.toLong()
            }
        } catch (ex: Exception) {
            Log.w(TAG, "getCshotIdCompatR error: $ex")
            cshotId = 0L
        }
        Log.d(TAG, "getCshotIdCompatR cshotId: $cshotId")
        return cshotId
    }

    /**
     * @return ImageFileBean list of all cShot items which under cShot_Cover list
     * attention: the method may perform unexpectedly when the cShot folder have images
     * which not in MediaStore
     */
    fun getCShotFiles(fileList: ArrayList<BaseFileBean>): ArrayList<BaseFileBean> {
        val resultList = ArrayList<BaseFileBean>()
        for (file in fileList) {
            file.mData?.let { path ->
                getParentFilePath(path).let { parent ->
                    JavaFileHelper.listFileBeans(PathFileWrapper(parent), false)?.let { list ->
                        for (bean in list) {
                           if (bean.mLocalType == MimeTypeHelper.IMAGE_TYPE) {
                               resultList.add(bean)
                           }
                        }
                    }

                }
            }
        }
        return resultList
    }
}