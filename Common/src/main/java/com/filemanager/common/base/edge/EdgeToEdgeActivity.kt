/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: - EdgeToEdgeAppCompatActivity
 ** Description:
 ** Version: 1.0
 ** Date : 2024/6/2
 ** Author: ********
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 **  ********    2024/6/2     1.0     create file
 ****************************************************************/
package com.filemanager.common.base.edge

import android.content.Intent
import android.os.Build
import android.os.Build.VERSION
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import com.filemanager.common.R
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.Log

open class EdgeToEdgeActivity : AppCompatActivity(), UpdatePaddingBottom {

    var paddingBottom: Int = 0
    var navPaddingBottom: Int = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (VERSION.SDK_INT <= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            return
        }
        if (!excludeEdgeToEdge()) {
            EdgeToEdgeManager.enableEdgeToEdge(window)
            EdgeToEdgeManager.isAppearanceLightSystemBars(
                window,
                !EdgeToEdgeManager.isDarkUiMode(this)
            )
            findViewById<View>(android.R.id.content)?.let { content ->
                EdgeToEdgeManager.observeOnApplyWindowInsetsAndUpdatePadding(content, this)
            }
        }
        EdgeToEdgeManager.enableLayoutInDisplayCutoutModeToAlways(window)
    }

    override fun onDestroy() {
        super.onDestroy()
        findViewById<View>(android.R.id.content)?.let { content ->
            EdgeToEdgeManager.removeObserveOnApplyWindowInsets(content)
        }
    }

    /**
     * 排除无需开启edgeToEdge页面
     */
    open fun excludeEdgeToEdge(): Boolean {
        return false
    }

    override fun update(paddingBottom: Int, navPaddingBottom: Int) {
        Log.d(TAG, "update padding paddingBottom = $paddingBottom, navPaddingBottom = $navPaddingBottom")
        this.paddingBottom = paddingBottom
        this.navPaddingBottom = navPaddingBottom
        updateNavigationToolPadding()
    }

    open fun updateNavigationToolPadding() {
    }

    override fun finish() {
        super.finish()
        if (UIConfigMonitor.isZoomWindowShow()) {
            overridePendingTransition(
                R.anim.coui_close_slide_enter_zoom,
                R.anim.coui_close_slide_exit_zoom
            )
        }
    }

    override fun startActivity(intent: Intent?) {
        super.startActivity(intent)
        if (UIConfigMonitor.isZoomWindowShow()) {
            overridePendingTransition(
                R.anim.coui_open_slide_enter_zoom,
                R.anim.coui_open_slide_exit_zoom
            )
        }
    }

    override fun startActivity(intent: Intent?, options: Bundle?) {
        super.startActivity(intent, options)
        if (UIConfigMonitor.isZoomWindowShow()) {
            overridePendingTransition(
                R.anim.coui_open_slide_enter_zoom,
                R.anim.coui_open_slide_exit_zoom
            )
        }
    }

    companion object {
        private const val TAG: String = "EdgeToEdgeActivity"
    }
}
