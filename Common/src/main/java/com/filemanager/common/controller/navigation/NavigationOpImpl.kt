/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : NavigationOpImpl
 ** Description : Navigation Operate impl
 ** Version     : 1.0
 ** Date        : 2023/03/23
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2023/03/23      1.0        create
 ***********************************************************************/
package com.filemanager.common.controller.navigation

import android.view.MenuItem
import com.filemanager.common.R
import com.filemanager.common.utils.Log

private const val TAG = "NavigationOperation"


/**
 * 由于navigationView 动画的原因，导致部分操作调用时机先于showNavigation()调用，此时的menuItems有可能为空，导致执行不生效
 */
class NavigationOpImpl {

    /**
     * 控制item是否显示
     */
    class EnableItemOp(
        private val isEnable: Boolean,
        private val hasDrm: Boolean,
        private val hasSelectedMultiLabels: Boolean,
        private val hasSelectedLabelsAllPin: Boolean,
        private val hasSelectedFileEmpty: Boolean,
        private val hasAndroidData: Boolean
    ) : NavigationOp {

        override fun invoke(menuType: NavigationType, menuItems: HashMap<Int, MenuItem>) {
            Log.e(TAG, "EnableItemOp invoke")
            menuType.setNavigateItemAble(
                menuItems,
                isEnable,
                hasDrm,
                hasSelectedMultiLabels,
                hasSelectedLabelsAllPin,
                hasSelectedFileEmpty,
                hasAndroidData
            )
        }
    }

    /**
     * 标签列表工具栏使用
     * 根据是否父子级和云盘是否显示，控制显示的item
     */
    class DisplayItemsOp(private val isChild: Boolean, private val cloudDiskVisible: Boolean) : NavigationOp {

        override fun invoke(menuType: NavigationType, menuItems: HashMap<Int, MenuItem>) {
            val cloudDiskItem = menuItems[R.id.navigation_upload_cloud]
            val deleteLabelItem = menuItems[R.id.navigation_delete_label]
            val labelMoreItem = menuItems[R.id.navigation_label_more]
            Log.e(TAG, "DisplayItemsOp  child:$isChild cloud:$cloudDiskVisible")
            if (isChild) {
                //小屏，如果云盘可见，显示more。云盘可见但不可用，不显示more及云盘
                if (cloudDiskVisible) {
                    val cloudIsEnable = cloudDiskItem?.isEnabled == true
                    cloudDiskItem?.isVisible = false
                    deleteLabelItem?.isVisible = !cloudIsEnable
                    labelMoreItem?.isVisible = cloudIsEnable
                } else {
                    cloudDiskItem?.isVisible = false
                    labelMoreItem?.isVisible = false
                    deleteLabelItem?.isVisible = true
                }
            } else {
                //大屏，不显示more
                labelMoreItem?.isVisible = false
                deleteLabelItem?.isVisible = true
                cloudDiskItem?.isVisible = cloudDiskVisible
            }
        }
    }
}

interface NavigationOp {
    fun invoke(menuType: NavigationType, menuItems: HashMap<Int, MenuItem>)
}