package com.oplus.filemanager.pcconnect

import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.MotionEvent
import android.view.ViewConfiguration
import androidx.annotation.VisibleForTesting
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.OnItemTouchListener
import com.filemanager.common.MyApplication
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.utils.Log
import kotlin.math.abs

class PCConnectItemTouchInterceptor: OnItemTouchListener {
    companion object {
        private const val TAG = "PCConnectItemTouchInterceptor"
        private const val MSG_DRAG_PRESS = 1000

        private val TOUCH_SLOP_SQUARE by lazy(LazyThreadSafetyMode.SYNCHRONIZED) {
            val configuration = ViewConfiguration.get(MyApplication.sAppContext)
            val touchSlop = configuration.scaledTouchSlop
            touchSlop * touchSlop
        }
    }

    private var mDownEvent = floatArrayOf(0F, 0F)
    private var mCurrentEvent = floatArrayOf(0F, 0F)
    @VisibleForTesting
    var mSentFileInfoToRemote = false

    private val mHandler: Handler
    private val mLongDragTimeout: Long
    private var mCurrentDownEvent: MotionEvent? = null

    init {
        mHandler = DragSelectionHandler()
        mLongDragTimeout = ViewConfiguration.getLongPressTimeout().toLong()
    }

    override fun onRequestDisallowInterceptTouchEvent(disallowIntercept: Boolean) {
    }

    override fun onInterceptTouchEvent(rv: RecyclerView, e: MotionEvent): Boolean {
        parseTouchEvent(e)
        parseTouchEventForSynergy(e)
        return false
    }

    override fun onTouchEvent(rv: RecyclerView, e: MotionEvent) {
        parseTouchEvent(e)
        parseTouchEventForSynergy(e)
    }

    private fun parseTouchEventForSynergy(ev: MotionEvent) {
        if (!PCConnectController.sInstance.isPCScreenCast() ||
            !PCConnectDataHelper.isTouchEventFromSynergy(ev)) {
            return
        }
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                mCurrentDownEvent?.recycle()
                mCurrentDownEvent = MotionEvent.obtain(ev)
                mCurrentDownEvent?.let {
                    mHandler.sendEmptyMessageDelayed(MSG_DRAG_PRESS, mLongDragTimeout)
                }
            }
            MotionEvent.ACTION_MOVE -> {
                if (!mHandler.hasMessages(MSG_DRAG_PRESS)) {
                    return
                }
                mCurrentDownEvent?.let {
                    val deltaX = (ev.x - it.x).toInt()
                    val deltaY = (ev.y - it.y).toInt()
                    val distance = deltaX * deltaX + deltaY * deltaY
                    if (distance > TOUCH_SLOP_SQUARE) {
                        mHandler.removeMessages(MSG_DRAG_PRESS)
                    }
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                mHandler.removeMessages(MSG_DRAG_PRESS)
                PCConnectController.sInstance.cancelDragFileOnRemote()
            }
        }
    }

    private fun parseTouchEvent(e: MotionEvent) {
        if (e.action == MotionEvent.ACTION_DOWN) {
            resetEventInfo()
        }
        if (PCConnectDataHelper.isTouchEventFromPC(e)
            && PCConnectController.sInstance.isPCScreenCast()
        ) {
            when (e.action) {
                MotionEvent.ACTION_DOWN -> {
                    mSentFileInfoToRemote = false
                    resetEventInfo(e.x, e.y)
                }
                MotionEvent.ACTION_MOVE -> {
                    mCurrentEvent[0] = e.x
                    mCurrentEvent[1] = e.y
                    val deltaX = (mCurrentEvent[0] - mDownEvent[0]).toInt()
                    val deltaY = (mCurrentEvent[1] - mDownEvent[1]).toInt()
                    val distance = deltaX * deltaX + deltaY * deltaY
                    //滑动距离超过阈值时才执行拖选操作
                    if (distance > TOUCH_SLOP_SQUARE) {
                        val hasMove = (mDownEvent[0] != mCurrentEvent[0]) || (mDownEvent[1] != mCurrentEvent[1])
                        if (hasMove && !mSentFileInfoToRemote) {
                            mSentFileInfoToRemote = true
                            PCConnectController.sInstance.dragFileOnRemote(
                                    mDownEvent[0],
                                    mDownEvent[1]
                            )
                        }
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    resetEventInfo()
                    PCConnectController.sInstance.cancelDragFileOnRemote()
                }
            }
        }
    }

    private fun resetEventInfo(x: Float = 0F, y: Float = 0F) {
        mDownEvent[0] = x
        mDownEvent[1] = y
        mCurrentEvent[0] = x
        mCurrentEvent[1] = y
    }

    fun checkViewCanLongPress(editMode: Boolean): Boolean {
        // In zoom window and pc connect, long press only can use to enter edit mode, select files is not supported,
        return when {
            UIConfigMonitor.isZoomWindowShow() -> {
                //for pad connect and phone, view can long press to select file
                if (PCConnectDataHelper.isTouchEventFromSynergy(mCurrentDownEvent)) {
                    return true
                }
                !editMode
            }
            editMode -> {
                Log.d(TAG, "checkViewCanLongPress: Mode=$editMode")
                true
            }
            else -> {
                Log.d(TAG, "checkViewCanLongPress: dEvent[${mDownEvent[0]}, ${mDownEvent[1]}], cEvent[${mCurrentEvent[0]}, ${mCurrentEvent[1]}]")
                val offset = 1
                val moveX = abs(mDownEvent[0] - mCurrentEvent[0])
                val moveY = abs(mDownEvent[1] - mCurrentEvent[1])
                !((moveX > offset) || (moveY > offset))
            }
        }
    }

    private fun dispatchDragPress() {
        PCConnectController.sInstance.dragFileOnRemote()
    }

    private inner class DragSelectionHandler : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            Log.d(TAG, "handleMessage what : " + msg.what)
            super.handleMessage(msg)
            when (msg.what) {
                MSG_DRAG_PRESS -> dispatchDragPress()
            }
        }
    }
}