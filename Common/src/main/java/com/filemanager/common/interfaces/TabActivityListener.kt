/***********************************************************
 * * Copyright (C), 2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:PrimaryTitleBehavior.kt
 * * Description:com.coloros.filemanager.behavior
 * * Version:1.0
 * * Date :2021.5.17
 * * Author:W9001165
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.interfaces

import com.filemanager.common.base.BaseFileBean

interface TabActivityListener<T : BaseFileBean> {
    fun initToolbarSelectedMode(
        needInit: Boolean,
        realFileSize: Int,
        selectedFileSize: Int,
        selectItems: ArrayList<T>
    )
    fun initToolbarNormalMode(needInit: Boolean, empty: Boolean)
    fun refreshScanModeItemIcon(withAnimation: Boolean = true)
    fun updateNeedSkipAnimation(withAnimation: Boolean)
}