/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/24
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.content.SharedPreferences
import com.filemanager.common.MyApplication
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout

object PreferencesUtils {
    const val SHARED_PREFS_NAME = "filemanager_preferences"
    private const val TAG = "PreferencesUtils"
    private const val PREFERENCES_TIME_OUT = 300L
    private const val MAX_RETRY_TIMES = 3
    const val SP_KEY_AUTO_PARSE_SWITCH = "sp_name_auto_switch"

    fun getPreferences(preferencesName: String): SharedPreferences? {
        return getPreferences(MyApplication.sAppContext, preferencesName)
    }

    @JvmStatic
    private fun getPreferences(context: Context, preferencesName: String): SharedPreferences? {
        return context.getSharedPreferences(preferencesName, Context.MODE_PRIVATE)
    }

    @JvmStatic
    fun getSelfStringSet(preferencesName: String, key: String, default: Set<String>? = null): Set<String>? {
        var result = default
        try {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(preferencesName)?.also {
                        result = it.getStringSet(key, default)
                    }
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "get failed: $preferencesName: [$key, $default], ${ex.message}")
            result = default
        }
        Log.d(TAG, "get: $preferencesName: [$key, $result, $default]")
        return result
    }

    @JvmStatic
    private fun <T> get(
        preferencesName: String,
        key: String,
        default: T?,
        isSet: Boolean = false,
        reTryTimes: Int = 0
    ): T? {
        var result = default
        try {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(preferencesName)?.also {
                        result = when (default) {
                            is Int -> it.getInt(key, default as Int)
                            is Float -> it.getFloat(key, default as Float)
                            is Boolean -> it.getBoolean(key, default as Boolean)
                            is Long -> it.getLong(key, default as Long)
                            (isSet && ((default == null) || (default is Set<*>))) -> it.getStringSet(key, default as? Set<String>)
                            else -> it.getString(key, default?.toString())
                        } as T
                    }
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "get failed: $preferencesName: [$key, $default], ${ex.message}")
            result = if (reTryTimes < MAX_RETRY_TIMES) {
                Log.d(TAG, "key$key reTryTimes$reTryTimes")
                get(preferencesName, key, default, isSet, reTryTimes + 1)
            } else {
                default
            }
        }
        Log.d(TAG, "get: $preferencesName: [$key, $result, $default]")
        return result
    }

    @JvmStatic
    private fun <T> get(
        context: Context,
        preferencesName: String,
        key: String,
        default: T?,
        isSet: Boolean = false
    ): T? {
        var result = default
        try {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(context, preferencesName)?.also {
                        result = when (default) {
                            is Int -> it.getInt(key, default as Int)
                            is Float -> it.getFloat(key, default as Float)
                            is Boolean -> it.getBoolean(key, default as Boolean)
                            is Long -> it.getLong(key, default as Long)
                            (isSet && ((default == null) || (default is Set<*>))) -> it.getStringSet(key, default as? Set<String>)
                            else -> it.getString(key, default?.toString())
                        } as T
                    }
                }
            }
        } catch (ex: Exception) {
            Log.w(TAG, "get failed: $preferencesName: [$key, $default], ${ex.message}")
            result = default
        }
        Log.d(TAG, "get: $preferencesName: [$key, $result, $default]")
        return result
    }

    @JvmStatic
    fun <T> put(preferencesName: String = SHARED_PREFS_NAME, key: String, value: T?) {
        Log.d("PreferencesUtils", "put: $preferencesName: [$key, $value]")
        try {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(preferencesName)?.edit()?.also {
                        putValue(it, key, value)
                        it.apply()
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "put failed: $preferencesName: [$key, $value], ${e.message}")
        }
    }

    @JvmStatic
    fun <T> putAll(preferencesName: String = SHARED_PREFS_NAME, map: Map<String, Any?>) {
        Log.d("PreferencesUtils", "putAll: $preferencesName, ${map.size}")
        try {
            runBlocking {
                withTimeout(PREFERENCES_TIME_OUT) {
                    getPreferences(preferencesName)?.edit()?.also {
                        for ((key, value) in map) {
                            putValue(it, key, value)
                        }
                        it.apply()
                    }
                }
            }
        } catch (e: Exception) {
            Log.w(TAG, "putAll failed: $preferencesName, ${e.message}")
        }
    }

    private fun putValue(editor: SharedPreferences.Editor, key: String, value: Any?) {
        when (value) {
            is Int -> editor.putInt(key, value)
            is Float -> editor.putFloat(key, value)
            is Boolean -> editor.putBoolean(key, value)
            is Long -> editor.putLong(key, value)
            is Set<*> -> editor.putStringSet(key, value as Set<String>)
            else -> editor.putString(key, value?.toString())
        }
    }

    @JvmStatic
    fun getInt(preferencesName: String = SHARED_PREFS_NAME, key: String, default: Int = 0): Int {
        return get(preferencesName, key, default) ?: default
    }

    @JvmStatic
    fun getFloat(preferencesName: String = SHARED_PREFS_NAME, key: String, default: Float = 0F): Float {
        return get(preferencesName, key, default) ?: default
    }

    @JvmStatic
    fun getBoolean(preferencesName: String = SHARED_PREFS_NAME, key: String, default: Boolean = false): Boolean {
        return get(preferencesName, key, default) ?: default
    }

    @JvmStatic
    fun getBoolean(
        context: Context,
        preferencesName: String = SHARED_PREFS_NAME,
        key: String,
        default: Boolean = false
    ): Boolean {
        return get(context, preferencesName, key, default) ?: default
    }

    @JvmStatic
    fun getLong(preferencesName: String = SHARED_PREFS_NAME, key: String, default: Long = 0L): Long {
        return get(preferencesName, key, default) ?: default
    }

    @JvmStatic
    fun getString(preferencesName: String = SHARED_PREFS_NAME, key: String, default: String? = null): String? {
        return get(preferencesName, key, default)
    }

    @JvmStatic
    fun getString(
        context: Context,
        preferencesName: String = SHARED_PREFS_NAME,
        key: String,
        default: String? = null
    ): String? {
        return get(context, preferencesName, key, default)
    }

    @JvmStatic
    fun getStringSet(preferencesName: String = SHARED_PREFS_NAME, key: String, default: Set<String>? = null): Set<String>? {
        return get(preferencesName, key, default, true) ?: default
    }

    @JvmStatic
    fun getAll(preferencesName: String = SHARED_PREFS_NAME): MutableMap<String, *>? {
        return getPreferences(preferencesName)?.all
    }

    @JvmStatic
    fun remove(preferencesName: String = SHARED_PREFS_NAME, key: String) {
        getPreferences(preferencesName)?.edit()?.remove(key)?.apply()
    }

    @JvmStatic
    fun clear(preferencesName: String = SHARED_PREFS_NAME){
        getPreferences(preferencesName)?.edit()?.clear()?.apply()
    }

    @JvmStatic
    fun haveKey(preferencesName: String = SHARED_PREFS_NAME, key: String): Boolean {
        return getPreferences(preferencesName)?.contains(key) ?: false
    }
}