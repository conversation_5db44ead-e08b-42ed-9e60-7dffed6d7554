/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 ** OPLUS Java File Skip Rule:MemberName,VariableOrder
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils;

import android.content.Context;
import android.text.TextUtils;

import com.filemanager.common.MyApplication;
import com.filemanager.common.constants.AdResourceConstants;
import com.filemanager.common.constants.Constants;
import com.filemanager.common.helper.CategoryHelper;
import com.filemanager.common.helper.MimeTypeHelper;
import com.filemanager.common.wrapper.RecycleBinLiveData;
import com.oplus.statistics.OplusTrack;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class StatisticsUtils {
    public static final long APP_CODE_VALUE = 20016L;
    public static final String HEY_PC_SHOW = "pc_connect";
    public static final String HEY_PC_CLICK = "click_pc_connect";

    public static final String FILE_CLEANUP = "clean_file";
    public static final String FTP = "remote_management";
    public static final String STARTUP_FTP = "start_remote_management";
    public static final String ALL_FILES = "all_file";
    public static final String ALL_FILES_SUCCESS_SEARCH = "all_success_search";
    public static final String SDCARD = "tfcard_file";
    public static final String OTG = "otg_file";
    public static final String OPEN_UNKNOWN_FILE = "open_unknown_file";
    public static final String OPEN_UNKNOWN_DOWNLOAD_APP = "download_app";
    public static final String DETETE_ACTION = "delete_action";
    public static final String COMPRESS_ACTION = "compress_action";
    public static final String RENAME_ACTION = "rename_action";
    public static final String DETAIL_ACTION = "detail_action";
    public static final String SEQUENCE_ACTION = "sequence_action";
    public static final String UNCOMPRESS_ACTION = "uncompress_action";
    public static final String OPEN_CLOUDDRIVER = "clouddriver_click";
    public static final String SUPPORT_CLOUDDRIVER = "supported_clouddriver";
    public static final String CATEGORY_AUDIO = "category_audio";
    public static final String CATEGORY_VEDIO = "category_vedio";
    public static final String CATEGORY_PICTURE = "category_pic";
    public static final String CATEGORY_DOC = "category_file";
    public static final String CATEGORY_APK = "category_install_file";
    public static final String CATEGORY_DOWNLOAD = "category_download";
    public static final String CATEGORY_COMPRESSION = "category_compressed";
    public static final String CATEGORY_BLUETOOTH = "category_bluetooth";
    public static final String CATEGORY_QQ = "category_qq";
    public static final String CATEGORY_WECHAT = "category_weixin";
    public static final String CATEGORY_TENCENT_DOCS = "category_tencent_docs";
    public static final String CATEGORY_KING_DOCS = "category_king_docs";
    /** onCommon start */
    public static final String CATEGORY_AUDIO_SEARCH = "category_audio_search";
    public static final String CATEGORY_AUDIO_SUCCESS_SEARCH = "category_audio_success_search";
    public static final String CATEGORY_VEDIO_SEARCH = "category_vedio_search";
    public static final String CATEGORY_VEDIO_SUCCESS_SEARCH = "category_vedio_success_search";
    public static final String CATEGORY_PICTURE_SEARCH = "category_picture_search";
    public static final String CATEGORY_PICTURE_SUCCESS_SEARCH = "category_picture_success_search";
    public static final String CATEGORY_DOC_SEARCH = "category_doc_search";
    public static final String CATEGORY_DOC_SUCCESS_SEARCH = "category_doc_success_search";
    public static final String CATEGORY_APK_SEARCH = "category_apk_search";
    public static final String CATEGORY_APK_SUCCESS_SEARCH = "category_apk_success_search";
    public static final String CATEGORY_QQ_SEARCH = "category_qq_search";
    public static final String CATEGORY_QQ_SUCCESS_SEARCH = "category_qq_success_search";
    public static final String CATEGORY_WECHAT_SEARCH = "category_wechat_search";
    public static final String CATEGORY_WECHAT_SUCCESS_SEARCH = "category_wechat_success_search";

    public static final String COPY_MENU_PRESSED = "copy_menu_pressed";
    public static final String CUT_MENU_PRESSED = "cut_menu_pressed";
    public static final String SEND_MENU_PRESSED = "send_menu_pressed";
    public static final String DELETE_MENU_PRESSED = "delete_menu_pressed";
    public static final String MOVE_INTO_SAFE_PRESSED = "move_into_safe_pressed";

    public static final String MOVE_TO_SDCARD_FAIL = "move_to_sdcard_fail";

    /** add for p00050235 */
    public static final String OAPS_TO_APPSTORE_FROM_HOME_SUCC_COUNT = "move_to_appstore_from_home_success_count";
    public static final String OAPS_TO_APPSTORE_FROM_HOME_FAIL_COUNT = "move_to_appstore_from_home_fail_count";
    public static final String OAPS_SHOW_APPSTORE_ENTRANCE_HOME_COUNT = "show_to_appstore_entrance_home_count";

    public static final String OAPS_TO_APPSTORE_FROM_APK_INS_SUCC_COUNT = "move_to_appstore_from_apk_install_success_count";
    public static final String OAPS_TO_APPSTORE_FROM_APK_INS_FAIL_COUNT = "move_to_appstore_from_apk_install_fail_count";
    public static final String OAPS_SHOW_APPSTORE_ENTRANCE_APK_INS_COUNT = "show_to_appstore_entrance_apk_install_count";

    public static final String OAPS_TO_APPSTORE_FROM_APK_UNINS_SUCC_COUNT = "move_to_appstore_from_apk_uninstall_success_count";
    public static final String OAPS_TO_APPSTORE_FROM_APK_UNINS_FAIL_COUNT = "move_to_appstore_from_apk_uninstall_fail_count";
    public static final String OAPS_SHOW_APPSTORE_ENTRANCE_APK_UNINS_COUNT = "show_to_appstore_entrance_apk_uninstall_count";

    public static final String OAPS_REQUEST_SERVER_CONFIG_SUCC_COUNT = "request_server_config_success_count";
    public static final String OAPS_REQUEST_SERVER_CONFIG_FAIL_COUNT = "request_server_config_fail_count";
    public static final String OAPS_REQUEST_SERVER_CONFIG_ERROR_COUNT = "request_server_config_error_count";
    /** add end p00050235 */
    /** add for p00030289 RecentView */
    public static final String RECENT_TAB_CLICKED = "recent_tab_clicked";
    public static final String RECENT_PULL_REFRESH_BY_USER = "recent_pull_refresh_by_user";
    public static final String RECENT_DELETE_RECORD = "recent_delete_record";
    public static final String RECENT_DELETE_RECORD_AND_FILE = "recent_delete_record_and_file";
    public static final String RECENT_MENU_EDIT_CLICKED = "recent_menu_edit_clicked";
    public static final String RECENT_TO_EDITMODE_BY_LONG_CLICKED = "recent_to_editmode_by_long_clicked";
    /** add end p00030289  */

    /** add for (TFS)13437  */
    public static final String COMPRESS_CATEGORY_ICON_CLICK_COUNT = "compress_category_icon_click_count";
    /**add end (TFS)13437  */

    public static final String SCAN_MODE_DOC_SWITCH = "doc_switch";
    public static final String SCAN_MODE_PIC_SWITCH = "pic_switch";
    public static final String SCAN_MODE_VIDEO_SWITCH = "video_switch";
    public static final String SCAN_MODE_AUDIO_SWITCH = "audio_switch";
    public static final String SCAN_MODE_ARCHIVES_SWITCH = "archives_switch";
    public static final String SCAN_MODE_APK_SWITCH = "apk_switch";
    public static final String SCAN_MODE_OTHER_SWITCH = "other_switch";
    public static final String SCAN_MODE_SDCARD_SWITCH = "sdcard_switch";

    public static final String EVENT_STORAGE_DETAILS_CLICK = "click_storage";

    /** add for HelpAndFeedBack  */
    public static final String IN_HELP_AND_FEEDBACK = "in_help_and_feedback";

    /** add for (TFS)423908 ShowHiddenFile  */
    public static final String SHOW_HIDDEN_FILE = "hidden_files";
    public static final String SWITCH_ON = "on";
    public static final String SWITCH_OFF = "off";

    /** add for (TFS) 640452 sort and android/data  */
    public static final String EVENT_CLICK_ANDROID_DATA = "android_data";
    public static final String EVENT_CLICK_SORT = "sort";
    public static final String SORT_BY_TIME = "0";
    public static final String SORT_BY_SIZE = "1";
    public static final String SORT_BY_TYPE = "2";
    public static final String SORT_BY_NAME = "3";
    public static final String SORT_BY_TAKEN_TIME = "4";

    public static final String TAG_AD_EXPOSURE = "ad_exposure";

    public static final String SHOW_APPMANAGER_ITEM = "appmanager_show_in_filemanager";
    public static final String EVENT_CLICK_APPMANAGER = "appmanager_click_in_filemanager";

    /**add for global search(TFS: 169363)  */
    public static final String SEARCH_HISTORY_RECORD_CLICK = "history_record_click";
    public static final String SEARCH_HISTORY_RECORD_CLEARUP = "history_record_clearup";
    public static final String SEARCH_TYPE_TAB_ALL_CLICK = "search_type_tab_all_click";
    public static final String SEARCH_TYPE_TAB_VIDEO_CLICK = "search_type_tab_video_click";
    public static final String SEARCH_TYPE_TAB_AUDIO_CLICK = "search_type_tab_audio_click";
    public static final String SEARCH_TYPE_TAB_PICTURE_CLICK = "search_type_tab_picture_click";
    public static final String SEARCH_TYPE_TAB_DOC_CLICK = "search_type_tab_doc_click";
    public static final String SEARCH_TYPE_TAB_APK_CLICK = "search_type_tab_apk_click";
    public static final String SEARCH_TYPE_TAB_ARCHIVES_CLICK = "search_type_tab_archives_click";
    public static final String SEARCH_CONDITION_CLICK = "search_condition_click";
    public static final String SEARCH_CONDITION_TIME_TODAY = "search_condition_time_today";
    public static final String SEARCH_CONDITION_TIME_3DAYS = "search_condition_time_3days";
    public static final String SEARCH_CONDITION_TIME_7DAYS = "search_condition_time_7days";
    public static final String SEARCH_CONDITION_TIME_30DAYS = "search_condition_time_30days";
    public static final String SEARCH_CONDITION_SUPERAPP1 = "search_condition_superapp1";
    public static final String SEARCH_CONDITION_SUPERAPP2 = "search_condition_superapp2";
    public static final String SEARCH_CONDITION_OPPOSHARE = "search_condition_opposhare";
    public static final String SEARCH_CONDITION_BLUETOOTH = "search_condition_bluetooth";
    public static final String SEARCH_CONDITION_DOWNLOAD = "search_condition_download";
    public static final String SEARCH_CONDITION_DOC = "search_condition_doc";
    public static final String SEARCH_CONDITION_AUDIO = "search_condition_audio";
    public static final String SEARCH_CONDITION_VIDEO = "search_condition_video";
    public static final String SEARCH_CONDITION_PICTURE = "search_condition_picture";
    public static final String SEARCH_CONDITION_ARCHIVE = "search_condition_archive";
    public static final String SEARCH_DURATION_TIME = "search_time";

    /**
     * 搜索关键字上传埋点Event_ID
     */
    public static final String SEARCH_KEYWORD = "search_keyword";

    public static final String SUPER_APP_ENTER_EDIT_MODE = "enter_edit_mode";
    public static final String SUPER_APP_SOURCE_DRAG = "source_drag";
    public static final String SUPER_APP_SOURCE_SWITCH = "source_switch";
    public static final String SUPER_APP_SOURCE_SWITCH_LAST_STATUS = "source_switch_last_status";//每天第一次操作开关后的状态
    public static final String SUPER_APP_SOURCE_SWITCH_FIRST_STATUS = "source_switch_first_status";//每天第一次调起进程时的状态
    public static final String SUPER_APP_SOURCE_SWITCH_NAME = "source_switch_name";
    /**
     * 广告开关埋点
     */
    public static final String AD_SWITCH_ID = "ad_switch";
    public static final String AD_SWITCH_STATUS = "ad_switch_status";
    public static final String ANDROID_DATA_ACCESS = "android_data_access";

    public static final String AD_ALL_EVENT = "ad_all_event";//广告全链路（请求、返回、展示）
    public static final String AD_ALL_CLICK = "ad_all_click";//广告点击

    public static final String AD_ACTION_TYPE = "ad_action_type";//行动类别
    public static final String AD_POS_ID = "ad_pos_id";//资源位请求编号
    public static final String AD_PAGE_NAME = "ad_page_name";//资源位所处页面
    public static final String AD_START_TIMESTAMP = "ad_start_timestamp";//发生时间戳
    public static final String AD_RETURN_RESULT = "ad_return_result";//成功、其他失败原因
    public static final String AD_CLICK_TYPE = "ad_click_type";//点击的位置

    /** add end (TFS: 169363)  */

    /** add for (2981352)  */
    public static final String EVENT_UPLOAD_TO_CLOUD = "upload_to_cloud";
    public static final String EVENT_ENCRYPTION_FILE = "encryption_file";
    public static final String EVENT_OTHER_WAYS_TO_OPEN = "other_ways_to_open";
    public static final String FILE_TYPE = "file_type";
    /** add end (2981352） */


    /**
     * add for (TFS)4474281, 满意度的统计
     */
    public static final String TAG_SATISFACTION = "2001611";
    public static final String CLICK_FILE_TYPE_OPTION = "click_file_type_option";
    public static final String FILE_EXTENSION = "file_extension";

    /** add for 4273912 file label */
    public static final String LABEL_TAB_CLICK = "label_tab_show";
    public static final String LABEL_FILE_TAG = "label_file_type_count";
    public static final String LABEL_CANCEL_FILE = "label_cancel_file";
    public static final String LABEL_SEARCH_IN_RESULT = "label_search_in_result";
    public static final String LABEL_SEARCH_CLICK = "label_search_result_click";
    public static final String LABEL_SELECT_OPERATION = "label_file_select_operation";
    public static final String LABEL_AI_SHOW = "label_display_ai_label";
    public static final String LABEL_AI_CLICK = "label_choose_ai";
    public static final String LABEL_KEY_FILE_TYPE = "label_file_type";
    public static final String LABEL_KEY_FILE_LABELS_COUNT = "label_file_labels_count";
    public static final String LABEL_KEY_CANCEL_SCENE = "label_cancel_file";
    public static final String LABEL_VALUE_CANCEL_SCENE_OTHER = "1";
    public static final String LABEL_VALUE_CANCEL_SCENE_LIST = "2";
    public static final String LABEL_KEY_OPERATION = "label_operation";
    public static final String LABEL_VALUE_OPERATION_PIN = "1";
    public static final String LABEL_VALUE_OPERATION_UNPIN = "2";
    public static final String LABEL_VALUE_OPERATION_SEND = "3";
    public static final String LABEL_VALUE_OPERATION_RENAME = "4";
    public static final String LABEL_VALUE_OPERATION_DELETE = "5";
    public static final String LABEL_VALUE_OPERATION_UPLOAD_CLOUD = "6";
    public static final String CRATE_NEW_LABEL = "crate_new_label";
    public static final String CRATE_NEW_LABEL_CLICK_POSITIVE_BUTTON = "crate_new_label_click_positive_button";
    public static final String ADD_FILE_FORM_LABEL_PAGE = "add_file_form_label_page";
    public static final String LONG_PRESS_CARD_TO_ENTER_EDIT_MODE = "long_press_card_to_enter_edit_mode";
    public static final String CLICK_CRAD_UPPER_LEFT_CORNER_TO_ENTER_EDIT_MODE = "click_card_upper_left_corner_to_enter_edit_mode";
    public static final String ADD_LABELCRAD_TO_LAUNCHER_SUCCESS = "add_labelcard_to_launcher_success";

    public static final String RECENT_FILE_MID_CARD_ADD = "recent_file_mid_card_add";

    public static final String RECENT_FILE_BIG_CARD_ADD = "recent_file_big_card_add";

    public static final String RECENT_FILE_CARD_EDIT = "recent_file_card_edit";
    public static final String RECENT_FILE_CARD_EDIT_ENTRANCE = "entrance";

    public static final String RECENT_FILE_CARD_CLICK = "recent_file_card_click";

    public static final String RECENT_FILE_OPEN_FROM_CARD = "recent_file_open_from_card";

    /** add for 4273912 file label end */

    /** add for (TFS)13455 RecycleBin  */
    public static final String MODEL_CODE_RECYCLE_BIN = "2001609";
    public static final String RECYCLE_BIN_OPEN = "recycle_bin_oepn_times";
    public static final String RECYCLE_BIN_RESTORE = "recycle_bin_restore_times";
    public static final String RECYCLE_BIN_DELETE = "recycle_bin_delete_times";
    public static final String RECYCLE_BIN_DELETE_ALL = "delete_all";
    /**add end (TFS)13455 */

    /** add for os13 */
    public static final String ACTION_FAVORITE = "action_favorite";
    public static final String ACTION_RECYCLE_BIN = "action_recycle_bin";
    public static final String ACTION_ENCRYPT = "action_encrypt";
    public static final String CATEGORY_TAB_CLICKED = "category_tab_clicked";
    public static final String ACTION_SEARCH = "action_search";
    public static final String ACTION_SETTING = "action_setting";
    public static final String RECENT_FILE_OPEN = "recent_file_open";
    public static final String RECENT_EXPAND_BUTTON_CLICKED = "recent_expand_button_clicked";
    public static final String OPEN_WITH_ACTION = "open_with_action";
    public static final String MORE_ITEM = "more_item";
    public static final String FILE_BROWSER_EDIT = "file_browser_edit";
    public static final String FILE_BROWSER_NEW_FOLDER = "file_browser_new_folder";
    public static final String FILE_BROWSER_SETTING = "file_browser_setting";
    public static final String LONG_CLICK_ENCRYPT = "long_click_encrypt";

    public static final String FILE_OPERATION = "file_operation";
    /** onCommon end */

    public static final String CLOUD_SHOW = "cloud_show";
    public static final String CLEANUP_SHOW = "cleanup_show";

    /** add for (TFS)4648241 SHOW_ENCRYPT_BOX  */
    public static final String SHOW_ENCRYPT_BOX = "need_show_encrypt_box";

    public static final String TENCENT_DOCS_SHOW = "tencent_docs_show";

    public static final String KING_DOCS_SHOW = "king_docs_show";
    public static final String ENCRYPT_SWITCH_ON = "on";
    public static final String ENCRYPT_SWITCH_OFF = "off";
    /**add end (TFS)4648241 */

    /** add for (TFS)6188675 OWORK_ENTRANCE  */
    public static final String OWORK_ENTRANCE = "owork_entrance";
    /**add end (TFS)6188675 */

    /** add for (5148519) RENAME_FILE  */
    public static final String RENAME_FILE = "rename_file";
    public static final String RELOCATE_FILE = "relocate_file";
    public static final String COPY_FILE = "copy_file";
    public static final String SECRET_FILE_STATUS = "secret_file_status";
    public static final String SOURCE_NAME = "source_name";
    public static final String SOURCE_FILE_TYPE = "source_file_type";
    public static final String SOURCE_FILE_PATH = "source_file_path";
    public static final String TARGET_NAME = "target_name";
    public static final String TARGET_FILE_TYPE = "target_file_type";
    public static final String TARGET_FILE_PATH = "target_file_path";
    public static final String DIRECTORY_FILE_TYPE = "other_file";
    /** add for (5148519) RENAME_FILE end */

    /** add for (5382613) OPEN_DOC  */
    public static final String OPEN_DOC = "open_doc";
    public static final String OPEN_VIDEO = "open_video";
    public static final String OPEN_VIDEO_TIME = "open_video_time";
    public static final String SOURCE_TAB_CLICK = "source_tab_click";
    public static final String SOURCE_TAB_TYPE = "source_tab_type";
    /**add for (5382613) OPEN_DOC end */

    /**
     * 查看txt、epub文本文件默认启动阅读、书城app
     */
    public static final String EVENT_READER_TO_OPEN = "reader_to_open";
    public static final String EVENT_BOOK_TO_OPEN = "book_to_open";

    /**
     * 拖拽，文管拖出数量
     */
    public static final String DRAG_TO_OTHER_APP = "drag_to_other_app";
    public static final String DRAG_TO_OTHER_APP_COUNT = "drag_to_other_app_count";

    public static final String EVENT_OPEN_APP_MARKET_MANAGER = "open_app_market_manager";
    public static final String HAS_RED_DOT = "has_red_dot";

    public static final String EVENT_APK_COUNT_INFO = "apk_count_info";
    public static final String UN_INSTALLED_APK_COUNT = "un_installed_apk_count";
    public static final String INSTALLED_APK_COUNT = "install_apk_count";
    /** add end P00091127 */


    /**
     * 随心开
     */
    public static final String TAG_FILE_OPEN_MODE = "action_file_open_mode";
    public static final String EVENT_CLICK_FILE_OPEN_MODE = "event_click_file_open_mode";
    public static final String CLICK_FILE_OPEN_MODE = "click_file_open_mode";
    public static final String FILE_PREVIEW_INSTALL_DIALOG = "event_open_any_install_dialog";

    /**
     * 2023 Q3优化埋点
     */
    public static final String EVENT_CLICK_STORAGE_FOLDER_FILE = "click_storage_folder_file";
    public static final String CLICK_STORAGE_FOLDER_FILE = "click_storage_folder_file";
    public static final String EVENT_CREATE_FOLDER = "create_folder";
    public static final String CREATE_FROM = "create_from";
    public static final String EVENT_CLICK_APP_FILE = "click_app_file";
    public static final String FOLDER_NAME = "folder_name";
    public static final String EVENT_CLICK_PIC_FOLDER_FILE = "click_pic_folder_file";
    public static final String EVENT_CLICK_EACH_CATEGORY_FILE = "click_each_category_file";
    public static final String CURRENT_PAGE = "current_page";
    public static final String EVENT_FILE_LABEL_COUNT = "file_label_count";
    public static final String EVENT_DOC_FILE_COUNT = "doc_file_count";
    public static final String EVENT_PIC_FILE_COUNT = "pic_file_count";
    public static final String EVENT_VIDEO_FILE_COUNT = "video_file_count";
    public static final String EVENT_AUDIO_FILE_COUNT = "audio_file_count";
    public static final String EVENT_APK_FILE_COUNT = "apk_file_count";
    public static final String EVENT_COMPRESS_FILE_COUNT = "compress_file_count";
    public static final String EVENT_ALL_SUCCESS_SEARCH_RETURN = "all_success_search_return";
    public static final String SEARCH_START_TIME = "search_start_time";
    public static final String SEARCH_END_TIME = "search_end_time";
    public static final String SEARCH_ELAPSED_TIME = "search_elapsed_time";
    public static final String EVENT_CLICK_RESULT_FILE_TYPE = "click_result_file_type";
    public static final String CURRENT_TAB = "current_tab";

    public static final String EVENT_MENU_SEND = "menu_send";
    public static final String EVENT_MENU_LABEL = "menu_label";
    public static final String EVENT_MENU_CUT = "menu_cut";
    public static final String EVENT_MENU_DELETE = "menu_delete";
    public static final String EVENT_MENU_COPY = "menu_copy";
    public static final String EVENT_MENU_DETAIL = "menu_detail";
    public static final String EVENT_MENU_UPLOAD_CLOUD = "menu_upload_cloud";
    public static final String EVENT_MENU_RENAME = "menu_rename";
    public static final String EVENT_MENU_ENCRYPT = "menu_encrypt";
    public static final String EVENT_MENU_OPEN_WITH = "menu_open_with";

    public static final String EVENT_MENU_DOWNLOAD_FROM_DRIVE = "menu_download_drive";

    public static final String EVENT_MENU_COMPRESS = "menu_compress";

    public static final String EVENT_MENU_DECOMPRESS = "menu_decompress";

    public static final String OPERATION_PAGE = "operation_page";

    public static final String EVENT_ALL_OPERATION = "all_operation";
    public static final String NUM = "num";
    public static final String OP_TYPE = "optype";
    public static final String OP_TIME = "optime";
    public static final String DEST_PATH = "destpath";
    public static final String PIC_NUM = "pic";
    public static final String VIDEO_NUM = "video";
    public static final String AUDIO_NUM = "audio";
    public static final String DOC_NUM = "doc";
    public static final String APK_NUM = "apk";
    public static final String COMPRESS_NUM = "compress";
    public static final String FOLDER_NUM = "folder";
    public static final String EVENT_ALL_DELETE_TOTALLY = "delete_totally";
    public static final String IS_DELETE_ALL = "is_delete_all";

    public static final String EVENT_PAGE_SEARCH = "page_search";
    public static final String EVENT_PAGE_SORT = "page_sort";
    public static final String EVENT_PAGE_EDIT = "page_edit";
    public static final String EVENT_PAGE_SETTING = "page_setting";
    public static final String EVENT_PAGE_TAB = "page_tab";
    public static final String OPERATION_TAB = "operation_tab";
    public static final String EVENT_CLICK_ALL_SYSTEM_FILE = "click_all_system_file";
    public static final String RECENT_TAB_SORT_CLICK = "recent_tab_sort_click";
    public static final String ALBUM_SORT_CLICK = "album_sort_click";

    public static final String EVENT_DECOMPRESS_FILE = "decompress_file";
    public static final String EVENT_OPEN_AUDIO_FILE = "open_audio_file";
    public static final String APP_PKG = "app_pkg";
    public static final String APP_NAME = "app_name";
    public static final String EVENT_SEARCH_CONDITION_TYPE = "search_condition_type";

    public static final String SCAN_MODE_FILE_DRIVE_SWITCH = "file_drive_scan_mode";

    /**
     * 打开三方云文档
     */
    public static final String OPEN_DRIVE_FILE = "open_drive_file";

    /**
     * 通过小程序打开三方云文档
     */
    public static final String OPEN_DRIVE_FILE_BY_MINIAPP = "open_drive_file_by_miniApp";

    /**
     * 重命名文件
     */
    public static final String RENAME_DRIVE_FILE = "rename_drive_file";
    public static final String RENAME_DRIVE_FILE_FAIL = "rename_drive_file_fail";
    /**
     * 下载文件
     */
    public static final String DOWNLOAD_DRIVE_FILE = "download_drive_file";
    public static final String DOWNLOAD_DRIVE_FILE_FAIL = "download_drive_file_fail";
    /**
     * 删除文件
     */
    public static final String DELETE_DRIVE_FILE = "delete_drive_file";
    public static final String DELETE_DRIVE_FILE_FAIL = "delete_drive_file_fail";
    /**
     * 授权
     */
    public static final String DRIVE_FILE_AUTH_FAIL = "drive_file_auth_fail";
    public static final String DRIVE_FILE_CANCEL_AUTH = "drive_file_cancel_auth";
    public static final String DRIVE_FILE_CANCEL_AUTH_FAIL = "drive_file_cancel_auth_fail";

    /**
     * 获取文档列表失败
     */
    public static final String DRIVE_FILE_GET_LIST_FAIL = "drive_file_get_list_fail";

    /**
     * 文件来源
     */
    public static final String FILE_SOURCE = "file_source";

    /**
     * 失败的原因
     */
    public static final String ERROR = "error";

    /**
     * 文件个数
     */
    public static final String COUNT = "count";

    /**
     * 显示分布式文管
     */
    public static final String EVENT_SHOW_DFM_ENTRY = "event_show_dfm_entry";
    /**
     * 点击分布式文管
     */
    public static final String EVENT_CLICK_DFM_ENTRY = "click_dfm_entry";
    /**
     * 从什么地方进入文管
     */
    public static final String EVENT_ENTRY_DFM_FROM = "entry_dfm_from";
    /**
     * 本机设备名称
     */
    public static final String SELF_DEVICE_NAME = "self_device_name";
    /**
     * 对端设备名称
     */
    public static final String CONNECT_DEVICE_NAME = "connect_device_name";
    /**
     * 连接结果，0：连接失败，>0 表示连接用时间
     */
    public static final String CONNECT_RESULT = "connect_result";
    /**
     * 从哪个入口进入文管，0：表示设备互联
     */
    public static final String ENTRY_FROM = "entry_from";
    /**
     * 搜索结果列表，上传的埋点来源key名称，对应
     * 本地：local
     * otg：otg
     * 跨设备：dfm
     * 腾讯文档：tencentDoc
     * 金山文档：kDoc
     * 3方应用文档：thirdapp
     */
    public static final String SEARCH_RESULT_KEY_SOURCE = "item_source";

    /**
     * 搜索结果列表，上传的埋点来源key名称，中3方应用的包名
     */
    public static final String SEARCH_RESULT_KEY_PACKAGENAME = "item_source_packagename";
    /**
     * 搜索结果列表，文件名称
     */
    public static final String SEARCH_RESULT_KEY_FILENAME = "item_filename";

    /**
     * 搜索结果列表，文件格式
     */
    public static final String SEARCH_RESULT_KEY_FILE_TYPE = "item_filetype";

    /**
     * 搜索结果列表文件操作长按操作上传的埋点Event_ID
     */
    public static final String SEARCH_RESULT_ITEM_OPERATE_EVENT = "search_item_operate_event";


    /**
     * 搜索结果列表，操作类型，包含： 复制，打标签，移动，删除，复制，详情，重命名，设为私密等
     *     const val OP_COPY = "copy"
     *     const val OP_CUT = "cut"
     *     const val OP_DELETE = "delete"
     *     const val OP_SEND = "send"
     *     const val OP_UPLOAD_CLOUD = "upload_cloud"
     *     const val OP_RENAME = "rename"
     *     const val OP_ENCRYPT = "encrypt"
     *     const val OP_OPEN_WITH = "open_with"
     */
    public static final String SEARCH_RESULT_KEY_OPERATION = "operation";

    /**
     * 搜索结果列表文件操作长按的单选和多选
     * 0 = 单端
     * 1 = 多选
     */
    public static final String SEARCH_RESULT_KEY_SELECT_MODE = "selectemode";


    /**
     * 搜索结果列表文件操作长按的单选和多选
     * 0 = 单端
     * 1 = 多选
     */
    public static final String SEARCH_RESULT_KEY_FROM_SEARCH = "from_search";
    /**
     * 三方应用采集时的埋点id
     */
    public static final String THIRD_FILE_DECT_EVENT_ID = "third_app_file_detect_event";

    /**
     * 3方应用采集时配置版本号
     */
    public static final String THIRD_FILE_DECT_KEY_CONFIG_VERSION = "detect_config_ver";
    /**
     * 3方应用采集时目标应用的packagename
     */
    public static final String THIRD_FILE_DECT_KEY_TARGET_PACKAGENAME = "target_package_name";

    public static final String THIRD_FILE_DECT_KEY_TARGET_PACKAGE_VERSION = "target_package_version";

    /**
     * 3方应用采集时目标应用的预览界面
     */
    public static final String THIRD_FILE_DECT_KEY_PREVIEW_ACTIVITY = "preview_activity";
    /**
     * 3方应用采集时目标应用的采集界面
     */
    public static final String THIRD_FILE_DECT_KEY_EXACT_ACTIVITY = "exact_activity";


    /**
     * 采集失败时的错误码，具体对饮到哪个步骤采集失败
     */
    public static final String THIRD_FILE_DECT_KEY_ERROR_CODE = "error_code";

    /**
     * 采集失败时，带入的图文提取框架中的采集结果
     */
    public static final String THIRD_FILE_DECT_KEY_VIEW_EXACT_RESULT = "view_exact_result";

    /**
     * 采集失败时，导致不匹配的正则表达式
     */
    public static final String THIRD_FILE_DECT_KEY_DATA_BEAN = "data_bean";

    /**
     * dmp版本号
     */
    public static final String THIRD_FILE_DMP_VERSION = "dmp_ver";

    /**
     * 场景智能版本号
     */
    public static final String THIRD_FILE_SCENE_VERSION = "scence_ver";

    /**
     * 采集成功失败
     * 0 = 失败
     * 1 = 成功
     */
    public static final String THIRD_FILE_DECT_KEY_RESULT = "result";


    /**
     * 能力初始化事件
     */
    public static final String THIRD_FILE_INIT_EVENT_ID = "third_app_file_init_event";

    /**
     * 初始化结果，正确或错误码
     */
    public static final String THIRD_FILE_SUPPORT_RESULT_CODE = "support_code";


    /**
     * 点击三方引用开关状态埋点eventid
     */
    public static final String THIRD_FILE_SWITCH_EVENT_ID = "third_app_switch_event";

    /**
     * 点击三方引用开关状态
     */
    public static final String THIRD_FILE_SWITCH_STATUS = "switch_status";

    /**
     * 打开状态
     */
    public static final int THIRD_FILE_SWITCH_STATUS_OPEN = 1;
    /**
     * 关闭状态
     */
    public static final int THIRD_FILE_SWITCH_STATUS_CLOSE = 0;

    /**
     * 点击三方引用授权卡片按钮eventid
     */
    public static final String THIRD_FILE_CARD_CLICK_EVENT_ID = "third_app_card_click_event";

    /**
     * 点击三方引用授权卡片按钮
     */
    public static final String THIRD_FILE_CARD_CLICK_ITEM = "click_button";

    /**
     * 系统文件访问权限开关
     */
    public static final String ANDROID_DATA_SWITCH_EVENT_ID = "android_data_switch_event";
    public static final String ANDROID_DATA_SWITCH_STATUS = "switch_status";

    public static final String DEEP_SEARCH_CLICK_EVENT = "deep_search_click_event";

    public static final int THIRD_FILE_CARD_CLICK_IGNORE = 0;
    public static final int THIRD_FILE_CARD_CLICK_OPEN = 1;
    public static final String AD_EVENT_CLICK = "ad_click";
    public static final String AD_EVENT_FILL = "ad_fill";
    public static final String AD_EVENT_SHOW = "ad_show";
    public static final String AD_EVENT_REQUEST = "ad_request";
    public static final String AD_REQUEST_FAIL = "ad_request_fail";

    public static final String INITIAL_MAX = "initial_max";
    public static final String AD_SOUCE_MAX = "max";

    public static final String INITIAL_OPLUS = "initial_oplus";
    public static final String AD_SOUCE_OPLUS = "oplus";

    public static final String AD_EVENT_TYPE = "ad_event_type";

    public static final String EVENT_AD_LOAD_TAG = "01_0000";
    public static final String REALME_FILE_EVENT = "realme_file_event";

    public static final String MAX_FILE_DAU = "max_File_DAU";
    /**
     * 插屏广告
     */
    public static final String INTERSTITIAL_ADS_REQ = "Interstitial_Ads_Req";
    public static final String INTERSTITIAL_ADS_REQ_FAIL = "Interstitial_Ads_Req_Fail";
    public static final String RECENT_INTERSTITIAL_ADS_FILL = "Recent_Interstitial_Ads_Fill";
    public static final String RECENT_INTERSTITIAL_ADS_DISPLAY = "Recent_Interstitial_Ads_Display";
    public static final String RECENT_INTERSTITIAL_ADS_CLICK = "Recent_Interstitial_Ads_Click";
    public static final String LABEL_INTERSTITIAL_ADS_FILL = "label_Interstitial_Ads_Fill";
    public static final String LABEL_INTERSTITIAL_ADS_DISPLAY = "label_Interstitial_Ads_Display";
    public static final String LABEL_INTERSTITIAL_ADS_CLICK = "label_Interstitial_Ads_Click";

    /**
     * banner
     */
    public static final String BANNER_ADS_REQ = "Banner_Ads_Req";
    public static final String BANNER_ADS_REQ_FAIL = "Banner_Ads_Req_Fail";
    public static final String HOMEPAGE_BANNER_ADS_FILL = "HomePage_Banner_Ads_Fill";
    public static final String HOMEPAGE_BANNER_ADS_DISPALY = "HomePage_Banner_Ads_Dispaly";
    public static final String HOMEPAGE_BANNER_ADS_CLICK = "HomePage_Banner_Ads_Click";
    public static final String SECONDARYPAGE_BANNER_ADS_FILL = "SecondaryPage_Banner_Ads_Fill";
    public static final String SECONDARYPAGE_BANNER_ADS_DISPALY = "SecondaryPage_Banner_Ads_Dispaly";
    public static final String SECONDARYPAGE_BANNER_ADS_CLICK = "SecondaryPage_Banner_Ads_Click";

    /**
     * 原生广告
     */
    public static final String NATIVE_ADS_REQ = "Native_Ads_Req";
    public static final String NATIVE_ADS_REQ_FAIL = "Native_Ads_Req_Fail";
    public static final String HOMEPAGE_NATIVE_ADS_FILL = "HomePage_Native_Ads_Fill";
    public static final String HOMEPAGE_NATIVE_ADS_DISPLAY = "HomePage_Native_Ads_Display";
    public static final String HOMEPAGE_NATIVE_ADS_CLICK = "HomePage_Native_Ads_Click";
    public static final String SECONDARYPAGE_NATIVE_ADS_FILL = "SecondaryPage_Native_Ads_Fill";
    public static final String SECONDARYPAGE_NATIVE_ADS_DISPLAY = "SecondaryPage_Native_Ads_Display";
    public static final String SECONDARYPAGE_NATIVE_ADS_CLICK = "SecondaryPage_Native_Ads_Click";

    public static final String AD_EVENT_REQUEST_SUCCESS = "ad_request_success";
    /*
    大屏精品化埋点
     */
    public static final String PAD_SLIDE_EXPAND_CLICK_EVENT = "pad_slide_expand_click_event";
    public static final String PAD_SLIDE_EDIT_CLICK_EVENT = "pad_slide_edit_click_event";
    public static final String PAD_SHOW_RECENT_CAMERA_EVENT = "pad_show_recent_camera_event";
    public static final String SHOW_PREVIEW_MODEL_EVENT = "show_preview_model_event";
    public static final String DRAG_FILES_EVENT = "drag_files_event";
    public static final String DRAG_FOLDER_EVENT = "drag_folder_event";
    public static final String FROM = "from";
    public static final String CLICK_STATE_KEY = "state";
    public static final String OPEN_VALUE = "1";
    public static final String CLOSE_VALUE = "0";
    /*end*/

    /*快捷方式*/
    public static final String KEY_SHORTCUT_EVENT = "key_shortcut_event";
    public static final String SHORTCUT_EVENT_FILE_TYPE = "file_type";
    public static final String SHORTCUT_EVENT_OPE = "ope";
    public static final int SHORTCUT_OPE_ADD = 0;
    public static final int SHORTCUT_OPE_UPDATE = 1;
    public static final int SHORTCUT_OPE_DELETE = 2;

    /*
    远程Mac文件查看/管理埋点
     */
    public static final String EVENT_REMOTE_DEV_CLICK = "event_remote_dev_click"; //用户点击首页远程设备
    public static final String EVENT_DEV_STATUS_CHANGE = "event_dev_status_change"; //远程设备状态发生改变
    public static final String EVENT_REMOTE_FILE_OPERATE = "event_remote_file_operate"; //远程设备文件操作
    public static final String REMOTE_KEY_DEV_STATUS = "status"; //设备状态
    public static final String REMOTE_KEY_DEV_TYPE = "dev_type"; //设备类型
    public static final String REMOTE_KEY_IS_BACKGROUND = "is_bg"; //是否文管在后台时触发
    public static final String REMOTE_KEY_OPERATE_TYPE = "ope_type"; //操作类型
    public static final String REMOTE_KEY_FILE_COUNT = "count"; //文件数量
    public static final String REMOTE_KEY_FILE_SIZE = "size"; //文件大小
    public static final int REMOTE_VALUE_DEV_TYPE_MAC = 1; //Mac设备
    public static final int REMOTE_VALUE_DEV_TYPE_WINDOWS = 2; //Windows设备
    public static final int REMOTE_VALUE_DEV_UNDISCOVERED = 0; //设备已离线
    public static final int REMOTE_VALUE_DEV_DISCOVERED = 1; //设备可连接
    public static final int REMOTE_VALUE_DEV_CONNECTED = 2; //设备已连接
    public static final String REMOTE_VALUE_DEV_DISCONNECTED_TO_CONNECTED = "02"; //设备状态变化：从断开到已连接
    public static final String REMOTE_VALUE_DEV_CONNECTED_TO_DISCONNECTED = "20"; //设备状态变化：从已连接到断开
    public static final int REMOTE_VALUE_FOREGROUND = 0; //前台
    public static final int REMOTE_VALUE_BACKGROUND = 1; //后台
    public static final int REMOTE_VALUE_OPERATE_GET_FILE_LIST = 1; //获取文件列表
    public static final int REMOTE_VALUE_OPERATE_DOWNLOAD = 2; //下载
    public static final int REMOTE_VALUE_OPERATE_DRAG_DROP_DOWNLOAD = 3; //拖拽触发的下载
    /*end*/

    /**
     * 云盘埋点
     */
    public static final String CLOUD_DRIVER_SUBTITLE = "cloud_driver_subtitle";

    static final long ONE_DAY_MILLIS = 24 * 60 * 60 * 1000;
    private static final String APP_CODE = "20016";
    /** add for 4273912 file label */
    private static final String LABEL_VALUE_TYPE_AUDIO = "1";
    private static final String LABEL_VALUE_TYPE_DOC = "2";
    private static final String LABEL_VALUE_TYPE_COMPRESS = "3";
    private static final String LABEL_VALUE_TYPE_PIC = "4";
    private static final String LABEL_VALUE_TYPE_VIDEO = "5";
    private static final String LABEL_VALUE_TYPE_DIRECTORY = "6";
    private static final String LABEL_VALUE_TYPE_INSTALL_APK = "7";
    private static final String LABEL_VALUE_TYPE_OTHER = "8";
    /** add for 4273912 file label end */

    /** add for (TFS)13455 RecycleBin  */
    private static final String RECYCLE_BIN_DELETE_SAVED_DATE = "recycle_bin_delete_file_saved_date";
    private static final String RECYCLE_BIN_RESTORE_SAVED_DATE = "recycle_bin_restore_file_saved_date";
    private static final String RECYCLE_BIN_AUTO_CLEAN_FILES_COUNT = "recycle_bin_auto_clean_files_count";
    private static final String RECYCLE_BIN_DELETE_FILES_COUNT = "recycle_bin_delete_forever_files_count";
    private static final String MAP_SAVE_DATE = "save_date";
    private static final String MAP_FILE_COUNT = "file_count";
    /**add end (TFS)13455 */
    private static final String SIZE_PICTURE = "size_pic";
    private static final String SIZE_VIDEO = "size_video";
    private static final String SIZE_AUDIO = "size_audio";
    private static final String SIZE_DOC = "size_doc";
    private static final String SIZE_INSTALL_FILE = "size_install_file";
    private static final String SIZE_COMPRESS = "size_compress";

    // 启动入口
    private static final String EVENT_ENTRY_LAUNCH = "entry_launch_event";
    private static final String ENTRY = "entry";
    private static final String SOURCE = "source";
    private static final String CARD_CONTENT_COMB = "card_content_comb";
    private static final String LANDING_PAGE = "landing_page";
    private static final String CLICK_TIMESTAMP = "click_timestamp";
    private static final String LAUNCH_TYPE = "launch_type";
    private static final String SESSION_ID = "session_id";
    // 页面曝光
    private static final String EVENT_PAGE_EXPOSURE = "page_exposure_event";
    private static final String PAGE_NAME = "page_name";
    private static final String EXP_TIMESTAMP = "exp_timestamp";
    private static final String PARENT_PAGE_NAME = "parent_page_name";
    // 广告全链路（加载、请求、返回、展示）
    private static final boolean ENABLE_ADVERT_STATISTICS = true;
    private static final String EVENT_AD_FLOW = "ad_flow_event";
    private static final String ACTION_TYPE = "action_type";
    private static final String UNIQUE_AD_REQUEST_ID = "unique_ad_request_id";
    private static final String AD_SLOT_ID = "ad_slot_id";
    private static final String START_TIMESTAMP = "start_timestamp";
    private static final String RETURN_RESULT = "return_result";
    private static final String SDK_SOURCE = "sdk_source";
    private static final String UU_ID = "uu_id";
    private static final String SDK_VERSION = "sdk_version";
    private static final String MEDIA_REQ_ID = "mediaReqId";
    private static final String AD_REQ_ID = "adReqId";
    private static final String TRACE_ID = "traceId";
    /**
     * 广告类型
     */
    private static final String AD_ACTION_REQUEST = "ad_request"; // 发送广告请求
    private static final String AD_ACTION_RETURN = "ad_return"; // 从广告侧拿到内容
    private static final String AD_ACTION_SHOW = "ad_show"; // 广告展示在资源位
    private static final String AD_ACTION_LOAD = "ad_load"; // 进入有广告展示位的页面
    /**
     * 资源显示位置
     */
    private static final String AD_SLOT_HOME = "ad_native_home";//首页中插
    private static final String AD_SLOT_SECOND_PAGE = "ad_native_second";//看具体二级页是什么内容就上报什么）
    private static final String AD_SLOT_SPLASH = "ad_splash";//开屏广告
    private static final String AD_SLOT_SPLASH_HOT = "ad_splash_hot";//开屏广告(热启动)
    private static final String AD_SLOT_MOVE = "ad_move";//文件搬家广告
    /**
     * 网络请求状态 1：请求中，2 ：成功、3：失败
     */
    private static final String AD_RETURN_REQUEST = "1";
    private static final String AD_RETURN_SUCCESS = "2";
    private static final String AD_RETURN_FAIL = "3";
    // 广告点击
    private static final String EVENT_AD_CLICK = "ad_click_event";

    private static final String TAG = "StatisticsUtils";

    private static final ConcurrentHashMap<String, Long> MEMORY_LAST_TIME = new ConcurrentHashMap<>();
    private static boolean sStatisticsEnable = true;
    private static String sAdSdkVersion = "";
    private static Map<String,String> sEncryptPkgMap = new ConcurrentHashMap<>();

    public static void setStatisticsEnable(boolean sStatisticsEnable) {
        StatisticsUtils.sStatisticsEnable = sStatisticsEnable;
    }

    public static void onCommon(Context context, String eventId) {
        if (context != null) {
            OplusTrack.onCommon(context, APP_CODE, APP_CODE, eventId, null);
        }
    }

    public static void onCommon(Context context, String eventId, String logTag) {
        if (context != null) {
            Log.d(TAG, "onCommon " + eventId);
            OplusTrack.onCommon(context, APP_CODE, logTag, eventId, null);
        }
    }

    public static void onCommon(Context context, String eventId, Map<String, String> logMap) {
        if (context != null) {
            OplusTrack.onCommon(context, APP_CODE, APP_CODE, eventId, logMap);
        }
    }

    /**
     * 统计埋点
     * @param context
     * @param logTag 事件分组
     * @param eventId 事件id
     * @param logMap 埋点数据
     */
    public static void onCommon(Context context, String logTag, String eventId, Map<String, String> logMap) {
        if (context != null) {
            OplusTrack.onCommon(context, APP_CODE, logTag, eventId, logMap);
        }
    }

    /**
     * 文件随心开统计埋点
     *
     * @param context
     * @param eventId 事件id
     * @param logMap  埋点数据
     */
    public static void onCommonByOpenAnyFile(Context context, String eventId, Map<String, String> logMap) {
        if (context != null) {
            OplusTrack.onCommon(context, APP_CODE, TAG_FILE_OPEN_MODE, eventId, logMap);
        }
    }

    public static void onPause(Context context) {
        if (!sStatisticsEnable) {
            return;
        }
        if (context != null) {
            OplusTrack.onPause(context);
        }
    }

    public static void onResume(Context context) {
        if (!sStatisticsEnable) {
            return;
        }
        if (context != null) {
            OplusTrack.onResume(context);
        }
    }

    public static void nearMeStatistics(Context context, String event) {
        if (!sStatisticsEnable) {
            return;
        }
        Log.d(TAG, "ev->" + event);
        onCommon(context, event);
    }

    public static void nearMeStatistics(Context context, int category) {
        if (!sStatisticsEnable) {
            return;
        }
        switch (category) {
            case CategoryHelper.CATEGORY_AUDIO:
                onCommon(context, CATEGORY_AUDIO);
                break;
            case CategoryHelper.CATEGORY_VIDEO:
                onCommon(context, CATEGORY_VEDIO);
                break;
            case CategoryHelper.CATEGORY_IMAGE:
                onCommon(context, CATEGORY_PICTURE);
                break;
            case CategoryHelper.CATEGORY_DOC:
                onCommon(context, CATEGORY_DOC);
                break;
            case CategoryHelper.CATEGORY_APK:
                onCommon(context, CATEGORY_APK);
                break;
            case CategoryHelper.CATEGORY_DOWNLOAD:
                onCommon(context, CATEGORY_DOWNLOAD);
                break;
            case CategoryHelper.CATEGORY_COMPRESS:
                onCommon(context, COMPRESS_CATEGORY_ICON_CLICK_COUNT);
                break;
            case CategoryHelper.CATEGORY_BLUETOOTH:
                onCommon(context, CATEGORY_BLUETOOTH);
                break;
            case CategoryHelper.CATEGORY_QQ:
                onCommon(context, CATEGORY_QQ);
                break;
            case CategoryHelper.CATEGORY_MICROMSG:
                onCommon(context, CATEGORY_WECHAT);
                break;
            case CategoryHelper.CATEGORY_TENCENT_DOCS:
                onCommon(context, CATEGORY_TENCENT_DOCS);
                break;
            case CategoryHelper.CATEGORY_K_DOCS:
                onCommon(context, CATEGORY_KING_DOCS);
                break;
            default:
                Map<String, String> eventMap = new HashMap<>();
                eventMap.put(SOURCE_TAB_TYPE, String.valueOf(category));
                onCommon(context, SOURCE_TAB_CLICK, eventMap);
                break;
        }
    }

    /**
     * commit file deleted count by hand or by auto-clean in RecycleBin
     *
     * @param context
     * @param category Effective value {@link RecycleBinLiveData#RECYCLE_DELETE},{@link RecycleBinLiveData#RECYCLE_AUTO_CLEAN }
     * @param count
     */
    public static void nearMeStatisticsRecycleBinDeleteCount(Context context, int category, long count) {
        if (!sStatisticsEnable) {
            return;
        }
        if (count <= 0) {
            return;
        }
        Map<String, String> eventMap = new HashMap<String, String>();
        switch (category) {
            case RecycleBinLiveData
                    .RECYCLE_DELETE: {
                eventMap.put(MAP_FILE_COUNT, "" + count);
                if (context != null) {
                    OplusTrack.onCommon(context, APP_CODE, MODEL_CODE_RECYCLE_BIN, RECYCLE_BIN_DELETE_FILES_COUNT, eventMap);
                }
                break;
            }
            case RecycleBinLiveData
                    .RECYCLE_AUTO_CLEAN: {
                eventMap.put(MAP_FILE_COUNT, "" + count);
                if (context != null) {
                    OplusTrack.onCommon(context, APP_CODE, MODEL_CODE_RECYCLE_BIN, RECYCLE_BIN_AUTO_CLEAN_FILES_COUNT, eventMap);
                }
                break;
            }
            default:
                break;
        }
    }

    /**
     * commit file saved days num in RecycleBin
     *
     * @param context
     * @param category Effective value {@link RecycleBinLiveData#RECYCLE_DELETE},
     * {@link RecycleBinLiveData#RECYCLE_AUTO_CLEAN },{@link RecycleBinLiveData#RECYCLE_RESTORE }
     * @param saveDays
     */
    public static void nearMeStatisticsRecycleBinFileSaveDate(Context context, int category, long saveDays) {
        if (!sStatisticsEnable) {
            return;
        }
        if (saveDays < 0) {
            return;
        }
        Map<String, String> eventMap = new HashMap<String, String>();
        switch (category) {
            case RecycleBinLiveData
                    .RECYCLE_DELETE:
            case RecycleBinLiveData
                    .RECYCLE_AUTO_CLEAN: {
                eventMap.put(MAP_SAVE_DATE, "" + saveDays);
                if (context != null) {
                    OplusTrack.onCommon(context, APP_CODE, MODEL_CODE_RECYCLE_BIN, RECYCLE_BIN_DELETE_SAVED_DATE, eventMap);
                }
                break;
            }
            case RecycleBinLiveData
                    .RECYCLE_RESTORE: {
                eventMap.put(MAP_SAVE_DATE, "" + saveDays);
                if (context != null) {
                    OplusTrack.onCommon(context, APP_CODE, MODEL_CODE_RECYCLE_BIN, RECYCLE_BIN_RESTORE_SAVED_DATE, eventMap);
                }
                break;
            }
            default:
                break;
        }
    }

    public static void nearMeStatisticsSuccessSearch(Context context, int category) {
        if (!sStatisticsEnable) {
            return;
        }
        if (context != null) {
            switch (category) {
                case CategoryHelper.CATEGORY_AUDIO:
                    OplusTrack.onCommon(context, APP_CODE, CATEGORY_AUDIO_SUCCESS_SEARCH, null);
                    break;
                case CategoryHelper.CATEGORY_VIDEO:
                    OplusTrack.onCommon(context, APP_CODE, CATEGORY_VEDIO_SUCCESS_SEARCH, null);
                    break;
                case CategoryHelper.CATEGORY_IMAGE:
                    OplusTrack.onCommon(context, APP_CODE, CATEGORY_PICTURE_SUCCESS_SEARCH, null);
                    break;
                case CategoryHelper.CATEGORY_DOC:
                    OplusTrack.onCommon(context, APP_CODE, CATEGORY_DOC_SUCCESS_SEARCH, null);
                    break;
                case CategoryHelper.CATEGORY_APK:
                    OplusTrack.onCommon(context, APP_CODE, CATEGORY_APK_SUCCESS_SEARCH, null);
                    break;
                case CategoryHelper.CATEGORY_QQ:
                    OplusTrack.onCommon(context, APP_CODE, CATEGORY_QQ_SUCCESS_SEARCH, null);
                    break;
                case CategoryHelper.CATEGORY_MICROMSG:
                    OplusTrack.onCommon(context, APP_CODE, CATEGORY_WECHAT_SUCCESS_SEARCH, null);
                    break;
                default:
                    break;
            }
        }
    }

    public static void statisticsCategoryMemorySize(int category, String totalMemorySize) {
        if (!sStatisticsEnable) {
            return;
        }
        String sizeType = null;
        switch (category) {
            case CategoryHelper.CATEGORY_APK:
                sizeType = SIZE_INSTALL_FILE;
                break;
            case CategoryHelper.CATEGORY_AUDIO:
                sizeType = SIZE_AUDIO;
                break;
            case CategoryHelper.CATEGORY_DOC:
                sizeType = SIZE_DOC;
                break;
            case CategoryHelper.CATEGORY_VIDEO:
                sizeType = SIZE_VIDEO;
                break;
            case CategoryHelper.CATEGORY_IMAGE:
                sizeType = SIZE_PICTURE;
                break;
            case CategoryHelper.CATEGORY_COMPRESS:
                sizeType = SIZE_COMPRESS;
                break;
            default:
                break;
        }
        if (sizeType != null) {
            long lastTime = 0;
            try {
                if (MEMORY_LAST_TIME.containsKey(sizeType)) {
                    lastTime = MEMORY_LAST_TIME.get(sizeType);
                }
            } catch (Exception e) {
                Log.d(TAG, "get memoryLastTime fail");
            }
            long currentTimeMillis = System.currentTimeMillis();
            if (Math.abs(currentTimeMillis - lastTime) > ONE_DAY_MILLIS) {
                HashMap<String, String> value = new HashMap<>();
                value.put(sizeType, totalMemorySize);
                onCommon(MyApplication.getSAppContext(), sizeType, value);
                MEMORY_LAST_TIME.put(sizeType, currentTimeMillis);
            }
        }
    }

    public static String getLabelFileType(int localType) {
        if (MimeTypeHelper.Companion.isAudioType(localType)) {
            return LABEL_VALUE_TYPE_AUDIO;
        } else if (MimeTypeHelper.Companion.isDocType(localType)) {
            return LABEL_VALUE_TYPE_DOC;
        } else if (MimeTypeHelper.Companion.isCompressType(localType)) {
            return LABEL_VALUE_TYPE_COMPRESS;
        } else if (MimeTypeHelper.IMAGE_TYPE == localType) {
            return LABEL_VALUE_TYPE_PIC;
        } else if (MimeTypeHelper.VIDEO_TYPE == localType) {
            return LABEL_VALUE_TYPE_VIDEO;
        } else if (MimeTypeHelper.DIRECTORY_TYPE == localType) {
            return LABEL_VALUE_TYPE_DIRECTORY;
        } else if (MimeTypeHelper.APPLICATION_TYPE == localType) {
            return LABEL_VALUE_TYPE_INSTALL_APK;
        } else {
            return LABEL_VALUE_TYPE_OTHER;
        }
    }

    /**
     * 显示dfm
     *
     * @param connectDeviceName
     */
    public static void showDFMEntry(Context context, String connectDeviceName) {
        Map<String, String> map = new HashMap<>();
        String selfDeviceName = AppUtils.getDevice();
        map.put(SELF_DEVICE_NAME, selfDeviceName);
        map.put(CONNECT_DEVICE_NAME, connectDeviceName);
        onCommon(context, EVENT_SHOW_DFM_ENTRY, map);
    }

    /**
     * 点击dfm
     *
     * @param context
     * @param connectDeviceName
     * @param connectTime       0:表示连接失败，>0 连接时长 单位：ms
     */
    public static void clickDFMEntry(Context context, String connectDeviceName, long connectTime) {
        Map<String, String> map = new HashMap<>();
        String selfDeviceName = AppUtils.getDevice();
        map.put(SELF_DEVICE_NAME, selfDeviceName);
        map.put(CONNECT_DEVICE_NAME, connectDeviceName);
        map.put(CONNECT_RESULT, connectTime + "");
        onCommon(context, EVENT_CLICK_DFM_ENTRY, map);
    }

    /**
     * 统计从那儿进来文管
     *
     * @param context
     * @param connectDeviceName
     * @param entryFrom         0:表示从设备互联进入
     */
    public static void statisticsDFMEntryFrom(Context context, String connectDeviceName, int entryFrom) {
        Map<String, String> map = new HashMap<>();
        String selfDeviceName = AppUtils.getDevice();
        map.put(SELF_DEVICE_NAME, selfDeviceName);
        map.put(CONNECT_DEVICE_NAME, connectDeviceName);
        map.put(ENTRY_FROM, entryFrom + "");
        onCommon(context, EVENT_ENTRY_DFM_FROM, map);
    }

    /**
     * 统计点击三方应用搜索的开关状态
     * @param switchStatus  THIRD_FILE_SWITCH_STATUS_OPEN, THIRD_FILE_SWITCH_STATUS_CLOSE
     */
    public static void statisticsThirdFileSwitch(Context context, int switchStatus) {
        Map<String, String> map = new HashMap<>();
        map.put(THIRD_FILE_SWITCH_STATUS, String.valueOf(switchStatus));
        Log.i(TAG, "statisticsThirdFileSwitch switchStatus : " + switchStatus);
        onCommon(context, THIRD_FILE_SWITCH_EVENT_ID, map);
    }


    /**
     * 统计点击三方应用引导授权卡片的点击事件
     * @param button  THIRD_FILE_SWITCH_STATUS_OPEN, THIRD_FILE_SWITCH_STATUS_CLOSE
     */
    public static void statisticsThirdFileCardClick(Context context, int button) {
        Map<String, String> map = new HashMap<>();
        map.put(THIRD_FILE_CARD_CLICK_ITEM, String.valueOf(button));
        Log.i(TAG, "statisticsThirdFileCardClick button : " + button);
        onCommon(context, THIRD_FILE_CARD_CLICK_EVENT_ID, map);
    }


    /**
     * 统计应用启动入口
     *
     * @param context     上下文
     * @param entry       入口包名或入口类型,分两种；外部调用传包名；文管自建入口通过标识自行区分
     * @param cardComb    桌面卡片内容组合
     * @param landingPage 落地页:包名.界面名,后台运行时为空
     */
    public static void statisticsEntryLaunch(Context context, String entry,  String cardComb, String landingPage) {
        if (!ENABLE_ADVERT_STATISTICS) {
            return;
        }
        if (TextUtils.isEmpty(entry)) {
            Log.d(TAG, "statisticsEntryLaunch entry is empty");
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put(ENTRY, encodePkg(entry));
        map.put(CARD_CONTENT_COMB, cardComb);
        map.put(LANDING_PAGE, landingPage);
        map.put(CLICK_TIMESTAMP, System.currentTimeMillis() + "");
        map.put(LAUNCH_TYPE, MyApplication.getAppLaunchType() + "");
        map.put(SESSION_ID, MyApplication.getSessionId());
        onCommon(context, EVENT_ENTRY_LAUNCH, map);
    }

    /**
     * 统计页面曝光,统计首页、资源二级页
     *
     * @param context        上下文
     * @param cardComb       桌面卡片内容组合,1为xxx；2为xxx；3为xxx...只有入口是卡片的时候会有数值，否则为空
     * @param pageName       具体的页面（包名）如首页、视频二级页、音频二级页
     * @param parentPageName 上级界面
     */
    public static void statisticsPageExposure(Context context, String cardComb, String pageName, String parentPageName) {
        if (!ENABLE_ADVERT_STATISTICS) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        map.put(CARD_CONTENT_COMB, cardComb);
        map.put(PAGE_NAME, pageName);
        map.put(EXP_TIMESTAMP, System.currentTimeMillis() + "");
        map.put(PARENT_PAGE_NAME, parentPageName);
        map.put(SESSION_ID, MyApplication.getSessionId());
        onCommon(context, EVENT_PAGE_EXPOSURE, map);
    }

    /**
     * 统计广告请求
     * @param context
     * @param pageName
     * @param sdkSource
     */
    public static void statisticsAdRequestFlow(Context context, String pageName, String sdkSource, String mediaReqId) {
        Map<String, String> map = new HashMap<>();
        map.put(MEDIA_REQ_ID, mediaReqId);
        statisticsAdFlow(context, AD_ACTION_REQUEST, pageName, AD_RETURN_REQUEST, sdkSource, map);
    }

    /**
     * 统计广告获取到内容
     * @param context
     * @param isSuccess
     * @param pageName
     */
    public static void statisticsAdReturnFlow(Context context, boolean isSuccess, String pageName, String sdkSource,
                                              String mediaReqId, String adReqId) {
        String result = AD_RETURN_FAIL;
        if (isSuccess) {
            result = AD_RETURN_SUCCESS;
        }
        Map<String, String> map = new HashMap<>();
        map.put(MEDIA_REQ_ID, mediaReqId);
        map.put(AD_REQ_ID, adReqId);
        statisticsAdFlow(context, AD_ACTION_RETURN, pageName, result, sdkSource, map);
    }

    /**
     * 统计广告显示在资源位
     * @param context
     * @param pageName
     * @param sdkSource
     */
    public static void statisticsAdShowFlow(Context context, String pageName, String sdkSource, String mediaReqId, String adReqId, String traceId) {
        Map<String, String> map = new HashMap<>();
        map.put(MEDIA_REQ_ID, mediaReqId);
        map.put(AD_REQ_ID, adReqId);
        map.put(TRACE_ID, traceId);
        statisticsAdFlow(context, AD_ACTION_SHOW, pageName, AD_RETURN_SUCCESS, sdkSource, map);
    }

    /**
     * 统计进入广告展示位
     * @param context
     * @param pageName
     * @param sdkSource
     */
    public static void statisticsAdLoadFlow(Context context, String pageName, String sdkSource, String mediaReqId, String adReqId, String traceId) {
        Map<String, String> map = new HashMap<>();
        map.put(MEDIA_REQ_ID, mediaReqId);
        map.put(AD_REQ_ID, adReqId);
        map.put(TRACE_ID, traceId);
        statisticsAdFlow(context, AD_ACTION_LOAD, pageName, AD_RETURN_SUCCESS, sdkSource, map);
    }

    /**
     * 统计广告全链路
     *
     * @param context      上下文
     * @param actionType   行动类别
     * @param pageName     资源位所处页面
     * @param returnResult 成功、其他失败原因
     * @param sdkSource    广告来源，max、opuls
     * @param paramMap    参数map，由于方法参数超长，只能采用map传递参数
     */
    public static void statisticsAdFlow(Context context, String actionType, String pageName, String returnResult, String sdkSource,
                                        Map<String, String> paramMap) {
        if (!ENABLE_ADVERT_STATISTICS) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        String adSlotId = getAdSlot(pageName);
        String adRequestId = getAdRequestId(adSlotId);
        map.put(ACTION_TYPE, actionType);
        map.put(UNIQUE_AD_REQUEST_ID, adRequestId);
        map.put(AD_SLOT_ID, adSlotId);
        map.put(PAGE_NAME, pageName);
        map.put(START_TIMESTAMP, System.currentTimeMillis() + "");
        map.put(RETURN_RESULT, returnResult);
        map.put(SDK_SOURCE, sdkSource);
        map.put(UU_ID, UUID.randomUUID().toString());
        map.put(SESSION_ID, MyApplication.getSessionId());
        map.put(SDK_VERSION, sAdSdkVersion);
        map.putAll(paramMap);
        onCommon(context, EVENT_AD_FLOW, map);
    }

    /**
     * 统计广告全链路
     *
     * @param context     上下文
     * @param pageName    资源位所处页面
     * @param sdkSource   广告来源，max、opuls
     */
    public static void statisticsAdClick(Context context, String pageName, String sdkSource, String mediaReqId, String adReqId, String traceId) {
        if (!ENABLE_ADVERT_STATISTICS) {
            return;
        }
        Map<String, String> map = new HashMap<>();
        String adSlotId = getAdSlot(pageName);
        String adRequestId = getAdRequestId(adSlotId);
        map.put(UNIQUE_AD_REQUEST_ID, adRequestId);
        map.put(AD_SLOT_ID, adSlotId);
        map.put(PAGE_NAME, pageName);
        map.put(START_TIMESTAMP, System.currentTimeMillis() + "");
        map.put(SDK_SOURCE, sdkSource);
        map.put(UU_ID, UUID.randomUUID().toString());
        map.put(SESSION_ID, MyApplication.getSessionId());
        map.put(SDK_VERSION, sAdSdkVersion);
        map.put(MEDIA_REQ_ID, mediaReqId);
        map.put(AD_REQ_ID, adReqId);
        map.put(TRACE_ID, traceId);
        onCommon(context, EVENT_AD_CLICK, map);
    }

    public static void setAdSdkVersion(String version) {
        Log.d(TAG, "setAdSdkVersion " + version);
        sAdSdkVersion = version;
    }

    /**
     * 获取广告的id
     * <a href="https://odocs.myoas.com/sheets/NJkbEZd6rvc9VBqR/CGgeU?la">查看</a>
     * @param adSlotId 广告资源位置
     * @return 广告的请求id
     */
    public static String getAdRequestId(String adSlotId) {
        boolean isOppo = Utils.isOppoPhone();
        boolean isRealme = Utils.isRealmePhone();
        String requestId = "";
        switch (adSlotId) {
            case AD_SLOT_SPLASH:
                requestId = AdResourceConstants.REQUEST_ID_SPLASH_COLD;
                break;
            case AD_SLOT_SPLASH_HOT:
                requestId = AdResourceConstants.REQUEST_ID_SPLASH_HOT;
                break;
            case AD_SLOT_HOME:
                if (isOppo) {
                    requestId = AdResourceConstants.REQUEST_ID_OPPO_HOME;
                } else if (isRealme) {
                    requestId = AdResourceConstants.REQUEST_ID_REALME_HOME;
                }
                break;
            default: // 第二页
                if (isOppo) {
                    requestId = AdResourceConstants.REQUEST_ID_OPPO_SUB;
                } else if (isRealme) {
                    requestId = AdResourceConstants.REQUEST_ID_REALME_SUB;
                }
                break;
        }
        return requestId;
    }

    /**
     * 根据页面获取广告资源id
     * @param page
     * @return
     */
    public static String getAdSlot(String page) {
        String slot = page;
        switch (page) {
            case Constants.PAGE_SPLASH:
                slot = AD_SLOT_SPLASH;
                break;
            case Constants.PAGE_SPLASH_HOT:
                slot = AD_SLOT_SPLASH_HOT;
                break;
            case Constants.PAGE_MAIN:
                slot = AD_SLOT_HOME;
                break;
            default:
                slot = AD_SLOT_SECOND_PAGE + "_" + page;
                break;
        }
        return slot;
    }

    public static String encodePkg(String pkg) {
        String encryptPkg = sEncryptPkgMap.get(pkg);
        if (TextUtils.isEmpty(encryptPkg)) {
            encryptPkg = EncoderUtils.encryptPackage(pkg);
            sEncryptPkgMap.put(pkg, encryptPkg);
        }
        return encryptPkg;
    }
}
