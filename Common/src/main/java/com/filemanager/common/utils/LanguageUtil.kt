/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.utils
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/8/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import java.util.Locale

object LanguageUtil {

    private const val LANGUE_ZH = "zh"
    private const val COUNTRY_CN = "CN"
    private const val COUNTRY_HK = "HK"
    private const val COUNTRY_TW = "TW"
    private const val COUNTRY_MX = "MX"

    private const val LANGUAGE_ZH_HANS = "zh-Hans"//中文简体
    private const val LANGUAGE_ZH_HANT = "zh-Hant"//中文繁体
    private const val LANGUAGE_EN = "en"//英语
    private const val LANGUAGE_ES = "es"//西班牙语
    private const val LANGUAGE_JA = "ja"//日语
    /**
     * 简体中文
     */
    @JvmStatic
    fun isZHCN(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals(LANGUE_ZH, true)
        val isCN = locale.country.equals(COUNTRY_CN, true)
        return isZH && isCN
    }

    /**
     * 繁体中文，中国台湾地区使用
     */
    @JvmStatic
    fun isZHTW(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals(LANGUE_ZH, true)
        val isTW = locale.country.equals(COUNTRY_TW, true)
        return isZH && isTW
    }

    /**
     * 繁体中文，中国香港以及其它中文地区
     */
    @JvmStatic
    fun isZHHK(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals(LANGUE_ZH, true)
        val isHK = locale.country.equals(COUNTRY_HK, true)
        return isZH && isHK
    }
    @JvmStatic
    fun getLocalizedResources(context: Context, desiredLocale: Locale): Resources {
        var conf = context.resources.configuration
        conf = Configuration(conf)
        conf.setLocale(desiredLocale)
        val localizedContext = context.createConfigurationContext(conf)
        return localizedContext.resources
    }
    @JvmStatic
    fun checkAndReturnLocale(): Locale {
        val defaultLocale = Locale.getDefault()

        // 检查是否为中文
        when (defaultLocale.language) {
            LANGUE_ZH ->  return defaultLocale
            LANGUAGE_ZH_HANS, LANGUAGE_ZH_HANT ->  return defaultLocale
            LANGUAGE_EN ->  return defaultLocale
            LANGUAGE_JA ->  return defaultLocale
            LANGUAGE_ES -> {
                val country = defaultLocale.country
                return if (COUNTRY_MX == country) {
                    Locale(LANGUAGE_ES, COUNTRY_MX)
                } else {
                    Locale.ENGLISH
                }
            }
            else ->  return Locale.ENGLISH
        }
    }

    /**
     * 判断是否是中文，包含繁体，简体
     */
    @JvmStatic
    fun isChineseLanguage(): Boolean {
        val locale = Locale.getDefault()
        val isZH = locale.language.equals(LANGUE_ZH, true)
        val isTW = locale.country.equals(COUNTRY_TW, true)
        val isHK = locale.country.equals(COUNTRY_HK, true)
        return isZH || isTW || isHK
    }

    @JvmStatic
    fun getCurrentLocalLanguage(): String {
        return Locale.getDefault().language
    }

    @JvmStatic
    fun getCurrentLocalCountry(): String {
        return Locale.getDefault().country
    }
}