/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.module.imageloader.glide
 * * Version     : 1.0
 * * Date        : 2020/5/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.imageloader.glide;

import android.graphics.Bitmap;

import androidx.annotation.NonNull;

import com.bumptech.glide.Priority;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.data.DataFetcher;
import com.filemanager.common.utils.DecodeUtils;
import com.filemanager.common.utils.Log;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;

public class DrmCoverFetcher implements DataFetcher<InputStream> {
    private final static String TAG = "DrmCoverFetcher";
    private final DrmCover model;

    public DrmCoverFetcher(DrmCover model) {
        this.model = model;
    }

    public DrmCover getModel() {
        return model;
    }

    @Override
    public void loadData(@NonNull Priority priority, @NonNull DataCallback<? super InputStream> callback) {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try {
            Bitmap bitmap = DecodeUtils.decode(model.getPath(), null);
            if (bitmap != null) {
                bitmap.compress(Bitmap.CompressFormat.PNG, 0, bos);
                byte[] bitmapData = bos.toByteArray();
                callback.onDataReady(new ByteArrayInputStream(bitmapData));
                bitmap.recycle();
            } else {
                callback.onLoadFailed(new Exception("DrmCoverFetcher loadData DrmCover fail"));
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            try {
                bos.close();
            } catch (Exception e) {
                Log.e(TAG, e.getMessage());
            }
        }
    }

    @Override
    public void cleanup() {
    }

    @Override
    public void cancel() {
        // cannot cancel
    }

    @NonNull
    @Override
    public Class<InputStream> getDataClass() {
        return InputStream.class;
    }

    @NonNull
    @Override
    public DataSource getDataSource() {
        return DataSource.LOCAL;
    }
}
