/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/11/22, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection

data class DropTag(var categoryType: Int, var type: Type) {

    var filePath: String? = null
    var isMac: Boolean = false

    //拖拽时作为响应view是否可穿透
    var canPenetrate: Boolean = true
    enum class Type {
        ITEM_VIEW,
        ITEM_VIEW_NOTRESPONSE, // 被选中的文件夹
        ITEM_VIEW_PROHIBIT, // 统一禁止
        ITEM_VIEW_FOLDER, // 文件夹
        FRAGMENT_VIEW, // 普通fragment页面
        TOOLBAR_MENU,
        TOOLBAR_MENU_BACK,
        RECENT_TAB,
        MAIN_TAB,
        MAC_FRAGMENT_VIEW,
    }
}