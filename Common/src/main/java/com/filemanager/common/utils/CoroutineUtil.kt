/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : CoroutineUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/2/5
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2024/2/5       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object CoroutineUtil {

    @JvmStatic
    fun launchDelay(scope: CoroutineScope, delay: Long, block: CoroutineScope.() -> Unit) {
        scope.launch(Dispatchers.Default) {
            delay(delay)
            withContext(Dispatchers.Main) {
                block()
            }
        }
    }
}