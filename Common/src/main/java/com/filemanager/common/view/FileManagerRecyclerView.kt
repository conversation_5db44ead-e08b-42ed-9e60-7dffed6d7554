/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/3/26
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.GridLayoutAnimationController.AnimationParameters
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.COUIRecyclerView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.animation.SideNavigationWithGridLayoutAnimator
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.Log
import com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
import com.oplus.dropdrag.OnSlideSelectionStateListener
import kotlin.math.abs
import kotlin.math.ceil
import kotlin.math.max

class FileManagerRecyclerView : COUIRecyclerView, OnSlideSelectionStateListener {
    companion object {
        private const val DEFAULT_MIN_MOVE_DESTANCE = 10
        private const val TAG = "FileManagerRecyclerView"
    }

    private var mShowHideListener: RecyclerViewFastScroller.ShowHideListener? = null

    /**
     * If the list and the Fragment are using the sliding selection frame.
     * You need to set the [mIsSupportDragSlide] value to true used to avoid Bug 378272.
     * Default value: false
     */
    private var mIsSupportDragSlide = false
    private var startX: Int = 0
    private var startY: Int = 0
    private var mRowsCount = 0
    private var mInitActionMove = true
    /**
     * define whether can touch recyclerView and move
     */
    var mTouchable: Boolean = true

    /**
     * define variable for side navigation open or close anim
     */
    var isOpenOrCloseSideNavigation: Boolean = false
    var windowWidth: Int = 0
    var hasCheckAnim: Boolean = false

    constructor(context: Context, attrs: AttributeSet? = null, defStyle: Int? = androidx.recyclerview.R.attr.recyclerViewStyle) : super(context, attrs, defStyle
        ?: 0) {
        init(context, attrs, defStyle ?: androidx.recyclerview.R.attr.recyclerViewStyle)
    }

    constructor(context: Context, attrs: AttributeSet? = null) : super(context, attrs) {
        init(context, attrs, androidx.recyclerview.R.attr.recyclerViewStyle)
    }

    private val mRowIndexMap by lazy {
        mutableMapOf<Int, Int>()
    }

    /**
     * pause image load while list is fast scrolling, and resume it while list is idle
     */
    fun setLoadStateForScroll(fragment: Fragment) {
        addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                if (newState == RecyclerView.SCROLL_STATE_IDLE) {
                    FileImageLoader.sInstance.resumeRequests(fragment)
                } else {
                    FileImageLoader.sInstance.pauseRequests(fragment)
                }
            }
        })
    }

    /**
     * stop ColorRecyclerView over Scroll and restore default location
     * the superclass must be ColorRecyclerView
     */
    fun stopOverScroll() {
        super.overScrollBy(0, 0, 0, 0, 0,
            0, 0, 0, false)
    }

    override fun stopScroll() {
        stopOverScroll()
        super.stopScroll()
    }

    fun setMIsSupportDragSlide(supportDragSlide: Boolean) {
        mIsSupportDragSlide = supportDragSlide
    }

    /**
     * Calculated [mRowsCount] and line number of each item [mRowIndexMap]
     */
    private fun initRowsCount(layoutManager: GridLayoutManager, counts: Int) {
        mRowIndexMap.clear()
        var countWithSpanSize = 0

        for (i in 0 until counts) {
            val span = layoutManager.spanSizeLookup.getSpanSize(i)
            if (span > 1) {
                val currentLineItemNum = countWithSpanSize % layoutManager.spanCount

                if ((currentLineItemNum + span) > layoutManager.spanCount) {
                    //Need to create a new line
                    countWithSpanSize += (layoutManager.spanCount - currentLineItemNum + span)
                } else {
                    countWithSpanSize += span
                }
            } else {
                countWithSpanSize += layoutManager.spanSizeLookup.getSpanSize(i)
            }
            mRowsCount = ceil(countWithSpanSize / layoutManager.spanCount.toFloat()).toInt()
            mRowIndexMap[i] = mRowsCount - 1
        }
    }

    override fun attachLayoutAnimationParameters(child: View?, params: ViewGroup.LayoutParams, indexs: Int, counts: Int) {
        val layoutManager = layoutManager
        val animationParams = params.layoutAnimationParameters ?: AnimationParameters()

        if ((adapter != null) && (layoutManager is GridLayoutManager) && (animationParams is AnimationParameters)) {
            params.layoutAnimationParameters = animationParams.apply {
                count = counts
                index = indexs

                // Calculated only at the beginning
                if (indexs == 0) {
                    initRowsCount(layoutManager, counts)
                }

                // Calculate the number of columns and rows in the grid
                rowsCount = mRowsCount
                columnsCount = layoutManager.spanCount

                // Calculate the column/row position in the grid
                column = index % columnsCount
                row = mRowIndexMap[indexs] ?: 0
            }
        } else {
            super.attachLayoutAnimationParameters(child, params, indexs, counts)
        }
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        when (ev.action) {
            MotionEvent.ACTION_DOWN -> {
                startX = ev.x.toInt()
                startY = ev.y.toInt()
                mInitActionMove = true
                parent.requestDisallowInterceptTouchEvent(true)
            }
            MotionEvent.ACTION_MOVE -> {
                val endX = ev.x.toInt()
                val endY = ev.y.toInt()
                val disX = abs(endX - startX)
                val disY = abs(endY - startY)
                if (mInitActionMove && (max(disX, disY) > DEFAULT_MIN_MOVE_DESTANCE)) {
                    mInitActionMove = if (disX > disY) {
                        parent.requestDisallowInterceptTouchEvent(false)
                        false
                    } else {
                        parent.requestDisallowInterceptTouchEvent(true)
                        false
                    }
                }
            }
            MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                if (mIsSupportDragSlide) {
                    dispatchSetPressed(false)
                }
                parent.requestDisallowInterceptTouchEvent(false)
            }
        }
        return try {
            super.dispatchTouchEvent(ev)
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "IllegalArgumentException: ${e.message}", e)
            false
        } catch (e: IllegalStateException) {
            Log.e(TAG, "IllegalStateException: ${e.message}", e)
            false
        }
    }

    private fun init(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int) {
        descendantFocusability = FOCUS_BLOCK_DESCENDANTS
    }

    override fun isPaddingOffsetRequired(): Boolean {
        return true
    }

    override fun getTopFadingEdgeStrength(): Float {
        return 0f
    }

    override fun getBottomPaddingOffset(): Int {
        return paddingBottom
    }

    override fun onSlideSelectionStart() {
        mShowHideListener?.setFastScrollerShowEnable(false)

    }

    override fun onSlideSelectionEnd() {
        mShowHideListener?.setFastScrollerShowEnable(true)
    }

    fun addOnShowHideListener(showHideListener: RecyclerViewFastScroller.ShowHideListener) {
        this.mShowHideListener = showHideListener
    }

    /**
     * Current only support LinearLayoutManager or its child class
     */
    fun fastSmoothScrollToTop() {
        if (layoutManager is LinearLayoutManager) {
            stopScroll()
            val firstVisiblePosition = (layoutManager as LinearLayoutManager).findFirstVisibleItemPosition()
            scrollToPosition(firstVisiblePosition.coerceAtMost(childCount))
            post {
                smoothScrollToPosition(0)
            }
        }
    }

    override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
        try {
            super.onLayout(changed, l, t, r, b)
            if (isOpenOrCloseSideNavigation && !hasCheckAnim) {
                (itemAnimator as? SideNavigationWithGridLayoutAnimator)?.checkAnim()
                hasCheckAnim = true
            }
        } catch (e: Exception) {
            Log.e(TAG, "onLayout error: $e", e)
        }
    }

    override fun onMeasure(widthSpec: Int, heightSpec: Int) {
        super.onMeasure(widthSpec, heightSpec)
        if (isOpenOrCloseSideNavigation && measuredWidth != windowWidth) {
            Log.d(TAG, "onMeasure width: $measuredWidth windowWidth: $windowWidth isOpenOrClose: $isOpenOrCloseSideNavigation")
            setMeasuredDimension(windowWidth, measuredHeight)
        }
    }
}