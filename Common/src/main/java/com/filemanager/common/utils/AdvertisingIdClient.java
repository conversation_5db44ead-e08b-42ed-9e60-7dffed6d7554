/***********************************************************
 * * Copyright (C), 2008-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File:com.coloros.filemanager.utils/AdvertisingIdClient.java
 * * Description:
 * * Version:
 * * Date :2019/5/23
 * * Author:W9000846
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.common.utils;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.ServiceConnection;
import android.content.pm.PackageManager;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Looper;
import android.os.Parcel;
import android.os.RemoteException;

import com.filemanager.common.MyApplication;
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils;

import java.util.concurrent.LinkedBlockingQueue;

public class AdvertisingIdClient {
    private static final String PKG_ANDROID_VEND = "com.android.vending";
    private static final String PKG_ANDROID_GMS = "com.google.android.gms";
    private static final String INTENT_GMS_ADS_IDENTIFIER = "com.google.android.gms.ads.identifier.service.START";
    private static final String FACE_TOKEN = "com.google.android.gms.ads.identifier.internal.IAdvertisingIdService";
    /**
     * Cannot call in the main thread, You must call in the other thread
     */
    public static String getGoogleAdId() throws Exception {
        if (Looper.getMainLooper() == Looper.myLooper()) {
            Log.w("Cannot call in the main thread, You must call in the other thread");
            return "";
        }
        PackageManager pm = MyApplication.getSAppContext().getPackageManager();
        pm.getPackageInfo(PKG_ANDROID_VEND, 0);
        AdvertisingConnection connection = new AdvertisingConnection();
        Intent intent = new Intent(INTENT_GMS_ADS_IDENTIFIER);
        intent.setPackage(PKG_ANDROID_GMS);
        CollectPrivacyUtils.collectInstalledAppList(PKG_ANDROID_GMS);
        if (MyApplication.getSAppContext().bindService(intent, connection, Context.BIND_AUTO_CREATE)) {
            try {
                AdvertisingInterface adInterface = new AdvertisingInterface(
                        connection.getBinder());
                return adInterface.getId();
            } finally {
                MyApplication.getSAppContext().unbindService(connection);
            }
        }
        return "";
    }

    private static final class AdvertisingConnection implements ServiceConnection {
        boolean mRetrieved = false;
        private final LinkedBlockingQueue<IBinder> mQueue = new LinkedBlockingQueue<>(1);

        public void onServiceConnected(ComponentName name, IBinder service) {
            try {
                this.mQueue.put(service);
            } catch (InterruptedException localInterruptedException) {
            }
        }

        public void onServiceDisconnected(ComponentName name) {
        }

        public IBinder getBinder() throws InterruptedException {
            if (this.mRetrieved) {
                throw new IllegalStateException();
            }
            this.mRetrieved = true;
            return this.mQueue.take();
        }
    }

    private static final class AdvertisingInterface implements IInterface {
        private IBinder mBinder;

        public AdvertisingInterface(IBinder pBinder) {
            mBinder = pBinder;
        }

        public IBinder asBinder() {
            return mBinder;
        }

        public String getId() throws RemoteException {
            Parcel data = Parcel.obtain();
            Parcel reply = Parcel.obtain();
            String id = null;
            try {
                data.writeInterfaceToken(FACE_TOKEN);
                mBinder.transact(1, data, reply, 0);
                reply.readException();
                id = reply.readString();
            } finally {
                reply.recycle();
                data.recycle();
            }
            return id;
        }

        public boolean isLimitAdTrackingEnabled(boolean paramBoolean)
                throws RemoteException {
            Parcel data = Parcel.obtain();
            Parcel reply = Parcel.obtain();
            boolean limitAdTracking = true;
            try {
                data.writeInterfaceToken(FACE_TOKEN);
                data.writeInt(paramBoolean ? 1 : 0);
                mBinder.transact(2, data, reply, 0);
                reply.readException();
                limitAdTracking = 0 != reply.readInt();
            } finally {
                reply.recycle();
                data.recycle();
            }
            return limitAdTracking;
        }
    }
}