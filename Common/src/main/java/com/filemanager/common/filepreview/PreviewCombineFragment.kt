/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: PreviewCombineFragment
 ** Description: Preview Combine Fragment
 ** Version: 1.0
 ** Date : 2024/10/09
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview

import android.content.Context
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.view.DragEvent
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import com.coui.appcompat.panel.COUIPanelMultiWindowUtils
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMFragment
import com.filemanager.common.base.RemoteFileBean
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.uiconfig.UIConfigMonitor
import com.filemanager.common.helper.uiconfig.type.IUIConfig
import com.filemanager.common.helper.uiconfig.type.ScreenSizeConfig
import com.filemanager.common.interfaces.IRefreshFragmentDataForDir
import com.filemanager.common.interfaces.OnGetRemoteDeviceInfoListener
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtAnimationUtil
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatisticsUtils
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewApi
import com.oplus.filemanager.interfaze.filepreview.IFilePreviewFragment
import com.oplus.filemanager.interfaze.filepreview.IFilesHeapUpFragment

class PreviewCombineFragment : BaseVMFragment<PreviewCombineViewModel>(), IPreviewOperate, IRefreshFragmentDataForDir {

    companion object {
        const val TAG = "PreviewCombineFragment"
        const val PREVIEW_LIST_FRAGMENT_TAG = "PREVIEW_LIST_FRAGMENT"
        private const val WHAT_UPLOAD_STATISTICS = 1
        private const val UPLOAD_STATISTICS_DELAY_TIME = 500L
    }

    private val handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                WHAT_UPLOAD_STATISTICS -> {
                    val from =
                        "${
                            (previewListFragment?.getFragmentInstance() as? BaseVMFragment<*>)?.getFragmentCategoryType()
                                ?: previewListFragmentClassName
                        }"
                    StatisticsUtils.onCommon(
                        baseVMActivity, StatisticsUtils.SHOW_PREVIEW_MODEL_EVENT, mapOf(
                            StatisticsUtils.FROM to from
                        )
                    )
                }
            }
        }
    }

    private var rootLayout: ViewGroup? = null
    private var dividerLine: View? = null
    private var parentChildLayout: PreviewParentChildLayout? = null
    private var previewListFragmentCreator: IPreviewListFragmentCreator? = null
    private var previewListFragment: IPreviewListFragment? = null
    private var previewEmptyFragment: PreviewEmptyFragment? = null
    private var viewModel: PreviewCombineViewModel? = null
    private var previewListFragmentClassName: String = ""
    private var isFirstSideNavigationStatus = true
    private var initObservePreviewStateCompleted = false
    private var lastSelectionState = false
    private var isSideNavigationOnClicked = false
    private var isNeedPreviewAnim = false
    private var sideCategoryType = -1

    override fun getLayoutResId(): Int {
        return R.layout.preview_combine_fragment
    }

    fun setPreviewListFragmentCreator(creator: IPreviewListFragmentCreator) {
        this.previewListFragmentCreator = creator
    }

    override fun initView(view: View) {
        rootLayout = view.findViewById(R.id.coordinator_layout)
        dividerLine = view.findViewById(R.id.divider_line)
        toolbar = view.findViewById(R.id.toolbar)
        parentChildLayout = view.findViewById(R.id.parentChildLayout)
        parentChildLayout?.bindTopAppBar(view.findViewById(R.id.appbar_layout))
    }

    override fun initData(savedInstanceState: Bundle?) {
        initToolbar()
        initParentChildLayout()
        initViewModel()
        rootLayout?.post {
            //动态拔出otg或dfm断连会直接切换到最近页，此时如果otg是快览模式，但最近页是宫格模式，需要关闭快览模式
            if (!isListMode() && viewModel?.previewState?.value == KtConstants.PREVIEW_OPEN) {
                viewModel?.closePreview()
            }
        }
        FileImageVHUtils.setShowPreview(viewModel?.isPreviewOpen() == true)
    }

    private fun initParentChildLayout() {
        parentChildLayout?.apply {
            previewListFragment = getCurrentPreviewListFragment()
            if (previewListFragment == null) {
                previewListFragment = previewListFragmentCreator?.create()
            }
            previewListFragment?.let {
                val bundle = arguments ?: return
                it.setFragmentArguments(bundle)
                it.setPreviewToolbar(toolbar)
                it.setPreviewOperate(this@PreviewCombineFragment)
                setPreviewList(childFragmentManager, it.getFragmentInstance(), PREVIEW_LIST_FRAGMENT_TAG)
                previewListFragmentClassName = it::class.simpleName ?: ""
            }
        }
        previewEmptyFragment = PreviewEmptyFragment()
    }

    private fun getCurrentPreviewListFragment(): IPreviewListFragment? {
        return childFragmentManager.findFragmentByTag(PREVIEW_LIST_FRAGMENT_TAG) as? IPreviewListFragment
    }

    private fun initViewModel() {
        if (viewModel != null) {
            return
        }
        viewModel = ViewModelProvider(this)[previewListFragmentClassName, PreviewCombineViewModel::class.java]
        if (previewListFragmentCreator == null) {
            previewListFragmentCreator = viewModel?.previewListFragmentCreator
        } else {
            viewModel?.previewListFragmentCreator = previewListFragmentCreator
        }
    }

    private fun initToolbar() {
        baseVMActivity?.apply {
            setSupportActionBar(toolbar)
        }
        rootLayout?.apply {
            setPadding(paddingLeft,
                COUIPanelMultiWindowUtils.getStatusBarHeight(baseVMActivity), paddingRight, paddingBottom)
        }
    }

    override fun startObserve() {
        Log.d(TAG, "startObserve")
        viewModel?.previewState?.observe(this) {
            delay { refreshPreviewItemIcon(false) }
            refreshPreviewContent()
            initObservePreviewStateCompleted = true
            updateDividerLineVisible()
        }
        baseVMActivity?.sideNavigationStatus?.observe(this) {
            Log.d(TAG, "observe sideNavigationStatus, value = $it, isFirst = $isFirstSideNavigationStatus")
            updateDividerLineVisible()
            if (isFirstSideNavigationStatus) {
                isFirstSideNavigationStatus = false
                return@observe
            }
            if (it == KtConstants.SIDE_NAVIGATION_OPEN && viewModel?.isPreviewOpen() == true) {
                viewModel?.closePreview()
            }
            updatePreviewMenuIconVisible()
            if (isSideNavigationOnClicked) {
                if (it == KtConstants.SIDE_NAVIGATION_CLOSE && canOpenPreviewWhenSideNavigationClosed()) {
                    viewModel?.openPreview()
                }
                isSideNavigationOnClicked = false
            }
        }
    }

    private fun canOpenPreviewWhenSideNavigationClosed(): Boolean {
        //判断当用户收起侧导时，是否开启文件预览。如果当前是中大屏的列表展示，且不是选择编辑模式，且不是远程设备未连接，就开启文件预览
        return isLargeScreen() && isListMode() && !isSelectionMode() && isRemoteDeviceNotConnected().not()
    }

    fun isRemoteDeviceNotConnected(): Boolean {
        //判断是否是远程设备未连接状态（包含可连接和已离线）
        (previewListFragment as? OnGetRemoteDeviceInfoListener)?.let {
            return it.getRemoteDeviceStatus() != RemoteDeviceConstants.CONNECTED
        }
        return false
    }

    private fun updateDividerLineVisible() {
        dividerLine?.visibility = if (isSideNavigationClose() && viewModel?.isPreviewOpen() == true) View.VISIBLE else View.GONE
    }

    override fun onResumeLoadData() {
        Log.d(TAG, "onResumeLoadData")
        previewListFragment?.let {
            arguments?.let { bundle ->
                if ((it as? Fragment)?.isStateSaved != true) {
                    it.setFragmentArguments(bundle)
                }
            }
            it.onResumeLoadData()
        }
        arguments?.apply {
            val bundleCategoryType = getInt(Constants.SIDE_CATEGORY_TYPE, -1)
            if (sideCategoryType != -1 && bundleCategoryType != sideCategoryType) {
                sideCategoryType = bundleCategoryType
                rootLayout?.apply {
                    super.setFragmentViewDragTag(this)
                }
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu, menuInflater: MenuInflater) {
        previewListFragment?.onCreateOptionsMenu(menu, menuInflater)
        refreshPreviewItemIcon(false)
    }

    private fun updatePreviewMenuIconVisible(isDelay: Boolean = false) {
        val isVisible = isLargeScreen() && isSideNavigationClose() && isListMode() && isRemoteDeviceNotConnected().not()
        if (isDelay) {
            delay { toolbar?.menu?.findItem(R.id.actionbar_preview)?.isVisible = isVisible }
        } else {
            toolbar?.menu?.findItem(R.id.actionbar_preview)?.isVisible = isVisible
        }
    }

    fun isLargeScreen(): Boolean {
        val screenState = UIConfigMonitor.getCurrentScreenState()
        return screenState == UIConfigMonitor.SCREEN_LAUNCH_FROM_LARGE || screenState == UIConfigMonitor.SCREEN_SMALL_TO_LARGE
    }

    private fun isSideNavigationClose(): Boolean {
        return baseVMActivity?.sideNavigationStatus?.value == KtConstants.SIDE_NAVIGATION_CLOSE
    }

    fun isListMode(): Boolean {
        return previewListFragment?.getScanMode() == KtConstants.SCAN_MODE_LIST
    }

    fun onMenuItemSelected(item: MenuItem): Boolean {
        if (item.itemId == R.id.actionbar_preview) {
            if (parentChildLayout?.isDoingAnim() == true) {
                Log.d(TAG, "preview menu, doing anim")
                return true
            }
            isNeedPreviewAnim = true
            viewModel?.clickPreviewItem()
            previewListFragment?.updateLeftRightMargin()
            return true
        }
        if (item.itemId == R.id.actionbar_scan_mode) {
            if (parentChildLayout?.isDoingAnim() == true) {
                Log.d(TAG, "scan mode menu, doing anim")
                return true
            }
            if (isListMode() && viewModel?.isPreviewOpen() == true) {
                isNeedPreviewAnim = true
                viewModel?.closePreview()
                previewListFragment?.updateLeftRightMargin()
            }
        } else if (item.itemId == R.id.action_select_cancel || item.itemId == android.R.id.home) {
            unselectedPreview()
        }
        val result = previewListFragment?.onMenuItemSelected(item) ?: false
        if (item.itemId == R.id.actionbar_scan_mode) {
            updatePreviewMenuIconVisible(true)
        }
        return result
    }

    fun pressBack(): Boolean {
        val inPreview = (getPreviewContentFragment() as? IFilePreviewFragment)?.pressBack() ?: false
        if (inPreview) {
            return true
        }
        unselectedPreview()
        return previewListFragment?.pressBack() == true
    }

    fun onNavigationItemSelected(item: MenuItem): Boolean {
        return previewListFragment?.onNavigationItemSelected(item) ?: false
    }

    fun fromSelectPathResult(code: Int, path: List<String>?) {
        (getPreviewContentFragment() as? IFilePreviewFragment)?.apply {
            Log.d(TAG, "fromSelectPathResult: handle by preview fragment, code=$code")
            fromSelectPathResult(code, path)
            if (code == MessageConstant.MSG_EDITOR_CUT) {
                unselectedPreview()
            }
        } ?: run {
            Log.d(TAG, "fromSelectPathResult: handle by list fragment, code=$code")
            previewListFragment?.fromSelectPathResult(code, path)
        }
        refreshPreviewContent()
    }

    fun setIsHalfScreen(isHalfScreen: Boolean) {
        previewListFragment?.setIsHalfScreen(isHalfScreen)
    }

    fun updatedLabel() {
        previewListFragment?.updatedLabel()
        (getPreviewContentFragment() as? IFilePreviewFragment)?.updatedLabel()
    }

    fun backToTop() {
        previewListFragment?.backToTop()
    }

    fun permissionSuccess() {
        previewListFragment?.permissionSuccess()
    }

    fun setCurrentFromOtherSide(currentPath: String) {
        previewListFragment?.setCurrentFromOtherSide(currentPath)
    }

    fun getCurrentPath(): String {
        return previewListFragment?.getCurrentPath() ?: ""
    }

    private fun refreshPreviewItemIcon(withAnimation: Boolean) {
        toolbar?.menu?.findItem(R.id.actionbar_preview)?.let {
            updatePreviewMenuIconVisible()
            val resId: Int = if (viewModel?.previewState?.value == KtConstants.PREVIEW_CLOSE) {
                com.filemanager.common.R.drawable.color_tool_menu_ic_preview_close
            } else {
                com.filemanager.common.R.drawable.color_tool_menu_ic_preview_open
            }
            if (withAnimation) {
                KtAnimationUtil.updateMenuItemWithFadeAnimate(it, resId, baseVMActivity)
            } else {
                it.setIcon(resId)
            }
        }
    }

    private fun refreshPreviewContent() {
        Log.d(TAG, "refreshPreviewContent previewState=${viewModel?.previewState?.value}")
        if (viewModel?.previewState?.value == KtConstants.PREVIEW_CLOSE) {
            if (isNeedPreviewAnim) {
                val recyclerView = (previewListFragment as? OnGetUIInfoListener)?.getRecyclerView()
                val isEmptyList = previewListFragment?.isEmptyList() == true
                parentChildLayout?.hidePreviewContent(true, recyclerView, isEmptyList)
                isNeedPreviewAnim = false
            } else {
                parentChildLayout?.hidePreviewContent(false)
                detachPreview(false)
            }
            viewModel?.clickFileLiveData?.value = null
            //退出快览模式停止计时
            handler.removeMessages(WHAT_UPLOAD_STATISTICS)
        } else {
            if (viewModel?.clickFileLiveData?.value != null) {
                viewModel?.clickFileLiveData?.value?.let {
                    previewClickedFile(it, viewModel?.clickFileLiveData)
                    viewModel?.clickFileLiveData?.value = it
                }
            } else if (previewListFragment?.isEmptyList() == true) {
                listEmptyFile()
            } else {
                unselectedPreview()
            }
            if (isNeedPreviewAnim) {
                val recyclerView = (previewListFragment as? OnGetUIInfoListener)?.getRecyclerView()
                val isEmptyList = previewListFragment?.isEmptyList() == true
                parentChildLayout?.showPreviewContent(true, recyclerView, isEmptyList)
                isNeedPreviewAnim = false
            } else {
                parentChildLayout?.showPreviewContent(false)
            }
            //进入快览模式开始计时,停留5S时上报
            handler.sendEmptyMessageDelayed(WHAT_UPLOAD_STATISTICS, UPLOAD_STATISTICS_DELAY_TIME)
        }
    }

    override fun isSupportPreview(): Boolean {
        return true
    }

    override fun onToolbarMenuUpdated(menu: Menu) {
        updatePreviewMenuIconVisible()
        refreshPreviewItemIcon(false)
    }

    override fun previewClickedFile(
        file: BaseFileBean?,
        clickFileLiveData: MutableLiveData<BaseFileBean?>?
    ): Boolean {
        if (this.viewModel?.isPreviewOpen() != true) {
            return false
        }
        if (file == null) {
            unselectedPreview()
            return true
        }
        //这里判断在非远程Bean时，才做文件存在的校验，远程的Bean时不做文件存在的校验
        if (file !is RemoteFileBean && !JavaFileHelper.exists(file)) {
            Log.d(TAG, "previewEditedFiles file not exist")
            clickFileLiveData?.value = null
            unselectedPreview()
            return true
        }
        if (viewModel?.clickFileLiveData?.value?.mData != file.mData || viewModel?.clickFileLiveData?.value == null) {
            if (file != null) {
                singleFilePreview(file)
            }
            viewModel?.clickFileLiveData = clickFileLiveData
            viewModel?.clickFileLiveData?.value = file
        }
        return true
    }

    override fun previewEditedFiles(files: List<BaseFileBean>?): Boolean {
        Log.d(TAG, "previewEditedFiles: ${files?.size}")
        if (this.viewModel?.isPreviewOpen() != true) {
            return false
        }
        viewModel?.clickFileLiveData?.value = null
        if (files.isNullOrEmpty()) {
            unselectedWhenHeapUpPreview(files != null)
            return true
        }
        multiFilesHeapUp(files)
        return true
    }

    private fun singleFilePreview(file: BaseFileBean) {
        lastSelectionState = true
        val contentsLayout = parentChildLayout ?: return
        val filePreviewApi = Injector.injectFactory<IFilePreviewApi>() ?: return
        runCatching {
            val filePreviewContent =
                filePreviewApi.obtainFilePreviewFragment(contentsLayout.context, file)
            previewListFragment?.getOperatorResultListener(filePreviewContent)
                ?.let(filePreviewContent::setOperatorResultListener)
            previewListFragment?.getOperateInterceptor()
                ?.let(filePreviewContent::setOperateInterceptor)
            contentsLayout.updatePreviewContent(
                childFragmentManager,
                filePreviewContent.fragmentInstance
            )
        }.onFailure {
            Log.e(TAG, "singleFilePreview onFailure: ${it.message}")
        }
    }

    /**
     * 多选堆叠取消选中时有动画效果，需要等动画结束再切换至未选中界面
     */
    private fun unselectedWhenHeapUpPreview(isSelectionMode: Boolean) {
        if (!lastSelectionState) {
            return
        }
        val fragment = getPreviewContentFragment()
        if (fragment is IFilesHeapUpFragment && isSelectionMode) {
            Log.d(TAG, "unselectedWhenHeapUpPreview")
            fragment.onDeselectionAnimationEnd {
                unselectedPreview(true)
            }
            fragment.updateSelectionFiles(emptyList())
            return
        }
        if (!isSelectionMode) {
            previewListFragment?.onResumeLoadData()
        }
        unselectedPreview(isSelectionMode)
    }

    private fun multiFilesHeapUp(files: List<BaseFileBean>) {
        val previewLayout = parentChildLayout ?: return
        lastSelectionState = true
        val fragment = getPreviewContentFragment()
        if (fragment is IFilesHeapUpFragment) {
            Log.d(TAG, "multiFilesHeapUp: update, size=${files.size}")
            fragment.updateSelectionFiles(files)
            return
        }
        Log.d(TAG, "multiFilesHeapUp: obtain, size=${files.size}")
        val filePreviewApi = Injector.injectFactory<IFilePreviewApi>() ?: return
        kotlin.runCatching {
            val heapUpContent = filePreviewApi.obtainFileHeapUpFragment(files)
            previewLayout.updatePreviewContent(childFragmentManager, heapUpContent.fragmentInstance)
        }.onFailure {
            Log.e(TAG, "multiFilesHeapUp: onFailure: ${it.message}")
        }
    }

    private fun getPreviewContentFragment(): Fragment? =
        if (this.isAdded) {
            parentChildLayout?.getPreviewContentFragment(childFragmentManager)
        } else null

    private fun unselectedPreview(isSelectionMode: Boolean = false) {
        if (viewModel?.isPreviewOpen() != true) {
            Log.d(TAG, "unselectedPreview preview has closed")
            return
        }
        detachPreview(isSelectionMode)
    }

    override fun listEmptyFile() {
        if (viewModel?.isPreviewOpen() != true) {
            Log.d(TAG, "listEmptyFile preview has closed")
            return
        }
        viewModel?.clickFileLiveData?.value = null
        previewEmptyFragment?.let {
            it.setHideContent(true)
            parentChildLayout?.updatePreviewContent(childFragmentManager, it)
        }
    }

    override fun isPreviewOpen(): Boolean {
        return viewModel?.isPreviewOpen() ?: false
    }

    override fun closePreview() {
        parentChildLayout?.hidePreviewContent(false)
        viewModel?.closePreview()
    }

    override fun handleDragEvent(event: DragEvent): Boolean? {
        return previewListFragment?.handleDragScroll(event)
    }

    override fun resetDragStatus() {
        previewListFragment?.resetScrollStatus()
    }

    private fun detachPreview(isSelectionMode: Boolean) {
        viewModel?.clickFileLiveData?.value = null
        previewEmptyFragment?.let {
            if (previewListFragment?.isEmptyList() == true) {
                it.setHideContent(true)
            } else {
                it.setShowEmptyDes(!isSelectionMode)
            }
            parentChildLayout?.updatePreviewContent(childFragmentManager, it)
        }
        lastSelectionState = false
    }

    override fun onUIConfigChanged(configList: MutableCollection<IUIConfig>) {
        if (!isAdded) {
            Log.d(TAG, "onUIConfigChanged, fragment has not been added")
            return
        }
        configList.forEach {
            if (it is ScreenSizeConfig) {
                screenSizeSwitch()
            }
        }
        childFragmentManager.fragments.forEach {
            if (it is UIConfigMonitor.OnUIConfigChangeListener) {
                it.onUIConfigChanged(configList)
            }
        }
    }

    private fun getCurrentRefreshDataForCreateDir(): IRefreshFragmentDataForDir? {
        return childFragmentManager.findFragmentByTag(PREVIEW_LIST_FRAGMENT_TAG) as? IRefreshFragmentDataForDir
    }

    private fun screenSizeSwitch() {
        val screenState = UIConfigMonitor.getCurrentScreenState()
        // 从大屏切换到小屏，只显示文件列表页
        if (screenState == UIConfigMonitor.SCREEN_LARGE_TO_SMALL) {
            Log.d(TAG, "switch -> large screen to small screen")
            if (viewModel?.isPreviewOpen() == true) {
                Log.d(TAG, "switch -> closePreview")
                parentChildLayout?.hidePreviewContent(false)
                viewModel?.closePreview()
                updateTabLayout()
            } else {
                refreshPreviewItemIcon(false)
            }
        }
        // 从小屏切换到大屏
        if (screenState == UIConfigMonitor.SCREEN_SMALL_TO_LARGE) {
            Log.d(TAG, "switch -> small screen to large screen")
            if (isSideNavigationClose() && isListMode() && isSelectionMode()) {
                //无侧导的列表展示，且处于选择编辑模式下，开启预览视图
                Log.d(TAG, "switch -> openPreview")
                if (viewModel?.isPreviewOpen() == false) {
                    viewModel?.openPreview()
                }
            } else {
                refreshPreviewItemIcon(false)
            }
        }
    }

    private fun updateTabLayout() {
        val tabLayout = previewListFragment?.getFragmentInstance()?.view?.findViewById<View>(R.id.tab_layout)
        tabLayout?.post {
            tabLayout.requestLayout()
        }
    }

    fun isSelectionMode(): Boolean {
        return previewListFragment?.isSelectionMode() ?: false
    }

    fun getPreviewFragment(): IPreviewListFragment? {
        return previewListFragment
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        val bundle = arguments ?: return
        sideCategoryType = bundle.getInt(Constants.SIDE_CATEGORY_TYPE, -1)
    }

    override fun getFragmentCategoryType(): Int {
        if (sideCategoryType != -1) {
            return sideCategoryType
        } else {
            return (previewListFragment as? BaseVMFragment<*>)?.getFragmentCategoryType() ?: -1
        }
    }

    fun onSideNavigationClicked(isOpen: Boolean): Boolean {
        isSideNavigationOnClicked = true
        baseVMActivity?.let {
            FileImageVHUtils.changedListMargin(
                it,
                it.sideNavigationContainer?.drawerViewWidth ?: 0,
                if (isOpen) KtConstants.SIDE_NAVIGATION_OPEN else KtConstants.SIDE_NAVIGATION_CLOSE
            )
        }
        if (isListMode()) {
            FileImageVHUtils.setShowPreview(!isOpen)
            previewListFragment?.updateLeftRightMargin()
            return false
        } else {
            FileImageVHUtils.setShowPreview(false)
            previewListFragment?.updateLeftRightMargin()
        }
        return previewListFragment?.onSideNavigationClicked(isOpen) ?: false
    }

    fun exitSelectionMode() {
        previewListFragment?.exitSelectionMode()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        handler.removeMessages(WHAT_UPLOAD_STATISTICS)
    }

    override fun refreshDataForDir(path: String, category: Int) {
        if (checkIfNeedRefreshForDir(category)) {
            val currentFragment = getCurrentRefreshDataForCreateDir()
            currentFragment?.refreshDataForDir(path, category)
        }
    }

    override fun renameToShortCutFolder(newName: String, file: BaseFileBean): String {
        val currentFragment = getCurrentRefreshDataForCreateDir()
        return currentFragment?.renameToShortCutFolder(newName, file).toString()
    }

    override fun renameToLabel(newName: String, labelId: Long) {
        val currentFragment = getCurrentRefreshDataForCreateDir()
        currentFragment?.renameToLabel(newName, labelId)
    }

    private fun checkIfNeedRefreshForDir(category: Int) =
        category == CategoryHelper.CATEGORY_FILE_BROWSER
                || category == CategoryHelper.CATEGORY_OTG_BROWSER
                || category == CategoryHelper.CATEGORY_SDCARD_BROWSER
                || CategoryHelper.isShortcutFolderType(category)
                || CategoryHelper.isLabelType(category)

    override fun onClickDir(path: String) {
        val currentFragment = getCurrentRefreshDataForCreateDir()
        currentFragment?.onClickDir(path)
    }

    fun getSelectedItemView(): ArrayList<View>? {
        val currentFragment = getCurrentPreviewListFragment()
        return currentFragment?.getSelectedItemView()
    }

    fun setNavigateItemAble() {
        val currentFragment = getCurrentPreviewListFragment()
        currentFragment?.setNavigateItemAble()
    }
}