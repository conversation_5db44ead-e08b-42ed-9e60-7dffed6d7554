/*********************************************************************
 ** Copyright (C), 2010-2023 Oplus. All rights reserved..
 ** File        : UserManagerUtils.kt
 ** Description : UserManagerUtils
 ** Version     : 1.0
 ** Date        : 2023/5/23 12:07
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  W9030342       2023/5/23       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.os.UserManager
import android.util.Log

object UserManagerUtils {

    const val TAG = "UserManagerUtils"

    @JvmStatic
    fun checkIsSystemUser(context: Context?): Boolean {
        val userManager = context?.getSystemService(Context.USER_SERVICE) as UserManager
        val isSysteUser = userManager.isSystemUser
        Log.i(TAG, "checkIsSystemUser result $isSysteUser")
        return isSysteUser
    }
}