/***********************************************************
 * * Copyright (C), 2008-2019 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:FolderNote.java
 * * Description:
 * * Version:1.0
 * * Date :2019/10/12
 * * Author:W9000843
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * *
 ****************************************************************/
package com.filemanager.common.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Xml;

import androidx.annotation.VisibleForTesting;

import com.filemanager.common.MyApplication;
import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.helper.VolumeEnvironment;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.util.HashMap;
import java.util.Locale;

public class FolderNote {

    private static final String TAG = "FolderNote";
    public static final String FOLDER_NOTE_CONFIG_VERSION = "folder_note_config";
    public static final String FOLDER_NOTE_CONFIG_LIST = "folder_note_config";
    public static final String FILEMANAGER_FOLDER_NOTE = "shared_prefs";
    public static final String FILEMANAGER_FOLDER_NOTES_NEARME_URI = "content://com.nearme.romupdate.provider.db/update_list";
    public static final String MYALBUM_DEFAULT_PATH = "myalbums_default_path";
    private static final boolean DEBUG = false;
    private static final String XML_FILE = FeatureCompat.getSIsExpRom() ? "folder_note_config_exp.xml" : "folder_note_config.xml";
    private static final String FOLDERNOTE_UPDATE_CONF = FeatureCompat.getSIsExpRom() ? "filemanager_folder_note_config_exp"
            : "filemanager_folder_note_config";
    private static final String TAG_NOTS = "item";
    private static final String ATTRIBUTE_FOLDER = "folder";
    private static final String ATTRIBUTE_NOTE = "note";
    private static final String ATTRIBUTE_NOTE_ZH_RCN = "note-zh-rCN";
    private static final String ATTRIBUTE_NOTE_ZH_RTW = "note-zh-rTW";
    private static final String NOTE_ZH_LANGUAGE = "zh";
    private static final String NOTE_ZH_HK = "HK";
    private static final String NOTE_ZH_TW = "TW";
    // #ifdef ODM_CM_EDIT
    // hua.huang@ODM_CM.BAT.FileManager.2755251, 2020/5/21, Add for change OPPO to Realme
    private static final String STR_OPPO = "OPPO";
    private static final String STR_REALME = "Realme";
    // #endif /* ODM_CM_EDIT */
    private static String sMyAlbumsPath = "/DCIM/MyAlbums";
    private static int sVersion = 0;
    private final Context mContext;
    private HashMap<String, Note> mLocalNotesMap = new HashMap<>();

    private static class FolderNoteHolder {
        private static FolderNote sInstance = new FolderNote();
    }

    private FolderNote() {
        mContext = MyApplication.getSAppContext();
        initialize(mContext);
    }

    public static FolderNote getInstance() {
        return FolderNoteHolder.sInstance;
    }

    public String getNoteName(String bucketId) {
        if (TextUtils.isEmpty(bucketId)) {
            return null;
        }

        String relativePath = bucketId;
        if (!bucketId.startsWith(File.separator)) {
            relativePath = File.separator + bucketId;
        }

        if (!bucketId.endsWith(File.separator)) {
            relativePath = bucketId + File.separator;
        }
        Note note = mLocalNotesMap.get(relativePath.toLowerCase());
        return ((note != null) ? note.getNoteName() : null);
    }

    @VisibleForTesting
    public static class Note {
        String mFolder;
        String mNote;
        String mNotezhrCN;
        String mNotezhrTW;

        public Note() {

        }

        public String getNoteName() {
            String country = Locale.getDefault().getCountry();
            String language = Locale.getDefault().getLanguage();
            if (DEBUG) {
                Log.d(TAG, "getNoteName: " + country + "-" + language + " " + this);
            }
            if (NOTE_ZH_LANGUAGE.equals(language)) {
                if (NOTE_ZH_HK.equals(country) || NOTE_ZH_TW.equals(country)) {
                    return getNotezhrTW();
                } else {
                    return getNotezhrCN();
                }
            }
            return getNote();
        }

        public String getNote() {
            return mNote;
        }

        public String getNotezhrCN() {
            return mNotezhrCN;
        }

        public String getNotezhrTW() {
            return mNotezhrTW;
        }

        @Override
        public String toString() {
            return "folder-note=[" + mFolder + ", " + mNote + ", " + mNotezhrCN + ", " + mNotezhrTW
                    + "]";
        }
    }

    public void initialize(Context context) {
        final int localVersion = PreferencesUtils.getInt(FOLDER_NOTE_CONFIG_LIST, FOLDER_NOTE_CONFIG_VERSION, 0);
        int xmlVersion = localVersion;
        String[] projection = new String[]{"version", "xml"};
        String xml = null;
        ContentResolver resolver = context.getContentResolver();
        Cursor cursor = null;

        try {
            cursor = resolver.query(Uri.parse(FILEMANAGER_FOLDER_NOTES_NEARME_URI), projection,
                    "filtername=" + "\"" + FOLDERNOTE_UPDATE_CONF + "\"", null, null);
            if ((cursor != null) && (cursor.moveToFirst())) {
                xml = cursor.getString(1);
                xmlVersion = cursor.getInt(0);
            }
        } catch (Exception e) {
            if (xml != null) {
                xml = null;
            }
            Log.e(TAG, "initialize: " + e.getMessage());
        } finally {
            if (null != cursor) {
                cursor.close();
                cursor = null;
            }
        }
        Log.d(TAG, "initialize xmlVer = " + xmlVersion + ", localVer = " + localVersion);
        if ((xml != null) && ((localVersion < xmlVersion) || ((localVersion == 0) && (xmlVersion == 0)))) {
            try {
                File file = new File(VolumeEnvironment.getDataDirPath(context, FILEMANAGER_FOLDER_NOTE, XML_FILE));
                if (file.exists()) {
                    if (!file.delete()) {
                        Log.e(TAG, "initialize delete error");
                        return;
                    }
                }
                parserXml(context, xml, null);
                saveMyAlbumPath();
            } catch (XmlPullParserException e) {
                Log.e(TAG, "initialize XmlPullParserException " + e.getMessage());
            } catch (IOException e) {
                Log.e(TAG, "initialize IOException " + e.getMessage());
            } catch (NumberFormatException e) {
                Log.e(TAG, "initialize NumberFormatException" + e.getMessage());
            } catch (Exception e) {
                Log.e(TAG, "initialize " + e.getMessage());
            }
        } else {
            try {
                File file = new File(VolumeEnvironment.getDataDirPath(context, FILEMANAGER_FOLDER_NOTE, XML_FILE));
                if (file.exists()) {
                    if (!file.delete()) {
                        Log.e(TAG, "initialize delete error");
                    }
                }
                parserXml(context, null, null);
                saveXmlVersion(sVersion);
                saveMyAlbumPath();
            } catch (XmlPullParserException e) {
                Log.e(TAG, "initialize XmlPullParserException " + e.getMessage());
            } catch (IOException e) {
                Log.e(TAG, "initialize IOException " + e.getMessage());
            } catch (NumberFormatException e) {
                Log.e(TAG, "initialize NumberFormatException" + e.getMessage());
            } catch (Exception e) {
                Log.e(TAG, "initialize " + e.getMessage());
            }
        }


    }

    private void parserXml(Context context, String xml, InputStream in)
            throws XmlPullParserException, IOException, NumberFormatException {
        String path = VolumeEnvironment.getInternalSdPath(context);
        try {
            XmlPullParser parser = Xml.newPullParser();
            if (!TextUtils.isEmpty(xml)) {
                parser.setInput(new StringReader(xml));
            } else {
                InputStream input = mContext.getAssets().open(XML_FILE);
                parser.setInput(input, "UTF-8");
            }
            int type = XmlPullParser.START_DOCUMENT;
            while ((type != XmlPullParser.END_DOCUMENT) && (type != XmlPullParser.START_TAG)) {
                type = parser.next();
            }

            while (true) {
                type = parser.next();
                if (type == XmlPullParser.END_DOCUMENT) {
                    break;
                }
                if ((type == XmlPullParser.END_TAG) || (type == XmlPullParser.TEXT)) {
                    continue;
                }
                String tagName = parser.getName();
                if ("version".equals(tagName)) {
                    String values = parser.nextText();
                    sVersion = Integer.parseInt(values);
                } else if (MYALBUM_DEFAULT_PATH.equals(tagName)) {
                    sMyAlbumsPath = parser.nextText();
                } else if (TAG_NOTS.equals(tagName)) {
                    String folder = parser.getAttributeValue(null, ATTRIBUTE_FOLDER);
                    if (TextUtils.isEmpty(folder)) {
                        continue;
                    }
                    String bucketId = path + folder;
                    String attributeNote = parser.getAttributeValue(null, ATTRIBUTE_NOTE);
                    String attributeNoteZhRcn = parser.getAttributeValue(null, ATTRIBUTE_NOTE_ZH_RCN);
                    String attributeNoteZhRtw = parser.getAttributeValue(null, ATTRIBUTE_NOTE_ZH_RTW);
                    // #ifdef ODM_CM_EDIT
                    // hua.huang@ODM_CM.BAT.FileManager.2755251, 2020/5/21, Add for change OPPO to Realme
                    if (Utils.isRealmePhone()) {
                        if ((attributeNote != null) && attributeNote.contains(STR_OPPO)) {
                            attributeNote = attributeNote.replaceAll(STR_OPPO, STR_REALME);
                        }
                        if ((attributeNoteZhRcn != null) && attributeNoteZhRcn.contains(STR_OPPO)) {
                            attributeNoteZhRcn = attributeNoteZhRcn.replaceAll(STR_OPPO, STR_REALME);
                        }
                        if ((attributeNoteZhRtw != null) && attributeNoteZhRtw.contains(STR_OPPO)) {
                            attributeNoteZhRtw = attributeNoteZhRtw.replaceAll(STR_OPPO, STR_REALME);
                        }
                    }
                    // #endif /* ODM_CM_EDIT */
                    Note note = new Note();
                    note.mFolder = folder;
                    note.mNote = attributeNote;
                    note.mNotezhrCN = attributeNoteZhRcn;
                    note.mNotezhrTW = attributeNoteZhRtw;
                    if (!folder.endsWith(File.separator)) {
                        folder += File.separator;
                    }
                    Log.d(TAG, "parserXml folder: " + folder);
                    mLocalNotesMap.put(folder.toLowerCase(), note);
                }
            }
        } catch (XmlPullParserException xppe) {
            Log.w(TAG, "Error reading historical recrod file: " + XML_FILE, xppe);
            return;
        } catch (IOException ioe) {
            Log.w(TAG, "Error reading historical recrod file: " + XML_FILE, ioe);
            return;
        } catch (Exception e) {
            Log.w(TAG, "[updateDB]", e);
            return;
        } finally {
            if (null != in) {
                in.close();
                in = null;
            }
        }

    }

    private static void saveMyAlbumPath() {
        PreferencesUtils.put(MYALBUM_DEFAULT_PATH, MYALBUM_DEFAULT_PATH, sMyAlbumsPath);
    }

    private static void saveXmlVersion(int xmlVersion) {
        PreferencesUtils.put(MYALBUM_DEFAULT_PATH, FOLDER_NOTE_CONFIG_VERSION, xmlVersion);
    }
}
