/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/5/25
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import androidx.lifecycle.MutableLiveData

open class BaseUiModel<T : BaseFileBean>(
    var fileList: List<T>,
    var stateModel: BaseStateModel,
    val selectedList: ArrayList<Int>,
    val keyMap: HashMap<Int, T>,
    val keyWord: String = ""
) {
    /**
     * 搜索结果列表，内部的item为BaseFileBean，包含搜索中分类header，更多footer，具体数据等
     */
    @Deprecated("Legacy offending naming", ReplaceWith("fileList"))
    val mFileList: List<T>
        get() = fileList

    /**
     * 选择结果类表，这个类表中的INT值为mFileList中对应的getItemKey值（各个Loader的getItemKey值），
     * 注意：只有选中的item的itemKey才会放在这个mSelectedList
     */
    @Deprecated("Legacy offending naming", ReplaceWith("selectedList"))
    val mSelectedList: ArrayList<Int>
        get() = selectedList

    /**
     * 跟UriLoadResult中的keyMap是同一个keyMap
     * key值为各个Loader(GlobalSearchLoader, ThirdAppFileSearchLoader, DriveFileSearchLoader)中的getItemKey值，用于选择模式，
     * value值为上面 mResultList中的各类具体的数据bean
     * 注意：这里针对不可参与选择的Bean没有getItemKey中赋值处理，itemKey相同，比如：CategoryFileWrapper，MoreFileWrapper得到的itemKey相同，可能有隐藏bug
     */
    @Deprecated("Legacy offending naming", ReplaceWith("keyMap"))
    val mKeyMap: HashMap<Int, T>
        get() = keyMap

    /**
     * 搜索的关键词
     */
    @Deprecated("Legacy offending naming", ReplaceWith("keyWord"))
    val mKeyWord: String
        get() = keyWord

    /**
     * 见下面的BaseStateModel注释解释
     */
    @Deprecated("Legacy offending naming", ReplaceWith("stateModel"))
    var mStateModel: BaseStateModel
        get() = stateModel
        set(value) = ::stateModel.set(value)
}

class BaseStateModel(var listModel: MutableLiveData<Int>) {
    var initState: Boolean = false

    /**
     * mListModel 中的Int值存在两种值：
     * KtConstants.LIST_NORMAL_MODE 代表正常列表状态
     * KtConstants.LIST_SELECTED_MODE 代表类表处于选择状态
     */
    @Deprecated("Legacy offending naming", ReplaceWith("listModel"))
    var mListModel: MutableLiveData<Int>
        get() = listModel
        set(value) = ::listModel.set(value)

    /**
     * 代表搜索中的状态
     * boolean = false 代表在搜索加载过程中，还未将搜索结果给到相应的GlobalSearchFragmentViewModel，
     * boolean = true  代表在搜索加载过一次数据，已经将搜索结果给到相应的GlobalSearchFragmentViewModel
     */
    @Deprecated("Legacy offending naming", ReplaceWith("initState"))
    var mInitState: Boolean
        get() = initState
        set(value) = ::initState.set(value)

    override fun toString(): String = "(${listModel.value}, $initState)"
}