/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.thread
 * * Version     : 1.0
 * * Date        : 2020/2/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.thread

import java.util.concurrent.ThreadFactory
import java.util.concurrent.atomic.AtomicInteger


class BaseThreadFactory : ThreadFactory {
    private val mCount = AtomicInteger(1)
    private var mThreadNamePrefix = "BaseThreadFactory"

    override fun newThread(r: Runnable): Thread {
        return Thread(r, mThreadNamePrefix + " #" + mCount.getAndIncrement())
    }
}