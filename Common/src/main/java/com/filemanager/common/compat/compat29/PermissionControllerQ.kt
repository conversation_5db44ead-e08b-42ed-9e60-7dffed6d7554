/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/13
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.compat.compat29

import android.app.Activity
import android.app.Dialog
import android.content.pm.PackageManager
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageButton
import android.widget.LinearLayout
import androidx.lifecycle.Lifecycle
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.emptyview.COUIEmptyStateView
import com.filemanager.common.controller.PermissionController
import com.filemanager.common.utils.PermissionUtils
import com.filemanager.common.R
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.WindowUtils
import com.filemanager.common.utils.isInvalid

class PermissionControllerQ(lifecycle: Lifecycle, listener: OnRequestPermissionListener) : PermissionController(lifecycle, listener) {
    companion object {
        private const val TAG = "PermissionControllerQ"
    }

    private var mAlwaysReject: Boolean? = null

    override fun internalCheckPermission(activity: Activity) {
        Log.d(TAG, "internalCheckPermission")
        // Start to check permission after showing guild dialog
        if (PermissionUtils.hasStoragePermission()) {
            mAlwaysReject = false
            mPermissionListener?.onPermissionSuccess()
        } else {
            if (activity.isInvalid()) {
                Log.d(TAG, "checkShowSettingGuildDialog: activity invalid")
                return
            }
            if (mAlwaysReject != true) {
                val result = PermissionUtils.requestStoragePermission(activity)
                setWaitPermissionGrantResult(result)
            } else if (mPermissionEmptyView == null) {
                if (mPermissionGuildDialog?.isShowing != true) {
                    mPermissionGuildDialog = createSettingGuildDialog(activity, object : OnPermissionGuildListener {
                        override fun onPermissionGuildResult(grant: Boolean, noLongerRemind: Boolean) {
                            if (grant) {
                                val result = PermissionUtils.openPermissionSetting(activity)
                                setWaitPermissionGrantResult(result)
                            }
                        }
                    })
                    mPermissionGuildDialog?.show()
                }
            }
        }
    }

    override fun showSettingGuildDialog(activity: Activity) {
        Log.d(TAG, "showSettingGuildDialog")
    }

    override fun checkGetInstalledAppsPermission(activity: Activity, isMainShow: Boolean) {
        Log.d(TAG, "checkGetInstalledAppsPermission")
    }

    override fun createPermissionEmptyView(activity: Activity): View {
        val viewRoot = LayoutInflater.from(activity).inflate(R.layout.permission_common_view_layout, null)
        val topBackIcon = viewRoot.findViewById<ImageButton>(R.id.top_back_icon)
        val toolbarLayout = viewRoot.findViewById<LinearLayout>(R.id.empty_toolbar_layout)
        val visible = if (WindowUtils.isMiddleAndLargeScreen(activity)) View.GONE else View.VISIBLE
        toolbarLayout.visibility = visible
        topBackIcon.setOnClickListener {
            activity.finish()
        }
        return viewRoot
    }

    override fun setWaitPermissionGrantResult(waiting: Boolean) {
        super.setWaitPermissionGrantResult(waiting)
        if (waiting.not()) {
            if (PermissionUtils.hasStoragePermission()) {
                mAlwaysReject = false
                mPermissionListener?.onPermissionSuccess()
            }
        }
    }

    override fun onPermissionsResultReturn(activity: Activity, requestCode: Int, permissions: Array<out String>?,
                                           grantResults: IntArray?) {
        Log.d(TAG, "onPermissionsResultReturn(): start")
        if (PermissionUtils.REQUEST_MANAGE_ALL_FILES_PERMISSIONS != requestCode) {
            return
        }
        if (permissions.isNullOrEmpty() || (grantResults?.isNotEmpty() != true)) {
            Log.d(TAG, "Failed requestPermissionsResult: grantResults is null or empty")
            return
        }
        var alwaysReject = false
        var reject = false
        for ((index, result) in grantResults.withIndex()) {
            if (result != PackageManager.PERMISSION_GRANTED) {
                reject = true
                if (!activity.shouldShowRequestPermissionRationale(permissions[index])) {
                    alwaysReject = true
                }
                break
            }
        }
        Log.d(TAG, "onPermissionsResultReturn(): reject=$reject, alwaysReject=$alwaysReject")
        mAlwaysReject = alwaysReject
        if (reject) {
            mPermissionListener?.onPermissionReject(alwaysReject)
        } else {
            mPermissionListener?.onPermissionSuccess()
        }
    }

    private fun createSettingGuildDialog(activity: Activity, listener: OnPermissionGuildListener): Dialog {
        Log.d(TAG, "createSettingGuildDialog()")
        val builder = COUIAlertDialogBuilder(activity)
                .setTitle(R.string.open_storage_permission)
                .setMessage(R.string.storage_permission_descrption)
                .setPositiveButton(R.string.goto_permission_setting) { p0, p1 ->
                    listener.onPermissionGuildResult(true)
                }
                .setNegativeButton(R.string.dialog_cancel, null)
                .setCancelable(false)
        val dialog = builder.create()
        dialog.setCanceledOnTouchOutside(false)
        return dialog
    }
}