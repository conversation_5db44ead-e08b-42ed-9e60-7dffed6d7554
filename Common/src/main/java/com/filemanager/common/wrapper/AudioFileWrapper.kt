/*********************************************************************
 * * Copyright (C), 2020, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2020/12/3
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.wrapper

import android.database.Cursor
import android.net.Uri
import com.filemanager.common.utils.Log

open class AudioFileWrapper : MediaFileWrapper {
    companion object {
        private const val TAG = "AudioFileWrapper"
    }
    var mDuration: Int? = null

    constructor()

    constructor(id: Int?, absPath: String?, displayName: String?, mimeType: String?, size: Long, dateModified: Long, duration: Int?, uri: Uri?) : super() {
        createData(id, absPath, displayName, mimeType, size, dateModified, uri)
        mDuration = duration
        mMediaDuration = duration?.toLong() ?: 0
    }


    @Deprecated("Not impl",
            ReplaceWith("createData(id, absPath, displayName, mimeType, size, dateModified, uri)"),
            DeprecationLevel.HIDDEN)
    override fun createData(cursor: Cursor, uri: Uri?) {
        Log.w(TAG, "Not impl, please use the method createData(id, absPath, displayName, mimeType, size, dateModified, uri) ")
    }


    override fun getOrientation(): Int {
        return mOrientation
    }

    override fun toString(): String {
        return "MediaFileWrapper(mOrientation=$mOrientation, mHeight=$mHeight, mWidth=$mWidth)+${super.toString()}"
    }


}
