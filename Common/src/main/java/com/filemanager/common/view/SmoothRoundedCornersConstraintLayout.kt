/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SmoothRoundedCornersConstraintLayout
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/12 10:53
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/4/12       1.0      create
 ***********************************************************************/
package com.filemanager.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import androidx.constraintlayout.widget.ConstraintLayout
import com.coui.appcompat.preference.COUIRecommendedDrawable
import com.coui.appcompat.pressfeedback.COUIPressFeedbackHelper
import com.filemanager.common.R
import com.filemanager.common.utils.ColorUtil

open class SmoothRoundedCornersConstraintLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mStrokeColor: Int = 0
    private var mRadius: Float = 0F
    var mIsDefaultBg = false
    var isEdit = false

    init {
        val array =
            context.obtainStyledAttributes(attrs, R.styleable.SmoothRoundedCornersConstraintLayout)
        mIsDefaultBg = array.getBoolean(
            R.styleable.SmoothRoundedCornersConstraintLayout_default_background,
            false
        )
        if (mIsDefaultBg) {
            mStrokeColor = ColorUtil.getCouiColorCardBackground(getContext())
        } else {
            mStrokeColor =
                array.getColor(R.styleable.SmoothRoundedCornersConstraintLayout_solid_color, 0)
        }
        val dp12 = context.resources.getDimension(R.dimen.dimen_12dp)
        mRadius =
            array.getDimension(R.styleable.SmoothRoundedCornersConstraintLayout_corner_radius, dp12)
        array.recycle()

        // 设置background
        setRoundCornerBg()

        // 设置点击反馈
        initPressFeedback(this)
    }

    /**
     * 设置圆角背景
     */
    open fun setRoundCornerBg() {
        val drawable = COUIRecommendedDrawable(mRadius, mStrokeColor)
        background = drawable
    }

    open fun setHoverBackground() {
        val drawable = AppCompatResources.getDrawable(context, R.drawable.bg_card_hover)
        background = drawable
    }

    fun refreshBg() {
        setRoundCornerBg()
    }

    private fun initPressFeedback(view: View?) {
        val type = if (mIsDefaultBg) {
            COUIPressFeedbackHelper.UNJUMPABLE_CARD_PRESS_FEEDBACK
        } else {
            COUIPressFeedbackHelper.CARD_PRESS_FEEDBACK
        }
        initPressFeedback(type, view, view)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun initPressFeedback(type: Int, feedbackView: View?, pressedView: View?) {
        val feedbackUtils = COUIPressFeedbackHelper(feedbackView, type)
        pressedView?.setOnTouchListener { v, event ->
            if (isEdit) {
                return@setOnTouchListener false
            }
            when (event?.action) {
                MotionEvent.ACTION_DOWN -> {
                    feedbackUtils.executeFeedbackAnimator(true)
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    feedbackUtils.executeFeedbackAnimator(false)
                }
            }
            return@setOnTouchListener false
        }
    }
}