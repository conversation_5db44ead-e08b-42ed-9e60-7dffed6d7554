/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : RemovableAppManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/12/30 14:52
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/12/30       1.0      create
 ***********************************************************************/
package com.filemanager.common.removableapp

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.view.LayoutInflater
import android.view.View
import androidx.activity.ComponentActivity
import androidx.annotation.WorkerThread
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.lifecycleScope
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.coui.appcompat.dialog.COUIRotatingDialogBuilder
import com.coui.appcompat.imageview.COUIRoundImageView
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.AppUtils.isAppInstalledByPkgName
import com.filemanager.common.utils.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Locale

/**
 * Removable App Manager.
 */
class RemovableAppManager(val activity: ComponentActivity) {

    private var mRemovableAppController = RemovableAppController(MyApplication.sAppContext)
    private var mRemovableAppProviderController = RemovableAppProviderController(MyApplication.sAppContext)

    /**
     * Obtain [Constants.OPEN_ANY_PKG_NAME] removable app info.
     */
    fun obtainRemovableInfo(): RemovableAppInfo? {
        return mRemovableAppProviderController.queryAppInfo(Constants.OPEN_ANY_PKG_NAME)
    }

    /**
     * Re-install FileManager App.
     */
    @WorkerThread
    fun reInstallApp(
        context: Context,
        removableAppInfo: RemovableAppInfo,
        callback: InstallResultCallback
    ) {
        activity.lifecycleScope.launch(Dispatchers.Default) {
            mRemovableAppController.open()
            mRemovableAppController.registerCallback(object : RemovableAppStateCallback {
                override fun onOpen() {
                    val installed = isInstalledOpenAny(context)
                    Log.d(TAG, "restoreApp -> onOpen installed = $installed")
                    if (!installed) {
                        internalInstallApp(context, removableAppInfo, callback)
                    }
                }

                override fun onClose() {
                    Log.d(TAG, "restoreApp -> onClose")
                }
            })
        }
    }

    private fun internalInstallApp(
        context: Context,
        removableAppInfo: RemovableAppInfo,
        callback: InstallResultCallback
    ) {
        val installingDialog = showInstallingDialog(context)
        mRemovableAppController.restoreApp(removableAppInfo.packageName) { success ->
            Log.d(TAG, "internalInstallApp -> restore result is $success")
            installingDialog.dismiss()
            callback.onInstalledResult(success)
        }
    }

    fun createRemovableConfirmDialog(
        context: Context,
        removableAppInfo: RemovableAppInfo,
        callback: (Boolean) -> Unit
    ): AlertDialog {
        val view = LayoutInflater.from(context).inflate(R.layout.removable_confirm_dialog_layout, null)
        initRemovableConfirmView(view, removableAppInfo)
        val title = String.format(
            Locale.getDefault(),
            context.getString(R.string.install_dialog_title),
            context.getString(R.string.string_documents)
        )
        return COUIAlertDialogBuilder(context)
            .setTitle(title)
            .setMessage(R.string.install_dialog_content)
            .setView(view)
            .setCancelable(false)
            .setPositiveButton(R.string.install_string) { _, _ ->
                callback.invoke(true)
            }
            .setNegativeButton(R.string.alert_dialog_cancel) { _, _ ->
                callback.invoke(false)
            }
            .show()
    }

    private fun initRemovableConfirmView(view: View, removableAppInfo: RemovableAppInfo) {
        val icon = view.findViewById<COUIRoundImageView>(R.id.removable_app_icon)
        removableAppInfo.icon?.let {
            icon.setImageDrawable(it.toDrawable(view.context))
        }
    }

    fun createInstallFailureDialog(context: Context, callback: InstallResultCallback): AlertDialog {
        return COUIAlertDialogBuilder(context)
            .setTitle(R.string.install_failed)
            .setPositiveButton(R.string.reinstall) { _, _ ->
                Log.d(TAG, "createInstallFailureDialog -> PositiveButton")
                reInstallApp(context, RemovableAppInfo(Constants.OPEN_ANY_PKG_NAME), callback)
            }
            .setNegativeButton(R.string.alert_dialog_cancel, null)
            .create()
    }

    private fun showInstallingDialog(context: Context): AlertDialog {
        val installingDialog = COUIRotatingDialogBuilder(context, context.getString(R.string.install_loading))
            .show().apply {
                setCanceledOnTouchOutside(false)
            }
        return installingDialog
    }

    fun ByteArray.toBitmap(): Bitmap {
        return BitmapFactory.decodeByteArray(this, 0, this.size)
    }

    fun ByteArray.toDrawable(context: Context): Drawable {
        return BitmapDrawable(context.resources, this.toBitmap())
    }

    companion object {
        private const val TAG = "RemovableAppManager"

        /**
         * Determine whether file manager is installed.
         */
        fun isInstalledOpenAny(context: Context): Boolean {
            return isAppInstalledByPkgName(context, Constants.OPEN_ANY_PKG_NAME)
        }
    }
}