/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/15
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.compat

import androidx.annotation.VisibleForTesting
import com.filemanager.common.compat.compat29.PropertyCompatQ
import com.filemanager.common.compat.compat30.PropertyCompatR
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import java.util.Objects

object PropertyCompat  {
    private const val TAG = "PropertyCompat"
    internal const val PROPERTY_CPU_INFO = "ro.hardware"
    internal const val PROPERTY_MODEL = "ro.product.name"
    internal const val PROPERTY_OTA_VERSION = "ro.build.version.ota"
    internal const val PROPERTY_ANDROID_VERSION = "ro.build.version.release"
    // This region is changed with the system Settings
    internal const val PROPERTY_SYSTEM_REGION = "persist.sys.oppo.region"
    // This region is the preset region, and not changed with the system settings
    internal const val PROPERTY_PHONE_MARK_REGION ="ro.oppo.regionmark"
    internal const val PROPERTY_COLOROS_VERSION = "ro.build.version.opporom"
    internal const val PROPERTY_LOG_PANIC = "persist.sys.assert.panic"
    internal const val PROPERTY_LOG_ENABLE = "persist.sys.assert.enable"
    internal const val PROPERTY_CONFIDENTIAL_VERSION = "persist.version.confidential"
    internal const val PROPERTY_ROM_SIZE = "ro.product.romsize"
    internal const val PROPERTY_SDCARD_SUPPORT = "persist.sys.filemanager.sdcard"
    internal const val FEATURE_REALME_MACHINE_MODEL = "ro.product.oplus_series"

    internal const val FEATURE_GDPR = "ro.oplus.image.my_heytap.type"

    const val PROPERTY_RSA = "ro.oplus.rsa"
    const val PROPERTY_RSA_PEP_VALUE = "rsa4-tier3"
    const val PROPERTY_LIGHT_OS = "ro.oplus.lightos"
    const val PROPERTY_ANIM_LEVEL = "persist.sys.oplus.anim_level"
    const val PROPERTY_CHANNEL = "persist.sys.channel.info"
    const val PROPERTY_CHANNEL_COUNTRY = "persist.sys.channel.country.info"
    const val PROPERTY_PIPELINE_CARRIER = "ro.oplus.pipeline.carrier"
    const val PROPERTY_PIPELINE_REGION = "ro.oplus.pipeline.region"

    @VisibleForTesting
    val sOPlusPropertyInterface by lazy {
        if (SdkUtils.isAtLeastR()) {
            PropertyCompatR
        } else {
            PropertyCompatQ
        }
    }

    @JvmStatic
    val sAnimLevel: String by lazy {
        sOPlusPropertyInterface.getAnimLevel()
    }

    @JvmStatic
    val sCpuInfo: String by lazy {
        sOPlusPropertyInterface.getCPUInfo()
    }

    @JvmStatic
    val sOplusSeries: String by lazy {
        sOPlusPropertyInterface.getOplusSeries()
    }

    @JvmStatic
    val sModel: String by lazy {
        sOPlusPropertyInterface.getModel()
    }

    @JvmStatic
    val sOTAInfo: String by lazy {
        sOPlusPropertyInterface.getOTAInfo()
    }

    @JvmStatic
    val sSystemRegion: String by lazy {
        sOPlusPropertyInterface.getSystemRegion()
    }

    @JvmStatic
    val sPhoneMarkRegion: String by lazy {
        sOPlusPropertyInterface.getPhoneMarkRegion()
    }

    @JvmStatic
    val sColorOSVersion: String by lazy {
        sOPlusPropertyInterface.getColorOSVersion()
    }

    @JvmStatic
    val sAndroidVersion: String by lazy {
        sOPlusPropertyInterface.getAndroidVersion()
    }

    @JvmStatic
    val sColorOSVersionCode: Int by lazy {
        val colorOSVersion = SdkUtils.getOSVersion()
        Log.d(TAG, "sColorOSVersionCode: $colorOSVersion")
        colorOSVersion
    }

    @JvmStatic
    val sLogEnable: Boolean by lazy {
        sOPlusPropertyInterface.isLogEnable()
    }

    @JvmStatic
    val sROMSize: String by lazy {
        sOPlusPropertyInterface.getRomSize()
    }

    @JvmStatic
    val sIsSupportSDCard: Boolean by lazy {
        sOPlusPropertyInterface.isSupportSDCard()
    }
    @JvmStatic
    val sIsGDPR: Boolean by lazy {
        sOPlusPropertyInterface.isGDPR()
    }
    @JvmStatic
    val sIsNeedUpdateByOta: Boolean by lazy {
        sOPlusPropertyInterface.isNeedUpdateByOta()
    }

    @JvmStatic
    val sIsPepProject: Boolean by lazy {
        val property = sOPlusPropertyInterface.getRSAProperty()
        // ro.oplus.rsa为rsa4-tier3  代表为pep项目
        val result = Objects.equals(property, PROPERTY_RSA_PEP_VALUE)
        Log.d(TAG, "isPepProject property:$property result:$result")
        result
    }

    @JvmStatic
    val sIsLightOS: Boolean by lazy {
        sOPlusPropertyInterface.isLightOS()
    }

    @JvmStatic
    val channelInfo: String by lazy {
        sOPlusPropertyInterface.getChannelInfo()
    }

    @JvmStatic
    val channelCountry: String by lazy {
        sOPlusPropertyInterface.getChannelCountry()
    }

    @JvmStatic
    val pipelineCarrier: String by lazy {
        sOPlusPropertyInterface.getPipelineCarrier()
    }

    @JvmStatic
    val pipelineRegion: String by lazy {
        sOPlusPropertyInterface.getPipelineRegion()
    }

    interface IPropertyInterface {
        fun getCPUInfo(): String
        fun getOTAInfo(): String
        fun getModel(): String
        fun getSystemRegion(): String
        fun getPhoneMarkRegion(): String
        fun getColorOSVersion(): String
        fun getAndroidVersion(): String
        fun isLogEnable(): Boolean
        fun getRomSize(): String
        fun getOplusSeries(): String
        fun isSupportSDCard(): Boolean
        fun isGDPR(): Boolean
        /**
         * get ro.virtual_ab_enabled by ota provider and use better in
         * WorkThread or CoroutineScope because of this.
         * @return if true, file manager need to update system by ota
         */
        fun isNeedUpdateByOta(): Boolean

        /**
         * 读取属性值：ro.oplus.rsa
         */
        fun getRSAProperty(): String

        /**
         * obtain system properties [PROPERTY_LIGHT_OS] to determine whether curr is light os.
         */
        fun isLightOS(): Boolean
        fun getAnimLevel(): String

        /**
         * get channel info of COTA version
         */
        fun getChannelInfo(): String

        /**
         * get channel country of cota version
         */

        fun getChannelCountry(): String

        /**
         * get pipeline carrier of cota customize version
         */
        fun getPipelineCarrier(): String

        /**
         * get pipeline region of cota public version
         */
        fun getPipelineRegion(): String
    }
}