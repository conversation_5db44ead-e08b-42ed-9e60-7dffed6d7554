/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: PreviewCombineViewModel
 ** Description: Preview Combine ViewModel
 ** Version: 1.0
 ** Date : 2024/10/09
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/
package com.filemanager.common.filepreview

import androidx.lifecycle.MutableLiveData
import com.filemanager.common.MyApplication
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseViewModel
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.util.PreviewUtils
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.WindowUtils

class PreviewCombineViewModel : BaseViewModel() {

    val previewState by lazy {
        val isPreviewOpen = PreviewUtils.isPreviewOpen()
        MutableLiveData<Int>(
            if (!isPreviewOpen || WindowUtils.isSmallScreen(MyApplication.appContext)) {
                KtConstants.PREVIEW_CLOSE
            } else {
                KtConstants.PREVIEW_OPEN
            }
        )
    }
    var previewListFragmentCreator: IPreviewListFragmentCreator? = null
    var clickFileLiveData: MutableLiveData<BaseFileBean?>? = null

    fun clickPreviewItem() {
        if (previewState.value == KtConstants.PREVIEW_OPEN) {
            closePreview()
        } else {
            openPreview()
        }
    }

    fun closePreview() {
        previewState.value = KtConstants.PREVIEW_CLOSE
        PreviewUtils.setPreviewState(false)
        FileImageVHUtils.setShowPreview(false)
    }

    fun openPreview() {
        previewState.value = KtConstants.PREVIEW_OPEN
        PreviewUtils.setPreviewState(true)
        FileImageVHUtils.setShowPreview(true)
    }

    fun isPreviewOpen(): Boolean {
        return previewState.value == KtConstants.PREVIEW_OPEN
    }
}