/***********************************************************
 * * Copyright (C), 2008-2017 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File:CategoryHelper.java
 * * Description:Gets the number of information displayed in the categorized view
 * * and provides a filtering related tool method.
 * * Version:1.0
 * * Date :20180802
 * * Author:dengzhicheng
 * * OPLUS Java File Skip Rule:MethodLength
 * *
 * * ---------------------Revision History: ---------------------
 * *  <author>    <data>    <version >    <desc>
 * * dengzhicheng  20180802  1.0  Gets the number of information displayed in the categorized view
 * * and provides a filtering related tool method.
 ****************************************************************/
package com.filemanager.common.helper;

import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import android.os.Handler;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.SparseArray;

import androidx.annotation.VisibleForTesting;

import com.filemanager.common.R;
import com.filemanager.common.compat.MediaStoreCompat;
import com.filemanager.common.constants.Constants;
import com.filemanager.common.utils.Log;
import com.filemanager.common.utils.PreferencesUtils;
import com.filemanager.common.utils.SdkUtils;
import com.filemanager.common.utils.WhiteListParser;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class CategoryHelper {
    private static final String TAG = "CategoryHelper";
    public static final String DATA = MediaStore.Files.FileColumns.DATA;
    /**
     * CATEGORY_ITEM_ORDER
     * 照片、视频、音频、文档、安装包、[下载]、（压缩包）、（收藏）、云盘、QQ、微信、[商店引流]、应用管理
     */
    public static final int ITEM_IMAGE = 0;
    public static final int ITEM_VIDEO = 1;
    public static final int ITEM_AUDIO = 2;
    public static final int ITEM_DOC = 3;
    public static final int ITEM_APK = 4;
    public static final int ITEM_COMPRESS = 5;
    public static final int ITEM_DOWNLOAD = 6;
    public static final int ITEM_CLOUD = 7;
    public static final int ITEM_QQ = 8;
    public static final int ITEM_MICROMSG = 9;
    public static final int ITEM_APP_ITEM_7 = 10;
    public static final int ITEM_APP_ITEM_8 = 11;
    public static final int ITEM_APP_ITEM_9 = 12;
    public static final int ITEM_OAPS = 13;
    public static final int ITEM_APPMANAGER = 14;
    public static final int ITEM_FIX_LAST_INDEX = ITEM_COMPRESS;
    public static final int ITEM_FIX_COUNT = ITEM_FIX_LAST_INDEX + 1;

    public static final int CATEGORY_FILE_PREVIEW = -1001;

    public static final int CATEGORY_IMAGE = 1;
    public static final int CATEGORY_VIDEO = 4;
    public static final int CATEGORY_AUDIO = 2;
    public static final int CATEGORY_DOC = 3;
    public static final int CATEGORY_APK = 16;
    public static final int CATEGORY_DOWNLOAD = 64;
    public static final int CATEGORY_COMPRESS = 32;
    public static final int CATEGORY_COMPRESS_PREVIEW = 33;
    public static final int CATEGORY_SPLASH = 99;
    public static final int CATEGORY_BLUETOOTH = 256;
    public static final int CATEGORY_REMOTE_PC_CONTROL = 257;
    public static final int CATEGORY_CLOUD = 901;
    public static final int CATEGORY_OAPS = 905;
    public static final int CATEGORY_QQ = 512;
    public static final int CATEGORY_APP_ITEM_7 = 9;
    public static final int CATEGORY_APP_ITEM_8 = 10;
    public static final int CATEGORY_APP_ITEM_9 = 11;
    public static final int CATEGORY_PAGE_ALBUM_SET = 13;
    public static final int CATEGORY_MICROMSG = 1024;
    public static final int CATEGORY_RECYCLE_BIN = 1001;
    public static final int CATEGORY_SEARCH = 1002;
    public static final int CATEGORY_RECENT = 1003;
    public static final int CATEGORY_SHARE = 1004;
    public static final int CATEGORY_PCCONNECT = 1005;
    public static final int CATEGORY_FILE_BROWSER = 1006;
    public static final int CATEGORY_OTG_BROWSER = 1007;
    public static final int CATEGORY_MORE_STORAGE = 10071;
    public static final int CATEGORY_APPMANAGER = 1008;
    public static final int CATEGORY_SDCARD_BROWSER = 1009;
    public static final int CATEGORY_FAVORITE = 1010;
    public static final int CATEGORY_LABEL_FILE = 1012;
    public static final int CATEGORY_PRIVATE_SAVE = 1013;
    public static final int CATEGORY_FOOTER = 1014;
    public static final int CATEGORY_SIDE_DIVIDER = 1015;
    public static final int CATEGORY_ADD_FOLDER = 1016;
    public static final int CATEGORY_ADD_LABEL = 1017;
    public static final int CATEGORY_CLEAN_GARBAGE = 1018;
    public static final int CATEGORY_QUESTIONNAIRE = 1019;
    public static final int CATEGORY_FILE_REMOTE_MAC = 1020;

    public static final int CATEGORY_APPMANAGER_VIEWTYPE = 1;
    public static final int CATEGORY_OWORK = 2050;

    public static final int CATEGORY_TENCENT_DOCS = 2052;
    public static final int CATEGORY_K_DOCS = 2053;
    public static final int CATEGORY_DFM = 2054;
    public static final int CATEGORY_SEARCH_LOCAL_FILE = 2055;
    public static final int CATEGORY_SEARCH_DRIVE_FILE = 2056;
    public static final int CATEGORY_SEARCH_THIRD_APP = 2057;
    public static final int CATEGORY_FILE_ALL = 2048;
    public static final int CATEGORY_FILL_RECENTLY = 2049;
    public static final int CATEGORY_MAIN = 2051;

    public static final int CATEGORY_FOLDER = 2058;

    /**
     * 组的类别比一般的类别数值大，用于中大屏动态加载的类别自增，避免重复。
     */
    public static final int CATEGORY_POSITION_GROUP = 1000000;
    public static final int CATEGORY_CLASSIFICATION_GROUP = 2000000;
    public static final int CATEGORY_SOURCE_GROUP = 3000000;
    public static final int CATEGORY_FOLDER_GROUP = 4000000;
    public static final int CATEGORY_LABEL_GROUP = 5000000;
    /**
     * 位置组里面的远程 Mac 设备类别,数值在 CATEGORY_POSITION_GROUP 和 CATEGORY_CLASSIFICATION_GROUP 之间
     */
    public static final int CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC = 1010000;

    public static final String[] DEFAULT_DOCUMENT_TYPE = new String[]{".doc", ".xls", ".ppt", ".pdf", ".ofd"};
    public static final long MIN_SIZE = 100;
    public static final String END_STR = "log.txt";
    public static final String DOWNLOAD = "Download";
    public static final String MMS = "Mms";
    public static final String RECORDINGS = "Recordings";
    public static final String RECORD = "Record";
    public static final String MUSIC = "Music";
    public static final String TXT = ".txt";
    private static final String TAG_VIDEO_FILTER_SIZE_VALUES = "video_filter_size_values";
    private static final String TAG_VIDEO_FILTER_DURATION_VALUES = "video_filter_duration_values";
    private static final String IGNORED_CATEGORY_PATH_TABLE_NAME = "ignored_category_path";
    private static final String DATABASE_DIR = "databases";
    private String mExternalPath;
    private String mInternalPath;
    private String[] mCategoryItemText;
    private Handler mHandler;
    private Context mContext;

    public CategoryHelper(Handler handler, Context context) {
        if (context == null) {
            return;
        }
        mHandler = handler;
        mContext = context;
        mInternalPath = VolumeEnvironment.getInternalSdPath(mContext);
        mExternalPath = VolumeEnvironment.getExternalSdPath(mContext);
    }

    /**
     * 判断是否是来源App的类别，此处统一判断
     *
     * @param type 类别
     * @return 是否是来源App类别
     */
    public static boolean isSuperAppType(int type) {
        return type > CATEGORY_SOURCE_GROUP && type < CATEGORY_FOLDER_GROUP;
    }

    /**
     * 判断是否是快捷文件夹
     *
     * @param type 类别
     * @return
     */
    public static boolean isShortcutFolderType(int type) {
        return type > CATEGORY_FOLDER_GROUP && type < CATEGORY_LABEL_GROUP;
    }

    /**
     * 判断是不是标签类别，在此统一判断
     *
     * @param type 类别
     * @return 是否是标签类别
     */
    public static boolean isLabelType(int type) {
        return type > CATEGORY_LABEL_GROUP;
    }

    /**
     * 判断类型是否是一组相同的类型，比如来源，快捷文件夹，标签的类型都是相同的
     *
     * @param type 类别
     * @return 判断类型是否是一组相同的类型
     */
    public static boolean isSameGroupType(int type, int otherType) {
        boolean isSuperApp = isSuperAppType(type) && isSuperAppType(otherType);
        boolean isShortcutFolder = isShortcutFolderType(type) && isShortcutFolderType(otherType);
        boolean isLabel = isLabelType(type) && isLabelType(otherType);
        return isSuperApp || isShortcutFolder || isLabel;
    }

    /**
     * 判断是否是远程 Mac 设备
     *
     * @param type 类别
     * @return
     */
    public static boolean isRemoteMacDeviceType(int type) {
        return type > CATEGORY_POSITION_GROUP_SUB_LIST_REMOTE_MAC && type < CATEGORY_CLASSIFICATION_GROUP;
    }

    public String getTitle(int index) {
        getCategoryItemsText();
        if ((index >= 0) && (mCategoryItemText != null) && (index < mCategoryItemText.length)) {
            return mCategoryItemText[index];
        }
        return "";
    }

    public String[] getCategoryItemsText() {
        if (null == mContext) {
            Log.e(TAG, "getCategoryItemsText null == mContext");
            return new String[]{};
        }
        if (mCategoryItemText == null) {
            mCategoryItemText = mContext.getResources().getStringArray(R.array.category_activity_title_new);
        }
        return mCategoryItemText;
    }

    @SuppressWarnings("PMD.LongMethod")
    public static void saveCategoryCountData(int category, long count, long size) {
        String countKey = null;
        String sizeKey = null;
        String tag = null;
        switch (category) {
            case CATEGORY_IMAGE: {
                countKey = Constants.RECORD_LAST_IMAGE_COUNT;
                sizeKey = Constants.RECORD_LAST_IMAGE_SIZE;
                tag = "image";
                break;
            }
            case CATEGORY_AUDIO: {
                countKey = Constants.RECORD_LAST_AUDIO_COUNT;
                sizeKey = Constants.RECORD_LAST_AUDIO_SIZE;
                tag = "audio";
                break;
            }
            case CATEGORY_VIDEO: {
                countKey = Constants.RECORD_LAST_VIDEO_COUNT;
                sizeKey = Constants.RECORD_LAST_VIDEO_SIZE;
                tag = "video";
                break;
            }
            case CATEGORY_DOC: {
                countKey = Constants.RECORD_LAST_DOC_COUNT;
                sizeKey = Constants.RECORD_LAST_DOC_SIZE;
                tag = "doc";
                break;
            }
            case CATEGORY_DOWNLOAD: {
                countKey = Constants.RECORD_LAST_DOWNLOAD_COUNT;
                sizeKey = Constants.RECORD_LAST_DOWNLOAD_SIZE;
                tag = "download";
                break;
            }
            case CATEGORY_APK: {
                countKey = Constants.RECORD_LAST_APK_COUNT;
                sizeKey = Constants.RECORD_LAST_APK_SIZE;
                tag = "apk";
                break;
            }
            case CATEGORY_COMPRESS: {
                countKey = Constants.RECORD_LAST_COMPRESS_COUNT;
                sizeKey = Constants.RECORD_LAST_COMPRESS_SIZE;
                tag = "compress";
                break;
            }
            case CATEGORY_QQ: {
                countKey = Constants.RECORD_LAST_QQ_COUNT;
                sizeKey = Constants.RECORD_LAST_QQ_SIZE;
                tag = "qq";
                break;
            }
            case CATEGORY_MICROMSG: {
                countKey = Constants.RECORD_LAST_MICROMSG_COUNT;
                sizeKey = Constants.RECORD_LAST_MICROMSG_SIZE;
                tag = "micromsg";
                break;
            }
            case ITEM_APP_ITEM_7: {
                countKey = Constants.RECORD_LAST_APK_NINE_COUNT;
                sizeKey = Constants.RECORD_LAST_APK_NINE_SIZE;
                tag = "nine";
                break;
            }
            case ITEM_APP_ITEM_8: {
                countKey = Constants.RECORD_LAST_APK_TEN_COUNT;
                sizeKey = Constants.RECORD_LAST_APK_TEN_SIZE;
                tag = "ten";
                break;
            }
            case ITEM_APP_ITEM_9: {
                countKey = Constants.RECORD_LAST_APK_ELEVEN_COUNT;
                sizeKey = Constants.RECORD_LAST_APK_ELEVEN_SIZE;
                tag = "eleven";
                break;
            }
            case CATEGORY_APPMANAGER: {
                countKey = Constants.RECORD_LAST_APP_INSTALL_COUNT;
                sizeKey = Constants.RECORD_LAST_APP_INSTALL_SIZE;
                tag = "appManager";
                break;
            }
            default:
                break;
        }
        if (null != countKey) {
            Log.d(TAG, tag + "|| count = " + count + "; size = " + size);
            writeCategoryItemNewRecordToFiles(count, countKey, size, sizeKey);
        }
    }

    @VisibleForTesting
    static void writeCategoryItemNewRecordToFiles(long count, String countKey, long size, String sizeKey) {
        Map<String, Long> map = new HashMap<>(2);
        map.put(countKey, count);
        map.put(sizeKey, size);
        PreferencesUtils.putAll(Constants.FILEMANAGER_RECORD, map);
    }

    public Uri getUri(int category) {
        switch (category) {
            case CATEGORY_IMAGE: {
                return MediaHelper.IMAGES_MEDIA_URI;
            }
            case CATEGORY_AUDIO: {
                return MediaHelper.AUDIO_MEDIA_URI;
            }
            case CATEGORY_VIDEO: {
                return MediaHelper.VIDEO_MEDIA_URI;
            }
            default:
                return MediaHelper.FILE_URI;
        }
    }

    public String getCountSql(int category) {
        StringBuilder sql = new StringBuilder();
        switch (category) {
            case CATEGORY_IMAGE:
            case CATEGORY_AUDIO:
            case CATEGORY_VIDEO: {
                break;
            }
            case CATEGORY_DOC:
            case CATEGORY_COMPRESS:
            case CATEGORY_APK: {
                return MediaStoreCompat.getMediaCountSqlQuery(category);
            }
            case CATEGORY_QQ:
            case CATEGORY_MICROMSG:
            case CATEGORY_DOWNLOAD:
                break;
            default:
                break;
        }
        return sql.toString();
    }

    public String getCountSqlWithRootPath(int category, boolean isExternalPath) {
        StringBuilder sql = new StringBuilder();
        sql.append(getCountSql(category));
        if (!sql.toString().isEmpty()) {
            sql.append(" AND ");
        }
        if (isExternalPath) {
            sql.append(" _data LIKE '");
            sql.append(mExternalPath);
            sql.append("%'");
        } else {
            sql.append(" _data LIKE '");
            sql.append(mInternalPath);
            sql.append("%'");
        }

        return sql.toString();
    }

    public static SparseArray<String> setIgnoredPath(Context context, int type) {
        if (context == null) {
            return null;
        }
        String dbName = context.getResources().getString(R.string.resourceFile);
        String dbPath = VolumeEnvironment.getDataDirPath(context, DATABASE_DIR, dbName);
        if (new File(dbPath).length() <= 0) {
            Log.e(TAG, "setIgnoredPath original db not exist!");
            return null;
        }
        Cursor cursor = null;
        String ignoredPath = null;
        SparseArray<String> ignoredPaths = new SparseArray<String>();
        SQLiteDatabase originalDB = SQLiteDatabase.openDatabase(dbPath, null, SQLiteDatabase.OPEN_READONLY);
        try {
            String where = "category_type = " + type + " OR category_type = " + 0;
            cursor = originalDB.query(IGNORED_CATEGORY_PATH_TABLE_NAME, new String[]{"ignored_path"}, where, null,
                    null, null, null);
            if ((null != cursor) && cursor.moveToFirst()) {
                int i = 0;
                do {
                    ignoredPath = cursor.getString(0);
                    ignoredPaths.put(i, ignoredPath);
                    i++;
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != originalDB) {
                originalDB.close();
            }
            if (null != cursor) {
                cursor.close();
            }
        }
        return ignoredPaths;
    }

    public static List<String> getIgnoredPath(Context context) {
        if (context == null) {
            return null;
        }
        String dbName = context.getResources().getString(R.string.resourceFile);
        String dbPath = VolumeEnvironment.getDataDirPath(context, DATABASE_DIR, dbName);
        if ((null == dbPath) || (new File(dbPath).length() <= 0)) {
            Log.e(TAG, "original db not exist!");
            return null;
        }
        Cursor cursor = null;
        String ignoredPath = null;
        List<String> ignoredPaths = new ArrayList<>();
        SQLiteDatabase originalDB = SQLiteDatabase.openDatabase(dbPath, null, SQLiteDatabase.OPEN_READONLY);
        try {
            cursor = originalDB.query(IGNORED_CATEGORY_PATH_TABLE_NAME, new String[]{"ignored_path"}, null, null,
                    null, null, null);
            if ((null != cursor) && cursor.moveToFirst()) {
                do {
                    ignoredPath = cursor.getString(0);
                    ignoredPaths.add(ignoredPath);
                } while (cursor.moveToNext());
            }
        } catch (Exception e) {
            Log.e(TAG, e.getMessage());
        } finally {
            if (null != originalDB) {
                originalDB.close();
            }
            if (null != cursor) {
                cursor.close();
            }
        }
        return ignoredPaths;
    }

    public static boolean isIgnoredPathWithType(String path, String internalPath, SparseArray<String> ignoredPaths) {
        if (TextUtils.isEmpty(path)) {
            Log.w(TAG, "path is empty");
            return true;
        }
        // check if the path be under the ignored path
        if (null != ignoredPaths) {
            for (int i = 0; i < ignoredPaths.size(); i++) {
                String string = internalPath + ignoredPaths.get(i);
                if (path.toLowerCase(Locale.getDefault()).startsWith(string.toLowerCase(Locale.getDefault()))) {
                    Log.v(TAG, "isIgnoredPathWithType path = " + path);
                    return true;
                }
            }
        }
        return false;
    }

    public static final String getAudioFilterSelection(Context context, String internalPath, String externalPath) {
        if (null == context) {
            Log.e(TAG, "getAudioFilterCondition input param error !");
            return "";
        }
        int sizeFilter = getVideoFilterSizeValue(context);
        int durationFilter = getVideoFilterDurationValue(context);
        String recordSelection = getAudioRecordSelection(internalPath, externalPath);
        Log.d(TAG, "getAudioFilterSelection sizeFilter = " + sizeFilter + " durationFilter = " + durationFilter);
        String selection = MediaStore.Audio.Media.DURATION + ">=" + durationFilter + " OR " + MediaStore.Audio.Media.SIZE + ">="
                + sizeFilter + " OR " + recordSelection;
        if (SdkUtils.isAtLeastR()) {
            StringBuilder builder = new StringBuilder(selection);
            MediaStoreCompat.INSTANCE.addFilterMultiAppClause(builder);
            return builder.toString();
        } else  {
            return selection;
        }
    }


    public static final String getAudioRecordSelection(String internalPath, String externalPath) {
        StringBuilder selectionBuilder = new StringBuilder();
        String internalRecord = internalPath + File.separator + CategoryHelper.RECORDINGS + File.separator;
        selectionBuilder.append(MediaStore.Audio.Media.DATA).append(" LIKE '%").append(internalRecord).append("%'");
        //add for recording files new path :storage/emulated/0/Music/Recordings
        String newInternalRecord = internalPath + File.separator + CategoryHelper.MUSIC + File.separator
                + CategoryHelper.RECORDINGS + File.separator;
        selectionBuilder.append(" OR " + MediaStore.Audio.Media.DATA + " LIKE '%").append(newInternalRecord).append("%'");
        if (!TextUtils.isEmpty(externalPath)) {
            String externalRecord = externalPath + File.separator + CategoryHelper.RECORDINGS + File.separator;
            selectionBuilder.append(" OR " + MediaStore.Audio.Media.DATA + " LIKE '%").append(externalRecord).append("%'");
            //add for recording files new path :/Music/Recordings
            String newExternalRecord = externalPath + File.separator + CategoryHelper.MUSIC + File.separator
                    + CategoryHelper.RECORDINGS + File.separator;
            selectionBuilder.append(" OR " + MediaStore.Audio.Media.DATA + " LIKE '%").append(newExternalRecord).append("%'");
        }
        return selectionBuilder.toString();
    }

    public static void videoFilterConfigParser(Map<String, Object> needSaveMap, String value) {
        if ((null == needSaveMap) || TextUtils.isEmpty(value)) {
            Log.e(TAG, "videoFilterConfigParser input param error !");
            return;
        }
        String[] filters = value.split(":");
        if ((null != filters) && (filters.length > 1)) {
            Log.d(TAG, "videoFilterConfigParser key = " + filters[0] + " value = " + filters[1]);
            if (TAG_VIDEO_FILTER_SIZE_VALUES.equals(filters[0]) || TAG_VIDEO_FILTER_DURATION_VALUES.equals(filters[0])) {
                needSaveMap.put(filters[0], Integer.parseInt(filters[1]));
            }
        }
    }

    public static int getVideoFilterSizeValue(final Context context) {
        if (context == null) {
            return 0;
        }
        return PreferencesUtils.getInt(WhiteListParser.FILEMANAGER_WHITE_LIST,
                TAG_VIDEO_FILTER_SIZE_VALUES, context.getResources().getInteger(R.integer.files_filter_size_values));
    }

    public static int getVideoFilterDurationValue(final Context context) {
        if (context == null) {
            return 0;
        }
        return PreferencesUtils.getInt(WhiteListParser.FILEMANAGER_WHITE_LIST,
                TAG_VIDEO_FILTER_DURATION_VALUES, context.getResources().getInteger(R.integer.files_filter_duration_values));
    }

    public static Boolean isStorageCategoryType(int type) {
        return (type == CATEGORY_FILE_BROWSER || type == CATEGORY_OTG_BROWSER || type == CATEGORY_SDCARD_BROWSER || type == CATEGORY_DFM);
    }

    public static Boolean isCloudDriveCategoryType(int type) {
        return type == CATEGORY_TENCENT_DOCS || type == CATEGORY_K_DOCS;
    }

    public static Boolean isCloudDiskCategoryType(int type) {
        return type == CATEGORY_CLOUD;
    }

    public static Boolean isDfmType(int type) {
        return type == CATEGORY_DFM;
    }
}
