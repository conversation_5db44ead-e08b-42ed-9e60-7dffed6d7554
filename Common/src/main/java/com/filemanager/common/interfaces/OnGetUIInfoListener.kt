/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - OnGetUIInfoListener.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2021/08/23
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 ****************************************************************/
package com.filemanager.common.interfaces

import androidx.lifecycle.ViewModel
import androidx.recyclerview.widget.RecyclerView

interface OnGetUIInfoListener {

    fun getViewModel(): ViewModel?

    fun getRecyclerView(): RecyclerView?
}