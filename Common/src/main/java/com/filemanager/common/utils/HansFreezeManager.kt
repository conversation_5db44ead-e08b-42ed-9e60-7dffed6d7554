/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : HansFreezeManager
 ** Description :
 ** Version     : 1.0
 ** Date        : 2023/1/4 15:58
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2023/1/4       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import com.filemanager.common.MyApplication
import com.oplus.app.IOplusProtectConnection
import com.oplus.app.OplusHansFreezeManager
import java.util.concurrent.atomic.AtomicInteger

/**
 * 延迟冻结管理类。
 *
 * 申请延迟冻结 https://odocs.myoas.com/docs/2wAlXO1p78c0wpAP
 * 密码： cvdtdu
 */
class HansFreezeManager {

    /**
     * 申请保活计数，每次保活时计数加1，取消保活时减1，在计数变为0时，调用取消保活的接口
     */
    private val counter = AtomicInteger(0)

    private var connection: IOplusProtectConnection? = null

    fun keepBackgroundRunning(context: Context? = null) {
        if (!isSupportHansFreeze()) {
            Log.e(TAG, "keepBackgroundRunning not support!!")
            return
        }
        val count = counter.getAndIncrement()
        Log.d(TAG, "keepBackgroundRunning -> count = $count")
        if (count == 0) {
            internalKeepBackgroundRunning(context)
        }
    }

    fun cancelFrozenDelay() {
        if (!isSupportHansFreeze()) {
            Log.e(TAG, "cancelFrozenDelay not support!!")
            return
        }
        val count = counter.decrementAndGet()
        Log.d(TAG, "cancelFrozenDelay -> count = $count")
        if (count <= 0) {
            internalCancelFrozenDelay()
        }
    }


    /**
     * 临时申请延迟冻结
     */
    fun requestFronzenDelay(
        timeout: Long = DEFAULT_FREEZE_TIME_OUT,
        reason: String = KEEP_REASON_PROVIDER
    ) {
        if (!isSupportHansFreeze()) {
            Log.e(TAG, "requestFronzenDelay not support!!")
            return
        }
        Log.d(TAG, "requestFronzenDelay start...")
        runCatching {
            ensureConnection()
            OplusHansFreezeManager.getInstance()
                .requestFrozenDelay(MyApplication.sAppContext, timeout, reason, connection)
        }.onFailure {
            Log.e(TAG, "requestFronzenDelay -> error: ", it)
        }
    }


    /**
     * 获取延迟冻结的时间
     */
    fun getFrozenDelayTime(context: Context): Long {
        if (!isSupportHansFreeze()) {
            Log.e(TAG, "getFrozenDelayTime not support!!")
            return 0L
        }
        Log.d(TAG, "getFrozenDelayTime")
        val result = runCatching {
            val tmp = OplusHansFreezeManager.getInstance().getFrozenDelayTime(context)
            Log.d(TAG, "getFrozenDelayTime tmp $tmp")
            tmp
        }.onFailure {
            Log.e(TAG, "getFrozenDelayTime -> error: ", it)
        }.getOrNull() ?: 0L
        return result
    }

    private fun isSupportHansFreeze(): Boolean {
        return SdkUtils.isAtLeastOS13Point1().also {
            if (!it) {
                Log.e(TAG, "isSupportHansFreeze os < 13.1")
            }
        }
    }


    /**
     * Keep app alive from freeze until cancel the request or exit.
     */
    private fun internalKeepBackgroundRunning(inputContext: Context?) {
        Log.d(TAG, "internalKeepBackgroundRunning inputContext $inputContext")
        runCatching {
            ensureConnection()
            var context = inputContext
            if (context == null) {
                context = MyApplication.appContext
            }
            OplusHansFreezeManager.getInstance()
                .keepBackgroundRunning(context, KEEP_REASON, true, connection)
        }.onFailure {
            Log.e(TAG, "internalKeepBackgroundRunning -> error: ", it)
        }
    }

    /**
     * Cancel keep alive request.
     */
    private fun internalCancelFrozenDelay() {
        Log.d(TAG, "internalCancelFrozenDelay")
        runCatching {
            OplusHansFreezeManager.getInstance().cancelFrozenDelay(MyApplication.sAppContext)
        }.onFailure {
            Log.e(TAG, "internalCancelFrozenDelay -> error:  ", it)
        }
    }

    private class ProtectConnect : IOplusProtectConnection.Stub() {
        override fun onSuccess() {
            Log.d(TAG, "Request delay freeze success.")
        }

        override fun onError(code: Int) {
            Log.w(TAG, "Request delay freeze error: $code")
        }

        override fun onTimeout() {
            Log.w(TAG, "Request delay freeze timeout")
        }
    }

    private fun ensureConnection() {
        if (connection == null) {
            connection = ProtectConnect()
        }
    }

    companion object {
        private const val TAG = "HansFreezeManager"
        private const val KEEP_REASON = "FileManager Service"
        private const val KEEP_REASON_PROVIDER = "FileManager AppSwitchProvider"
        //延时冻结时间7s，这里以ms为单位
        private const val DEFAULT_FREEZE_TIME_OUT = 10000L

        val instance: HansFreezeManager by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            HansFreezeManager()
        }
    }
}