/***********************************************************
 * * Copyright (C), 2010 - 2020 Oplus. All rights reserved..
 * * File: PrivateSafeUtilTest
 * * Description: 私密保险箱工具类
 * * Version: 1.0
 * * Date : 2022/1/4
 * * Author:<EMAIL>
 * *
 * * ---------------------Revision History: ---------------------
 * *     <author>              <data>      <version >        <desc>
 * * <EMAIL>    2022/1/4     1.0         私密保险箱工具类
 ****************************************************************/
package com.filemanager.common.utils

import android.content.Context
import com.filemanager.common.MyApplication
import java.lang.IllegalArgumentException
import java.lang.reflect.InvocationTargetException

object PrivateSafeUtil {
    private const val TAG = "PrivateSafeUtil2"

    /**
     * 检测是否禁用私密保险箱
     * 由系统的值确认，打开应用获取一次即可，默认为不禁用
     * @return true 私密保险箱不可用
     */
    @JvmStatic
    fun checkIsDisabledPrivateGarden(): Boolean {
        try {
            val clazz = Class.forName("android.os.customize.OplusCustomizeRestrictionManager")
            val instanceMethod = clazz.getMethod("getInstance", Context::class.java)
            val `object` = instanceMethod.invoke(null, MyApplication.sAppContext)
            val getMethods = clazz.getMethod("isPrivateSafeDisabled")
            return getMethods.invoke(`object`) as Boolean
        } catch (e: ClassNotFoundException) {
            Log.e(TAG, "checkIsDisabledPrivateGarden $e")
        } catch (e: NoSuchMethodException) {
            Log.e(TAG, "checkIsDisabledPrivateGarden $e")
        } catch (e: SecurityException) {
            Log.e(TAG, "checkIsDisabledPrivateGarden $e")
        } catch (e: IllegalAccessException) {
            Log.e(TAG, "checkIsDisabledPrivateGarden $e")
        } catch (e: IllegalArgumentException) {
            Log.e(TAG, "checkIsDisabledPrivateGarden $e")
        } catch (e: InvocationTargetException) {
            Log.e(TAG, "checkIsDisabledPrivateGarden $e")
        }
        return false
    }
}