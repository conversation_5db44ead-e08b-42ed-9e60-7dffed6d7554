/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : Q3OptimizeStatics
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/9/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/9/28       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.app.Activity
import android.os.Environment
import android.provider.MediaStore
import android.view.DragEvent
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.DFMMediaFile
import com.filemanager.common.base.DriveFileWrapper
import com.filemanager.common.base.ThirdAppFileWrapper
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.MediaFileCompat
import com.filemanager.common.constants.MessageConstant
import com.filemanager.common.constants.RemoteDeviceConstants
import com.filemanager.common.dragselection.DefaultDropListener
import com.filemanager.common.dragselection.DragDropAction
import com.filemanager.common.dragselection.util.DropUtil
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.MimeTypeHelper.Companion.getTypeFromPath
import com.filemanager.common.sort.SortHelper
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.io.FilenameUtils
import java.io.File
import kotlin.math.abs

object OptimizeStatisticsUtil {

    private const val TAG = "OptimizeStatisticsUtil"
    const val IMAGE = "image"
    const val IMAGE_SET = "image_set"
    const val VIDEO = "video"
    const val AUDIO = "audio"
    const val DOCUMENT = "document"
    const val APK = "apk"
    const val COMPRESS = "compress"
    private const val TOTAL = "total"
    const val ALL_STORAGE = "phone_storage"
    const val WECHAT = "WeChat"
    const val QQ = "QQ"
    const val DFM = "dfm"

    @VisibleForTesting
    const val DOWNLOADS = "downloads"

    @VisibleForTesting
    const val BLUETOOTH = "bluetooth"

    @VisibleForTesting
    const val OWORK = "owork"

    @VisibleForTesting
    const val PC_CONNECT = "pcconnect"

    @VisibleForTesting
    const val OSHARE = "oshare"

    @VisibleForTesting
    const val TAB_ALL = Integer.MIN_VALUE

    @VisibleForTesting
    const val CATEGORY_IMAGE_DISPLAY = 99

    @VisibleForTesting
    const val CATEGORY_FILE_SOURCE = 999

    @VisibleForTesting
    const val FILE_BROWSER_ACTIVITY = "com.oplus.filebrowser.FileBrowserActivity"

    @VisibleForTesting
    const val OTG_ACTIVITY = "com.oplus.filebrowser.otg.OtgFileBrowserActivity"

    @VisibleForTesting
    const val ALBUM_ACTIVITY = "com.oplus.filemanager.category.album.ui.AlbumActivity"

    @VisibleForTesting
    const val VIDEO_AUDIO_ACTIVITY =
        "com.oplus.filemanager.category.audiovideo.ui.CategoryAudioActivity"

    @VisibleForTesting
    const val DOC_ACTIVITY = "com.oplus.filemanager.category.document.ui.DocumentActivity"

    @VisibleForTesting
    const val APK_ACTIVITY = "com.oplus.filemanager.category.apk.ui.ApkActivity"

    @VisibleForTesting
    const val COMPRESS_ACTIVITY = "com.filemanager.categorycompress.ui.CategoryCompressActivity"

    @VisibleForTesting
    const val SUPER_ACTIVITY = "com.filemanager.superapp.ui.superapp.SuperAppActivity"

    @VisibleForTesting
    const val DFM_ACTIVITY = "com.oplus.filemanager.categorydfm.ui.CategoryDfmActivity"

    /**
     * 首页-文件
     */
    const val HOME_PAGE_FILE = "home_page_file"

    /**
     * 首页-最近
     */
    const val RECENT = "recent"

    /**
     * 首页-标签
     */
    const val LABEL = "label"
    const val LABEL_FILE = "label_file"
    const val SHORTCUT_FOLDER = "shortcut_folder"

    const val OP_COPY = "copy"
    const val OP_CUT = "cut"
    const val OP_DELETE = "delete"
    const val OP_SEND = "send"
    const val OP_UPLOAD_CLOUD = "upload_cloud"
    const val OP_RENAME = "rename"
    const val OP_ENCRYPT = "encrypt"
    const val OP_OPEN_WITH = "open_with"
    const val OP_LABEL = "label"
    const val OP_DETAIL = "detail"
    const val OP_COMPRESS = "compress"
    const val OP_DECOMPRESS = "decompress"
    const val OP_DOWNLOAD_DRIVE = "download_drive"


    const val SEARCH_CONDITION_TIME = 0
    const val SEARCH_CONDITION_SOURCE = 1
    const val SEARCH_CONDITION_FORMAT = 2
    const val SEARCH_CONDITION_MIX = 3

    const val CREATE_FOLDER_FROM_PHONE_STORAGE = 0
    const val CREATE_FOLDER_FROM_SD_CARD = 1
    const val CREATE_FOLDER_FROM_OTG = 2
    const val CREATE_FOLDER_FROM_COPY = 3
    const val CREATE_FOLDER_FROM_CUT = 4
    const val CREATE_FOLDER_FROM_SHARE = 5
    const val CREATE_FOLDER_FROM_SAVE = 6

    const val PAGE_PHONE_STORAGE = "phone_storage"
    const val PAGE_SD_CARD = "sdcard"
    const val PAGE_OTG = "otg"

    const val SOURCE_LOCAL = "sdcard"
    const val SOURCE_DFM = "dfm"
    const val SOURCE_TENCENT_DRIVE = "drive_tencent_docs"
    const val SOURCE_KDOC_DRIVE = "drive_kdocs"
    const val SOURCE_THIRD_APP = "third_app"

    const val SELECT_MODE_SINGLE = 0
    const val SELECT_MODE_MULTI = 1

    const val OPERATE_FROM_SEARCH = 1
    const val OPERATE_NOT_SEARCH = 0


    private val COMMON_DIRS by lazy {
        arrayOf(
            Environment.getExternalStorageDirectory().absolutePath + File.separator + Environment.DIRECTORY_DOWNLOADS,
            Environment.getExternalStorageDirectory().absolutePath + File.separator + Environment.DIRECTORY_DOCUMENTS
        )
    }

    data class OperationInfo(
        val fileCount: String,
        var opType: String = "",
        val opTime: String,
        var destPath: String = "",
        val files: List<BaseFileBean>
    )

    data class CategoryInfo(
        var pic: Int = 0,
        var video: Int = 0,
        var audio: Int = 0,
        var doc: Int = 0,
        var apk: Int = 0,
        var compress: Int = 0,
        var folder: Int = 0
    )

    @JvmStatic
    private fun isRootDir(path: String, onlyPhoneStorage: Boolean): Boolean {
        val rootDir = if (onlyPhoneStorage) {
            Environment.getExternalStorageDirectory().absolutePath + File.separator
        } else {
            FolderPackageUtil.getRootDir(path) + File.separator
        }
        val splits = path.split(rootDir)
        val contains = if (splits.size > 1) {
            splits[1].contains(File.separator)
        } else {
            true
        }
        return !contains
    }

    @JvmStatic
    private fun isCommonDir(path: String): Boolean {
        for (dir in COMMON_DIRS) {
            if (path.startsWith(dir)) {
                return true
            }
        }
        return false
    }

    /**
     * 在设备存储根目录点击文件夹/文件
     */
    @JvmStatic
    fun clickStorageFolderFile(path: String, type: String, ext: String = "") {
        if (isRootDir(path, true).not()) return
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_CLICK_STORAGE_FOLDER_FILE, mapOf(
                StatisticsUtils.CLICK_STORAGE_FOLDER_FILE to type,
                StatisticsUtils.FILE_EXTENSION to ext
            )
        )
        Log.d(TAG, "clickStorageFolderFile type:$type ext:$ext")
    }

    /**
     * 新建文件夹(设备存储页、sd卡、OTG、移动/复制的选择文件夹页面，分享页面，保存页面)
     */
    @JvmStatic
    fun createFolder(from: Int = -1, actionCode: Int = -1) {
        val createFrom = if (from == -1) {
            when (actionCode) {
                MessageConstant.MSG_EDITOR_COPY -> CREATE_FOLDER_FROM_COPY
                MessageConstant.MSG_EDITOR_CUT -> CREATE_FOLDER_FROM_CUT
                MessageConstant.MSG_SAVE -> CREATE_FOLDER_FROM_SHARE
                MessageConstant.MSG_SAVE_AND_RENAME -> CREATE_FOLDER_FROM_SAVE
                else -> -1
            }
        } else {
            from
        }
        if (createFrom == -1) return
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_CREATE_FOLDER,
            mapOf(StatisticsUtils.CREATE_FROM to createFrom.toString())
        )
    }

    /**
     * 点击带logo的应用文件夹
     */
    @JvmStatic
    fun clickAppFileLogo(path: String, file: BaseFileBean) {
        if ((file.originPackage.isNullOrEmpty().not()) || isCommonDir(path)) {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_CLICK_APP_FILE,
                mapOf(StatisticsUtils.FOLDER_NAME to file.mDisplayName)
            )
            Log.d(
                TAG,
                "clickAppFileLogo originPackage:${file.originPackage} foldName:${file.mDisplayName}"
            )
        }
    }

    /**
     * 在图片分类页面点击打开
     */
    @JvmStatic
    fun clickPicFolder() {
        StatisticsUtils.onCommon(appContext, StatisticsUtils.EVENT_CLICK_PIC_FOLDER_FILE)
    }

    /**
     * 在各个页面点击文件（图片、音视频、文档、安装包、压缩包、文件来源、最近、标签等）
     */
    @JvmStatic
    fun clickEachCategoryFile(file: BaseFileBean, page: String) {
        if (page.isEmpty() || ALL_STORAGE == page) {
            Log.d(TAG, "clickEachCategoryFile return page:$page")
            return
        }
        val ext = FilenameUtils.getExtension(file.mData)
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_CLICK_EACH_CATEGORY_FILE, mapOf(
                StatisticsUtils.CURRENT_PAGE to page, StatisticsUtils.FILE_EXTENSION to ext
            )
        )
        Log.d(TAG, "clickEachCategoryFile page:$page ext:$ext")
    }

    /**
     * 在最近页面点击文件
     */
    @JvmStatic
    fun clickRecentFile(file: BaseFileBean) {
        val ext = FilenameUtils.getExtension(file.mData)
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.RECENT_FILE_OPEN,
            mapOf(StatisticsUtils.FILE_EXTENSION to ext)
        )
        Log.d(TAG, "clickRecentFile ext:$ext file:$file")
    }

    /**
     * 上报标签、文档、图片等文件数量
     * 定时上报，每 24 小时一次
     */
    @Suppress("TooGenericExceptionCaught")
    @JvmStatic
    fun categoryFileCount(category: Int, fileCount: String) {
        var eventId: String? = null
        when (category) {
            CategoryHelper.CATEGORY_LABEL_FILE -> eventId = StatisticsUtils.EVENT_FILE_LABEL_COUNT
            CategoryHelper.CATEGORY_DOC -> eventId = StatisticsUtils.EVENT_DOC_FILE_COUNT
            CategoryHelper.CATEGORY_IMAGE -> eventId = StatisticsUtils.EVENT_PIC_FILE_COUNT
            CategoryHelper.CATEGORY_VIDEO -> eventId = StatisticsUtils.EVENT_VIDEO_FILE_COUNT
            CategoryHelper.CATEGORY_AUDIO -> eventId = StatisticsUtils.EVENT_AUDIO_FILE_COUNT
            CategoryHelper.CATEGORY_APK -> eventId = StatisticsUtils.EVENT_APK_FILE_COUNT
            CategoryHelper.CATEGORY_COMPRESS -> eventId = StatisticsUtils.EVENT_COMPRESS_FILE_COUNT
            else -> {}
        }
        if (eventId == null) {
            return
        }
        if (checkMoreThanPeriodTime(eventId, StatisticsUtils.ONE_DAY_MILLIS)) {
            StatisticsUtils.onCommon(
                appContext, eventId, mapOf(eventId to fileCount)
            )
            Log.d(TAG, "categoryFileCount eventId:$eventId fileCount:$fileCount")
        }
    }

    @VisibleForTesting
    @JvmStatic
    fun checkMoreThanPeriodTime(key: String, periodTime: Long): Boolean {
        val lastTime = PreferencesUtils.getLong(key = key)
        val currentTimeMillis = TimeHelper.getNow()
        if (abs(currentTimeMillis - lastTime) > periodTime) {
            PreferencesUtils.put(key = key, value = currentTimeMillis)
            return true
        }
        return false
    }

    /**
     * 成功搜索返回结果
     */
    @JvmStatic
    fun searchSuccess(startTime: Long, endTime: Long, elapsedTime: Long) {
        val context = appContext
        val start = Utils.getDateAndTimeFormat(context, startTime)
        val end = Utils.getDateAndTimeFormat(context, endTime)
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_ALL_SUCCESS_SEARCH_RETURN, mapOf(
                StatisticsUtils.SEARCH_START_TIME to start,
                StatisticsUtils.SEARCH_END_TIME to end,
                StatisticsUtils.SEARCH_ELAPSED_TIME to elapsedTime.toString()
            )
        )
        Log.d(TAG, "searchSuccess start:$start end:$end elapsedTime:$elapsedTime")
    }

    /**
     * 搜索结果点击分类格式， 复用之前的埋点，在之前的埋点上加入新增字段，复用之前的埋点
     * SEARCH_RESULT_KEY_SOURCE         来源
     * SEARCH_RESULT_KEY_FILENAME       名称
     * SEARCH_RESULT_KEY_PACKAGENAME    三方应用包名
     */
    @JvmStatic
    fun clickSearchResultFileType(file: BaseFileBean, tabCategory: Int) {
        val curTab = when (tabCategory) {
            TAB_ALL -> TOTAL
            CategoryHelper.CATEGORY_IMAGE -> IMAGE
            CategoryHelper.CATEGORY_VIDEO -> VIDEO
            CategoryHelper.CATEGORY_AUDIO -> AUDIO
            CategoryHelper.CATEGORY_DOC -> DOCUMENT
            CategoryHelper.CATEGORY_APK -> APK
            CategoryHelper.CATEGORY_COMPRESS -> COMPRESS
            else -> TOTAL
        }
        val ext = FilenameUtils.getExtension(file.mData)
        val clickInfo = getSearchItemClickInfo(file)
        val packageName = clickInfo.packageName
        val map = mutableMapOf(
            StatisticsUtils.CURRENT_TAB to curTab,
            StatisticsUtils.FILE_EXTENSION to ext,
            StatisticsUtils.SEARCH_RESULT_KEY_SOURCE to clickInfo.source,
            StatisticsUtils.SEARCH_RESULT_KEY_FILENAME to clickInfo.fileName
        )
        if (packageName != null) {
            map.putIfAbsent(StatisticsUtils.SEARCH_RESULT_KEY_PACKAGENAME, packageName)
        }
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_CLICK_RESULT_FILE_TYPE, map
        )
        Log.d(TAG, "clickSearchResultFileType curTab:$curTab ext:$ext")
    }

    @JvmStatic
    fun signalToPage(signal: String): String {
        val context = appContext
        val page = when (signal) {
            context.getString(R.string.string_videos) -> VIDEO
            context.getString(R.string.string_audio) -> AUDIO
            context.getString(R.string.string_wechat), "com.tencent.mm" -> WECHAT
            context.getString(R.string.string_qq), "com.tencent.mobileqq" -> QQ
            context.getString(R.string.download) -> DOWNLOADS
            context.getString(R.string.bluetooth) -> BLUETOOTH
            context.getString(R.string.owork_space),
            KtUtils.getOWorkName(KtUtils.OWORK_NAME_TYPE_1),
            KtUtils.getOWorkName(KtUtils.OWORK_NAME_TYPE_2),
            KtUtils.getOWorkName(KtUtils.OWORK_NAME_TYPE_3) -> OWORK
            context.getString(R.string.hey_pc_name) -> PC_CONNECT
            context.getString(R.string.oneplus_share), context.getString(R.string.realme_share), context.getString(
                R.string.oppo_share
            ) -> OSHARE

            else -> signal
        }
        Log.d(TAG, "signalToPage signal:$signal page:$page")
        return page
    }

    @JvmStatic
    fun getOptionPage(activity: Activity, source: String): String {
        val page = if (FeatureCompat.isSmallScreenPhone) {
            when (activity.componentName.className) {
                FILE_BROWSER_ACTIVITY, OTG_ACTIVITY -> ALL_STORAGE
                ALBUM_ACTIVITY -> IMAGE
                VIDEO_AUDIO_ACTIVITY -> source
                DOC_ACTIVITY -> DOCUMENT
                APK_ACTIVITY -> APK
                COMPRESS_ACTIVITY -> COMPRESS
                SUPER_ACTIVITY -> source
                DFM_ACTIVITY -> DFM
                else -> ""
            }
        } else if (activity.componentName.className == DefaultDropListener.MAIN_ACTIVITY
            || activity.componentName.className == DefaultDropListener.MAIN_EXPORT_ACTIVITY
        ) {
            when (DragDropAction.getMainCategoryType(activity)) {
                CategoryHelper.CATEGORY_FILE_BROWSER, CategoryHelper.CATEGORY_OTG_BROWSER -> ALL_STORAGE
                CATEGORY_IMAGE_DISPLAY -> IMAGE
                CategoryHelper.CATEGORY_VIDEO -> VIDEO
                CategoryHelper.CATEGORY_AUDIO -> AUDIO
                CategoryHelper.CATEGORY_DOC -> DOCUMENT
                CategoryHelper.CATEGORY_APK -> APK
                CategoryHelper.CATEGORY_COMPRESS -> COMPRESS
                CATEGORY_FILE_SOURCE -> source
                CategoryHelper.CATEGORY_DFM -> DFM
                else -> ""
            }
        } else {
            ""
        }
        Log.d(TAG, "getOptionPage page:$page source:$source")
        return page
    }

    @JvmStatic
    private fun operateMenuStatics(selecteFileBeans: List<BaseFileBean>, fromSearch: Boolean, opType: String) {
        val size = selecteFileBeans.size
        val isSingle = size == 1
        for (bean in selecteFileBeans) {
            val itemOperationInfo = getItemOperationInfo(bean, fromSearch, opType, isSingle)
            val eventId = getMenuEventIdByOpType(opType)
            menueItemOperate(itemOperationInfo, eventId)
        }
    }


    @JvmStatic
    fun onSend(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_SEND)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_SEND,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
            Log.d(TAG, "onSend page:$page")
        }
    }

    @JvmStatic
    fun onLabel(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_LABEL)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_LABEL,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
            Log.d(TAG, "onLabel page:$page")
        }
    }

    @JvmStatic
    fun onCut(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_CUT)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_CUT,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
            Log.d(TAG, "onCut page:$page")
        }
    }


    @JvmStatic
    fun onDelete(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_DELETE)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_DELETE,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    fun onCopy(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_COPY)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_COPY,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    fun onDetail(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_DETAIL)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_DETAIL,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    fun onUploadCloud(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_UPLOAD_CLOUD)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_UPLOAD_CLOUD,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    fun onRename(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_RENAME)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_RENAME,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    fun onEncrypt(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_ENCRYPT)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_ENCRYPT,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    fun onOpenWith(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_OPEN_WITH)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_OPEN_WITH,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    fun onCompress(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_COMPRESS)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_COMPRESS,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }


    @JvmStatic
    fun onDecompress(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_DECOMPRESS)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_DECOMPRESS,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }


    @JvmStatic
    fun onDownloadDrive(page: String, selecteFileBeans: List<BaseFileBean>? = null, fromSearch: Boolean = false) {
        if (!selecteFileBeans.isNullOrEmpty()) {
            operateMenuStatics(selecteFileBeans, fromSearch, OP_DOWNLOAD_DRIVE)
        } else {
            StatisticsUtils.onCommon(
                appContext,
                StatisticsUtils.EVENT_MENU_DOWNLOAD_FROM_DRIVE,
                mapOf(StatisticsUtils.OPERATION_PAGE to page)
            )
        }
    }

    @JvmStatic
    private fun getMenuEventIdByOpType(opType: String): String {
        val result = when (opType) {
            OP_COPY -> StatisticsUtils.EVENT_MENU_COPY
            OP_CUT -> StatisticsUtils.EVENT_MENU_CUT
            OP_DELETE -> StatisticsUtils.EVENT_MENU_DELETE
            OP_SEND -> StatisticsUtils.EVENT_MENU_SEND
            OP_UPLOAD_CLOUD -> StatisticsUtils.EVENT_UPLOAD_TO_CLOUD
            OP_DETAIL -> StatisticsUtils.EVENT_MENU_DETAIL
            OP_RENAME -> StatisticsUtils.EVENT_MENU_RENAME
            OP_ENCRYPT -> StatisticsUtils.EVENT_MENU_ENCRYPT
            OP_OPEN_WITH -> StatisticsUtils.EVENT_MENU_OPEN_WITH
            OP_LABEL -> StatisticsUtils.EVENT_MENU_LABEL
            OP_COMPRESS -> StatisticsUtils.EVENT_MENU_COMPRESS
            OP_DECOMPRESS -> StatisticsUtils.EVENT_MENU_DECOMPRESS
            OP_DOWNLOAD_DRIVE -> StatisticsUtils.EVENT_MENU_DOWNLOAD_FROM_DRIVE
            else -> ""
        }
        return result
    }


    /**
     * 1.管理文件行为的埋点（复制、移动、删除、重命名、上传云盘、发送文件、设为私密、其他方式打开的操作记录）
     * 2.最近删除页面彻底删除文件
     */
    @JvmStatic
    fun allOperation(operationInfo: OperationInfo, isDeleteTotally: Boolean = false, isDeleteAll: Boolean = false) {
        var ext = ""
        val categoryInfo = CategoryInfo()
        operationInfo.files.forEach {
            if (it.mLocalType == MimeTypeHelper.DIRECTORY_TYPE) {
                categoryInfo.folder++
            } else {
                when (MimeTypeHelper.getMediaTypeByType(it.mLocalType)) {
                    MediaStore.Files.FileColumns.MEDIA_TYPE_IMAGE -> categoryInfo.pic++
                    MediaStore.Files.FileColumns.MEDIA_TYPE_VIDEO -> categoryInfo.video++
                    MediaStore.Files.FileColumns.MEDIA_TYPE_AUDIO -> categoryInfo.audio++
                    MediaFileCompat.MEDIA_TYPE_DOC -> categoryInfo.doc++
                    MediaFileCompat.MEDIA_TYPE_APK -> categoryInfo.apk++
                    MediaFileCompat.MEDIA_TYPE_COMPRESS -> categoryInfo.compress++
                }
            }
        }
        if (operationInfo.files.size == 1) {
            val fileName = operationInfo.files[0].mDisplayName ?: ""
            ext = FilenameUtils.getExtension(fileName)
        }
        if (isDeleteTotally) {
            clickDeleteTotally(
                isDeleteAll, operationInfo.fileCount, operationInfo.opTime, ext, categoryInfo
            )
        } else {
            clickAllOperation(operationInfo, categoryInfo, ext)
        }
    }

    /**
     * 管理文件行为的埋点（复制、移动、删除、重命名、上传云盘、发送文件、设为私密、其他方式打开的操作记录）
     */
    @JvmStatic
    private fun clickAllOperation(operationInfo: OperationInfo, categoryInfo: CategoryInfo, ext: String) {
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_ALL_OPERATION, mapOf(
                StatisticsUtils.NUM to operationInfo.fileCount,
                StatisticsUtils.OP_TYPE to operationInfo.opType,
                StatisticsUtils.OP_TIME to operationInfo.opTime,
                StatisticsUtils.DEST_PATH to operationInfo.destPath,
                StatisticsUtils.PIC_NUM to categoryInfo.pic.toString(),
                StatisticsUtils.VIDEO_NUM to categoryInfo.video.toString(),
                StatisticsUtils.AUDIO_NUM to categoryInfo.audio.toString(),
                StatisticsUtils.DOC_NUM to categoryInfo.doc.toString(),
                StatisticsUtils.APK_NUM to categoryInfo.apk.toString(),
                StatisticsUtils.COMPRESS_NUM to categoryInfo.compress.toString(),
                StatisticsUtils.FOLDER_NUM to categoryInfo.folder.toString(),
                StatisticsUtils.FILE_EXTENSION to ext
            )
        )
        Log.d(TAG, "allOperation categoryCount:$categoryInfo ext:$ext")
    }

    /**
     * 最近删除页面彻底删除文件
     */
    @JvmStatic
    private fun clickDeleteTotally(
        isDeleteAll: Boolean,
        fileCount: String,
        opTime: String,
        ext: String,
        categoryInfo: CategoryInfo
    ) {
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_ALL_DELETE_TOTALLY, mapOf(
                StatisticsUtils.IS_DELETE_ALL to isDeleteAll.toString(),
                StatisticsUtils.NUM to fileCount,
                StatisticsUtils.OP_TIME to opTime,
                StatisticsUtils.PIC_NUM to categoryInfo.pic.toString(),
                StatisticsUtils.VIDEO_NUM to categoryInfo.video.toString(),
                StatisticsUtils.AUDIO_NUM to categoryInfo.audio.toString(),
                StatisticsUtils.DOC_NUM to categoryInfo.doc.toString(),
                StatisticsUtils.APK_NUM to categoryInfo.apk.toString(),
                StatisticsUtils.COMPRESS_NUM to categoryInfo.compress.toString(),
                StatisticsUtils.FOLDER_NUM to categoryInfo.folder.toString(),
                StatisticsUtils.FILE_EXTENSION to ext
            )
        )
        Log.d(TAG, "deleteTotally categoryCount:$categoryInfo ext:$ext")
    }

    /**
     * 点击各页面内的搜索按钮，不包括首页(文件、最近、标签)
     */
    @JvmStatic
    fun pageSearch(page: String) {
        if (page.isEmpty()) return
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_PAGE_SEARCH,
            mapOf(StatisticsUtils.OPERATION_PAGE to page)
        )
    }

    @JvmStatic
    fun pageSort(page: String) {
        if (page.isEmpty()) return
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_PAGE_SORT,
            mapOf(StatisticsUtils.OPERATION_PAGE to page)
        )
    }

    @JvmStatic
    fun recentSort(sortMode: Int) {
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.RECENT_TAB_SORT_CLICK,
            getSortMap(sortMode)
        )
    }

    @JvmStatic
    fun albumSort(sortMode: Int) {
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.ALBUM_SORT_CLICK,
            getSortMap(sortMode)
        )
    }

    @JvmStatic
    private fun getSortMap(sortMode: Int): Map<String, String> {
        val map = HashMap<String, String>()
        when (sortMode) {
            SortHelper.FILE_TIME_REVERSE_ORDER -> map[StatisticsUtils.EVENT_CLICK_SORT] = StatisticsUtils.SORT_BY_TIME

            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> map[StatisticsUtils.EVENT_CLICK_SORT] = StatisticsUtils.SORT_BY_SIZE

            SortHelper.FILE_TYPE_ORDER -> map[StatisticsUtils.EVENT_CLICK_SORT] = StatisticsUtils.SORT_BY_TYPE

            SortHelper.FILE_NAME_ORDER -> map[StatisticsUtils.EVENT_CLICK_SORT] = StatisticsUtils.SORT_BY_NAME

            SortHelper.FILE_DATE_TAKEN_ORDER -> map[StatisticsUtils.EVENT_CLICK_SORT] = StatisticsUtils.SORT_BY_TAKEN_TIME
            else -> {}
        }
        return map
    }

    @JvmStatic
    fun pageEdit(page: String) {
        if (page.isEmpty()) return
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_PAGE_EDIT,
            mapOf(StatisticsUtils.OPERATION_PAGE to page)
        )
    }

    @JvmStatic
    fun pageSetting(page: String) {
        if (page.isEmpty()) return
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_PAGE_SETTING,
            mapOf(StatisticsUtils.OPERATION_PAGE to page)
        )
        Log.d(TAG, "pageSetting page:$page")
    }

    /**
     * 在有tab的页面，点击二级tab/左右滑动
     */
    @JvmStatic
    fun pageTab(page: String, tab: String) {
        if (page.isEmpty()) return
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_PAGE_TAB, mapOf(
                StatisticsUtils.OPERATION_PAGE to page, StatisticsUtils.OPERATION_TAB to tab
            )
        )
    }

    /**
     * 在文件来源-微信/qq点击全部系统文件
     */
    @JvmStatic
    fun clickAllSystemFile(page: String) {
        if (page.isEmpty()) return
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_CLICK_ALL_SYSTEM_FILE,
            mapOf(StatisticsUtils.OPERATION_PAGE to page)
        )
    }

    /**
     * 压缩文件解压成功
     */
    @JvmStatic
    fun deCompressFile(path: String) {
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_DECOMPRESS_FILE,
            mapOf(StatisticsUtils.FILE_EXTENSION to FilenameUtils.getExtension(path))
        )
    }

    /**
     * 默认应用直接打开音频文件跳转其他应用
     */
    @JvmStatic
    fun openAudioFile(path: String, pkg: String) {
        val appName = when (pkg) {
            "com.tencent.blackkey" -> "MOO音乐"
            "com.tencent.qqmusic" -> "QQ音乐"
            "cmccwm.mobilemusic" -> "咪咕音乐"
            "cn.wenyu.bodian" -> "波点音乐"
            "com.netease.cloudmus" -> "网易云音乐"
            "cn.kuwo.player" -> "酷我音乐"
            "com.kugou.android.el" -> "酷狗大字版"
            "com.kugou.android" -> "酷狗音乐"
            "com.heytap.music" -> "音乐1"
            "com.oppo.music" -> "音乐2"
            "com.kwai.hisense" -> "回森"
            else -> "其他"
        }
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.EVENT_OPEN_AUDIO_FILE, mapOf(
                StatisticsUtils.APP_PKG to pkg,
                StatisticsUtils.APP_NAME to appName,
                StatisticsUtils.FILE_EXTENSION to FilenameUtils.getExtension(path)
            )
        )
    }

    /**
     * 搜索筛选条件分类(多个筛选条件、按时间筛选、按来源筛选、按格式筛选)
     */
    @JvmStatic
    fun searchConditionType(type: Int) {
        if (type == -1) return
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.EVENT_SEARCH_CONDITION_TYPE,
            mapOf(StatisticsUtils.EVENT_SEARCH_CONDITION_TYPE to type.toString())
        )
    }

    /**
     * 打开未知文件
     */
    @JvmStatic
    fun openUnknownFile(path: String) {
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.OPEN_UNKNOWN_FILE,
            mapOf(StatisticsUtils.FILE_EXTENSION to FilenameUtils.getExtension(path))
        )
    }

    @JvmStatic
    fun clickAllFile(page: String) {
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.ALL_FILES, mapOf(StatisticsUtils.OPERATION_PAGE to page)
        )
    }

    data class ItemClickInfo(
        val source: String,
        val fileName: String,
        val fileExt: String,
        val packageName: String? = null
    )

    data class ItemOperationInfo(
        val opearteType: String,
        val singleSelete: Boolean,
        val itemClickInfo: ItemClickInfo,
        val isFromSearch: Boolean
    )

    data class ThirdAppDetectInfo(
        var configVerson: String,
        var packageName: String,
        var packageVersion: String,
        var previewActivity: String,
        var exactActivity: String,
        var errorCode: Int,
        var viewExactResult: String,
        var dateBean: String,
        var result: Int,
        var dmpVersion: String = ""
    )

    data class AdSwitchStatus(
        var adSwitchStatus: String
    )


    @JvmStatic
    fun getItemOperationInfo(fileBean: BaseFileBean, isFromSearch: Boolean, opType: String, singleSelete: Boolean): ItemOperationInfo {
        val itemClickInfo = getSearchItemClickInfo(fileBean)
        val result = ItemOperationInfo(opType, singleSelete, itemClickInfo, isFromSearch)
        return result
    }


    @JvmStatic
    fun getSearchItemClickInfo(fileBean: BaseFileBean): ItemClickInfo {
        val fileName = fileBean.mDisplayName ?: ""
        var source = SOURCE_LOCAL
        var packageName: String? = null
        when (fileBean) {
            is DFMMediaFile -> source = SOURCE_DFM
            is DriveFileWrapper -> {
                if (fileBean.isKDocs()) {
                    source = SOURCE_KDOC_DRIVE
                } else {
                    source = SOURCE_TENCENT_DRIVE
                }
            }

            is ThirdAppFileWrapper -> {
                source = SOURCE_THIRD_APP
                packageName = fileBean.sourcePackageName
            }
        }
        val fileType = FilenameUtils.getExtension(fileName)
        val result = ItemClickInfo(source, fileName, fileType, packageName)
        Log.i(TAG, "getSearchItemClickInfo result $result")
        return result
    }


    /**
     * 上传搜索关键词埋点
     */
    @JvmStatic
    fun searchWord(keyWord: String) {
        StatisticsUtils.onCommon(
            appContext,
            StatisticsUtils.SEARCH_KEYWORD,
            mapOf(StatisticsUtils.SEARCH_KEYWORD to keyWord)
        )
        Log.i(TAG, "searchWord $keyWord")
    }

    /**
     * 广告埋点
     */
    @JvmStatic
    fun adSwitch(isChecked: Boolean) {
        val switchStatus = if (isChecked) {
            StatisticsUtils.OPEN_VALUE
        } else {
            StatisticsUtils.CLOSE_VALUE
        }
        val mutableMap = mutableMapOf(StatisticsUtils.AD_SWITCH_STATUS to switchStatus)
        StatisticsUtils.onCommon(appContext, StatisticsUtils.AD_SWITCH_ID, mutableMap)
    }

    /**
     * 深度搜索点击
     */
    @JvmStatic
    fun deepSearchClickEvent() {
        StatisticsUtils.onCommon(appContext, StatisticsUtils.DEEP_SEARCH_CLICK_EVENT)
    }

    @JvmStatic
    fun menueItemOperate(operationInfo: ItemOperationInfo, eventId: String) {
        val clickInfo = operationInfo.itemClickInfo
        val fileType = FilenameUtils.getExtension(clickInfo.fileName)
        val mode = if (operationInfo.singleSelete) {
            SELECT_MODE_SINGLE
        } else {
            SELECT_MODE_MULTI
        }
        val fromSearch = if (operationInfo.isFromSearch) {
            OPERATE_FROM_SEARCH
        } else {
            OPERATE_NOT_SEARCH
        }
        val map: MutableMap<String, String> = mutableMapOf(
            StatisticsUtils.OP_TYPE to operationInfo.opearteType,
            StatisticsUtils.SEARCH_RESULT_KEY_SOURCE to clickInfo.source,
            StatisticsUtils.SEARCH_RESULT_KEY_FILENAME to clickInfo.fileName,
            StatisticsUtils.FILE_EXTENSION to fileType,
            StatisticsUtils.SEARCH_RESULT_KEY_SELECT_MODE to mode.toString(),
            StatisticsUtils.SEARCH_RESULT_KEY_FROM_SEARCH to fromSearch.toString()
        )
        if (clickInfo.packageName != null) {
            map.putIfAbsent(StatisticsUtils.SEARCH_RESULT_KEY_PACKAGENAME, clickInfo.packageName)
        }
        if (eventId.isNotEmpty()) {
            Log.i(TAG, "menueItemOperate eventId $eventId, map $map")
            StatisticsUtils.onCommon(
                appContext, eventId, map
            )
        }
    }


    @JvmStatic
    fun detectThirdAppResult(detectInfo: ThirdAppDetectInfo) {
        val map: MutableMap<String, String> = mutableMapOf(
            StatisticsUtils.THIRD_FILE_DECT_KEY_CONFIG_VERSION to detectInfo.configVerson,
            StatisticsUtils.THIRD_FILE_DECT_KEY_TARGET_PACKAGENAME to detectInfo.packageName,
            StatisticsUtils.THIRD_FILE_DECT_KEY_TARGET_PACKAGE_VERSION to detectInfo.packageVersion,
            StatisticsUtils.THIRD_FILE_DECT_KEY_PREVIEW_ACTIVITY to detectInfo.previewActivity,
            StatisticsUtils.THIRD_FILE_DECT_KEY_EXACT_ACTIVITY to detectInfo.exactActivity,
            StatisticsUtils.THIRD_FILE_DECT_KEY_RESULT to detectInfo.result.toString(),
            StatisticsUtils.THIRD_FILE_DECT_KEY_ERROR_CODE to detectInfo.errorCode.toString(),
            StatisticsUtils.THIRD_FILE_DECT_KEY_VIEW_EXACT_RESULT to detectInfo.viewExactResult,
            StatisticsUtils.THIRD_FILE_DECT_KEY_DATA_BEAN to detectInfo.dateBean
        )
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.THIRD_FILE_DECT_EVENT_ID, map
        )
    }

    @JvmStatic
    fun supportThirdAppResult(supportCode: Int, sceneServiceVersion: String, dmpVersion: String) {
        val map: MutableMap<String, String> = mutableMapOf(
            StatisticsUtils.THIRD_FILE_SUPPORT_RESULT_CODE to supportCode.toString(),
            StatisticsUtils.THIRD_FILE_DMP_VERSION to dmpVersion,
            StatisticsUtils.THIRD_FILE_SCENE_VERSION to sceneServiceVersion,
        )
        StatisticsUtils.onCommon(
            appContext, StatisticsUtils.THIRD_FILE_INIT_EVENT_ID, map
        )
    }

    @JvmStatic
    fun dragFilesResult(activity: Activity, dragEvent: DragEvent, source: String, to: String, dragToSide: String) {
        val parseData = DropUtil.parseDragFile(dragEvent)
        (activity as? BaseVMActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
            val list = Injector.injectFactory<IFileOperateApi>()?.getFileBeanList(
                activity, parseData, isMediaFiles = false
            )
            if (list?.isEmpty() == true) return@launch
            val dragStatisticsData = buildDragStatisticsData(list)
            val picDetail = StringBuilder()
            for ((key, value) in dragStatisticsData.pidDetail) {
                picDetail.append(key).append(":").append(value).append(",")
            }
            val videoDetail = StringBuilder()
            for ((key, value) in dragStatisticsData.videoDetail) {
                videoDetail.append(key).append(":").append(value).append(",")
            }
            val audioDetail = StringBuilder()
            for ((key, value) in dragStatisticsData.audioDetail) {
                audioDetail.append(key).append(":").append(value).append(",")
            }
            val compressDetail = StringBuilder()
            for ((key, value) in dragStatisticsData.compressDetail) {
                compressDetail.append(key).append(":").append(value).append(",")
            }
            val docDetail = StringBuilder()
            for ((key, value) in dragStatisticsData.docDetail) {
                docDetail.append(key).append(":").append(value).append(",")
            }
            val apkDetail = StringBuilder()
            for ((key, value) in dragStatisticsData.apkDetail) {
                apkDetail.append(key).append(":").append(value).append(",")
            }
            val otherDetail = StringBuilder()
            for ((key, value) in dragStatisticsData.otherDetail) {
                otherDetail.append(key).append(":").append(value).append(",")
            }
            val map: MutableMap<String, String> = mutableMapOf(
                "source" to source, "to" to to, "num" to list?.size.toString(), "pic" to dragStatisticsData.pic.toString(),
                "pic_detail" to picDetail.toString(), "video" to dragStatisticsData.video.toString(), "video_detail" to videoDetail.toString(),
                "audio" to dragStatisticsData.audio.toString(), "audio_detail" to audioDetail.toString(), "doc" to dragStatisticsData.doc.toString(),
                "compress" to dragStatisticsData.compress.toString(), "compress_detail" to compressDetail.toString(),
                "doc_detail" to docDetail.toString(), "apk" to dragStatisticsData.apk.toString(), "apk_detail" to apkDetail.toString(),
                "other" to dragStatisticsData.other.toString(), "other_detail" to otherDetail.toString(), "dragToSide" to dragToSide)
            StatisticsUtils.onCommon(appContext, StatisticsUtils.DRAG_FILES_EVENT, map)
        }
    }

    @JvmStatic
    fun dragFolderResult(activity: Activity, dragEvent: DragEvent, targetPath: String, isInternal: String) {
        val parseData = DropUtil.parseDragFile(dragEvent)
        (activity as? BaseVMActivity)?.lifecycleScope?.launch(Dispatchers.IO) {
            val list = Injector.injectFactory<IFileOperateApi>()?.getFileBeanList(
                activity, parseData, isMediaFiles = false
            )
            if (list?.isEmpty() == true) return@launch
            val dragStatisticsData = buildDragFolderStatisticsData(list)
            val picDetail = StringBuilder()
            val picOriginPath = StringBuilder()
            buildDetail(dragStatisticsData.pidDetail, dragStatisticsData.pidDetailList, picDetail, picOriginPath)
            val videoDetail = StringBuilder()
            val videoOriginPath = StringBuilder()
            buildDetail(dragStatisticsData.videoDetail, dragStatisticsData.videoDetailList, videoDetail, videoOriginPath)
            val audioDetail = StringBuilder()
            val audioOriginPath = StringBuilder()
            buildDetail(dragStatisticsData.audioDetail, dragStatisticsData.audioDetailList, audioDetail, audioOriginPath)
            val compressDetail = StringBuilder()
            val compressOriginPath = StringBuilder()
            buildDetail(dragStatisticsData.compressDetail, dragStatisticsData.compressDetailList, compressDetail, compressOriginPath)
            val docDetail = StringBuilder()
            val docOriginPath = StringBuilder()
            buildDetail(dragStatisticsData.docDetail, dragStatisticsData.docDetailList, docDetail, docOriginPath)
            val apkDetail = StringBuilder()
            val apkOriginPath = StringBuilder()
            buildDetail(dragStatisticsData.apkDetail, dragStatisticsData.apkDetailList, apkDetail, apkOriginPath)
            val otherDetail = StringBuilder()
            val otherOriginPath = StringBuilder()
            buildDetail(dragStatisticsData.otherDetail, dragStatisticsData.otherDetailList, otherDetail, otherOriginPath)
            val map: MutableMap<String, String> = mutableMapOf(
                "target_path" to targetPath, "is_internal" to isInternal, "num" to list?.size.toString(),
                "pic" to dragStatisticsData.pic.toString(), "pic_detail" to picDetail.toString(), "pic_origin_path" to picOriginPath.toString(),
                "video" to dragStatisticsData.video.toString(), "video_detail" to videoDetail.toString(),
                "video_origin_path" to videoOriginPath.toString(), "audio" to dragStatisticsData.audio.toString(),
                "audio_detail" to audioDetail.toString(), "audio_origin_path" to audioOriginPath.toString(),
                "doc" to dragStatisticsData.doc.toString(), "doc_detail" to docDetail.toString(),
                "doc_origin_path" to docOriginPath.toString(), "compress" to dragStatisticsData.compress.toString(),
                "compress_detail" to compressDetail.toString(), "compress_origin_path" to compressOriginPath.toString(),
                "apk" to dragStatisticsData.apk.toString(), "apk_detail" to apkDetail.toString(),
                "apk_origin_path" to apkOriginPath.toString(), "other" to dragStatisticsData.other.toString(),
                "other_detail" to otherDetail.toString(), "other_origin_path" to otherOriginPath.toString())
            StatisticsUtils.onCommon(appContext, StatisticsUtils.DRAG_FOLDER_EVENT, map)
        }
    }

    /**
     * @param ope  #SHORTCUT_OPE_ADD = 0;
     * #SHORTCUT_OPE_UPDATE = 1;
     * #SHORTCUT_OPE_DELETE = 2;
     */
    @JvmStatic
    fun shortcutEvent(ope: Int, fileType: String, page: String?) {
        val map = mutableMapOf(
            StatisticsUtils.SHORTCUT_EVENT_OPE to ope.toString(),
            StatisticsUtils.SHORTCUT_EVENT_FILE_TYPE to fileType,
        )
        if (page.isNullOrEmpty().not()) {
            map[StatisticsUtils.OPERATION_PAGE] = page!!
        }
        StatisticsUtils.onCommon(appContext, StatisticsUtils.KEY_SHORTCUT_EVENT, map)
    }

    @JvmStatic
    private fun buildDetail(
        typeDetail: Map<String, Int>,
        pathList: List<String>,
        detail: StringBuilder,
        originPath: StringBuilder
    ) {
        for ((key, value) in typeDetail) {
            detail.append(key).append(":").append(value).append(",")
        }
        if (pathList.isNotEmpty()) {
            for ((index, value) in pathList.withIndex()) {
                originPath.append("[").append(value).append("]")
                if (index < pathList.size - 1) {
                    originPath.append(",")
                }
            }
        }
    }

    @JvmStatic
    private fun buildDragStatisticsData(list: ArrayList<BaseFileBean>?): DragStatisticsData {
        var pic = 0
        val picDetailMap = mutableMapOf<String, Int>()
        var video = 0
        val videoDetailMap = mutableMapOf<String, Int>()
        var audio = 0
        val audioDetailMap = mutableMapOf<String, Int>()
        var compress = 0
        val compressDetailMap = mutableMapOf<String, Int>()
        var doc = 0
        val docDetailMap = mutableMapOf<String, Int>()
        var apk = 0
        val apkDetailMap = mutableMapOf<String, Int>()
        var other = 0
        val otherDetailMap = mutableMapOf<String, Int>()
        list?.forEach { file ->
            val (fileExtension, type) = getFileType(file)
            when {
                type == MimeTypeHelper.IMAGE_TYPE -> {
                    val size = picDetailMap[fileExtension] ?: 0
                    picDetailMap[fileExtension] = size.plus(1)
                    pic++
                }
                type == MimeTypeHelper.VIDEO_TYPE -> {
                    val size = videoDetailMap[fileExtension] ?: 0
                    videoDetailMap[fileExtension] = size.plus(1)
                    video++
                }
                MimeTypeHelper.isAudioType(type) -> {
                    val size = audioDetailMap[fileExtension] ?: 0
                    audioDetailMap[fileExtension] = size.plus(1)
                    audio++
                }
                MimeTypeHelper.isDocType(type) || MimeTypeHelper.isOtherDocType(type) -> {
                    val size = docDetailMap[fileExtension] ?: 0
                    docDetailMap[fileExtension] = size.plus(1)
                    doc++
                }
                type == MimeTypeHelper.APPLICATION_TYPE -> {
                    val size = apkDetailMap[fileExtension] ?: 0
                    apkDetailMap[fileExtension] = size.plus(1)
                    apk++
                }
                type == MimeTypeHelper.COMPRESSED_TYPE -> {
                    val size = compressDetailMap[fileExtension] ?: 0
                    compressDetailMap[fileExtension] = size.plus(1)
                    compress++
                }
                else -> {
                    val size = otherDetailMap[fileExtension] ?: 0
                    otherDetailMap[fileExtension] = size.plus(1)
                    other++
                }
            }
        }
        return DragStatisticsData(pic, picDetailMap, video, videoDetailMap, audio, audioDetailMap,
            doc, docDetailMap, apk, apkDetailMap, compress, compressDetailMap, other, otherDetailMap)
    }

    @JvmStatic
    private fun buildDragFolderStatisticsData(list: ArrayList<BaseFileBean>?): DragFolderStatisticsData {
        var pic = 0
        val picDetailMap = mutableMapOf<String, Int>()
        val picDetailList = mutableListOf<String>()
        var video = 0
        val videoDetailMap = mutableMapOf<String, Int>()
        val videoDetailList = mutableListOf<String>()
        var audio = 0
        val audioDetailMap = mutableMapOf<String, Int>()
        val audioDetailList = mutableListOf<String>()
        var compress = 0
        val compressDetailMap = mutableMapOf<String, Int>()
        val compressDetailList = mutableListOf<String>()
        var doc = 0
        val docDetailMap = mutableMapOf<String, Int>()
        val docDetailList = mutableListOf<String>()
        var apk = 0
        val apkDetailMap = mutableMapOf<String, Int>()
        val apkDetailList = mutableListOf<String>()
        var other = 0
        val otherDetailMap = mutableMapOf<String, Int>()
        val otherDetailList = mutableListOf<String>()
        list?.forEach { file ->
            val (fileExtension, type) = getFileType(file)
            when {
                type == MimeTypeHelper.IMAGE_TYPE -> {
                    buildDragData(picDetailMap, fileExtension, file, picDetailList)
                    pic++
                }
                type == MimeTypeHelper.VIDEO_TYPE -> {
                    buildDragData(videoDetailMap, fileExtension, file, videoDetailList)
                    video++
                }
                MimeTypeHelper.isAudioType(type) -> {
                    buildDragData(audioDetailMap, fileExtension, file, audioDetailList)
                    audio++
                }
                MimeTypeHelper.isDocType(type) || MimeTypeHelper.isOtherDocType(type) -> {
                    buildDragData(docDetailMap, fileExtension, file, docDetailList)
                    doc++
                }
                type == MimeTypeHelper.APPLICATION_TYPE -> {
                    buildDragData(apkDetailMap, fileExtension, file, apkDetailList)
                    apk++
                }
                type == MimeTypeHelper.COMPRESSED_TYPE -> {
                    buildDragData(compressDetailMap, fileExtension, file, compressDetailList)
                    compress++
                }
                else -> {
                    buildDragData(otherDetailMap, fileExtension, file, otherDetailList)
                    other++
                }
            }
        }
        return DragFolderStatisticsData(pic, picDetailMap, picDetailList, video, videoDetailMap, videoDetailList,
            audio, audioDetailMap, audioDetailList, doc, docDetailMap, docDetailList, apk, apkDetailMap, apkDetailList,
            compress, compressDetailMap, compressDetailList, other, otherDetailMap, otherDetailList)
    }

    @JvmStatic
    private fun getFileType(file: BaseFileBean): Pair<String, Int> {
        val fileExtension = FilenameUtils.getExtension(file.mDisplayName)
        Log.d(TAG, "fileExtension: $fileExtension")
        val type = getTypeFromPath(file.mData)
        return Pair(fileExtension, type)
    }

    @JvmStatic
    private fun buildDragData(
        detailMap: MutableMap<String, Int>,
        fileExtension: String,
        file: BaseFileBean,
        detailList: MutableList<String>
    ) {
        val size = detailMap[fileExtension] ?: 0
        file.mData?.let { detailList.add(it) }
        detailMap[fileExtension] = size.plus(1)
    }

    /**
     * 点击首页远程设备
     */
    @JvmStatic
    fun clickHomeRemoteDeviceEvent(deviceStatus: Int, deviceType: Int) {
        val statusValue = when (deviceStatus) {
            RemoteDeviceConstants.CONNECTED -> StatisticsUtils.REMOTE_VALUE_DEV_CONNECTED
            RemoteDeviceConstants.DISCOVERED -> StatisticsUtils.REMOTE_VALUE_DEV_DISCOVERED
            else -> StatisticsUtils.REMOTE_VALUE_DEV_UNDISCOVERED
        }
        val map = mutableMapOf(
            StatisticsUtils.REMOTE_KEY_DEV_STATUS to statusValue.toString(),
            StatisticsUtils.REMOTE_KEY_DEV_TYPE to deviceType.toString()
        )
        StatisticsUtils.onCommon(appContext, StatisticsUtils.EVENT_REMOTE_DEV_CLICK, map)
    }

    /**
     * 远程设备状态发生改变
     */
    @JvmStatic
    fun remoteDeviceStatusChangedEvent(status: String, isBg: Boolean) {
        val isBackgroundValue = if (isBg) StatisticsUtils.REMOTE_VALUE_BACKGROUND else StatisticsUtils.REMOTE_VALUE_FOREGROUND
        val map = mutableMapOf(
            StatisticsUtils.REMOTE_KEY_DEV_STATUS to status,
            StatisticsUtils.REMOTE_KEY_IS_BACKGROUND to isBackgroundValue.toString()
        )
        StatisticsUtils.onCommon(appContext, StatisticsUtils.EVENT_DEV_STATUS_CHANGE, map)
    }

    /**
     * 远程设备文件操作
     */
    @JvmStatic
    fun remoteFileOperateEvent(operateType: Int, fileCount: Int? = null, size: Long? = null) {
        val map = mutableMapOf(
            StatisticsUtils.REMOTE_KEY_OPERATE_TYPE to operateType.toString()
        )
        fileCount?.let {
            map.put(StatisticsUtils.REMOTE_KEY_FILE_COUNT, it.toString())
        }
        size?.let {
            val displaySize = Utils.byteCountToDisplaySize(it)
            map.put(StatisticsUtils.REMOTE_KEY_FILE_SIZE, displaySize)
        }
        StatisticsUtils.onCommon(appContext, StatisticsUtils.EVENT_REMOTE_FILE_OPERATE, map)
    }

    data class DragStatisticsData(
        val pic: Int,
        val pidDetail: Map<String, Int>,
        val video: Int,
        val videoDetail: Map<String, Int>,
        val audio: Int,
        val audioDetail: Map<String, Int>,
        val doc: Int,
        val docDetail: Map<String, Int>,
        val apk: Int,
        val apkDetail: Map<String, Int>,
        val compress: Int,
        val compressDetail: Map<String, Int>,
        val other: Int,
        val otherDetail: Map<String, Int>
    )

    data class DragFolderStatisticsData(
        val pic: Int,
        val pidDetail: Map<String, Int>,
        val pidDetailList: List<String>,
        val video: Int,
        val videoDetail: Map<String, Int>,
        val videoDetailList: List<String>,
        val audio: Int,
        val audioDetail: Map<String, Int>,
        val audioDetailList: List<String>,
        val doc: Int,
        val docDetail: Map<String, Int>,
        val docDetailList: List<String>,
        val apk: Int,
        val apkDetail: Map<String, Int>,
        val apkDetailList: List<String>,
        val compress: Int,
        val compressDetail: Map<String, Int>,
        val compressDetailList: List<String>,
        val other: Int,
        val otherDetail: Map<String, Int>,
        val otherDetailList: List<String>
    )
}