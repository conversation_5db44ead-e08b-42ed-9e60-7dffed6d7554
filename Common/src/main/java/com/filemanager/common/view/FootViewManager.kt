/*********************************************************************
 * * Copyright (C), 2010-2020, Oplus Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        : FootViewManager.kt
 * * Description : Foot View Manager
 * * Version     : 1.0
 * * Date        : 2020/9/21
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>        <data>       <version>       <desc>
 * *  W9001165       2020/9/21       1.0           create
 ***********************************************************************/
package com.filemanager.common.view

import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter

class FootViewManager<T : BaseFileBean>(adapter: BaseSelectionRecycleAdapter<*, T>) {
    private val mAdapter = adapter
    private var mHasFootView: Boolean = false
    private var mIndex: Int = -1

    fun addFootView(data: MutableList<T>) {
        if (data.isNotEmpty() && (data.last().mFileWrapperViewType == BaseFileBean.TYPE_FILE_LIST_FOOTER)) {
            mIndex = data.size - 1
            mHasFootView = true
        } else {
            mHasFootView = false
        }
    }

    fun hasFootView(): Boolean = mHasFootView

    /**
     * if adapter has not FootView return -1
     */
    fun getFootViewPosition(): Int {
        return mIndex
    }
}

interface FootViewOperation {
    fun operation(): FootViewManager<*>
    fun hasFootView(): Boolean
    fun getFootViewPosition(): Int
    fun needShowFootView(): Boolean
    fun isFootView(position: Int): Boolean
    fun getFootString(count: Int): String
}