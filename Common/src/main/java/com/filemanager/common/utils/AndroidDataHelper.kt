/*********************************************************************
 * * Copyright (C), 2024, OPlus. All rights reserved.
 * * File        :  -
 * * Description :
 * * Version     : 1.0
 * * Date        : 2025/5/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.ForegroundColorSpan
import android.view.View
import android.view.View.OnClickListener
import com.coui.appcompat.clickablespan.COUIClickableSpan
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.wrapper.PathFileWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File
import java.util.Locale

object AndroidDataHelper {
    private const val TAG = "AndroidDataHelper"
    private val mInternalPath: String? = VolumeEnvironment.getInternalSdPath(appContext)
    val ANDROID_DATA_PATH = "$mInternalPath/Android/\u200Bdata"
    val ANDROID_DATA_NORMAL_PATH = "$mInternalPath/Android/data"
    const val PREF_ANDROID_DATA_ACCESS = "pref_android_data_access"
    val installedNameMap: HashMap<String, String>? by lazy { initInstalledNameMap() }
    val isSupportViewAndroidData: Boolean by lazy { checkSupportViewAndroidData() }
    var showAndroidDataSettingSwitch: Boolean = true
    //是否可以操作Android/data文件
    var allowEditAndroidData: Boolean = false
    //是否打开Android/data访问能力
    var openAndroidData: Boolean? = null
        get() {
            if (field == null) {
                field = PreferencesUtils.getBoolean(key = PREF_ANDROID_DATA_ACCESS, default = true)
            }
            return field == true && canViewAndroidDataFiles && isSupportViewAndroidData
        }
    //Android/data访问功能是否开启，可云控控制开关
    var canViewAndroidDataFiles: Boolean = true

    @JvmStatic
    fun replacePath(originalPath: String): String {
        return if (openAndroidData == true) {
            originalPath.replace(Regex("(?<=^|/)Android/data(?=/|\$)"), "Android/\u200Bdata")
        } else {
            originalPath
        }
    }
    @JvmStatic
    fun hasAndroidDataFile(files: List<out BaseFileBean>?): Boolean {
        if (openAndroidData != true || null == files || files.isEmpty()) {
            return false
        }
        files.forEach {
            if (it.mData?.startsWith(ANDROID_DATA_PATH) == true ||
                it.mData?.startsWith(ANDROID_DATA_NORMAL_PATH) == true
            ) {
                return true
            }
        }
        return false
    }

    @JvmStatic
    fun isAndroidDataPath(path: String): Boolean {
        return path.startsWith(ANDROID_DATA_PATH, ignoreCase = true) || path.startsWith(ANDROID_DATA_NORMAL_PATH, ignoreCase = true)
    }

    @JvmStatic
    fun isAndroidData(path: String): Boolean {
        return path.equals(ANDROID_DATA_PATH, ignoreCase = true) || path.equals(ANDROID_DATA_NORMAL_PATH, ignoreCase = true)
    }

    @JvmStatic
    fun checkSupportViewAndroidData(): Boolean {
        kotlin.runCatching {
            val supportData = File(ANDROID_DATA_PATH).list().isNotEmpty()
            Log.d(TAG, "checkSupportViewAndroidData $supportData")
            return SdkUtils.isAtLeastR() && supportData
        }.onFailure {
            Log.e(TAG, "checkSupportViewAndroidData ${it.message}")
        }
        return false
    }

    @JvmStatic
    fun searchAndroidDataMainDirectory(key: String): ArrayList<BaseFileBean>? {
        if (openAndroidData == true) {
            val packageNameList = File(ANDROID_DATA_PATH).list()
            val result = ArrayList<BaseFileBean>()
            installedNameMap?.let {
                packageNameList?.forEach { packageName ->
                    val name = it[packageName]
                    if ((name?.contains(key, ignoreCase = true) == true || packageName.contains(key, ignoreCase = true))
                        && (HiddenFileHelper.isDisplayFile(packageName))
                    ) {
                        Log.d(TAG, "searchAndroidDataMainDirectory find $name")
                        val fileBean = PathFileWrapper(ANDROID_DATA_PATH.plus(File.separator).plus(packageName))
                        fileBean.originPackage = packageName
                        result.add(fileBean)
                    }
                }
            }
            return result
        }
        return null
    }

    @JvmStatic
    fun searchAndroidDataFiles(key: String, deepSearchListener: DeepSearchListener) {
        val pathList = mutableListOf<String>()
        if (openAndroidData == true) {
            GlobalScope.launch(Dispatchers.IO) {
                var index = 0L
                File(ANDROID_DATA_PATH).listFiles().forEach { file ->
                    file.walk().filter {
                        index++
                        it.name != file.name && it.name.contains(key, ignoreCase = true)
                    }.forEach { file ->
                        if (HiddenFileHelper.isDisplayFile(file.path)) {
                            deepSearchListener.findFilePath(file.path)
                            pathList.add(file.path)
                        }
                    }
                }
                Log.d(TAG, "searchAndroidDataFiles $index")
                deepSearchListener.searchDone(pathList)
            }
        }
    }


    interface DeepSearchListener {
        fun findFilePath(path: String)
        fun searchDone(pathList: List<String>)
    }

    @JvmStatic
    private fun initInstalledNameMap(): HashMap<String, String> {
        if (PermissionUtils.hasGetInstalledAppsPermission()) {
            val pm = MyApplication.sAppContext.packageManager
            val info = pm.getInstalledPackages(0)
            if (info.size > 0) {
                val hashMap = HashMap<String, String>()
                for (packageInfo in info) {
                    packageInfo.applicationInfo?.apply {
                        loadLabel(pm).toString()?.apply {
                            hashMap[packageInfo.packageName] = this
                        }
                    }
                }
                return hashMap
            }
        } else {
            Log.e(TAG, "no installed permission")
        }
        return HashMap<String, String>()
    }

    @JvmStatic
    fun buildEmptyDeepSearchTips(context: Context, tips: Int, click: Int, clickListener: OnClickListener?): CharSequence {
        val resources = MyApplication.appContext.resources
        val linkString2 = resources.getString(click)
        val deepTipsString: String = resources.getString(tips)
        val spannableString = SpannableStringBuilder(deepTipsString)
        val startIndex = deepTipsString.indexOf(linkString2)
        if (startIndex > 0) {
            val endIndex = startIndex + linkString2.length
            spannableString.setSpan(object : COUIClickableSpan(context) {
                override fun onClick(widget: View) {
                    clickListener?.onClick(widget)
                }
            }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            spannableString.setSpan(
                ForegroundColorSpan(context.getColor(com.filemanager.common.R.color.text_color_black_alpha_60)),
                0, startIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        return spannableString
    }

    @JvmStatic
    fun buildDeepSearchTips(context: Context, tips: Int, click: Int, clickListener: OnClickListener?): CharSequence {
        val resources = MyApplication.appContext.resources
        val deepTipsString: String
        val linkString2 = resources.getString(click)
        deepTipsString = String.format(
            Locale.getDefault(),
            resources.getString(tips),
            linkString2
        )

        val spannableString = SpannableStringBuilder(deepTipsString)
        val startIndex = deepTipsString.indexOf(linkString2)
        if (startIndex > 0) {
            val endIndex = startIndex + linkString2.length
            spannableString.setSpan(object : COUIClickableSpan(context) {
                override fun onClick(widget: View) {
                    clickListener?.onClick(widget)
                    OptimizeStatisticsUtil.deepSearchClickEvent()
                }
            }, startIndex, endIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE)
            spannableString.setSpan(
                ForegroundColorSpan(context.getColor(com.filemanager.common.R.color.text_color_black_alpha_60)),
                0, startIndex, Spannable.SPAN_INCLUSIVE_EXCLUSIVE
            )
        }
        return spannableString
    }
}