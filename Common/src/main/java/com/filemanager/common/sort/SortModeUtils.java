/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version:
 ** Date :
 ** Author:
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.sort;

import android.content.Context;

import com.filemanager.common.utils.PreferencesUtils;

public class SortModeUtils {
    public static final String RECORD_SORTMODE = "RECORD_SORTMODE";
    public static final String CATEGORY_SORT_RECORD = "category";
    public static final String ORDER_SUFFIX = "_order";
    public static final String DOC_SORT = "_doc";
    public static final String RECYCLE_SORT_RECORD = "recycle_bin";
    public static final String REMOTE_MAC_SORT_RECORD = "remote_mac";

    public static final String ALBUM_FILE_SORT_RECORD = "album";
    public static final String RECENT_FILE_SORT_RECORD = "recent_file";
    public static final String CLOUD_FILE_SORT_RECORD = "cloud_file";
    public static final String BROWSER_SORT_RECORD = "browser";
    public static final String BROWSER_LAST_SORT_RECORD = "browser_last";
    public static final String RECORD_CATEGORY_MODE = "record_mode";
    public static final String RECORD_DEFAULT_OFFSET = "default_set";
    public static final String SHARED_PREFS_NAME = "com.filemanager_preferences";

    /**
     * get parameter from sharePreferences
     */
    public static int getSharedSortMode(Context context) {
        return PreferencesUtils.getInt(SHARED_PREFS_NAME, RECORD_SORTMODE, SortHelper.FILE_NAME_ORDER);
    }

    public static int getSharedSortMode(Context context, String recordMode) {
        if (SortRecordModeFactory.getRecentFileKey() == recordMode) {
            //产品诉求去掉最近页面的排序功能，最近页面按修改时间排序
            return SortHelper.FILE_TIME_REVERSE_ORDER;
        } else {
            return PreferencesUtils.getInt(SHARED_PREFS_NAME, recordMode, SortRecordModeFactory.INSTANCE.getDefaultValue(recordMode));
        }
    }

    /**
     * 按照类型进行排序，获取排序的顺序：正序或者倒序
     * @param recordMode 排序类型，默认是recordeMode, 回收站是@code{#RECYCLE_SORT_RECORD} ,particular是PARTICULAR_CATEGORY_SORT_RECORD
     * @return true:表示 DESC false:表示 ASC
     */
    public static boolean getSharedSortOrder(String recordMode) {
        return PreferencesUtils.getBoolean(SHARED_PREFS_NAME, recordMode + ORDER_SUFFIX, true);
    }

    public static void putSharedSortOrder(String recordMode, boolean desc) {
        PreferencesUtils.put(SortModeUtils.SHARED_PREFS_NAME, recordMode + ORDER_SUFFIX, desc);
    }

    /**
     * dfm 中对文档有进行分类筛选
     * @param recordMode
     * @param index
     */
    public static void putDocSortType(String recordMode, int index) {
        PreferencesUtils.put(SortModeUtils.SHARED_PREFS_NAME, recordMode + DOC_SORT, index);
    }

    public static int getDocSortType(String recordMode) {
        return PreferencesUtils.getInt(SortModeUtils.SHARED_PREFS_NAME, recordMode + DOC_SORT, 0);
    }
}
