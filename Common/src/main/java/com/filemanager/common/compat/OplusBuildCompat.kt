/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OplusBuildCompat
 ** Description : OplusBuildCompat
 ** Version     : 1.0
 ** Date        : 2024/09/12 10:40
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/12     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

import com.filemanager.common.utils.SdkUtils

object OplusBuildCompat {
    const val UNKNOWN: Int = 0
    const val OS_1_0: Int = 1
    const val OS_1_2: Int = 2
    const val OS_1_4: Int = 3
    const val OS_2_0: Int = 4
    const val OS_2_1: Int = 5
    const val OS_3_0: Int = 6
    const val OS_3_1: Int = 7
    const val OS_3_2: Int = 8
    const val OS_5_0: Int = 9
    const val OS_5_1: Int = 10
    const val OS_5_2: Int = 11
    const val OS_6_0: Int = 12
    const val OS_6_1: Int = 13
    const val OS_6_2: Int = 14
    const val OS_6_7: Int = 15
    const val OS_7_0: Int = 16
    const val OS_7_1: Int = 17
    const val OS_7_2: Int = 18
    const val OS_11_0: Int = 19
    const val OS_11_1: Int = 20
    const val OS_11_2: Int = 21
    const val OS_11_3: Int = 22
    const val OS_12_0: Int = 23
    const val OS_12_1: Int = 24
    const val OS_12_2: Int = 25
    const val OS_13_0: Int = 26
    const val OS_13_1: Int = 27
    const val OS_13_1_1: Int = 28
    const val OS_13_2: Int = 29
    const val OS_14_0: Int = 30
    const val OS_14_0_1: Int = 31
    const val OS_14_0_2: Int = 32
    const val OS_14_1_0: Int = 33
    const val OS_15_0_0: Int = 34
    const val OS_15_0_1: Int = 35
    const val OS_15_0_2: Int = 36
    const val OS_16_0: Int = 37

    @JvmStatic
    fun getOplusOSVERSION(): Int {
        return CompatUtils.compactSApi({
            com.oplus.os.OplusBuild.getOplusOSVERSION()
        }, {
            com.heytap.addon.os.OplusBuild.getOplusOSVERSION()
        })
    }

    /**
     * 获取 os sdk 版本中第一位版本号
     * 版本号为两位，从android-T 13.0 以上开始定义，13.0 以下无法获取
     * to test the mock
     */
    @JvmStatic
    fun getOSSdkVersion(): Int {
        if (SdkUtils.isAtLeastT()) {
            return com.oplus.os.OplusBuild.VERSION.SDK_VERSION
        }
        return UNKNOWN
    }

    /**
     * 获取 os sdk sub 版本中第二位版本号
     * 版本号为两位，从android-T 13.0 以上开始定义，13.0 以下无法获取
     * to test the mock
     */
    @JvmStatic
    fun getOSSdkSubVersion(): Int {
        if (SdkUtils.isAtLeastT()) {
            return com.oplus.os.OplusBuild.VERSION.SDK_SUB_VERSION
        }
        return UNKNOWN
    }

    @JvmStatic
    fun getVersionRelease(): String {
        return CompatUtils.compactSApi({
            com.oplus.os.OplusBuild.VERSION.RELEASE
        }, {
            com.heytap.addon.os.OplusBuild.VERSION.RELEASE
        })
    }
}
