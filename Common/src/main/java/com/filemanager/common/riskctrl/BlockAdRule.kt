/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: BlockAdRule.kt
 ** Description: 风控策略中屏蔽广告的策略
 ** Version: 1.0
 ** Date : 2025/04/03
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/03   1.0    create
 ****************************************************************/
package com.filemanager.common.riskctrl

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import java.net.URI

@Keep
data class BlockAdRule(
    /**
     * 针对公司的屏蔽
     */
    val cp: List<String>? = null,

    /**
     * 针对指定域名
     */
    @SerializedName("host")
    val hosts: List<String>? = null,

    /**
     * 针对指定页面
     */
    @SerializedName("urls")
    val urls: List<String>? = null
) {

    /**
     * 是否被屏蔽
     */
    fun isBlock(adUrl: String): Boolean {
        // 判断页面
        if (urls?.contains(adUrl) == true) {
            return true
        }
        // 判断域名
        val uri = URI(adUrl)
        val adHost = uri.host
        hosts?.forEach {
            if (adHost.startsWith(it)) {
                return true
            }
        }
        return false
    }
}