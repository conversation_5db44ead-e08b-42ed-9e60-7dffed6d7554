/***********************************************************
 ** Copyright (C), 2010-2020, Oplus. All rights reserved.
 ** FileName: StrategyItem.kt
 ** Description: 风控策略的每一条Item
 ** Version: 1.0
 ** Date : 2025/04/03
 ** Author: chao.xue
 **
 ** ---------------------Revision History: ---------------------
 ** <author>   <date>   <version > <desc>
 *  chao.xue 2025/04/03   1.0    create
 ****************************************************************/
package com.filemanager.common.riskctrl

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class StrategyItem(
    val module: String,

    val action: Int,

    @SerializedName("dataIds")
    val dataIds: List<String>? = null,

    /**
     * 广告屏蔽规则，直接保持json格式返回
     */
    @SerializedName("rules")
    val adBlockRule: String? = null
)