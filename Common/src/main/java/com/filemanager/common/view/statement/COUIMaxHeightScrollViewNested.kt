/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : COUIFullPageStatementNew
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/9/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view.statement

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import com.coui.appcompat.statement.COUIMaxHeightScrollView

class COUIMaxHeightScrollViewNested @JvmOverloads constructor(
    mContext: Context,
    attrs: AttributeSet
) : COUIMaxHeightScrollView(
    mContext, attrs
) {
    private var maxY: Int = 0
    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        maxY = getChildAt(0).measuredHeight - measuredHeight
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): Boolean {
        ev?.let {
            when (ev.action) {
                MotionEvent.ACTION_DOWN -> parent.parent.requestDisallowInterceptTouchEvent(true)


                MotionEvent.ACTION_MOVE -> {
                    if (scrollY in 1 until maxY) {
                        parent.requestDisallowInterceptTouchEvent(true)
                    } else {
                        parent.requestDisallowInterceptTouchEvent(false)
                    }
                }

                MotionEvent.ACTION_UP ->
                    parent.parent.requestDisallowInterceptTouchEvent(false)
            }
        }
        return super.dispatchTouchEvent(ev)
    }
}