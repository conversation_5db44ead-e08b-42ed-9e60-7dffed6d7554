/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date : 2024/10/24, 80352284
 ** Author: 80352284
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.dragselection.util

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Environment
import android.provider.DocumentsContract
import android.view.DragEvent
import androidx.lifecycle.lifecycleScope
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants.DOCUMENT_READER_URI_PREFIX
import com.filemanager.common.constants.KtConstants.FILE_MANAGER_URI_PREFIX
import com.filemanager.common.constants.KtConstants.FILE_MANAGER_URI_PREFIX_ONEPLUS
import com.filemanager.common.constants.KtConstants.IS_DRAG_FROM_MAC
import com.filemanager.common.dragselection.action.DragParseData
import com.filemanager.common.fileutils.FileMediaHelper
import com.filemanager.common.fileutils.getAllFilePathFromDirectory
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.CustomToast
import com.filemanager.common.utils.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File

object DropUtil {

    const val TAG = "DropUtil"
    val DOWNLOAD_PATH: String by lazy { Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath }
    val STORAGE_ROOT_PATH: String by lazy { Environment.getExternalStorageDirectory().absolutePath }
    private const val TRANSFER_PATH_PREFIX = ".com_coloros_smartsidebar/transferdock/"
    private const val OPLUS_DRAG_PATH_PREFIX = "Download/OplusDragAndDrop/OPLUSDRAG"
    private const val DOCUMENTS_UI_AUTHORITY = "com.android.externalstorage.documents"

    @JvmStatic
    fun parseDragFile(event: DragEvent): DragParseData {

        val data = DragParseData()
        event.clipData?.apply {
            val count = itemCount
            for (i in 0 until count) {
                val uri = getItemAt(i).uri
                val text = getItemAt(i).text
                val html = getItemAt(i).htmlText
                val uriString = uri?.toString() ?: ""
                val isDocumentReaderUri = uriString.startsWith(DOCUMENT_READER_URI_PREFIX)
                if (uri != null && uriString.isNotEmpty()  && !isDocumentReaderUri && !isFileManagerUri(uriString)) {
                    data.uriList.add(uri)
                }
                if (text != null && text.isNotEmpty()) {
                    data.textList.add(text.toString())
                }
                if (html != null && html.isNotEmpty()) {
                    data.htmlList.add(html)
                }
            }

            val bundle = description?.extras
            val folderPathList = bundle?.getStringArray(CommonConstants.KEY_FOLDER_PATH_LIST)
            folderPathList?.let {
                for (path in it) {
                    data.folderList.add(path)
                }
            }

            val noneMediaPathList = bundle?.getStringArray(CommonConstants.KEY_NONE_MEDIA_PATH_LIST)
            noneMediaPathList?.let {
                for (path in it) {
                    data.noneMediaPathList.add(path)
                }
            }
            val isFromMac = bundle?.getBoolean(IS_DRAG_FROM_MAC)
            data.isMacDragData = isFromMac == true
        }
        return data
    }

    @JvmStatic
    fun isFileManagerUri(uriString: String): Boolean {
        return uriString.startsWith(FILE_MANAGER_URI_PREFIX) || uriString.startsWith(
            FILE_MANAGER_URI_PREFIX_ONEPLUS
        )
    }

    /**
     * 检查是否是公共媒体库文件，中转站传输过来的文件也是公共媒体库文件，但其在私有目录下
     * @param mediaFileCallback 公共媒体库文件回调，处理从最近文件taskbar或其他应用拖入公共媒体库文件的文件，回调在主线程
     * @param privacyFileCallback 私有文件回调，处理从三方拖入的文件，包括中转站，回调在主线程
     */
    @JvmStatic
    fun checkIsMediaFiles(
        dragParseData: DragParseData,
        activity: Activity,
        mediaFileCallback: () -> Boolean,
        privacyFileCallback: () -> Boolean
    ): Boolean {
        val uriList = dragParseData.uriList
        val textList = dragParseData.textList
        val htmlList = dragParseData.htmlList
        val folderList = dragParseData.folderList
        val noneMediaList = dragParseData.noneMediaPathList
        if (uriList.isEmpty() && (textList.isNotEmpty() || htmlList.isNotEmpty())) {
            Log.d(TAG, "checkIsMediaFiles -> no uri files, return!")
            return privacyFileCallback.invoke()
        }
        val uri = if (uriList.isNotEmpty()) uriList[0] else null
        if (uri == null && (folderList.isNotEmpty() || noneMediaList.isNotEmpty())) {
            Log.d(TAG, "checkIsMediaFiles -> uri file is null, folder or none media files!")
            return mediaFileCallback.invoke()
        }
        if (dragParseData.isEmpty()) {
            Log.d(TAG, "checkIsMediaFiles -> empty data, return!")
            CustomToast.showShort(com.filemanager.common.R.string.drag_not_support_position)
            return false
        }
        //检查是否是公共媒体库文件
        (activity as BaseVMActivity).lifecycleScope.launch(Dispatchers.IO) {
            val property = FileMediaHelper.getOpenableProperty(activity, uri)
            val path = property.mData
            val transferPath = DOWNLOAD_PATH + File.separator + TRANSFER_PATH_PREFIX
            val oplusDragPath = STORAGE_ROOT_PATH + File.separator + OPLUS_DRAG_PATH_PREFIX
            //如果查不到媒体库文件，或者是中转站文件，或者是系统拖拽文件，直接保存
            if (path.isNullOrEmpty() || path.startsWith(transferPath) || path.startsWith(oplusDragPath)
                || !File(path).exists()
            ) {
                withContext(Dispatchers.Main) {
                    privacyFileCallback.invoke()
                }
            } else {
                withContext(Dispatchers.Main) {
                    mediaFileCallback.invoke()
                }
            }
        }
        return true
    }

    @JvmStatic
    fun isExistPrivateDirectory(listUris: ArrayList<Uri>, context: Context): Boolean {
        var isPrivate = false
        if (listUris.isEmpty() || !isUriFromDocumentsUI(listUris[0])) {
            return false
        }
        for (uri in listUris) {
            if (isPrivateDataFromDocumentsUI(context, uri)) {
                isPrivate = true
                break
            }
        }
        return isPrivate
    }

    @JvmStatic
    fun isUriFromDocumentsUI(uri: Uri): Boolean {
        return uri.scheme == "content" && uri.authority == DOCUMENTS_UI_AUTHORITY
    }

    @JvmStatic
    fun isPrivateDataFromDocumentsUI(context: Context, uri: Uri): Boolean {
        runCatching {
            val path = getFilePathFromDocumentsUI(uri)
            return path?.let { filePath ->
                isPathPrivate(context, filePath)
            } ?: false
        }.onFailure {
            Log.w(TAG, "isPrivateDataFromDocumentsUI onFailure: ${it.message}")
        }
        return false
    }

    @JvmStatic
    private fun getFilePathFromDocumentsUI(uri: Uri): String? {
        runCatching {
            val documentId = DocumentsContract.getDocumentId(uri)
            val startFlag = "primary:"
            if (documentId.startsWith(startFlag)) {
                return "/storage/emulated/0/${documentId.substring(startFlag.length)}"
            }
        }.onFailure {
            Log.w(TAG, "getFilePathFromDocumentFile onFailure: ${it.message}")
        }
        return null
    }

    @JvmStatic
    private fun isPathPrivate(context: Context, path: String): Boolean {
        val appFilesDir = context.filesDir.parentFile?.parentFile?.path
        val androidDataDir = context.externalCacheDir?.parentFile?.parentFile?.path
        return appFilesDir?.let { path.startsWith(it) } == true ||
                path.startsWith(androidDataDir.orEmpty())
    }

    @JvmStatic
    fun handleDocumentsUIFile(listUris: ArrayList<Uri>): ArrayList<Uri> {
        if (listUris.isEmpty() || !isUriFromDocumentsUI(listUris[0])) {
            return listUris
        }
        val uris = arrayListOf<Uri>()
        for (uri in listUris) {
            val path = getFilePathFromDocumentsUI(uri) ?: continue
            val file = File(path)
            if (!file.isDirectory) {
                uris.add(Uri.fromFile(file))
            } else {
                val filePathList = getAllFilePathFromDirectory(file)
                if (filePathList.isEmpty()) {
                    Log.d(TAG, "handleDocumentsUIFile dir is empty: $path")
                } else {
                    for (path in filePathList) {
                        uris.add(Uri.fromFile(File(path)))
                    }
                }
            }
        }
        return uris
    }

    @JvmStatic
    fun parseDocumentUIUris(context: Context, uriList: List<Uri>): DragParseData {
        val parseData = DragParseData()
        for (uri in uriList) {
            if (isPrivateDataFromDocumentsUI(context, uri)) {
                parseData.uriList.add(uri)
            } else {
                val path = getFilePathFromDocumentsUI(uri) ?: continue
                val file = File(path)
                if (file.isDirectory) {
                    parseData.folderList.add(file.absolutePath)
                } else {
                    parseData.mediaPathList.add(file.absolutePath)
                }
            }
        }
        Log.d(TAG, "parseDocumentUIUris uris:${parseData.uriList.size} paths:${parseData.mediaPathList.size} folders:${parseData.folderList.size}")
        return parseData
    }

    @JvmStatic
    fun checkIsMediaFragmentTag(dragTag: String?): Boolean {
        val tagSet = HashSet<String>()
        tagSet.add(CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_IMAGE)
        tagSet.add(CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_VIDEO)
        tagSet.add(CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_AUDIO)
        tagSet.add(CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_DOC)
        tagSet.add(CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_APK)
        tagSet.add(CommonConstants.DRAG_FROM_FRAGMENT + CategoryHelper.CATEGORY_COMPRESS)

        return tagSet.contains(dragTag)
    }

    @JvmStatic
    fun getCategoryTypeByDragTag(dragTag: String?): Int {
        if (dragTag == null || !dragTag.startsWith(CommonConstants.DRAG_FROM_FRAGMENT)) return -1
        var categoryType = -1
        runCatching {
            categoryType = dragTag.replace(CommonConstants.DRAG_FROM_FRAGMENT, "").toInt()
        }.onFailure {
            Log.e(TAG, "getCategoryTypeByDragTag $it")
        }
        return categoryType
    }
}