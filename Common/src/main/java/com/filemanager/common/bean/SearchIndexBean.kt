/*********************************************************************
 * * Copyright (C), 2020-2030 Oplus. All rights reserved..
 * * File        : SearchIndexBean.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/4/16
 * * Author      : huangyuanwang
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.bean

import android.util.Log
import com.google.gson.GsonBuilder
import com.google.gson.annotations.SerializedName
import java.text.SimpleDateFormat
import java.util.Date

data class SearchIndexBean(
    var identification: String = "",
    var checkSum: Long = DEFAULT_CHECK_SUM,
    var searchDataBean: SearchDataBean = SearchDataBean(),
) {
    companion object {

        const val TAG = "SearchIndexBean"

        const val DEFAULT_CHECK_SUM = 0L
    }
}


data class SearchDataBean(
    var fileName: String = "",
    var sourcePackage: String = "",
    var sourceName: String = "",
    var fileTime: Long = 0,
    var sourceType: Int = 0,
    var fileSize: Long = 0,
    var detectTime: Long = 0
) {

    companion object {

        const val TAG = "SearchDataBean"


        fun convertTimeLongToFormatString(timeStamp: Long): String {
            val pattern = "yyyy/M/d"
            val date = Date(timeStamp)
            val simpleDataformat = SimpleDateFormat(pattern)
            val timeString = simpleDataformat.format(date)
            Log.i(TAG, "convertTimeLongToFormatString input $timeStamp, output $timeString")
            return timeString
        }
    }

    /**
     * 这个true代表这个dataClass中只有identification和checksum两个字段有效，其他都是无效数据
     */
    fun isRawData(): Boolean {
        return fileName.isEmpty() &&
                sourceName.isEmpty() &&
                sourcePackage.isEmpty() &&
                (fileSize == 0L) &&
                (fileTime == 0L) &&
                (sourceType == 0) &&
                (detectTime == 0L)
    }
}


data class SearchResultBean(
    var searchDataBean: SearchDataBean,
    var highLight: String,
    var identification: String,
    var checkSum: Long
)


data class HighLightItem(
    @SerializedName("content") var contentString: String,
    @SerializedName("position") var positionString: String,
    @SerializedName("hasPrefix") var hasPrefefix: Boolean = false,
    @SerializedName("hasSuffix") var hasSuffix: Boolean = false
) {
    companion object {
        const val TAG = "HighLightItem"
    }

    @Transient
    private var positionList: MutableList<Pair<Int, Int>> = mutableListOf()

    private fun initPositionList(): List<Pair<Int, Int>> {
        Log.i(TAG, "initPositionList start positionString $positionString")
        val result = mutableListOf<Pair<Int, Int>>()
        val stringList = positionString.split(" ")
        var start = 0
        var end = 0
        stringList.forEachIndexed { index, s ->
            val itemInt = runCatching {
                s.toInt()
            }.getOrNull() ?: return@forEachIndexed
            if (index % 2 == 0) {
                start = itemInt
            } else {
                end = itemInt
                result.add(Pair(start, end))
            }
        }
        Log.i(TAG, "initPositionList end positionList $result")
        return result
    }

    @Transient
    private var keyWordList: List<String> = mutableListOf()

    private fun initKeyWordList(): List<String> {
        val list = mutableListOf<String>()
        val positionList = positionString.split(" ")
        val indexSize = (positionList.size) / 2
        Log.d(TAG, "HighLightEntity indexSize $indexSize")
        if (indexSize == 0) {
            return list
        }
        for (i in 0 until indexSize) {
            contentString.substring(
                positionList[i * 2].toInt(),
                positionList[i * 2 + 1].toInt()
            ).let { key ->
                list.add(key)
                Log.d(TAG, "HighLightEntity key $key")
            }
        }
        return list
    }

    fun getPositionList(): List<Pair<Int, Int>> {
        if (positionList.isEmpty()) {
            initPositionList()
        }
        return positionList
    }

    fun getKeyWordList(): List<String> {
        if (keyWordList.isNullOrEmpty()) {
            keyWordList = initKeyWordList()
        }
        return keyWordList
    }
}

data class HighLightItems(
    @SerializedName("name") var nameHighLight: HighLightItem?,
    @SerializedName("source_name") var sourceNameHight: HighLightItem?
) {
    companion object {
        const val TAG = "HighLightItems"

        fun parseFromJsonString(jsonString: String?): HighLightItems {
            Log.i(TAG, "parseFromJsonString input $jsonString")
            val gb = GsonBuilder().setPrettyPrinting()
                .serializeNulls().create()
            val result = gb.fromJson(jsonString, HighLightItems::class.java)
            Log.i(TAG, "parseFromJsonString output $result")
            return result
        }
    }
}


