/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : The animation of add items should not be displayed when the data is loaded for the first time
 * * Version     : 1.0
 * * Date        : 2020/8/17
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.animation

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.TimeInterpolator
import android.animation.ValueAnimator
import androidx.core.view.ViewCompat
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.SimpleItemAnimator
import com.coui.appcompat.animation.COUIInEaseInterpolator
import com.coui.appcompat.animation.COUIMoveEaseInterpolator
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.filemanager.common.MyApplication

open class FolderTransformAnimator : SimpleItemAnimator() {
    companion object {
        private const val TAG = "FolderTransformAnimator"
        private const val ADD_TRANSFORM_UP_X_D = 270f
        private const val ADD_TRANSFORM_DOWN_X_D = -70f
        private const val ADD_REMOVE_DURATION = 300L
        private const val ADD_FOLDER_OUT_DURATION = 150L
        private const val ADD_FOLDER_IN_DURATION = 100L
    }

    var mIsFolderInAnimation = true
    private var isFirstFolderInAdd = true
    private val mIsDarkModel by lazy { COUIDarkModeUtil.isNightMode(MyApplication.sAppContext) }

    //Used to determine whether to display the animation of adding data, the animation should not be displayed when the data is loaded for the first time or notifyDataSetChanged with no animate
    var mSkipAddRemoveAnimation = false
    private var skipAnimator: INeedSkipAnimator? = null
    private val DEBUG = false
    private var mDefaultInterpolator: TimeInterpolator? = null
    private val mPendingRemovals = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingAdditions = ArrayList<RecyclerView.ViewHolder>()
    private val mPendingMoves: ArrayList<MoveInfo> = ArrayList<MoveInfo>()
    private val mPendingChanges: ArrayList<ChangeInfo> = ArrayList<ChangeInfo>()

    private val mAdditionsList = ArrayList<ArrayList<RecyclerView.ViewHolder>>()
    private val mMovesList: ArrayList<ArrayList<MoveInfo>> = ArrayList<ArrayList<MoveInfo>>()
    private val mChangesList: ArrayList<ArrayList<ChangeInfo>> = ArrayList<ArrayList<ChangeInfo>>()

    private val mAddAnimations = ArrayList<RecyclerView.ViewHolder?>()
    private val mMoveAnimations = ArrayList<RecyclerView.ViewHolder?>()
    private val mRemoveAnimations = ArrayList<RecyclerView.ViewHolder?>()
    private val mChangeAnimations = ArrayList<RecyclerView.ViewHolder?>()

    class MoveInfo(var holder: RecyclerView.ViewHolder, var fromX: Int, var fromY: Int, var toX: Int, var toY: Int)

    class ChangeInfo private constructor(oldHolder: RecyclerView.ViewHolder, newHolder: RecyclerView.ViewHolder?) {
        var oldHolder: RecyclerView.ViewHolder? = oldHolder
        var newHolder: RecyclerView.ViewHolder? = newHolder
        var fromX = 0
        var fromY = 0
        var toX = 0
        var toY = 0

        constructor(oldHolder: RecyclerView.ViewHolder, newHolder: RecyclerView.ViewHolder?,
                    fromX: Int, fromY: Int, toX: Int, toY: Int) : this(oldHolder, newHolder) {
            this.fromX = fromX
            this.fromY = fromY
            this.toX = toX
            this.toY = toY
        }

        override fun toString(): String {
            return ("ChangeInfo{"
                    + "oldHolder=" + oldHolder
                    + ", newHolder=" + newHolder
                    + ", fromX=" + fromX
                    + ", fromY=" + fromY
                    + ", toX=" + toX
                    + ", toY=" + toY
                    + '}')
        }

    }

    override fun runPendingAnimations() {
        val removalsPending = mPendingRemovals.isNotEmpty()
        val movesPending = mPendingMoves.isNotEmpty()
        val changesPending = mPendingChanges.isNotEmpty()
        val additionsPending = mPendingAdditions.isNotEmpty()
        if (!removalsPending && !movesPending && !additionsPending && !changesPending) {
            // nothing to animate
            return
        }
        //Log.d(TAG, "runPendingAnimations mPendingRemovals $mPendingRemovals, mPendingAdditions $mPendingAdditions")
        for (holder in mPendingRemovals) {
            // First, remove stuff
            animateRemoveImpl(holder)
        }
        mPendingRemovals.clear()
        // Next, move stuff
        if (movesPending) {
            val moves = ArrayList<MoveInfo>()
            moves.addAll(mPendingMoves)
            mMovesList.add(moves)
            mPendingMoves.clear()
            val mover = Runnable {
                for (moveInfo in moves) {
                    animateMoveImpl(moveInfo.holder, moveInfo.fromX, moveInfo.fromY,
                            moveInfo.toX, moveInfo.toY)
                }
                moves.clear()
                mMovesList.remove(moves)
            }
            if (removalsPending) {
                val view = moves[0].holder.itemView
                ViewCompat.postOnAnimationDelayed(view, mover, removeDuration)
            } else {
                mover.run()
            }
        }
        // Next, change stuff, to run in parallel with move animations
        if (changesPending) {
            val changes = ArrayList<ChangeInfo>()
            changes.addAll(mPendingChanges)
            mChangesList.add(changes)
            mPendingChanges.clear()
            val changer = Runnable {
                for (change in changes) {
                    animateChangeImpl(change)
                }
                changes.clear()
                mChangesList.remove(changes)
            }
            if (removalsPending) {
                val holder = changes[0].oldHolder
                if (holder != null) {
                    ViewCompat.postOnAnimationDelayed(holder.itemView, changer, removeDuration)
                }
            } else {
                changer.run()
            }
        }
        // Next, add stuff
        if (additionsPending) {
            val additions = ArrayList<RecyclerView.ViewHolder>()
            additions.addAll(mPendingAdditions)
            mAdditionsList.add(additions)
            mPendingAdditions.clear()
            val adder = Runnable {
                for (holder in additions) {
                    animateAddImpl(holder)
                }
                additions.clear()
                mAdditionsList.remove(additions)
            }
            if (removalsPending || movesPending || changesPending) {
                val removeDuration = if (removalsPending) removeDuration else 0
                val moveDuration = if (movesPending) moveDuration else 0
                val changeDuration = if (changesPending) changeDuration else 0
                val totalDelay = removeDuration + Math.max(moveDuration, changeDuration)
                val view = additions[0].itemView
                ViewCompat.postOnAnimationDelayed(view, adder, totalDelay)
            } else {
                if (isFirstFolderInAdd) {
                    isFirstFolderInAdd = false
                    adder.run()
                } else {
                    val addViewDelay =
                        if (mIsFolderInAnimation) ADD_FOLDER_IN_DURATION else ADD_FOLDER_OUT_DURATION
                    val view = additions[0].itemView
                    ViewCompat.postOnAnimationDelayed(view, adder, addViewDelay)
                }
            }
        }
    }

    override fun animateRemove(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder)
        holder.itemView.alpha = 1f
        holder.itemView.translationX = 0f
        mPendingRemovals.add(holder)
        return true
    }

    open fun animateRemoveImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val prevAlpha = view.alpha
        val animation = view.animate()
        mRemoveAnimations.add(holder)
        val needSkipRemove = skipAnimator?.needSkipRemoveAnimator() ?: false
        /*Log.d(TAG, "animateRemoveImpl needSkipRemove $needSkipRemove mIsFolderInAnimation $mIsFolderInAnimation holder $holder, " +
                "holder LayoutPosition ${holder.layoutPosition}," +
                "holeder bindingAdapterPosition ${holder.bindingAdapterPosition}, holder absAdapterPos: ${holder.absoluteAdapterPosition}")*/
        val animatorListener = object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animator: Animator) {
                dispatchRemoveStarting(holder)
            }

            override fun onAnimationEnd(animator: Animator) {
                view.translationX = 0f
                view.alpha = prevAlpha
                animation.setListener(null)
                dispatchRemoveFinished(holder)
                mRemoveAnimations.remove(holder)
                dispatchFinishedWhenDone()
            }
        }
        val animateDuration = if (mSkipAddRemoveAnimation || needSkipRemove) {
            0
        } else {
            ADD_REMOVE_DURATION
        }
        animation.alpha(0f).setInterpolator(COUIMoveEaseInterpolator()).duration = animateDuration
        if (mIsFolderInAnimation) {
            animation.translationX(ADD_TRANSFORM_DOWN_X_D).setInterpolator(COUIMoveEaseInterpolator()).setDuration(animateDuration)
                    .setListener(animatorListener).start()
        } else {
            animation.translationX(ADD_TRANSFORM_UP_X_D).setInterpolator(COUIMoveEaseInterpolator()).setDuration(animateDuration)
                    .setListener(animatorListener).start()
        }
    }

    override fun animateAdd(holder: RecyclerView.ViewHolder): Boolean {
        resetAnimation(holder)
        holder.itemView.alpha = 0f
        if (mIsFolderInAnimation) {
            holder.itemView.translationX = ADD_TRANSFORM_UP_X_D
        } else {
            holder.itemView.translationX = ADD_TRANSFORM_DOWN_X_D
        }
        mPendingAdditions.add(holder)
        return true
    }

    fun animateAddImpl(holder: RecyclerView.ViewHolder) {
        val view = holder.itemView
        val animationEndAlpha = 1.0f
        val animation = view.animate()
        mAddAnimations.add(holder)
        /*Log.d(TAG, "animateAddImpl mIsFolderInAnimation $mIsFolderInAnimation holder $holder, holder LayoutPosition ${holder.layoutPosition}," +
                "holeder bindingAdapterPosition ${holder.bindingAdapterPosition}, holder absAdapterPos: ${holder.absoluteAdapterPosition}")*/
        val animatorLister = object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animator: Animator) {
                dispatchAddStarting(holder)
            }

            override fun onAnimationCancel(animator: Animator) {
                view.translationX = 0f
                view.alpha = animationEndAlpha
            }

            override fun onAnimationEnd(animator: Animator) {
                view.alpha = animationEndAlpha
                animation.setListener(null)
                dispatchAddFinished(holder)
                mAddAnimations.remove(holder)
                dispatchFinishedWhenDone()
            }
        }
        animation.alpha(animationEndAlpha).setInterpolator(COUIInEaseInterpolator()).duration = if (mSkipAddRemoveAnimation) {
            0
        } else {
            ADD_REMOVE_DURATION
        }
        animation.translationX(0f).setInterpolator(COUIInEaseInterpolator()).setDuration(if (mSkipAddRemoveAnimation) {
            0
        } else {
            ADD_REMOVE_DURATION
        }).setListener(animatorLister).start()
    }

    override fun animateMove(holder: RecyclerView.ViewHolder, fromX: Int, fromY: Int,
                             toX: Int, toY: Int): Boolean {
        var fromX = fromX
        var fromY = fromY
        val view = holder.itemView
        fromX += holder.itemView.translationX.toInt()
        fromY += holder.itemView.translationY.toInt()
        resetAnimation(holder)
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        if (deltaX == 0 && deltaY == 0) {
            dispatchMoveFinished(holder)
            return false
        }
        if (deltaX != 0) {
            view.translationX = -deltaX.toFloat()
        }
        if (deltaY != 0) {
            view.translationY = -deltaY.toFloat()
        }
        mPendingMoves.add(MoveInfo(holder, fromX, fromY, toX, toY))
        return true
    }

    fun animateMoveImpl(holder: RecyclerView.ViewHolder, fromX: Int, fromY: Int, toX: Int, toY: Int) {
        val view = holder.itemView
        val deltaX = toX - fromX
        val deltaY = toY - fromY
        /*Log.d(TAG, "animateMoveImpl mIsFolderInAnimation $mIsFolderInAnimation holder $holder, holder LayoutPosition ${holder.layoutPosition}," +
                "holeder bindingAdapterPosition ${holder.bindingAdapterPosition}, holder absAdapterPos: ${holder.absoluteAdapterPosition}," +
                "fromX: $fromX, fromY: $fromY, toX $toX, toY $toY")*/
        if (deltaX != 0) {
            view.animate().translationX(0f)
        }
        if (deltaY != 0) {
            view.animate().translationY(0f)
        }
        // TODO: make EndActions end listeners instead, since end actions aren't called when
        // vpas are canceled (and can't end them. why?)
        // need listener functionality in VPACompat for this. Ick.
        val animation = view.animate()
        mMoveAnimations.add(holder)
        animation.setDuration(moveDuration).setListener(object : AnimatorListenerAdapter() {
            override fun onAnimationStart(animator: Animator) {
                dispatchMoveStarting(holder)
            }

            override fun onAnimationCancel(animator: Animator) {
                if (deltaX != 0) {
                    view.translationX = 0f
                }
                if (deltaY != 0) {
                    view.translationY = 0f
                }
            }

            override fun onAnimationEnd(animator: Animator) {
                animation.setListener(null)
                dispatchMoveFinished(holder)
                mMoveAnimations.remove(holder)
                dispatchFinishedWhenDone()
            }
        }).start()
    }

    override fun animateChange(oldHolder: RecyclerView.ViewHolder, newHolder: RecyclerView.ViewHolder?,
                               fromX: Int, fromY: Int, toX: Int, toY: Int): Boolean {
        if (oldHolder === newHolder) {
            // Don't know how to run change animations when the same view holder is re-used.
            // run a move animation to handle position changes.
            return animateMove(oldHolder, fromX, fromY, toX, toY)
        }
        val prevTranslationX = oldHolder.itemView.translationX
        val prevTranslationY = oldHolder.itemView.translationY
        val prevAlpha = oldHolder.itemView.alpha
        resetAnimation(oldHolder)
        val deltaX = (toX - fromX - prevTranslationX).toInt()
        val deltaY = (toY - fromY - prevTranslationY).toInt()
        // recover prev translation state after ending animation
        oldHolder.itemView.translationX = prevTranslationX
        oldHolder.itemView.translationY = prevTranslationY
        oldHolder.itemView.alpha = prevAlpha
        if (newHolder != null) {
            // carry over translation values
            resetAnimation(newHolder)
            newHolder.itemView.translationX = -deltaX.toFloat()
            newHolder.itemView.translationY = -deltaY.toFloat()
            newHolder.itemView.alpha = 0f
        }
        mPendingChanges.add(ChangeInfo(oldHolder, newHolder, fromX, fromY, toX, toY))
        return true
    }

    fun animateChangeImpl(changeInfo: ChangeInfo) {
        val holder = changeInfo.oldHolder
        val view = holder?.itemView
        val oldViewAlpha = 1f
        val newHolder = changeInfo.newHolder
        val newView = newHolder?.itemView
        val newViewAlpha = 1f
        /*Log.d(TAG, "animateChangeImpl, mIsFolderInAnimation $mIsFolderInAnimation, oldholder LayoutPosition ${holder.layoutPosition}," +
                "oldholder bindingAdapterPosition ${holder.bindingAdapterPosition}, oldholder absAdapterPos: ${holder.absoluteAdapterPosition}," +
                ", newHolder LayoutPostion ${newHolder?.layoutPosition}, newholder bindingAdapterPosition ${newHolder?.bindingAdapterPosition}" +
                "changeInfo $changeInfo")*/
        if (view != null) {
            val oldViewAnim = view.animate().setDuration(
                    changeDuration)
            changeInfo.oldHolder?.apply {
                mChangeAnimations.add(this)
            }
            oldViewAnim.translationX(changeInfo.toX - changeInfo.fromX.toFloat())
            oldViewAnim.translationY(changeInfo.toY - changeInfo.fromY.toFloat())
            oldViewAnim.alpha(0f).setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationStart(animator: Animator) {
                    dispatchChangeStarting(changeInfo.oldHolder, true)
                }

                override fun onAnimationEnd(animator: Animator) {
                    oldViewAnim.setListener(null)
                    view.alpha = oldViewAlpha
                    view.translationX = 0f
                    view.translationY = 0f
                    dispatchChangeFinished(changeInfo.oldHolder, true)
                    mChangeAnimations.remove(changeInfo.oldHolder)
                    dispatchFinishedWhenDone()
                }
            }).start()
        }
        if (newView != null) {
            val newViewAnimation = newView.animate()
            changeInfo.newHolder?.apply {
                mChangeAnimations.add(this)
            }
            newViewAnimation.translationX(0f).translationY(0f).setDuration(changeDuration)
                    .alpha(newViewAlpha).setListener(object : AnimatorListenerAdapter() {
                        override fun onAnimationStart(animator: Animator) {
                            dispatchChangeStarting(changeInfo.newHolder, false)
                        }

                        override fun onAnimationEnd(animator: Animator) {
                            newViewAnimation.setListener(null)
                            newView.alpha = newViewAlpha
                            newView.translationX = 0f
                            newView.translationY = 0f
                            dispatchChangeFinished(changeInfo.newHolder, false)
                            mChangeAnimations.remove(changeInfo.newHolder)
                            dispatchFinishedWhenDone()
                        }
                    }).start()
        }
    }

    open fun endChangeAnimation(infoList: MutableList<ChangeInfo>, item: RecyclerView.ViewHolder) {
        for (i in infoList.indices.reversed()) {
            val changeInfo = infoList[i]
            if (endChangeAnimationIfNecessary(changeInfo, item)) {
                if (changeInfo.oldHolder == null && changeInfo.newHolder == null) {
                    infoList.remove(changeInfo)
                }
            }
        }
    }

    open fun endChangeAnimationIfNecessary(changeInfo: ChangeInfo) {
        if (changeInfo.oldHolder != null) {
            endChangeAnimationIfNecessary(changeInfo, changeInfo.oldHolder!!)
        }
        if (changeInfo.newHolder != null) {
            endChangeAnimationIfNecessary(changeInfo, changeInfo.newHolder!!)
        }
    }

    open fun endChangeAnimationIfNecessary(changeInfo: ChangeInfo, item: RecyclerView.ViewHolder): Boolean {
        var oldItem = false
        if (changeInfo.newHolder === item) {
            changeInfo.newHolder = null
        } else if (changeInfo.oldHolder === item) {
            changeInfo.oldHolder = null
            oldItem = true
        } else {
            return false
        }
        item.itemView.translationX = 0f
        item.itemView.translationY = 0f
        dispatchChangeFinished(item, oldItem)
        return true
    }

    override fun endAnimation(item: RecyclerView.ViewHolder) {
        val view = item.itemView
        // this will trigger end callback which should set properties to their target values.
        view.animate().cancel()
        val viewAlpha = 1f
        // TODO if some other animations are chained to end, how do we cancel them as well?
        for (i in mPendingMoves.indices.reversed()) {
            val moveInfo: MoveInfo = mPendingMoves[i]
            if (moveInfo.holder === item) {
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(item)
                mPendingMoves.removeAt(i)
            }
        }
        endChangeAnimation(mPendingChanges, item)
        if (mPendingRemovals.remove(item)) {
            view.translationX = 0f
            view.alpha = viewAlpha
            dispatchRemoveFinished(item)
        }
        if (mPendingAdditions.remove(item)) {
            view.translationX = 0f
            view.alpha = viewAlpha
            dispatchAddFinished(item)
        }
        for (i in mChangesList.indices.reversed()) {
            val changes: ArrayList<ChangeInfo> = mChangesList[i]
            endChangeAnimation(changes, item)
            if (changes.isEmpty()) {
                mChangesList.removeAt(i)
            }
        }
        for (i in mMovesList.indices.reversed()) {
            val moves: ArrayList<MoveInfo> = mMovesList[i]
            for (j in moves.indices.reversed()) {
                val moveInfo = moves[j]
                if (moveInfo.holder === item) {
                    view.translationY = 0f
                    view.translationX = 0f
                    dispatchMoveFinished(item)
                    moves.removeAt(j)
                    if (moves.isEmpty()) {
                        mMovesList.removeAt(i)
                    }
                    break
                }
            }
        }
        for (i in mAdditionsList.indices.reversed()) {
            val additions = mAdditionsList[i]
            if (additions.remove(item)) {
                view.translationX = 0f
                view.alpha = viewAlpha
                dispatchAddFinished(item)
                if (additions.isEmpty()) {
                    mAdditionsList.removeAt(i)
                }
            }
        }

        // animations should be ended by the cancel above.
        check(!(mRemoveAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mRemoveAnimations list")
        }
        check(!(mAddAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mAddAnimations list")
        }
        check(!(mChangeAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mChangeAnimations list")
        }
        check(!(mMoveAnimations.remove(item) && DEBUG)) {
            ("after animation is cancelled, item should not be in "
                    + "mMoveAnimations list")
        }
        dispatchFinishedWhenDone()
    }

    open fun resetAnimation(holder: RecyclerView.ViewHolder) {
        if (mDefaultInterpolator == null) {
            mDefaultInterpolator = ValueAnimator().interpolator
        }
        holder.itemView.animate().interpolator = mDefaultInterpolator
        endAnimation(holder)
    }

    override fun isRunning(): Boolean {
        return (!mPendingAdditions.isEmpty()
                || !mPendingChanges.isEmpty()
                || !mPendingMoves.isEmpty()
                || !mPendingRemovals.isEmpty()
                || !mMoveAnimations.isEmpty()
                || !mRemoveAnimations.isEmpty()
                || !mAddAnimations.isEmpty()
                || !mChangeAnimations.isEmpty()
                || !mMovesList.isEmpty()
                || !mAdditionsList.isEmpty()
                || !mChangesList.isEmpty())
    }

    /**
     * Check the state of currently pending and running animations. If there are none
     * pending/running, call [.dispatchAnimationsFinished] to notify any
     * listeners.
     */
    fun dispatchFinishedWhenDone() {
        if (!isRunning) {
            dispatchAnimationsFinished()
        }
    }

    override fun endAnimations() {
        var count = mPendingMoves.size
        for (i in count - 1 downTo 0) {
            val item: MoveInfo = mPendingMoves[i]
            val view = item.holder.itemView
            view.translationY = 0f
            view.translationX = 0f
            dispatchMoveFinished(item.holder)
            mPendingMoves.removeAt(i)
        }
        count = mPendingRemovals.size
        for (i in count - 1 downTo 0) {
            val item = mPendingRemovals[i]
            dispatchRemoveFinished(item)
            mPendingRemovals.removeAt(i)
        }
        count = mPendingAdditions.size
        for (i in count - 1 downTo 0) {
            val item = mPendingAdditions[i]
            item.itemView.alpha = 1f
            item.itemView.translationX = 0f
            dispatchAddFinished(item)
            mPendingAdditions.removeAt(i)
        }
        count = mPendingChanges.size
        for (i in count - 1 downTo 0) {
            endChangeAnimationIfNecessary(mPendingChanges[i])
        }
        mPendingChanges.clear()
        if (!isRunning) {
            return
        }
        var listCount = mMovesList.size
        for (i in listCount - 1 downTo 0) {
            val moves: ArrayList<MoveInfo> = mMovesList[i]
            count = moves.size
            for (j in count - 1 downTo 0) {
                val moveInfo = moves[j]
                val item = moveInfo.holder
                val view = item.itemView
                view.translationY = 0f
                view.translationX = 0f
                dispatchMoveFinished(moveInfo.holder)
                moves.removeAt(j)
                if (moves.isEmpty()) {
                    mMovesList.remove(moves)
                }
            }
        }
        listCount = mAdditionsList.size
        for (i in listCount - 1 downTo 0) {
            val additions = mAdditionsList[i]
            count = additions.size
            for (j in count - 1 downTo 0) {
                val item = additions[j]
                val view = item.itemView
                view.translationX = 0f
                dispatchAddFinished(item)
                additions.removeAt(j)
                if (additions.isEmpty()) {
                    mAdditionsList.remove(additions)
                }
            }
        }
        listCount = mChangesList.size
        for (i in listCount - 1 downTo 0) {
            val changes: ArrayList<ChangeInfo> = mChangesList[i]
            count = changes.size
            for (j in count - 1 downTo 0) {
                endChangeAnimationIfNecessary(changes[j])
                if (changes.isEmpty()) {
                    mChangesList.remove(changes)
                }
            }
        }
        cancelAll(mRemoveAnimations)
        cancelAll(mMoveAnimations)
        cancelAll(mAddAnimations)
        cancelAll(mChangeAnimations)
        dispatchAnimationsFinished()
    }

    fun cancelAll(viewHolders: List<RecyclerView.ViewHolder?>) {
        for (i in viewHolders.indices.reversed()) {
            viewHolders[i]?.itemView?.animate()?.cancel()
        }
    }

    /**
     * {@inheritDoc}
     *
     *
     * If the payload list is not empty, DefaultItemAnimator returns `true`.
     * When this is the case:
     *
     *  * If you override [.animateChange], both
     * ViewHolder arguments will be the same instance.
     *
     *  *
     * If you are not overriding [.animateChange],
     * then DefaultItemAnimator will call [.animateMove] and
     * run a move animation instead.
     *
     *
     */
    override fun canReuseUpdatedViewHolder(viewHolder: RecyclerView.ViewHolder,
                                           payloads: List<Any?>): Boolean {
        return !payloads.isEmpty() || super.canReuseUpdatedViewHolder(viewHolder, payloads)
    }

    /**
     * 注册接口
     */
    fun registerNeedSkipAnimator(needSkipAnimator: INeedSkipAnimator) {
        //Log.d(TAG, "registerNeedSkipAnimator $needSkipAnimator")
        skipAnimator = needSkipAnimator
    }

    /**
     * 解注册接口
     */
    fun unRegisterNeddSkipAnimator() {
        skipAnimator = null
    }

    interface INeedSkipAnimator {

        /**
         * 定义是否需要跳过被Remove的Item动画
         */
        fun needSkipRemoveAnimator(): Boolean
    }
}