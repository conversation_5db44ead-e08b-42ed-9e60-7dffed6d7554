/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.filemanager.common.view.SelectItemLayout
 * * Version     : 1.0
 * * Date        : 2021/7/6
 * * Author      : W9001165
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.util.AttributeSet
import androidx.constraintlayout.widget.ConstraintLayout

open class SelectItemLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private var mIsChecked = false

    fun setChecked(checked: Boolean){
        mIsChecked = checked
    }
}