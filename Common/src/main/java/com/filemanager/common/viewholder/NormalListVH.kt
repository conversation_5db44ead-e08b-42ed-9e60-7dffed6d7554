/*********************************************************************
 * * Copyright (C), 2010-2020, OPPO Mobile Comm Corp., Ltd.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.viewholder
 * * Version     : 1.0
 * * Date        : 2020/9/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.viewholder

import android.content.Context
import android.graphics.Rect
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.core.view.setPadding
import androidx.core.view.updateLayoutParams
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseSelectionRecycleAdapter
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.base.CheckBoxAnimateInput
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.FileWrapper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.imageloader.glide.GlideLoader
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.utils.FileImageLoader
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.LabelVHUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.ViewUtils
import com.filemanager.common.view.FileThumbView
import com.filemanager.common.view.TextViewSnippet
import com.filemanager.common.wrapper.AudioFileWrapper
import com.filemanager.common.wrapper.ImageFileWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference

class NormalListVH(convertView: View, private val mImgRadius: Int = 0) : BaseNormalVH(convertView) {
    private var mImg: FileThumbView = convertView.findViewById(R.id.file_list_item_icon)
    private var mTitle: TextView = convertView.findViewById(R.id.file_list_item_title)
    private var mDetail: TextView = convertView.findViewById(R.id.file_list_item_detail)
    private var mDuration: TextView = convertView.findViewById(R.id.file_duration_tv)
    private var rootView: ConstraintLayout = convertView.findViewById(R.id.normal_list_item_root)
    private var iconContainer: View = convertView.findViewById(R.id.file_list_item_icon_container)
    private val mRtl = Utils.isRtl()
    private val docRadius = MyApplication.sAppContext.resources.getDimensionPixelSize(R.dimen.dimen_2dp)
    private var isDisplayLastOpenTime = false       //是否显示最近打开时间，文档页面排序为打开时间时使用
    var loadDocThumbnail = true

    companion object {
        private const val TAG = "NormalListVH"
        private const val SECOND = 1000L
        fun getLayoutId(): Int {
            return R.layout.normal_scan_list_item
        }

        fun getDisplayTime(file: BaseFileBean, isDisplayLastOpenTime: Boolean): Long = if (isDisplayLastOpenTime) {
            if (file.lastOpenTime == 0L) file.mDateModified else file.lastOpenTime
        } else {
            file.mDateModified
        }
    }

    init {
        mCheckBox = convertView.findViewById(R.id.listview_scrollchoice_checkbox)
    }

    override fun updateViewHolder(context: Context, key: Int?, file: BaseFileBean, choiceMode: Boolean, selectionArray: MutableList<Int>
                                  , sizeCache: HashMap<String, String>, threadManager: ThreadManager, adapter: BaseSelectionRecycleAdapter<*, *>) {
        if (file.mData == null) {
            Log.d(TAG, "updateViewHolder path null")
            return
        }
        updateLeftRightMargin()
        if (file is ImageFileWrapper) {
            mImg.setImgCShotState(file.getCShot() > 0)
        }
        LabelVHUtils.displayLabelFlag(file, context, mTitle)
        if (mRtl) {
            if (!choiceMode) {
                mTitle.setPadding(context.resources.getDimensionPixelSize(R.dimen.default_margin), mTitle.paddingTop, mTitle.paddingRight, mTitle.paddingBottom)
                mDetail.setPadding(context.resources.getDimensionPixelSize(R.dimen.default_margin), mDetail.paddingTop, mTitle.paddingRight, mDetail.paddingBottom)
            } else {
                mTitle.setPadding(context.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end), mTitle.paddingTop, mDetail.paddingRight, mTitle.paddingBottom)
                mDetail.setPadding(context.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end), mDetail.paddingTop, mDetail.paddingRight, mDetail.paddingBottom)
            }
        } else {
            if (!choiceMode) {
                mTitle.setPadding(mTitle.paddingLeft, mTitle.paddingTop, context.resources.getDimensionPixelSize(R.dimen.default_margin), mTitle.paddingBottom)
                mDetail.setPadding(mDetail.paddingLeft, mDetail.paddingTop, context.resources.getDimensionPixelSize(R.dimen.default_margin), mDetail.paddingBottom)
            } else {
                mTitle.setPadding(mTitle.paddingLeft, mTitle.paddingTop, context.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end), mTitle.paddingBottom)
                mDetail.setPadding(mDetail.paddingLeft, mDetail.paddingTop, context.resources.getDimensionPixelSize(R.dimen.category_edit_mode_padding_end), mDetail.paddingBottom)
            }
        }
        mImg.setDrmState(file.mLocalType == MimeTypeHelper.DRM_TYPE)
        FileImageVHUtils.updateFileListImgSize(context, mImg, file)
        mTitle.text = file.mDisplayName
        (mTitle as TextViewSnippet).apply {
            setTextViewStyleForFixedWidth()
            this.post {
                val title = file.mDisplayName ?: return@post
                val moreOne = this.isMoreThanOneLine(title)
                setIconConstraintSet(moreOne)
            }
        }
        mDetail.tag = file.mData
        if (file.mLocalType == MimeTypeHelper.IMAGE_TYPE || file.mLocalType == MimeTypeHelper.VIDEO_TYPE) {
            mImg.setStrokeStyle(FileThumbView.STROKE_2DP)
        } else {
            mImg.setStrokeStyle(FileThumbView.STROKE_NONE)
        }
        if (file.mLocalType == MimeTypeHelper.APPLICATION_TYPE) {
            mImg.let {
                it.setBorderStyle(0F, 0F)
                FileImageLoader.sInstance.clear(context, it)
                mImg.mErrorLoadTimes = 0
                mImg.setCallBack(object : FileThumbView.LoadCallBack {
                    override fun onError() {
                        displayApplicationWithDetail(context, file, sizeCache, threadManager)
                    }
                })
                displayApplicationWithDetail(context, file, sizeCache, threadManager)
            }
        } else {
            val padding = when (file.mLocalType) {
                MimeTypeHelper.AUDIO_TYPE -> {
                    MyApplication.sAppContext.resources.getDimension(R.dimen.file_list_image_padding)
                        .toInt()
                }
                else -> 0
            }
            mImg.setPadding(padding)
            val radius = ViewUtils.getIconRadius(file.mLocalType, docRadius, mImgRadius)
            FileImageLoader.sInstance.clear(context, mImg)
            FileImageLoader.sInstance.displayDefault(
                file,
                mImg,
                0,
                radius,
                FileImageLoader.THUMBNAIL_TYPE_LIST,
                loadDocThumbnail,
                isCoverError = true,
                isSmallDoc = true
            )
            showDetail(context, file, sizeCache, threadManager)
        }
        mImg.scaleType = ImageView.ScaleType.FIT_CENTER
        showDurationIfNeed(file, mDuration)
        mCheckBox?.apply {
            adapter.setCheckBoxAnim(CheckBoxAnimateInput(false, choiceMode, null, this, layoutPosition))
        }
        val alpha = HiddenFileHelper.getAlphaWithHidden(file.mDisplayName, adapter.mIsDarkModel)
        mTitle.alpha = alpha
        mDetail.alpha = alpha
        mImg.alpha = alpha
    }

    private fun displayApplicationWithDetail(
        context: Context,
        file: BaseFileBean,
        sizeCache: HashMap<String, String>,
        threadManager: ThreadManager
    ) {
        FileImageLoader.sInstance.displayApplicationWithDetail(file, mImg,
            { applicationInfoDetail ->
                val tagDetail = mDetail.tag as? String
                if (applicationInfoDetail?.mPath == tagDetail && !TextUtils.isEmpty(applicationInfoDetail?.mApkVersion)) {
                    mDetail.visibility = View.VISIBLE
                    (context as? BaseVMActivity)?.launch(Dispatchers.Main) {
                        applicationInfoDetail?.mPath?.apply {
                            val apkDetail = Utils.formatSize(FileWrapper(this))
                            if (!TextUtils.isEmpty(apkDetail)) {
                                mDetail.text = withContext(Dispatchers.IO) {
                                    Utils.getApplicationDetailFormat(
                                        context,
                                        applicationInfoDetail.mApkName,
                                        applicationInfoDetail.mApkVersion,
                                        FileWrapper(applicationInfoDetail.mPath)
                                    )
                                }
                            }
                        }
                    }
                } else {
                    showDetail(context, file, sizeCache, threadManager)
                }
            })
    }

    private fun setIconConstraintSet(isMoreThanOneLine: Boolean) {
        val constraintSet = ConstraintSet()
        constraintSet.apply {
            clone(rootView)
            clear(R.id.file_list_item_icon, ConstraintSet.TOP)
            clear(R.id.file_list_item_icon, ConstraintSet.BOTTOM)
            if (isMoreThanOneLine) {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    R.id.file_list_item_title,
                    ConstraintSet.TOP
                )
            } else {
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )
                connect(
                    R.id.file_list_item_icon,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )
            }
            applyTo(rootView)
        }
    }

    fun showDetail(context: Context, file: BaseFileBean, sizeCache: HashMap<String, String>
                   , threadManager: ThreadManager) {
        val lastModified = getDisplayTime(file, isDisplayLastOpenTime)
        val size = sizeCache.get(file.mData + file.mSize + lastModified)
        if (!size.isNullOrEmpty()) {
            val dateAndTime = if (lastModified == 0L) "" else Utils.getDateFormat(context, lastModified)
            if (dateAndTime.isEmpty()) {
                mDetail.text = size
            } else {
                mDetail.text =
                    Utils.formatDetail(context, size, dateAndTime)
            }
        } else {
            threadManager.execute(CategoryRunnable(TAG, mDetail, sizeCache, file, file.mData!!, isDisplayLastOpenTime))
        }
    }

    /**
     * ViewHolder reuse causes display exception,holder reset default value
     */
    fun resetVHData() {
        FileImageLoader.sInstance.clear(mImg.context, mImg)
        mTitle.text = ""
        mDetail.text = ""
        mDuration.text = ""
    }

    private fun showDurationIfNeed(file: BaseFileBean, mDuration: TextView) {
        val duration = if ((file is AudioFileWrapper) && (file.mLocalType == MimeTypeHelper.VIDEO_TYPE) && (file.mDuration != null)) {
            file.mDuration?.let {
                if (it > 0) {
                    mDuration.visibility = View.VISIBLE
                    KtUtils.formatVideoTime(file.mDuration!! / SECOND)
                } else {
                    mDuration.visibility = View.GONE
                    null
                }
            }
        } else if (MimeTypeHelper.VIDEO_TYPE == file.mLocalType && file.mMediaDuration != 0L) {
            mDuration.visibility = View.VISIBLE
            KtUtils.formatVideoTime(file.mMediaDuration / SECOND)
        } else {
            mDuration.visibility = View.GONE
            null
        }
        mDuration.text = duration
    }

    class CategoryRunnable : FileRunnable {
        constructor(
            tag: String,
            weakTextView: TextView?,
            sizeCache: HashMap<String, String>,
            file: BaseFileBean,
            path: String,
            isDisplayLastOpenTime: Boolean
        ) : super(Runnable {
            val detail = WeakReference(weakTextView).get()
            if (detail != null) {
                val formatStorageDetail = KtUtils.formatSize(file)
                detail.post(Runnable {
                    val currentPath = detail.tag as? String
                    val lastModified = getDisplayTime(file, isDisplayLastOpenTime)
                    if (path == currentPath) {
                        val dateAndTime = if (lastModified == 0L) "" else Utils.getDateFormat(MyApplication.sAppContext, lastModified)
                        sizeCache.put(path + file.mSize + lastModified, formatStorageDetail)
                        if (dateAndTime.isEmpty()) {
                            detail.text = formatStorageDetail
                        } else {
                            detail.text =
                                Utils.formatDetail(detail.context, formatStorageDetail, dateAndTime)
                        }
                    }
                })
            }
        }, tag)
    }

    override fun isInSelectRegionImpl(event: MotionEvent): Boolean {
        val checkBoxRect = Rect()
        mCheckBox?.getGlobalVisibleRect(checkBoxRect) ?: return false
        return checkBoxRect.contains(event.rawX.toInt(), event.rawY.toInt())
    }

    fun setDisplayLastOpenTime(display: Boolean) {
        isDisplayLastOpenTime = display
    }

    fun setBorderRoundCornerType(roundCornerByCornerType: Boolean, roundCornerType: Int = GlideLoader.ROUND_CONNER_NONE) {
        mImg.setBorderRoundCornerType(roundCornerByCornerType, roundCornerType = roundCornerType)
    }

    fun setSelected(isSelected: Boolean) {
        rootView.isSelected = isSelected
    }

    private fun updateLeftRightMargin() {
        iconContainer.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
        }
        mTitle.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
        mDetail.updateLayoutParams<ConstraintLayout.LayoutParams> {
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }
}