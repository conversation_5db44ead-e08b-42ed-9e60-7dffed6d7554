/*********************************************************************************
 ** Copyright (C), 2019-2029, OPLUS Mobile Comm Corp., Ltd
 ** All rights reserved.
 **
 ** File        : MathUtil.kt
 ** Description : Common Math utility
 ** Version     : 1.0
 ** Date        : 2020/03/23
 ** Author      : <PERSON><PERSON>.Shu@Apps.Gallery3D
 ** TAG         : OPLUS_ARCH_EXTENDS
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                            <date>      <version>   <desc>
 ** ------------------------------------------------------------------------------
 ** Ji<PERSON>.Shu@Apps.Gallery3D             2020/03/23  1.0         OPLUS_ARCH_EXTENDS
 *********************************************************************************/
package com.filemanager.common.view.fastscrolll

/**
 * 基础数学工具类
 */
object MathUtil {
    /**
     * **钳位方法，把src钳位在`[`min, max`]`的范围中。**
     *
     * 适用于[Byte]、[Int]、[Long]、[Float]、[Double]等类型的钳位，同时也适用于能够被默认[Comparator]比较的
     * 引用类型。如果需要比较需要使用特殊方法才能对比的对象，使用
     * *`fun <T : Comparable<*>> clamp(src: T, min: T, max: T, comparator: Comparator<T>): T`*
     *
     * @param src 需要被钳位的数值
     * @param min 钳位范围的最小值
     * @param max 钳位范围的最大值
     * @return 如果src小于等于min，则返回min，如果src大于等于max，则返回max，否则返回src原值
     */
    @JvmStatic
    fun <T : Comparable<*>> clamp(src: T, min: T, max: T): T = when {
        ((src is Float) && src.isNaN()) -> src
        ((src is Double) && src.isNaN()) -> src
        (compareValues(src, min) < 0) -> min
        (compareValues(src, max) > 0) -> max
        else -> src
    }
}
