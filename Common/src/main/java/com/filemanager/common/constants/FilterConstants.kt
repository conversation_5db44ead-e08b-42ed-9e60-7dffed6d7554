/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: FilterConstants.kt
 ** Description:  FilterConstants
 ** Version: 1.0
 ** Date: 2021/5/24
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.constants

object FilterConstants {
    //新增的不能和CategoryClobalSearch下的FilterConstants内值重叠
    const val FILTER_FROM_DOWNLOAD = 1 shl 5
    const val FILTER_FROM_TRANSFER = 1 shl 6
    const val FILTER_FROM_BLUETOOTH = 1 shl 7
    const val FILTER_FROM_PC_CONNECT = 1 shl 8
    const val FILTER_FROM_CURRENT = 1 shl 9
    const val FILTER_FROM_OWORK = 1 shl 10
    //与FilterConstants.FILTER_THIRD_APP_QQ错开
    const val FILTER_FROM_REMOTE_PC_CONTROL = (1 shl 11) + 1
    //多个三方应用会基于FILTER_FROM_SUPER_BASE_VALUE累加
    const val FILTER_FROM_SUPER_BASE_VALUE = 1 shl 21
}