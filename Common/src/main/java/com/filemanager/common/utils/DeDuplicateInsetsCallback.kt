/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : DeDuplicateInsetsCallback.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   wanghonglei                2023/6/14       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.view.View
import androidx.core.view.OnApplyWindowInsetsListener
import androidx.core.view.WindowInsetsCompat
import java.util.Objects

abstract class DeDuplicateInsetsCallback : OnApplyWindowInsetsListener {

    private var lastWindowInsets: WindowInsetsCompat? = null

    override fun onApplyWindowInsets(v: View, insets: WindowInsetsCompat): WindowInsetsCompat {
        if (!Objects.equals(lastWindowInsets, insets)) {
            lastWindowInsets = insets
            onApplyInsets(v, insets)
        }
        // Return the insets so that they keep going down the view hierarchy
        return insets
    }

    /**
     * onApplyInsets
     */
    abstract fun onApplyInsets(v: View, insets: WindowInsetsCompat)
}