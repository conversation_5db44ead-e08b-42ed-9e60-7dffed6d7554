/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: PermissionUtils.kt
 ** Description: The until for permission check
 ** Version: 1.0
 ** Date: 2021/6/25
 ** Author: <EMAIL>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/

package com.filemanager.common.componentutils

import android.app.Activity
import android.content.Intent
import android.util.Log
import com.filemanager.common.utils.SdkUtils

object ExportPrivacyUtils {
    private const val TAG = "OnePlus_PermissionUtils"
    private const val PRIVACY_ACTION = "android.oem.intent.action.OP_LEGAL"
    private const val PRIVACY_NOTICE_TYPE = "op_legal_notices_type"
    private const val PRIVACY_KEY = "key_from_settings"
    private const val PRIVACY_ACTION_T = "com.oplus.bootreg.activity.statementpage"
    private const val PRIVACY_NOTICE_TYPE_T = "statement_intent_flag"
    private const val PRIVACY_PACKAGE_T = "com.coloros.bootreg"
    private const val PRIVACY_STATEMENT_OLD = 2

    fun openExportPrivacyPolicy(activity: Activity): Boolean {
        try {
            if (SdkUtils.isAtLeastT()) {
                Log.d(TAG, "startExportPrivacyPolicyActivity T")
                val intent = Intent(PRIVACY_ACTION_T)
                intent.putExtra(PRIVACY_NOTICE_TYPE_T, PRIVACY_STATEMENT_OLD)
                intent.setPackage(PRIVACY_PACKAGE_T)
                activity.startActivity(intent)
            } else {
                Log.d(TAG, "openExportPrivacyPolicy")
                val intent = Intent(PRIVACY_ACTION)
                intent.putExtra(PRIVACY_NOTICE_TYPE, 3)
                intent.putExtra(PRIVACY_KEY, true)
                activity.startActivity(intent)
            }
        } catch (e: Exception) {
            Log.e(TAG, "openPrivacyPolicy: Failed to open privacy page: ${e.message}")
        }
        return true
    }
}