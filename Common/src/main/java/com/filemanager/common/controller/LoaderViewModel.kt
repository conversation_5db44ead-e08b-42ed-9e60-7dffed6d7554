/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.controller.LoaderViewModel
 * * Description : 统一控制LoaderController的创建，根据传入的ViewModelStoreOwner为宿主，只为宿主保留一个LoaderController。
 * 					如SuperListFragment中使用parentFragment当成owner 所有SuperListFragment使用同一个LoaderController
 * 					如FileBrowserFragment，使用this获取LoaderController，一个Fragment使用一个LoaderController
 * 					LoaderController将在Owner(ViewModel)的生合周期结束时(onCleared())释放
 * * Version     : 1.0
 * * Date        : 2022/11/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller

import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import com.filemanager.common.base.BaseViewModel

class LoaderViewModel : BaseViewModel() {

    companion object {
        fun getLoaderController(owner: ViewModelStoreOwner): LoaderController {
            val viewModel = ViewModelProvider(owner)[LoaderViewModel::class.java]
            return viewModel.getLoadController()
        }
    }

    private var mLoaderController: LoaderController? = null

    fun getLoadController(): LoaderController {
        if (mLoaderController == null) {
            mLoaderController = LoaderController()
        }
        return mLoaderController!!
    }

    override fun onCleared() {
        super.onCleared()
        mLoaderController?.onDestroy()
    }
}