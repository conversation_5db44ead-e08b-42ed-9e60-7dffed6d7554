/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : DriveFileWrapper
 ** Description : DriveFileWrapper
 ** Version     : 1.0
 ** Date        : 2024/04/25 19:42
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/04/25       1.0      create
 ***********************************************************************/
package com.filemanager.common.base

import com.filemanager.common.helper.MimeTypeHelper
import java.util.Objects

open class DriveFileWrapper : BaseFileBean() {
    var id: String = ""
    var source: String = ""
    var title: String = ""
    var type: String = ""
    var url: String = ""

    companion object {
        /**
         * 在线文档
         */
        const val TENCENT_FILE_TYPE_DOC = "doc"

        /**
         * 在线幻灯片
         */
        const val TENCENT_FILE_TYPE_SLIDE = "slide"

        /**
         * 在线表格
         */
        const val TENCENT_FILE_TYPE_SHEET = "sheet"

        /**
         * 在线 PDF
         */
        const val TENCENT_FILE_TYPE_PDF = "pdf"

        /**
         * 文件来源：腾讯文档
         */
        const val SOURCE_TYPE_TENCENT = "tencent"

        /**
         * 文件来源：金山文档
         */
        const val SOURCE_TYPE_KINGSSOFT = "kingssoft"

        /**
         * 秒转成毫秒，需要乘以1000
         */
        const val TIME_CONVERT_VALUE = 1000L
    }


    fun isSupportDownload(): Boolean {
        return if (source == SOURCE_TYPE_TENCENT) {
            this.type == TENCENT_FILE_TYPE_DOC ||
                    this.type == TENCENT_FILE_TYPE_SLIDE ||
                    this.type == TENCENT_FILE_TYPE_SHEET ||
                    this.type == TENCENT_FILE_TYPE_PDF
        } else {
            true
        }
    }

    fun isTencentDocs(): Boolean {
        return Objects.equals(this.source, SOURCE_TYPE_TENCENT)
    }

    fun isKDocs(): Boolean {
        return Objects.equals(this.source, SOURCE_TYPE_KINGSSOFT)
    }

    override fun isDir(): Boolean {
        return super.isDir() || mLocalType == MimeTypeHelper.KDOCS_SHARE_FOLDER_TYPE
    }

    override fun toString(): String {
        return "DriveFileWrapper(id='$id', source='$source', title='$title', type='$type', url='$url')"
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as DriveFileWrapper

        if (id != other.id) return false
        if (source != other.source) return false
        if (title != other.title) return false
        if (type != other.type) return false
        if (url != other.url) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + source.hashCode()
        result = 31 * result + title.hashCode()
        result = 31 * result + type.hashCode()
        result = 31 * result + url.hashCode()
        return result
    }
}