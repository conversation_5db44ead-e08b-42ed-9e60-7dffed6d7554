/***********************************************************
 ** Copyright (C), 2020-2025 Oplus. All rights reserved.
 ** File:  - IDraggingActionOperate.kt
 ** Description: Page scrolling and distraction animation to regain view
 ** Version: 1.0
 ** Date : 2025/04/10
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  zhangyitong  2025/04/10    1.0        create
 ****************************************************************/
package com.filemanager.common.interfaces

import android.view.DragEvent
import android.view.View

interface IDraggingActionOperate {
    fun handleDragEvent(event: DragEvent?): Boolean?

    fun getSelectedItemView(): ArrayList<View>?

    fun setNavigateItemAble()

    fun getDragCurrentPath(): String?
}