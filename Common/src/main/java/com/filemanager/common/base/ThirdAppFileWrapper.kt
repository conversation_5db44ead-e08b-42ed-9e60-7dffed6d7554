/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ThirdAppFileWrapper
 ** Description : 三方应用文件的结果item的UI层级的数据
 ** Version     : 1.0
 ** Date        : 2024/05/22 10:24
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/05/06       1.0      create
 ***********************************************************************/
package com.filemanager.common.base

import android.util.Log
import com.filemanager.common.bean.HighLightItem
import com.filemanager.common.bean.HighLightItems
import com.filemanager.common.bean.SearchResultBean
import com.filemanager.common.helper.MimeTypeHelper

class ThirdAppFileWrapper(searchResultBean: SearchResultBean) : BaseFileBean() {


    companion object {
        const val TAG = "ThirdAppFileWrapper"
    }

    var identification: String = ""
    var sourceName: String = ""
    var sourcePackageName: String = ""
    var thirdFileTime: Long = -1L
    var detectTime: Long = -1L
    var fileSize: Long = -1L
    var titleHighLight: HighLightItem
    var sourceHighLight: HighLightItem

    init {
        this.mDisplayName = searchResultBean.searchDataBean.fileName
        this.mLocalType = MimeTypeHelper.getTypeFromPath(this.mDisplayName)
        this.mData = searchResultBean.searchDataBean.fileName
        this.mDateModified = searchResultBean.searchDataBean.detectTime
        this.mMimeType = MimeTypeHelper.getMimeTypeFromPath(this.mDisplayName)
        this.mSize = searchResultBean.searchDataBean.fileSize

        this.identification = searchResultBean.identification
        this.sourceName = searchResultBean.searchDataBean.sourceName
        this.sourcePackageName = searchResultBean.searchDataBean.sourcePackage
        this.thirdFileTime = searchResultBean.searchDataBean.fileTime
        this.detectTime = searchResultBean.searchDataBean.detectTime
        this.fileSize = searchResultBean.searchDataBean.fileSize
        this.titleHighLight = getTitleHighLight(searchResultBean)
        this.sourceHighLight = getSourcenameHighLight(searchResultBean)
        toString()
    }

    private fun getTitleHighLight(searchResultBean: SearchResultBean): HighLightItem {
        //这里中子还未定，这里先本地写死一个，后续中子联调的时候重新搞一个
        val highLightString = searchResultBean.highLight
        val items = runCatching {
            HighLightItems.parseFromJsonString(highLightString)
        }.getOrNull()
        val result = items?.nameHighLight ?: HighLightItem(searchResultBean.searchDataBean.fileName, "")
        Log.i(TAG, "getTitleHighLight result $result")
        return result
    }


    private fun getSourcenameHighLight(searchResultBean: SearchResultBean): HighLightItem {
        //这里中子还未定，这里先本地写死一个,后续中子联调的时候重新搞一个
        val highLightString = searchResultBean.highLight
        val items = runCatching {
            HighLightItems.parseFromJsonString(highLightString)
        }.getOrNull()
        val result = items?.sourceNameHight ?: HighLightItem(searchResultBean.searchDataBean.sourceName, "")
        Log.i(TAG, "getSourcenameHighLight result $result")
        return result
    }


    override fun toString(): String {
        return "ThirdAppFileWrapper(mDisplayName=$mDisplayName, mLocalType=$mLocalType, sourceName=$sourceName," +
                " sourcePackageName=$sourcePackageName, thirdFileTime=$thirdFileTime," + " detectTime=$detectTime," +
                " fileSize=$fileSize, titleHighLight=$titleHighLight, mLocalType $mLocalType, mData: $mData, mDateModified $mDateModified" +
                " mMimeType=$mMimeType, mSize=$mSize, mFileWrapperViewType=$mFileWrapperViewType, mFileWrapperTypeNum=$mFileWrapperTypeNum," +
                " mHasLabel=$mHasLabel, originPackage=$originPackage)"
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as ThirdAppFileWrapper
        if (mDisplayName != other.mDisplayName) return false
        if (sourceName != other.sourceName) return false
        if (sourcePackageName != other.sourcePackageName) return false
        if (thirdFileTime != other.thirdFileTime) return false
        if (detectTime != other.detectTime) return false
        if (fileSize != other.fileSize) return false
        if (titleHighLight != other.titleHighLight) return false
        if (sourceHighLight != other.sourceHighLight) return false
        return true
    }

    override fun hashCode(): Int {
        var result = mDisplayName.hashCode()
        result = 31 * result + sourceName.hashCode()
        result = 31 * result + sourcePackageName.hashCode()
        result = 31 * result + thirdFileTime.hashCode()
        result = 31 * result + detectTime.hashCode()
        result = 31 * result + fileSize.hashCode()
        result = 31 * result + titleHighLight.hashCode()
        result = 31 * result + sourceHighLight.hashCode()
        return result
    }
}