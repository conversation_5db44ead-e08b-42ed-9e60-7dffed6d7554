/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : OplusIntentCompat
 ** Description : OplusIntentCompat
 ** Version     : 1.0
 ** Date        : 2024/09/12 17:10
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/09/12     1.0      create
 ***********************************************************************/
package com.filemanager.common.compat

object OplusIntentCompat {

    private const val OPLUS_ACTION_MEDIA_SCANNER_SCAN_ALL: String = "oplus.intent.action.MEDIA_SCAN_ALL"
    private const val OPLUS_ACTION_SKIN_CHANGED: String = "oplus.intent.action.SKIN_CHANGED"

    private const val OPPO_ACTION_MEDIA_SCANNER_SCAN_ALL = "oppo.intent.action.MEDIA_SCAN_ALL"
    private const val OPPO_ACTION_SKIN_CHANGED = "oppo.intent.action.SKIN_CHANGED"

    val ACTION_MEDIA_SCANNER_SCAN_ALL: String by lazy {
        CompatUtils.compactSApi({
            OPLUS_ACTION_MEDIA_SCANNER_SCAN_ALL
        }, {
            OPPO_ACTION_MEDIA_SCANNER_SCAN_ALL
        })
    }

    val ACTION_SKIN_CHANGED: String by lazy {
        CompatUtils.compactSApi({
            OPLUS_ACTION_SKIN_CHANGED
        }, {
            OPPO_ACTION_SKIN_CHANGED
        })
    }
}