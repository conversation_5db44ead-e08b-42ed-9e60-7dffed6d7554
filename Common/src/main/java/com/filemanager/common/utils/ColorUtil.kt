/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : ColorUtil.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/5/16
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/5/16      1.0        create
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import com.coui.appcompat.contextutil.COUIContextUtil
import com.coui.appcompat.darkmode.COUIDarkModeUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.R

object ColorUtil {

    const val TAG = "ColorUtil"
    @VisibleForTesting
    const val DARK_MODE_LEVEL_ENHANCE = 0
    @VisibleForTesting
    const val DARK_MODE_LEVEL_MODERATE = 1
    @VisibleForTesting
    const val DARK_MODE_LEVEL_SOFT = 2
    private const val DARK_MODE_LEVEL_KEY = "DarkMode_style_key"
    @VisibleForTesting
    const val DARK_MODE_LEVEL_ENHANCE_COLOR = 0xff1a1a1a.toInt()
    @VisibleForTesting
    const val DARK_MODE_LEVEL_MODERATE_COLOR = 0xff2f2f2f.toInt()
    @VisibleForTesting
    const val DARK_MODE_LEVEL_SOFT_COLOR = 0xff454545.toInt()

    @VisibleForTesting
    const val LABEL_CARD_CHECKED_DARK_MODE_LEVEL_ENHANCE_COLOR = 0xff333333.toInt()
    @VisibleForTesting
    const val LABEL_CARD_CHECKED_DARK_MODE_LEVEL_MODERATE_COLOR = 0xff454545.toInt()
    @VisibleForTesting
    const val LABEL_CARD_CHECKED_DARK_MODE_LEVEL_SOFT_COLOR = 0xff595959.toInt()
    @VisibleForTesting
    const val LABEL_CARD_CHECKED_LIGHT_MODE = 0xfff0f0f0.toInt()

    @JvmStatic
    fun getCouiColorCardBackground(context: Context): Int {
        var color = COUIContextUtil.getAttrColor(context, com.support.appcompat.R.attr.couiColorCard)
        if (COUIDarkModeUtil.isNightMode(context)) {
            when (getDarkModeLevel()) {
                DARK_MODE_LEVEL_ENHANCE -> color = DARK_MODE_LEVEL_ENHANCE_COLOR
                DARK_MODE_LEVEL_MODERATE -> color = DARK_MODE_LEVEL_MODERATE_COLOR
                DARK_MODE_LEVEL_SOFT -> color = DARK_MODE_LEVEL_SOFT_COLOR
            }
        }
        return color
    }

    @JvmStatic
    fun getFileLabelCardBackground(context: Context, isChecked: Boolean = false): Int {
        val color: Int = if (isChecked) {
            if (COUIDarkModeUtil.isNightMode(context)) {
                when (getDarkModeLevel()) {
                    DARK_MODE_LEVEL_ENHANCE -> LABEL_CARD_CHECKED_DARK_MODE_LEVEL_ENHANCE_COLOR
                    DARK_MODE_LEVEL_MODERATE -> LABEL_CARD_CHECKED_DARK_MODE_LEVEL_MODERATE_COLOR
                    DARK_MODE_LEVEL_SOFT -> LABEL_CARD_CHECKED_DARK_MODE_LEVEL_SOFT_COLOR
                    else -> LABEL_CARD_CHECKED_DARK_MODE_LEVEL_ENHANCE_COLOR
                }
            } else {
                LABEL_CARD_CHECKED_LIGHT_MODE
            }
        } else {
            if (COUIDarkModeUtil.isNightMode(context)) {
                when (getDarkModeLevel()) {
                    DARK_MODE_LEVEL_ENHANCE -> DARK_MODE_LEVEL_ENHANCE_COLOR
                    DARK_MODE_LEVEL_MODERATE -> DARK_MODE_LEVEL_MODERATE_COLOR
                    DARK_MODE_LEVEL_SOFT -> DARK_MODE_LEVEL_SOFT_COLOR
                    else -> DARK_MODE_LEVEL_ENHANCE_COLOR
                }
            } else {
                context.getColor(com.support.appcompat.R.color.coui_color_label_on_color)
            }
        }
        return color
    }

    @JvmStatic
    fun getDarkModeLevel() =
        Settings.System.getInt(MyApplication.sAppContext.contentResolver, DARK_MODE_LEVEL_KEY, 0)
}