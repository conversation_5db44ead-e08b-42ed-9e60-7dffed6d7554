/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/3/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller

import android.content.pm.PackageManager
import android.content.pm.ResolveInfo
import android.widget.AdapterView
import androidx.activity.ComponentActivity
import androidx.annotation.VisibleForTesting

import androidx.core.content.FileProvider
import androidx.lifecycle.Lifecycle
import com.coui.appcompat.poplist.COUIPopupListWindow
import com.coui.appcompat.poplist.PopupListItem
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.navigation.NavigationController
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.interfaces.fileoprate.IFileOperate
import com.filemanager.common.thread.BaseThreadTask
import com.filemanager.common.thread.FileRunnable
import com.filemanager.common.thread.ThreadManager
import com.filemanager.common.thread.ThreadPriority
import com.filemanager.common.thread.ThreadType
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.filemanager.common.utils.noMoreAction
import com.oplus.filemanager.interfaze.fileoperate.IFileOperateApi
import java.io.File

class MoreItemController(lifecycle: Lifecycle) : BaseLifeController {
    companion object {
        private const val TAG = "MoreItemController"
        private const val UNIQUE_CODE = "com.filemanager.common.controller.MoreItemController"
        /** 当前工具栏显示数量，5或7，大屏时显示7，小屏时，在更多页面中更改弹出选项 */
        var currentNavToolSize = 0
            get() = field.noMoreAction()

        @JvmStatic
        fun checkSupportCreateShortcut(selectList: List<BaseFileBean>?): Boolean {
            if (selectList == null) return false
            if (selectList.size == 1) {
                val localType = selectList[0].mLocalType
                return localType != MimeTypeHelper.APPLICATION_TYPE
                        && localType != MimeTypeHelper.KDOCS_OTL_TYPE
                        && localType != MimeTypeHelper.KDOCS_KSHEET_TYPE
                        && localType != MimeTypeHelper.KDOCS_DBT_TYPE
                        && localType != MimeTypeHelper.TENCENT_FORM_TYPE
                        && localType != MimeTypeHelper.TENCENT_MIND_TYPE
                        && localType != MimeTypeHelper.TENCENT_FLOWCHART_TYPE
                        && localType != MimeTypeHelper.TENCENT_SMART_SHEET_TYPE
                        && localType != MimeTypeHelper.TENCENT_SMART_CANVAS_TYPE
                        && localType != MimeTypeHelper.KDOCS_SHARE_FOLDER_TYPE
            }
            return false
        }

        @JvmStatic
        fun  checkIfSupportCloud(selectList: List<BaseFileBean>): Boolean {
            //上传到云盘中这里otg的也需要放开，这里去掉otg的判定
            return checkIsDisplayCloudDisk()
        }

        @JvmStatic
        private fun checkIsDisplayCloudDisk(): Boolean =
            PreferencesUtils.getBoolean(key = CommonConstants.CLOUD_FUNCTION_SHOW, default = true)

        @JvmStatic
        fun checkIfSupportEncryption(selectList: List<BaseFileBean>): Boolean {
            return FeatureCompat.sIsSupportEncryption && !JavaFileHelper.hasFolder(selectList)
        }
    }

    private var mPopupWindow: COUIPopupListWindow? = null
    private var mMoreMenuNamesList = mutableListOf<String>()

    init {
        lifecycle.addObserver(this)
    }

    @Deprecated("Only recent fragment use it")
    fun showMoreItemPopupWindow(
        mActivity: BaseVMActivity,
        selectList: List<BaseFileBean>,
        supportConfig: SupportConfig,
        moreItemClickListener: MoreItemClickListener
    ) {
        innerShowMoreItemPopupWindow(
            mActivity,
            selectList,
            supportConfig,
            null,
            moreItemClickListener
        )
    }

    fun showMoreItemPopupWindow(
        activity: ComponentActivity,
        selectList: List<BaseFileBean>,
        supportConfig: SupportConfig,
        fileOperator: IFileOperate
    ) {
        innerShowMoreItemPopupWindow(activity, selectList, supportConfig, fileOperator, null)
    }

    @Suppress("MagicNumber")
    private fun innerShowMoreItemPopupWindow(
        activity: ComponentActivity,
        selectList: List<BaseFileBean>,
        supportConfig: SupportConfig,
        fileOperator: IFileOperate?,
        moreItemClickListener: MoreItemClickListener?
    ) {
        initPopupWindowListener(activity, fileOperator, moreItemClickListener)
        selectList.let {
            val selectCount = it.size
            Log.d(TAG, "MoreItemController selectList size=$selectCount")
            if (selectCount == 0) {
                return
            }
            showItemAfterCheck(selectList, supportConfig, activity)
        }
    }

    @VisibleForTesting
    fun showItemAfterCheck(
        selectList: List<BaseFileBean>,
        supportConfig: SupportConfig,
        activity: ComponentActivity
    ) {
        ThreadManager.sThreadManager.execute(MoreItemControllerThreadTask(FileRunnable({
            val itemList = checkDisplayItems(selectList, supportConfig)
            activity.runOnUiThread {
                if (mPopupWindow?.isShowing != true) {
                    mPopupWindow?.itemList = itemList
                    if (!activity.isFinishing && !activity.isDestroyed) {
                        mPopupWindow?.show(activity.findViewById(R.id.navigation_more))
                    }
                }
            }
        }, TAG)))
    }

    @VisibleForTesting
    fun checkDisplayItems(
        selectList: List<BaseFileBean>,
        supportConfig: SupportConfig
    ): MutableList<PopupListItem> {
        val itemList = mutableListOf<PopupListItem>()
        val allItems = appContext.resources.getStringArray(R.array.mark_action_more_cmcc)
        val allFuncItems = arrayListOf<String>()
        allItems.forEach { item ->
            if (item.equals(appContext.resources.getString(R.string.cloud_disk_item_menu))) {
                if (checkIfSupportCloud(selectList) && supportConfig.isSupportCloudDisk) {
                    Log.d(TAG, "checkDisplayItems SupportCloud")
                    allFuncItems.add(item)
                }
            } else if (item.equals(appContext.resources.getString(R.string.menu_file_list_compress))) {
                if (checkIfSupportCompress(selectList, supportConfig.isSupportCompress)) {
                    Log.d(TAG, "checkDisplayItems SupportCompress")
                    allFuncItems.add(item)
                }
            } else if (item.equals(appContext.resources.getString(R.string.menu_file_list_decompress))) {
                if (checkIfSupportDecompress(selectList)) {
                    Log.d(TAG, "checkDisplayItems SupportDecompress")
                    allFuncItems.add(item)
                }
            } else if (item.equals(appContext.resources.getString(R.string.menu_file_list_rename))) {
                if (checkIfSupportRename(selectList) && (AndroidDataHelper.allowEditAndroidData || !AndroidDataHelper.hasAndroidDataFile(selectList)
                            )
                ) {
                    Log.d(TAG, "checkDisplayItems SupportRename")
                    allFuncItems.add(item)
                }
            } else if (item.equals(appContext.resources.getString(R.string.encryption_item_menu))) {
                if (supportConfig.isSupportEncryption && checkIfSupportEncryption(selectList) && !AndroidDataHelper.hasAndroidDataFile(selectList)
                ) {
                    Log.d(TAG, "checkDisplayItems SupportEncryption")
                    allFuncItems.add(item)
                }
            } else if (item.equals(appContext.resources.getString(R.string.open_with))) {
                if (checkIfSupportOpenByOther(selectList)) {
                    Log.d(TAG, "checkDisplayItems OpenByOther")
                    allFuncItems.add(item)
                }
            } else if (item.equals(appContext.resources.getString(R.string.menu_file_create_shortcut))) {
                // 创建桌面快捷方式： 条件1：仅一个文件按时才会显示 条件2：安装包类型不显示 条件3：腾讯和金山文档不显示
                if (supportConfig.isSupportShortCut && checkSupportCreateShortcut(selectList)) {
                    Log.d(TAG, "show create shortcut")
                    allFuncItems.add(item)
                }
            } else if (currentNavToolSize < NavigationController.SCREEN_MEDIUM_ITEM_SIZE) {
                //小屏时，加上工具栏上缺少的：复制、详细信息两项
                allFuncItems.add(item)
            } else if (currentNavToolSize < NavigationController.SCREEN_LARGE_ITEM_SIZE) {
                //中屏时，加上工具栏上缺少的：详细信息项
                if (item.equals(appContext.resources.getString(R.string.detail_message))) {
                    allFuncItems.add(item)
                }
            }
        }
        mMoreMenuNamesList.clear()
        val builder = PopupListItem.Builder()
        allFuncItems.forEach { item ->
            mMoreMenuNamesList.add(item)
            val listItem = builder.reset()
                .setTitle(item)
                .setIsEnable(true)
                .build()
            itemList.add(listItem)
        }
        return itemList
    }

    private fun checkIfSupportOpenByOther(selectList: List<BaseFileBean>): Boolean {
        if (selectList.size == 1) {
            val fileType = selectList[0].mLocalType
            if ((fileType == MimeTypeHelper.DIRECTORY_TYPE) || (fileType == MimeTypeHelper.COMPRESSED_TYPE)
                || (fileType == MimeTypeHelper.APPLICATION_TYPE)) {
                return false
            } else {
                if (checkIfNotSupportOpenOtherByApi(selectList, fileType)) return false
            }
        } else {
            return false
        }
        return true
    }

    @VisibleForTesting
    fun checkIfNotSupportOpenOtherByApi(selectList: List<BaseFileBean>, fileType: Int = 0): Boolean {
        val firstBean = selectList[0]
        val filePath = firstBean.mData ?: return false
        val localFileUri = firstBean.mLocalFileUri ?: FileProvider.getUriForFile(appContext, KtConstants.AUTHORITIES, File(filePath))
        val intent = Injector.injectFactory<IFileOperateApi>()?.getOpenFileIntent(appContext, firstBean.mLocalType, localFileUri, filePath)
        intent?.let {
            var list: List<ResolveInfo>? =
                appContext.packageManager.queryIntentActivities(intent, PackageManager.MATCH_DEFAULT_ONLY)
            list = filterUnAccessIntent(fileType, list)
            if ((list?.size ?: 0) <= 1) {
                return true
            }
            Log.d(TAG, "checkIfSupportOpenByOther list size ${list?.size}")
        }
        return false
    }

    @VisibleForTesting
    fun filterUnAccessIntent(fileType: Int, list: List<ResolveInfo>?): List<ResolveInfo>? {
        var listFinal = list
        when (fileType) {
            MimeTypeHelper.IMAGE_TYPE -> {
                //图片类型需要过滤掉文件随心开的打开方式
                listFinal = list?.filter { item ->
                    item.activityInfo.packageName != KtConstants.QUICK_PREVIEW_PACKAGE
                }
            }
        }
        return listFinal
    }

    private fun checkIfSupportRename(selectList: List<BaseFileBean>?): Boolean {
        if (selectList == null) return false
        return selectList.size == 1
    }

    private fun checkIfSupportDecompress(selectList: List<BaseFileBean>?): Boolean {
        selectList?.let {
            if (selectList.size == 1) {
                val type = selectList[0].mLocalType
                if (type == MimeTypeHelper.COMPRESSED_TYPE) {
                    return true
                }
            }
        }
        return false
    }

    private fun checkIfSupportCompress(selectList: List<BaseFileBean>?, supportCompress: Boolean): Boolean {
        if (supportCompress) {
            selectList?.let {
                if (selectList.size == 1) {
                    val type = selectList[0].mLocalType
                    if (type == MimeTypeHelper.COMPRESSED_TYPE) {
                        //压缩与解压功能互斥
                        return false
                    }
                }
            }
        }
        return supportCompress
    }

    @VisibleForTesting
    fun initPopupWindowListener(
        activity: ComponentActivity,
        fileOperator: IFileOperate?,
        moreItemClickListener: MoreItemClickListener?
    ) {
        if (mPopupWindow == null) {
            mPopupWindow = COUIPopupListWindow(activity)
            mPopupWindow!!.setDismissTouchOutside(true)
            initClickListener(fileOperator, activity, moreItemClickListener)
        }
    }

    private fun initClickListener(
        fileOperator: IFileOperate?,
        activity: ComponentActivity,
        moreItemClickListener: MoreItemClickListener?
    ) {
        mPopupWindow!!.setOnItemClickListener(AdapterView.OnItemClickListener { _, _, position, _ ->
            if (Utils.isQuickClick(Utils.FRAGMENT_QUICK_CLICK)) {
                return@OnItemClickListener
            }
            if ((position >= 0) && (position < mMoreMenuNamesList.size)) {
                when (mMoreMenuNamesList[position]) {
                    appContext.getString(R.string.menu_file_list_copy) -> {
                        Log.d(TAG, "MoreItemController click copy")
                        fileOperator?.onSelectCopyDir(activity)
                        moreItemClickListener?.onCopy(activity)
                    }
                    appContext.getString(R.string.menu_file_list_compress) -> {
                        Log.d(TAG, "MoreItemController click compress")
                        fileOperator?.onSelectCompressDest(activity)
                        moreItemClickListener?.onClickCompress(activity)
                        StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.COMPRESS_ACTION)
                    }
                    appContext.getString(R.string.menu_file_list_decompress) -> {
                        Log.d(TAG, "MoreItemController click decompress")
                        fileOperator?.onSelectDecompressDest(activity)
                        moreItemClickListener?.onClickDeCompress(activity)
                        StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.UNCOMPRESS_ACTION)
                    }
                    appContext.getString(R.string.menu_file_list_rename) -> {
                        Log.d(TAG, "MoreItemController click rename")
                        fileOperator?.onRename(activity)
                        moreItemClickListener?.onClickRename(activity)
                        StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.RENAME_ACTION)
                    }
                    appContext.getString(R.string.encryption_item_menu) -> {
                        Log.d(TAG, "MoreItemController click encryption")
                        fileOperator?.onEncrypt(activity)
                        moreItemClickListener?.onClickEncryption(activity)
                    }
                    appContext.getString(R.string.detail_message) -> {
                        Log.d(TAG, "MoreItemController click detail")
                        fileOperator?.onDetail(activity)
                        moreItemClickListener?.onClickDetail(activity)
                        StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.DETAIL_ACTION)
                    }
                    appContext.getString(R.string.open_with) -> {
                        Log.d(TAG, "MoreItemController click open with")
                        fileOperator?.onOpenByOther(activity)
                        moreItemClickListener?.onClickOtherOpen(activity)
                        StatisticsUtils.nearMeStatistics(activity, StatisticsUtils.OPEN_WITH_ACTION)
                    }
                    appContext.getString(R.string.cloud_disk_item_menu) -> {
                        Log.d(TAG, "MoreItemController click cloud drive")
                        fileOperator?.onUploadCloudDisk(activity)
                        moreItemClickListener?.onClickCloudDisk(activity)
                    }
                    appContext.getString(R.string.menu_file_create_shortcut) -> {
                        // 点击创建文档快捷方式
                        Log.d(TAG, "MoreItemController click create shortcut")
                        fileOperator?.createShortCut(activity)
                        moreItemClickListener?.createShortCut(activity)
                    }
                }
            }
            mPopupWindow?.dismiss()
        })
    }

    fun isShowPopupWindow(): Boolean {
        return mPopupWindow?.isShowing ?: false
    }

    override fun onDestroy() {
        Log.e(TAG, "onDestroy")
        mPopupWindow?.let {
            if (it.isShowing) {
                it.dismiss()
            }
        }
        mPopupWindow = null
        ThreadManager.sThreadManager.cancelThread(UNIQUE_CODE)
    }

    internal class MoreItemControllerThreadTask(runnable: FileRunnable) :
        BaseThreadTask<Void>(runnable, null, ThreadManager.sThreadManager) {
        override fun getUniqueFlag(): String {
            return UNIQUE_CODE
        }

        override fun getPriority(): ThreadPriority {
            return ThreadPriority.HIGH
        }

        override fun getThreadType(): ThreadType {
            return ThreadType.NORMAL_THREAD
        }

        init {
            runnable.mRunnableName = getUniqueFlag()
        }
    }

    data class SupportConfig(
        val isSupportCompress: Boolean,
        val isSupportCloudDisk: Boolean,
        val isSupportEncryption: Boolean,
        val isSupportShortCut: Boolean
    )
}

/**
 * only recent file implements this interface
 * **/
interface MoreItemClickListener {
    fun onClickCompress(mActivity: ComponentActivity)
    fun onClickDeCompress(mActivity: ComponentActivity)
    fun onClickRename(mActivity: ComponentActivity)
    fun onClickEncryption(mActivity: ComponentActivity): Unit?
    fun onClickDetail(mActivity: ComponentActivity)
    fun onClickOtherOpen(mActivity: ComponentActivity)
    fun onClickCloudDisk(mActivity: ComponentActivity)
    fun onCopy(mActivity: ComponentActivity)
    fun createShortCut(mActivity: ComponentActivity)
}