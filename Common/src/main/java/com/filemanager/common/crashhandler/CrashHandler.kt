/***********************************************************
 ** Copyright (C), 2020-2030 Oplus. All rights reserved..
 ** File: - CrashHandler
 ** Description: XXXXXXXXXXXXXXXXXXXXX.
 ** Version: 1.0
 ** Date : 2024/9/23
 ** Author: <EMAIL>
 **
 ** --------------------- Revision History: -------------------------------
 ** <********>	<NA> 	  <version >	   <desc>
 ****************************************************************/
package com.filemanager.common.crashhandler

import androidx.annotation.Keep
import com.filemanager.common.MyApplication
import com.filemanager.common.crashhandler.SdkCrashCatchUtils.CRASH_COUNT
import com.filemanager.common.crashhandler.SdkCrashCatchUtils.CRASH_TIME
import com.filemanager.common.crashhandler.SdkCrashCatchUtils.LIMIT_CRASH_COUNT
import com.filemanager.common.managers.SPManagerUtil.getValue
import com.filemanager.common.managers.SPManagerUtil.putValue
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.json.JSONObject
import java.lang.Thread.UncaughtExceptionHandler

class CrashHandler : UncaughtExceptionHandler {
    companion object {
        private const val TAG = "CrashHandler"
        const val KEY_CRASH_FILE_MANAGER_APP_VERSION = "key_crash_file_manager_app_version"

        @JvmStatic
        val instance: CrashHandler by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            CrashHandler()
        }
    }

    private var defaultHandler: UncaughtExceptionHandler? = null
    private var crashInfo = ArrayList<SdkCrashInfo>()
    fun init() {
        kotlin.runCatching {
            MainScope().launch(Dispatchers.IO) {
                crashInfo = SdkCrashCatchUtils.loadLocalConfig()
                val isAppVersionUpdate = AppUtils.getVersionCode() > MyApplication.appContext.getValue(KEY_CRASH_FILE_MANAGER_APP_VERSION, 0)
                crashInfo.forEach { crashInfo ->
                    SdkCrashCatchUtils.sp?.let {
                        if (isAppVersionUpdate) {
                            Log.i(TAG, "init,the app version updated，clear crash times")
                            //升级版本清空crash次数重新计数
                            val currentTime = System.currentTimeMillis()
                            val saveJson = JSONObject()
                            saveJson.put(CRASH_COUNT, 0)
                            saveJson.put(CRASH_TIME, currentTime)
                            it.edit().putString(crashInfo.sdkPackageName, saveJson.toString()).commit()
                            MyApplication.appContext.putValue(KEY_CRASH_FILE_MANAGER_APP_VERSION, AppUtils.getVersionCode())
                        }
                    }
                }
                SdkCrashCatchUtils.loadAllCanCallApi()
            }
            defaultHandler = Thread.getDefaultUncaughtExceptionHandler()
            Thread.setDefaultUncaughtExceptionHandler(this)
        }.onFailure {
            Log.e(TAG, "init error:${it.message}")
        }
    }

    override fun uncaughtException(thread: Thread, e: Throwable) {
        val crashSdkInfo = matchSdkException(e)
        if (crashSdkInfo != null) {
            count(crashSdkInfo)
        }
        defaultHandler?.uncaughtException(thread, e)
    }

    private fun count(crashSdkInfo: SdkCrashInfo) {
        kotlin.runCatching {
            SdkCrashCatchUtils.sp?.let {
                val countString = it.getString(crashSdkInfo.sdkPackageName, "")
                val currentTime = System.currentTimeMillis()
                Log.i(TAG, "count countString = $countString")
                if (countString?.isNotEmpty() == true) {
                    val jsonObject = JSONObject(countString)
                    var count = jsonObject.getInt(CRASH_COUNT)
                    val firstCrashTime = jsonObject.getLong(CRASH_TIME)
                    count++
                    if (SdkCrashCatchUtils.isSameDay(firstCrashTime)) {
                        val jo = JSONObject()
                        jo.put(CRASH_COUNT, count)
                        jo.put(CRASH_TIME, firstCrashTime)
                        it.edit().putString(crashSdkInfo.sdkPackageName, jo.toString()).commit()
                        //有超过三次的时候读取一下sp值，更新缓存变量
                        if (count >= LIMIT_CRASH_COUNT) {
                            SdkCrashCatchUtils.loadAllCanCallApi()
                        }
                    } else {
                        val jo = JSONObject()
                        //清空，重新存储第一次崩溃的时间
                        jo.put(CRASH_COUNT, 1)
                        jo.put(CRASH_TIME, currentTime)
                        it.edit().putString(crashSdkInfo.sdkPackageName, jo.toString()).commit()
                    }
                    Log.i(
                        TAG,
                        "count = $count, currentTime = $currentTime, firstCrashTime = $firstCrashTime"
                    )
                } else {
                    val jsonObject = JSONObject()
                    jsonObject.put(CRASH_COUNT, 1)
                    jsonObject.put(CRASH_TIME, currentTime)
                    it.edit().putString(crashSdkInfo.sdkPackageName, jsonObject.toString()).commit()
                }
            }
        }.onFailure {
            Log.e(TAG, "count error:${it.message}")
        }
    }

    private fun matchSdkException(e: Throwable): SdkCrashInfo? {
        kotlin.runCatching {
            val stackTraceElement = e.stackTrace
            stackTraceElement.forEach {
                val className = it.className
                Log.i(TAG, "matchSdkException className = $className")
                crashInfo.forEach { sdkCrashInfo ->
                    Log.i(TAG, "matchSdkException sdkCrashInfo.crashInfo = ${sdkCrashInfo.sdkPackageName}")
                    if (className.startsWith(sdkCrashInfo.sdkPackageName)) {
                        return sdkCrashInfo
                    }
                }
            }
        }.onFailure {
            Log.e(TAG, "matchSdkException error:${it.message}")
        }

        return null
    }

    @Keep
    data class SdkCrashInfo(
        val sdkCrashCount: Int,
        val sdkPackageName: String,
        val firstCrashTime: Long = 0L
    )
}