/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : MainLabelViewMode
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/25
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/7/25      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.filelabel.ui

import android.database.sqlite.SQLiteException
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.filemanager.common.base.BaseStateModel
import com.filemanager.common.base.BaseUiModel
import com.filemanager.common.base.SelectionViewModel
import com.filemanager.common.constants.Constants.LABEL_MAX_COUNT
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.controller.OnLoaderListener
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.OptimizeStatisticsUtil
import com.oplus.dropdrag.SelectionTracker
import com.oplus.filemanager.filelabel.util.checkExist
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.parentchild.bean.CategoryListBean
import com.oplus.filemanager.provider.FileLabelDBHelper
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelEntity
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

class MainLabelViewModel :
    SelectionViewModel<FileLabelEntityWrapper, MainLabelViewModel.LabelUiModel>() {

    companion object {
        const val TAG = "MainLabelViewModel"

        const val ERROR_ADD_FAILED = 20
    }

    val mModeState = BaseStateModel(MutableLiveData(KtConstants.LIST_NORMAL_MODE))
    val errorState = MutableLiveData<Int>()

    fun loadLabels(isNeedRefreshDB: Boolean = false) {
        viewModelScope.launch(Dispatchers.IO) {
            dataLoadState.postValue(OnLoaderListener.STATE_START)
            if (isNeedRefreshDB) {
                deleteAllUnExistFile()
            }
            loadLabelsOnIO()
        }
    }

    @VisibleForTesting
    fun loadLabelsOnIO() {
        val labels = FileLabelDBHelper.getAllLabels()
        wrapLabelFileList(labels.toMutableList())
        OptimizeStatisticsUtil.categoryFileCount(CategoryHelper.CATEGORY_LABEL_FILE, labels.size.toString())
    }

    /**
     * 删除数据库中所有标签下不存在的文件或文件夹
     */
    private suspend fun deleteAllUnExistFile() {
        val unExistFilePaths = arrayListOf<String>()
        val labels = FileLabelDBHelper.getAllLabels()
        for (label in labels) {
            val fileList = FileLabelMappingDBHelper.getFileListByLabelId(label.id)
            fileList?.let {
                for (file in fileList) {
                    if (!checkExist(file)) {
                        unExistFilePaths.add(file.filePath)
                    }
                }
            }
        }
        Log.d(TAG, "deleteAllUnExistFile ${unExistFilePaths.size}")
        if (unExistFilePaths.size > 0) {
            FileLabelMappingDBHelper.removeMappingFileByPaths(unExistFilePaths)
        }
    }

    @VisibleForTesting
    fun getItemKey(fileLabelEntityWrapper: FileLabelEntityWrapper): Int {
        return fileLabelEntityWrapper.label.name.hashCode()
    }

    class LabelUiModel(
        fileList: List<FileLabelEntityWrapper>,
        allEntityList: MutableList<FileLabelEntityWrapper>,
        stateModel: BaseStateModel,
        selectedList: ArrayList<Int> = arrayListOf(),
        keyMap: HashMap<Int, FileLabelEntityWrapper> = hashMapOf()
    ) :
        BaseUiModel<FileLabelEntityWrapper>(fileList, stateModel, selectedList, keyMap) {
        val mAllLabelsWithFilesList: MutableList<FileLabelEntityWrapper> = allEntityList

        fun getAllLabelsCount(): Int {
            return mAllLabelsWithFilesList.size
        }
    }

    override fun getRealFileSize(): Int {
        return uiState.value?.mAllLabelsWithFilesList?.size ?: 0
    }

    override fun loadData() {
        Log.d(TAG, "Selection View Model loadData")
    }

    override fun getRecyclerViewScanMode(): SelectionTracker.LAYOUT_TYPE {
        return SelectionTracker.LAYOUT_TYPE.GRID
    }

    @VisibleForTesting
    fun wrapLabelFileList(data: MutableList<FileLabelEntity>?) {
        if (data != null && data.size > 0) {
            val keyMap = HashMap<Int, FileLabelEntityWrapper>()
            val wrapperList = findLabelWrappers(data)
            postLabelUiModel(wrapperList, arrayListOf(), keyMap)
        } else {
            postLabelUiModel(mutableListOf(), arrayListOf(), hashMapOf())
        }
        dataLoadState.postValue(OnLoaderListener.STATE_DONE)
    }

    @VisibleForTesting
    fun findLabelWrappers(data: MutableList<FileLabelEntity>): MutableList<FileLabelEntityWrapper> {
        val wrapperList = mutableListOf<FileLabelEntityWrapper>()
        data.forEach { fileLabelEntity ->
            val fileList = FileLabelMappingDBHelper.getFileListByLabelId(fileLabelEntity.id)?.filter {
                HiddenFileHelper.isDisplayFile(it.filePath) && File(it.filePath).exists()
            }
            val fileLabelEntityWrapper = FileLabelEntityWrapper(fileLabelEntity)
            fileLabelEntityWrapper.fileList = fileList
            Log.d(TAG, "findLabelWrappers label:${fileLabelEntity.name},fileList:${fileList?.size}")
            wrapperList.add(fileLabelEntityWrapper)
        }
        return wrapperList
    }

    @VisibleForTesting
    fun postLabelUiModel(
        wrapperList: MutableList<FileLabelEntityWrapper>,
        selectedKeyList: ArrayList<Int>,
        keyMap: HashMap<Int, FileLabelEntityWrapper>
    ) {
        val labelUiModel = LabelUiModel(mutableListOf(), wrapperList, mModeState, selectedKeyList, keyMap)
        uiState.postValue(labelUiModel)
    }

    fun addLabel(name: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val resultId = FileLabelDBHelper.insertFileLabel(
                FileLabelEntity(0, name, 0, 0, 0, lastUsedTime = System.currentTimeMillis())
            )
            if (resultId != null) {
                loadLabels()
            } else {
                errorState.postValue(ERROR_ADD_FAILED)
            }
        }
    }

    fun deleteLabel(labelEntry: FileLabelEntity?) {
        if (labelEntry == null) {
            Log.d(TAG, "deleteLabel label entry is null, return!")
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            try {
                FileLabelDBHelper.deleteFileLabel(labelEntry)
            } catch (e: SQLiteException) {
                Log.e(TAG, "deleteLabel:$e")
            }
        }
    }

    /**
     * 保存小屏编辑的标签数据
     */
    fun saveMainEditLabelData(itemsBeanList: MutableList<MainCategoryItemsBean>) {
        viewModelScope.launch(Dispatchers.IO) {
            val labelEntryList = ArrayList<FileLabelEntity>()
            var currentTime = System.currentTimeMillis()
            for (itemBean in itemsBeanList) {
                val labelId = itemBean.dbID
                val labelEntry = uiState.value?.mAllLabelsWithFilesList?.find { it.label.id == labelId }?.label
                labelEntry?.let {
                    it.lastUsedTime = currentTime
                    it.pinTimeStamp = 0L
                    labelEntryList.add(it)
                    currentTime--
                }
            }
            runCatching {
                FileLabelDBHelper.updateFileLabelList(labelEntryList)
            }.onFailure {
                Log.e(TAG, "saveEditLabelData:$it")
            }
        }
    }

    /**
     * 保存侧导编辑的标签数据
     */
    fun saveSideEditLabelData(labelCategoryList: ArrayList<CategoryListBean>) {
        viewModelScope.launch(Dispatchers.IO) {
            val labelEntryList = ArrayList<FileLabelEntity>()
            var currentTime = System.currentTimeMillis()
            for (sideBean in labelCategoryList) {
                val labelEntry = sideBean.labelEntry
                labelEntry?.let {
                    it.lastUsedTime = currentTime
                    it.pinTimeStamp = 0L
                    labelEntryList.add(it)
                    currentTime--
                }
            }
            runCatching {
                FileLabelDBHelper.updateFileLabelList(labelEntryList)
            }.onFailure {
                Log.e(TAG, "saveEditLabelData:$it")
            }
        }
    }

    fun isReachLabelMaxCount(): Boolean {
        return (uiState.value?.getAllLabelsCount() ?: 0) >= LABEL_MAX_COUNT
    }
}