/**************************************************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * File: - FileTraceUtil.java
 * * Description:
 * * Version: 1.0
 * * Date : 2018/05/08
 * * Author: <EMAIL>
 * *
 * * ---------------------Revision History: --------------------------------------------------------
 * *  <author>                            <data>         <version >     <desc>
 * *  <EMAIL>    2018/05/08    1.0       Delete file
 *************************************************************************************************/

package com.filemanager.common.utils;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.text.TextUtils;

import com.filemanager.common.MyApplication;
import com.filemanager.common.compat.FeatureCompat;
import com.filemanager.common.compat.OplusUsbEnvironmentCompat;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.text.SimpleDateFormat;
import java.util.Date;

public class FileTraceUtil {
    private static final String TAG = "FileTraceUtil";

    public static final class TraceAction {
        public static final String MARK_DELETE = "mark_delete";
        public static final String MARK_RECYCLE_DELETE = "mark_recycle_delete";
        public static final String MARK_RECYCLE_AUTO_CLEAN = "mark_recycle_auto_clean";
        public static final String MARK_REMOVE = "mark_remove";
        public static final String MARK_ENCRYPT = "mark_encrypt";
        public static final String MARK_DECRYPT = "mark_decrypt";

    }

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM-dd HH:mm:ss.SSS");
    private static final boolean DEBUG_VERBOSE = false;
    private static final int MAX_TRACE_FILE_COUNT = 3;
    private static final int MAX_TRACE_STATE_FILE_BYTE = 1024 * 1024; // 1M
    private static final String TRACE_FILE_NAME = "trace_FileManager.csv";
    private static final int MAX_TRACE_CACHE_COUNT = 100;
    private volatile static FileTraceUtil sInstance;
    private Context mContext;
    private HandlerThread mWorkerThread;
    private Handler mWorkerHandler;
    private String mVersionString = "";
    private StringBuilder mActionTrace;
    private int mTraceStateCacheCount = 0;

    private FileTraceUtil(Context context) {
        mContext = context;
        mWorkerThread = new HandlerThread("file-trace");
        mWorkerThread.start();
        Looper looper = mWorkerThread.getLooper();
        if (looper != null) {
            mWorkerHandler = new Handler(looper);
        }
    }

    public static FileTraceUtil getInstance() {
        if (sInstance == null) {
            synchronized (FileTraceUtil.class) {
                if (sInstance == null) {
                    sInstance = new FileTraceUtil(MyApplication.getSAppContext());
                }
            }
        }
        return sInstance;
    }

    public void traceAction(String action, final int sceneType, String desId) {
        if (FeatureCompat.getSIsExpRom()) {
            Log.d(TAG, "traceAction: is ExpRom not trace");
            return;
        }

        Log.d(TAG, "traceAction action = " + action + ",sceneType = " + sceneType + ",desId = " + desId);
        if ((mContext == null) || (mWorkerHandler == null)) {
            return;
        }
        String actionString = "";
        if (action != null) {
            actionString = action;
        }
        String desString = "";
        if (desId != null) {
            desString = desId;
        }
        if (DEBUG_VERBOSE) {
            Log.d(TAG, "traceAction action is " + actionString + " scene is " + sceneType + " desString is " + desString);
        }
        final String pamAction = actionString;
        final String pamGuid = desString;
        mWorkerHandler.post(new Runnable() {
            @Override
            public void run() {
                if (mActionTrace == null) {
                    mActionTrace = new StringBuilder();
                }
                if (TextUtils.isEmpty(mVersionString)) {
                    mVersionString = getVersion(mContext, true, true);
                }
                mTraceStateCacheCount++;
                mActionTrace.append(DATE_FORMAT.format(new Date()) + "|" + pamAction + "|" + pamGuid
                        + "|" + sceneType + "\n");
                if (mTraceStateCacheCount >= MAX_TRACE_CACHE_COUNT) {
                    writeTraceToFile();
                }
            }
        });
    }

    public void traceAtThisMoment() {
        if (FeatureCompat.getSIsExpRom()) {
            Log.d(TAG, "traceAtThisMoment: is ExpRom not trace");
            return;
        }

        Log.d(TAG, "traceLayoutAtThisMoment");
        if (mWorkerHandler != null) {
            mWorkerHandler.post(new Runnable() {
                @Override
                public void run() {
                    if (mActionTrace != null) {
                        writeTraceToFile();
                    }
                }
            });
        }
    }

    private boolean isRunningOnTheWorkThread() {
        Thread currentThread = Thread.currentThread();
        return currentThread.equals(mWorkerThread);
    }

    private void deleteTraceFileIfNecessary(boolean deleteAll) {
        if (mContext == null) {
            Log.e(TAG, "deleteTraceFileIfNecessary. The mContext is null!");
            return;
        }

        String cachePath = OplusUsbEnvironmentCompat.getInternalPath(mContext) + "/ColorOS/TraceLog";
        File filesDir = new File(cachePath);
        File file = new File(filesDir, TRACE_FILE_NAME);
        // If the file has space to write, don't need to delete old files.
        if (!deleteAll && (file.length() < MAX_TRACE_STATE_FILE_BYTE)) {
            if (DEBUG_VERBOSE) {
                Log.d(TAG, "deleteTraceFileIfNecessary. There is space to write. The bytes of file is " + file.length());
            }
            return;
        }

        for (int fileIndex = (MAX_TRACE_FILE_COUNT - 1); fileIndex >= 0; fileIndex--) {
            if (fileIndex == 0) {
                file = new File(filesDir, TRACE_FILE_NAME);
            } else {
                file = new File(filesDir, TRACE_FILE_NAME + fileIndex);
            }

            if (file.exists()) {
                if (deleteAll || (fileIndex == (MAX_TRACE_FILE_COUNT - 1))) { // Delete the oldest file
                    try {
                        boolean deleteSuccess = file.delete();
                        if (!deleteSuccess) {
                            Log.w(TAG, "deleteTraceFileIfNecessary. Delete file trace trace file fail! The file is " + file);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "deleteTraceFileIfNecessary.  has exception ：" + e.getMessage());
                    }
                } else { // Rename other filenames plus one
                    boolean renameSuccess = file.renameTo(new File(filesDir, TRACE_FILE_NAME + (fileIndex + 1)));
                    if (!renameSuccess) {
                        Log.w(TAG, "deleteTraceFileIfNecessary. Rename file trace file fail! The file is " + file);
                    }
                }
            } else {
                Log.d(TAG, "deleteTraceFileIfNecessary. The file tracee trace file is not exist. The file is " + file);
            }
        }
    }

    private void writeTraceToFile() {
        if (mContext == null) {
            Log.e(TAG, "writeLayoutsToFile. The mContext is null!");
            return;
        }
        if (!isRunningOnTheWorkThread()) {
            Log.w(TAG, "writeLayoutsToFile. The thread is not work thread!");
            return;
        }

        OutputStreamWriter out = null;
        try {
            // If trace files count is more than max, than delete the earliest trace files first.
            deleteTraceFileIfNecessary(false);

            String cachePath = OplusUsbEnvironmentCompat.getInternalPath(mContext) + "/ColorOS/FileManager";
            File filesDir = new File(cachePath);
            File file = new File(filesDir, TRACE_FILE_NAME);
            FileOutputStream fos = new FileOutputStream(file, true);
            out = new OutputStreamWriter(fos, "UTF-8");
            out.write("VERSION:" + mVersionString + "\n");
            out.write(getActionTraceString());
            out.flush();
        } catch (IOException e) {
            Log.e(TAG, "writeLayoutsToFile. e is " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "writeLayoutsToFile. e is " + e.getMessage());
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    Log.e(TAG, "writeTraceToFile error !");
                }
            }
        }
    }

    private String getActionTraceString() {
        if (mActionTrace == null) {
            Log.e(TAG, "getActionTraceString. The mActionTrace is null!");
            return "";
        }
        String trace = mActionTrace.toString();
        mActionTrace = null;
        mTraceStateCacheCount = 0;
        return trace;
    }

    public void deleteTraceFileIfExpRom() {
        if (!FeatureCompat.getSIsExpRom()) {
            return;
        }

        Log.d(TAG, "deleteTraceFileIfExpRom: start delete trace file");
        // delete /ColorOS/TraceLog trace file
        deleteTraceFileIfNecessary(true);

        // delete /ColorOS/FileManager trace file
        deleteFileManagerTrace();
    }

    private void deleteFileManagerTrace() {
        String cachePath = OplusUsbEnvironmentCompat.getInternalPath(mContext) + "/ColorOS/FileManager";
        File filesDir = new File(cachePath);
        File file = new File(filesDir, TRACE_FILE_NAME);
        if (file.exists()) {
            try {
                boolean deleteSuccess = file.delete();
                if (!deleteSuccess) {
                    Log.w(TAG, "deleteFileManagerTrace. Delete trace file");
                }
            } catch (Exception e) {
                Log.e(TAG, "deleteFileManagerTrace has exception! :" + e.getMessage());
            }
        } else {
            Log.e(TAG, "deleteFileManagerTrace. The trace file is not exist. The file is " + file);
        }
    }

    public static String getVersion(Context context, boolean needDate, boolean needCommit) {
        if (FeatureCompat.getSIsExpRom()) {
            Log.d(TAG, "getVersion: is ExpRom not trace");
            return "";
        }

        final PackageManager pm = context.getPackageManager();
        try {
            final PackageInfo packageInfo = pm.getPackageInfo(context.getPackageName(),
                    PackageManager.GET_META_DATA);
            if ((packageInfo != null) && (packageInfo.versionName != null)) {
                final StringBuilder version = new StringBuilder(packageInfo.versionName);
                if ((packageInfo.applicationInfo != null)
                        && (packageInfo.applicationInfo.metaData != null) && needDate) {
                    if (needCommit) {
                        final Object versionCommitObject = packageInfo.applicationInfo.metaData.get("versionCommit");
                        if (versionCommitObject != null) {
                            String commit = versionCommitObject.toString() + "";
                            if ((!TextUtils.isEmpty(commit)) && (!commit.contains("_"))) {
                                commit = "_" + commit;
                            }
                            version.append(commit);
                        }
                    }
                    String versionDate = "";
                    final Object versionDateObject = packageInfo.applicationInfo.metaData
                            .get("versionDate");
                    if (versionDateObject != null) {
                        versionDate = versionDateObject.toString();
                        version.append("_").append(versionDate);
                    }
                }
                return version.toString();
            }
        } catch (final PackageManager.NameNotFoundException e) {
        }
        return "";
    }
}
