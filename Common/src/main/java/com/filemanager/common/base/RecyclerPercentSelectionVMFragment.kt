/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - RecyclerSelectionVMFragment.kt
 ** Description:
 ** Version: 1.0
 ** Date : 2020/06/02
 ** Author: <PERSON><PERSON><PERSON>.<PERSON>
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/02    1.0     create
 ****************************************************************/

package com.filemanager.common.base

import android.os.Bundle
import android.view.MotionEvent
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import com.filemanager.common.dragselection.DefaultDragListener
import com.filemanager.common.dragselection.DefaultSelectDelegate
import com.filemanager.common.dragselection.FileDragDropScanner
import com.filemanager.common.utils.Log
import com.oplus.dropdrag.OnDragStartListener
import com.oplus.dropdrag.OnItemClickListener
import com.oplus.dropdrag.base.DefaultDetailsLookup
import com.oplus.dropdrag.base.DefaultKeyProvider
import com.oplus.dropdrag.dragdrop.DragDropScanner
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.AnimatorSet
import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import com.filemanager.common.constants.KtConstants
import android.view.animation.LinearInterpolator
import android.widget.ImageView
import androidx.recyclerview.widget.RecyclerView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.controller.navigation.NavigationInterface
import com.filemanager.common.dragselection.DragUtils
import com.filemanager.common.interfaces.OnGetUIInfoListener
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.view.FileManagerPercentWidthRecyclerView
import com.filemanager.common.view.fastscrolll.RecyclerViewFastScroller
import com.oplus.dropdrag.SelectionTracker

abstract class RecyclerPercentSelectionVMFragment<VM : SelectionViewModel<out BaseFileBean, out BaseUiModel<out BaseFileBean>>> :
    BaseVMFragment<VM>(), OnDragStartListener, OnItemClickListener<Int>, OnGetUIInfoListener {
    companion object {
        private const val TAG = "RecyclerSelectionVMFragment"
    }

    protected var mRecyclerView: FileManagerPercentWidthRecyclerView? = null
    protected var mRecyclerViewFastScroller: RecyclerViewFastScroller? = null
    protected var mViewModel: VM? = null
    private var mDragScanner: DragDropScanner<BaseFileBean>? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        mViewModel = createViewModel()
        super.onViewCreated(view, savedInstanceState)
        initSelectionTracker()
    }

    /**
     * 创建viewModel
     */
    protected abstract fun createViewModel(): VM?

    override fun getViewModel() = mViewModel

    @Suppress("TooGenericExceptionCaught")
    private fun initSelectionTracker() {
        mRecyclerView?.let { recyclerView ->
            recyclerView.setMIsSupportDragSlide(true)
            try {
                val keyProvider = DefaultKeyProvider(recyclerView)
                val detailsLookup = DefaultDetailsLookup(recyclerView)
                val trackerBuilder = com.oplus.dropdrag.RecycleSelectionBuilder(
                    javaClass.name,
                    recyclerView,
                    DefaultSelectDelegate(mViewModel),
                    keyProvider,
                    detailsLookup
                )
                trackerBuilder.withSlideSelection(true)
                trackerBuilder.withSlideSelectionStateListener(recyclerView)
                trackerBuilder.withOnDragStartListener(this)
                trackerBuilder.withOnItemClickListener(this)
                PCConnectAction.getItemTouchInterceptor()?.let {
                    trackerBuilder.withOnItemTouchListener(it)
                }
                recyclerView.layoutManager?.let {
                    if ((it is GridLayoutManager) && (it.spanCount > 1)) {
                        SelectionTracker.LAYOUT_TYPE.GRID
                    } else SelectionTracker.LAYOUT_TYPE.LIST
                } ?: SelectionTracker.LAYOUT_TYPE.LIST
                trackerBuilder.build()
            } catch (ex: Exception) {
                Log.w(TAG, "initSelectionTracker error: $ex")
            }
        } ?: Log.w(TAG, "initSelectionTracker error: mRecyclerView is null")
    }

    override fun onDragStart(e: MotionEvent): Boolean {
        Log.d(TAG, "onDragStart dragging:${DragUtils.isDragging}")
        if (DragUtils.isDragging) return false
        mDragScanner?.cancel(true)
        val view = mRecyclerView?.findChildViewUnder(e.x, e.y) ?: return false
        var result = false
        mRecyclerView?.post {
            val position = mRecyclerView?.getChildAdapterPosition(view) ?: 0
            if (position == -1) {
                Log.e(TAG, "onDragStart position is -1")
                result = false
                return@post
            }
            val dragHoldDownFile = mViewModel?.mUiState?.value?.mFileList?.get(position)
            val activityContext = this.activity ?: run { result = false; return@post }
            val selectList = mViewModel?.getSelectItems() ?: run { result = false; return@post }
            val viewMode = mViewModel?.getRecyclerViewScanMode()
            val dragHoldDrawable = if (viewMode == SelectionTracker.LAYOUT_TYPE.LIST) {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_list_item_icon).drawable
            } else {
                view.findViewById<ImageView>(com.filemanager.common.R.id.file_grid_item_icon).drawable
            }

            val itemViewList = ArrayList<View>()
            selectList.forEach { baseFileBean ->
                val fileList = (mRecyclerView?.adapter as? BaseSelectionRecycleAdapter<RecyclerView.ViewHolder, *>)?.mFiles
                val indexOf = fileList?.indexOf(baseFileBean)
                if (indexOf != null && indexOf >= 0 && indexOf < fileList.size) {
                    val viewHolder = mRecyclerView?.findViewHolderForAdapterPosition(indexOf)
                    mRecyclerView?.adapter?.notifyItemChanged(indexOf)
                    if (viewHolder != null) {
                        itemViewList.add(viewHolder.itemView)
                    }
                }
            }
            DragUtils.createSelectedFileList(selectList)
            (baseVMActivity as? NavigationInterface)?.let { mViewModel?.setNavigateItemAble(it) }
            mDragScanner = FileDragDropScanner(
                activityContext,
                DefaultDragListener(
                    activityContext,
                    view,
                    dragHoldDownFile,
                    dragHoldDrawable,
                    getFragmentCategoryType(),
                    e,
                    viewMode
                ).addSelectedView(itemViewList),
                viewMode,
                false
            )
            mDragScanner?.let {
                if (it.addData(selectList)) {
                    it.execute()
                }
            }
            Log.d(TAG, "onDragStart end")
        }
        return result
    }

    override fun getRecyclerView() = mRecyclerView

    @SuppressLint("ObjectAnimatorBinding")
    fun changeActionModeAnim(toolbar: COUIToolbar, runnable: Runnable, isShowAnimation: Boolean? = true) {
        if (isShowAnimation == false) {
            runnable.run()
            return
        }
        toolbar.clearAnimation()
        val toolbarOutAnimation = ObjectAnimator.ofFloat(toolbar, "alpha", 1f, 0f)
        val toolbarInAnimation = ObjectAnimator.ofFloat(toolbar, "alpha", 0f, 1f)
        val toolbarAnimatorSet = AnimatorSet().apply {
            duration = KtConstants.ACTION_MODE_ALPHA_DURATION
            interpolator = LinearInterpolator()
            play(toolbarOutAnimation).before(toolbarInAnimation)
        }

        toolbarOutAnimation.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                runnable.run()
            }
        })
        toolbarAnimatorSet.start()
    }
}