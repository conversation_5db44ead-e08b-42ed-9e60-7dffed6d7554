/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : Constants
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/12/04
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.bean.remotedevice

import com.filemanager.common.constants.Constants
import com.oplus.filemanager.interfaze.remotedevice.IRemoteFileLoadCallback

object Constants {

    const val REMOTE_PATH_PREFIX = Constants.REMOTE_PATH_PREFIX

    //错误码定义
    const val ERROR_CODE_SUC = IRemoteFileLoadCallback.ERROR_CODE_SUC
    const val ERROR_CODE_FAILED_NORMAL = IRemoteFileLoadCallback.ERROR_CODE_FAILED_NORMAL
    const val ERROR_CODE_FAILED_A = IRemoteFileLoadCallback.ERROR_CODE_FAILED_A
    const val ERROR_CODE_CANCEL = IRemoteFileLoadCallback.ERROR_CODE_CANCEL
    const val ERROR_CODE_PASSWORD_ERROR = IRemoteFileLoadCallback.ERROR_CODE_PASSWORD_ERROR
    const val ERROR_CODE_NO_ARG = 4
    const val ERROR_CODE_TIMEOUT = 5
    const val ERROR_CODE_INTERRUPTED = 14
    const val ERROR_CODE_SDK_QUERY_EXCEPTION = 6
    const val ERROR_CODE_NO_SUPPORTED = 7
    const val ERROR_CODE_NO_NETWORK = 8
    const val ERROR_CODE_NO_ROOTPATH_FOUND = 9
    const val ERROR_CODE_ROOTPATH_NOT_IN_CACHE = 10
    const val ERROR_CODE_ROOTPATH_NO_LINK_DEVICE = 11
    const val ERROR_CODE_ROOTPATH_INVOKE_ERROR = 12
    const val ERROR_CODE_ROOTPATH_SDK_ERROR = 13

    fun isResultSuc(errorCode: Int): Boolean {
        return errorCode == ERROR_CODE_SUC
    }

    fun isSdkError(errorCode: Int): Boolean {
        return errorCode == ERROR_CODE_CANCEL || errorCode == ERROR_CODE_FAILED_NORMAL || errorCode == ERROR_CODE_FAILED_A
    }

    fun isNoRootPathError(errorCode: Int): Boolean {
        return errorCode == ERROR_CODE_NO_ROOTPATH_FOUND
    }

    fun isNetWorkError(errorCode: Int): Boolean {
        return errorCode == ERROR_CODE_NO_NETWORK
    }

    fun isPasswordError(errorCode: Int): Boolean {
        return errorCode == ERROR_CODE_PASSWORD_ERROR
    }

    fun isOtherError(errorCode: Int): Boolean {
        return !isResultSuc(errorCode) && !isSdkError(errorCode) && !isNetWorkError(errorCode)
    }
}