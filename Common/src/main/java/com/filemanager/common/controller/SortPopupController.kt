/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.controller
 * * Version     : 1.0
 * * Date        : 2020/4/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.controller

import android.app.Activity
import android.os.Bundle
import android.view.View
import androidx.annotation.VisibleForTesting
import androidx.lifecycle.Lifecycle
import com.filemanager.common.constants.Constants
import com.filemanager.common.sort.SelectItemListener
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortPopup
import com.filemanager.common.utils.KtViewUtils
import com.filemanager.common.utils.Log

class SortPopupController : BaseLifeController {
    companion object {
        private const val TAG = "SortPopupController"
    }

    @VisibleForTesting
    var mSortPopUp: SortPopup? = null

    constructor(lifecycle: Lifecycle) {
        lifecycle.addObserver(this)
    }

    fun showSortPopUp(
        mActivity: Activity,
        tempSortType: Int,
        anchorView: View?,
        categoryType: String,
        selectItemListener: SelectItemListener
    ) {
        val width = KtViewUtils.getWindowSize(mActivity).x
        val bundle = Bundle()
        bundle.putString(SortModeUtils.RECORD_CATEGORY_MODE, categoryType)
        if (tempSortType != 0) {
            bundle.putInt(Constants.TEMP_SORT_TYPE, tempSortType)
        }
        val anchor: View? = anchorView
        bundle.putInt(SortModeUtils.RECORD_DEFAULT_OFFSET, width)
        if (mSortPopUp == null) {
            mSortPopUp = SortPopup(mActivity, bundle)
            mSortPopUp?.setOnPathClickListener(selectItemListener)
        } else {
            mSortPopUp?.initDate(bundle)
        }
        anchor?.post {
            mSortPopUp?.showPopUp(anchor)
        } ?: Log.w(TAG, "anchor is null")
    }

    fun showSortPopUp(
        mActivity: Activity,
        anchorView: View?,
        bundle: Bundle,
        selectItemListener: SelectItemListener
    ) {
        val width = KtViewUtils.getWindowSize(mActivity).x
        bundle.putInt(SortModeUtils.RECORD_DEFAULT_OFFSET, width)
        val anchor: View? = anchorView
        if (mSortPopUp == null) {
            mSortPopUp = SortPopup(mActivity, bundle)
            mSortPopUp?.setOnPathClickListener(selectItemListener)
        } else {
            mSortPopUp?.initDate(bundle)
        }
        anchor?.post {
            mSortPopUp?.showPopUp(anchor)
        } ?: Log.w(TAG, "anchor is null")
    }

    override fun onDestroy() {
        mSortPopUp = null
    }

    /**
     * 当布局切换时，需要隐藏排序弹框
     */
    fun hideSortPopUp() {
        mSortPopUp?.dismissPopUp()
    }
}
