/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/4/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import androidx.core.os.OperationCanceledException
import com.filemanager.common.thread.ExecutorFactory
import com.filemanager.common.thread.ThreadType
import java.util.concurrent.CountDownLatch
import java.util.concurrent.Executor

abstract class FileTaskLoader<D> : FileLoader<D> {

    internal inner class LoadTask : FileAsyncTask<Void, Void, D>(), Runnable {
        private val mDone = CountDownLatch(1)

        // Set to true to indicate that the task has been posted to a handler for
        // execution at a later time.  Used to throttle updates.
        var mWaiting: Boolean = false

        /* Runs on a worker thread */
        override fun doInBackground(vararg params: Void): D? {
            try {
                val data = <EMAIL>()
                return data
            } catch (ex: OperationCanceledException) {
                if (!isCancelled()) {
                    return null
                }
                return null
            }
        }

        override fun onPreExecute() {
            <EMAIL>(this)
        }

        /* Runs on the UI thread */
        override fun onPostExecute(data: D?) {
            try {
                <EMAIL>(this, data)
            } finally {
                mDone.countDown()
            }
        }

        /* Runs on the UI thread */
        override fun onCancelled(data: D?) {
            try {
                <EMAIL>(this, data)
            } finally {
                mDone.countDown()
            }
        }

        /* Runs on the UI thread, when the waiting task is posted to a handler.
         * This method is only executed when task execution was deferred (waiting was true). */
        override fun run() {
            mWaiting = false
            <EMAIL>()
        }

        /* Used for testing purposes to wait for the task to complete. */
        fun waitForLoader() {
            try {
                mDone.await()
            } catch (e: InterruptedException) {
                // Ignore
            }

        }
    }

    private var mExecutor: Executor? = ExecutorFactory.getExecutor(ThreadType.LOADER_THREAD)

    @Volatile
    internal var mTask: LoadTask? = null
    @Volatile
    internal var mCancellingTask: LoadTask? = null

    internal var mUpdateThrottle: Long = 0
    internal var mLastLoadCompleteTime: Long = -10000
    internal var mHandler = Handler(Looper.getMainLooper())

    constructor(context: Context) : super(context) {
    }

    /**
     * Set amount to throttle updates by.  This is the minimum time from
     * when the last [.loadInBackground] call has completed until
     * a new load is scheduled.
     *
     * @param delayMS Amount of delay, in milliseconds.
     */
    fun setUpdateThrottle(delayMS: Long) {
        mUpdateThrottle = delayMS
    }

    override fun onForceLoad() {
        super.onForceLoad()
        cancelLoad()
        mTask = LoadTask()
        executePendingTask()
    }

    override fun onCancelLoad(): Boolean {
        mTask?.let {
            if (!mStarted) {
                mContentChanged = true
            }
            if (mCancellingTask != null) {
                // There was a pending task already waiting for a previous
                // one being canceled; just drop it.
                if (it.mWaiting) {
                    it.mWaiting = false
                    mHandler.removeCallbacks(it)
                }
                mTask = null
                return false
            } else if (it.mWaiting) {
                // There is a task, but it is waiting for the time it should
                // execute.  We can just toss it.
                it.mWaiting = false
                mHandler.removeCallbacks(it)
                mTask = null
                return false
            } else {
                val cancelled = it.cancel(true)
                if (cancelled) {
                    mCancellingTask = it
                    cancelLoadInBackground()
                }
                mTask = null
                return cancelled
            }
        }
        return false
    }

    /**
     * Called if the task was canceled before it was completed.  Gives the class a chance
     * to clean up post-cancellation and to properly dispose of the result.
     *
     * @param data The value that was returned by [.loadInBackground], or null
     * if the task threw [OperationCanceledException].
     */
    fun onCanceled(data: D?) {}

    internal fun executePendingTask() {
        mTask?.let {
            if (mCancellingTask == null) {
                if (it.mWaiting) {
                    it.mWaiting = false
                    mHandler.removeCallbacks(it)
                }
                if (mUpdateThrottle > 0) {
                    val now = SystemClock.uptimeMillis()
                    if (now < mLastLoadCompleteTime + mUpdateThrottle) {
                        // Not yet time to do another load.
                        it.mWaiting = true
                        mHandler.postAtTime(it, mLastLoadCompleteTime + mUpdateThrottle)
                        return
                    }
                }
                it.executeOnExecutor(mExecutor, null)
            }
        }

    }

    internal fun dispatchOnCancelled(task: LoadTask, data: D?) {
        onCanceled(data)
        if (mCancellingTask == task) {
            rollbackContentChanged()
            mLastLoadCompleteTime = SystemClock.uptimeMillis()
            mCancellingTask = null
            deliverCancellation()
            executePendingTask()
        }
    }

    internal fun dispatchOnLoadComplete(task: LoadTask, data: D?) {
        if (mTask != task) {
            dispatchOnCancelled(task, data)
        } else {
            if (mAbandoned) {
                // This cursor has been abandoned; just cancel the new data.
                onCanceled(data)
            } else {
                commitContentChanged()
                mLastLoadCompleteTime = SystemClock.uptimeMillis()
                mTask = null
                deliverResult(data)
            }
        }
    }

    internal fun dispatchOnLoadStart(task: LoadTask) {
        if (mTask === task) {
            deliverStarted()
        }
    }

    /**
     * Called on a worker thread to perform the actual load and to return
     * the result of the load operation.
     *
     * Implementations should not deliver the result directly, but should return them
     * from this method, which will eventually end up calling [.deliverResult] on
     * the UI thread.  If implementations need to process the results on the UI thread
     * they may override [.deliverResult] and do so there.
     *
     * To support cancellation, this method should periodically check the value of
     * [.isLoadInBackgroundCanceled] and terminate when it returns true.
     * Subclasses may also override [.cancelLoadInBackground] to interrupt the load
     * directly instead of polling [.isLoadInBackgroundCanceled].
     *
     * When the load is canceled, this method may either return normally or throw
     * [OperationCanceledException].  In either case, the [Loader] will
     * call [.onCanceled] to perform post-cancellation cleanup and to dispose of the
     * result object, if any.
     *
     * @return The result of the load operation.
     *
     * @throws OperationCanceledException if the load is canceled during execution.
     *
     * @see .isLoadInBackgroundCanceled
     *
     * @see .cancelLoadInBackground
     *
     * @see .onCanceled
     */
    abstract fun loadInBackground(): D?

    /**
     * Calls [.loadInBackground].
     *
     * This method is reserved for use by the loader framework.
     * Subclasses should override [.loadInBackground] instead of this method.
     *
     * @return The result of the load operation.
     *
     * @throws OperationCanceledException if the load is canceled during execution.
     *
     * @see .loadInBackground
     */
    protected fun onLoadInBackground(): D? {
        return loadInBackground()
    }

    /**
     * Called on the main thread to abort a load in progress.
     *
     * Override this method to abort the current invocation of [.loadInBackground]
     * that is running in the background on a worker thread.
     *
     * This method should do nothing if [.loadInBackground] has not started
     * running or if it has already finished.
     *
     * @see .loadInBackground
     */
    fun cancelLoadInBackground() {}

    /**
     * Returns true if the current invocation of [.loadInBackground] is being canceled.
     *
     * @return True if the current invocation of [.loadInBackground] is being canceled.
     *
     * @see .loadInBackground
     */
    fun isLoadInBackgroundCanceled(): Boolean {
        return mCancellingTask != null
    }

    /**
     * Locks the current thread until the loader completes the current load
     * operation. Returns immediately if there is no load operation running.
     * Should not be called from the UI thread: calling it from the UI
     * thread would cause a deadlock.
     *
     *
     * Use for testing only.  **Never** call this from a UI thread.
     *
     * @hide
     */
    fun waitForLoader() {
        val task = mTask
        task?.waitForLoader()
    }
}