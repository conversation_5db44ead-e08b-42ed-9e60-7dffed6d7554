/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/9/9
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.view

import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.content.res.Configuration
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.animation.LinearInterpolator
import androidx.annotation.ColorInt
import androidx.annotation.VisibleForTesting
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import com.coui.appcompat.contextutil.COUIContextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_ALL
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_LEFT_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_LEFT_TOP_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_NONE
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_NONE_RECENT_IMAGE
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_RIGHT_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY
import com.filemanager.common.imageloader.glide.GlideLoader.ROUND_CONNER_RIGHT_TOP_ONLY
import com.filemanager.common.imageloader.glide.RoundRectUtil
import com.filemanager.common.utils.KtThumbnailHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.Utils

open class FileThumbView : AppCompatImageView {
    companion object {
        private const val TAG = "FileThumbView"
        private const val MAX_BITMAP_SIZE = 100 * 1024 * 1024 // 100 MB
        val DEFAULT_BORDER_SIZE = MyApplication.sAppContext.resources.getDimension(R.dimen.divider_stroke_width)
        const val STROKE_NONE = 0
        const val STROKE_RADIUS = 1
        const val STROKE_RECT = 2
        const val STROKE_4DP = 3
        const val STROKE_6DP = 4
        const val MAX_RELOAD_TIMES = 5
        const val STROKE_2DP = 6
        private const val FADE_IN_DURATION = 100L
        private const val FADE_OUT_DURATION = 100L
        private const val DARK_MODE_ALPHA = 50
        private const val LIGHT_MODE_ALPHA = 40
    }

    @VisibleForTesting
    var mStrokeStyle = STROKE_NONE

    @VisibleForTesting
    var mBorderRadius = 0F

    @VisibleForTesting
    var mBorderSize = 0F

    @VisibleForTesting
    @ColorInt
    var mBorderColor: Int = 0

    private var mIsShowLabelFlag = false
    // Drm
    private var mDrmState = false
    private var mDrmDrawable: Drawable? = null

    // Image cshot
    private var mImgCShotState = false
    private var mImgCShotDrawable: Drawable? = null
    private var mFileLabelFlagDrawable: Drawable? = null

    private var isRoundCornerByCornerType = true
    private var mRoundCornerType = ROUND_CONNER_ALL

    private val mPaint by lazy {
        Paint().apply {
            isAntiAlias = true
            color = context.getColor(R.color.color_text_ripple_bg_color)
            style = Paint.Style.STROKE
            strokeWidth = context.resources.getDimension(R.dimen.divider_background_height)
        }
    }

    private var mCallBack: LoadCallBack? = null
    var mErrorLoadTimes = 0

    private val mDefaultBorderColor by lazy {
        COUIContextUtil.getAttrColor(
            context,
            com.support.appcompat.R.attr.couiColorDivider
        )
    }
    // 新增悬停相关属性
    private val hoverPaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.hover_color)
        isAntiAlias = true
    }
    private var isHovered = false
    private var currentAlpha = 0
    private var currentMaxAlpha = 0 // 动态最大透明度值
    private lateinit var fadeInAnimator: ValueAnimator
    private lateinit var fadeOutAnimator: ValueAnimator

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        try {
            val a = context.obtainStyledAttributes(attrs, R.styleable.FileThumbView)
            mBorderRadius = a.getDimension(R.styleable.FileThumbView_border_radius, mBorderRadius)
            mBorderSize = a.getDimension(R.styleable.FileThumbView_border_size, mBorderSize)
            mBorderColor = a.getColor(R.styleable.FileThumbView_border_color, mDefaultBorderColor)
            mStrokeStyle = a.getInt(R.styleable.FileThumbView_oplus_thumb_stroke_style, -1)
            a.recycle()

            setBorderInfo()
            updateHoverColorForTheme()
            initHover()
        } catch (e: Exception) {
            Log.e(TAG, "constructor", e)
        }
    }
    private fun updateHoverColorForTheme() {
        val isDarkMode = (resources.configuration.uiMode and
                Configuration.UI_MODE_NIGHT_MASK) == Configuration.UI_MODE_NIGHT_YES

        currentMaxAlpha = if (isDarkMode) {
            DARK_MODE_ALPHA
        } else {
            LIGHT_MODE_ALPHA
        }

        // 初始化动画器
        initAnimators()
    }

    private fun initAnimators() {
        fadeInAnimator = ValueAnimator.ofInt(0, currentMaxAlpha).apply {
            duration = FADE_IN_DURATION
            interpolator = LinearInterpolator()
            addUpdateListener { animation ->
                if (!isHovered) return@addUpdateListener
                val alpha = animation.animatedValue as Int
                hoverPaint.alpha = alpha
                currentAlpha = alpha
                invalidate()
            }
        }

        fadeOutAnimator = ValueAnimator.ofInt(currentMaxAlpha, 0).apply {
            duration = FADE_OUT_DURATION
            interpolator = LinearInterpolator()
            addUpdateListener { animation ->
                if (isHovered) return@addUpdateListener
                val alpha = animation.animatedValue as Int
                hoverPaint.alpha = alpha
                currentAlpha = alpha
                invalidate()
            }
        }
    }
    private fun initHover() {
        isHovered = false
        isFocusable = true
        isFocusableInTouchMode = true
    }

    override fun onHoverEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_HOVER_ENTER -> {
                isForceDarkAllowed = false
                isHovered = true
                fadeOutAnimator.cancel()
                fadeInAnimator.start()
                return true
            }
            MotionEvent.ACTION_HOVER_EXIT -> {
                isForceDarkAllowed = true
                isHovered = false
                fadeInAnimator.cancel()
                fadeOutAnimator.start()
                return true
            }
        }
        return super.onHoverEvent(event)
    }

    override fun dispatchDraw(canvas: Canvas) {
        super.dispatchDraw(canvas)
        if (currentAlpha > 0) {
            val path = RoundRectUtil.getPath(
                0f, 0f,
                width.toFloat(), height.toFloat(),
                mBorderRadius,
                tl = true, tr = true, bl = true, br = true
            )
            canvas.drawPath(path, hoverPaint)
        }
    }

    override fun onConfigurationChanged(newConfig: Configuration?) {
        super.onConfigurationChanged(newConfig)
        // 取消所有正在运行的动画
        fadeInAnimator.cancel()
        fadeOutAnimator.cancel()

        // 安全更新主题和动画器
        updateHoverColorForTheme()

        // 根据当前悬停状态重置动画状态
        if (isHovered) {
            hoverPaint.alpha = currentMaxAlpha
        } else {
            hoverPaint.alpha = 0
        }
        invalidate()
    }

    private fun setBorderInfo(borderSize: Float = context.resources.getDimension(R.dimen.divider_stroke_width)) {
        when (mStrokeStyle) {
            STROKE_RADIUS -> {
                mBorderRadius = (KtThumbnailHelper.DEFAULT_ROUND_RADIUS / KtThumbnailHelper.DEFAULT_IMAGE_SIZE) * measuredWidth
                if (mBorderSize == 0F) {
                    mBorderSize = borderSize
                }
            }
            STROKE_RECT -> {
                //do nothing for radius. FileThumbView_border_radius is priority
                mBorderRadius = 0F
                if (mBorderSize == 0F) {
                    mBorderSize = borderSize
                }
            }
            STROKE_2DP -> {
                //list 圆角是 2 dp
                mBorderRadius = context.resources.getDimensionPixelSize(R.dimen.dimen_2dp).toFloat()
                if (mBorderSize == 0F) {
                    mBorderSize = borderSize
                }
            }
            STROKE_4DP -> {
                //list 圆角是 file_list_bg_radius 4
                mBorderRadius = context.resources.getDimensionPixelSize(R.dimen.file_list_bg_radius).toFloat()
                if (mBorderSize == 0F) {
                    mBorderSize = borderSize
                }
            }
            STROKE_6DP -> {
                // grid 圆角是 scan_grid_bg_radius 6
                mBorderRadius = context.resources.getDimensionPixelSize(R.dimen.scan_grid_bg_radius).toFloat()
                if (mBorderSize == 0F) {
                    mBorderSize = borderSize
                }
            }
            STROKE_NONE -> {
                //do not draw
                mBorderSize = 0F
            }
            else -> {
                //do nothing
            }
        }
        mPaint.color = mBorderColor
        mPaint.strokeWidth = mBorderSize
    }

    fun setBorderStyle(
        borderRadius: Float,
        borderSize: Float = context.resources.getDimension(R.dimen.divider_stroke_width),
        @ColorInt borderColor: Int = mDefaultBorderColor
    ) {
        mBorderRadius = borderRadius
        mBorderSize = borderSize
        mBorderColor = borderColor
        mPaint.color = mBorderColor
        mPaint.strokeWidth = mBorderSize
    }

    /**
     * One of STROKE_NONE, STROKE_RADIUS or STROKE_RECT
     *
     * set radius and border will be better
     */
    fun setStrokeStyle(stroke: Int) {
        mStrokeStyle = stroke
        setBorderInfo()
    }

    fun setDrmState(isDrm: Boolean) {
        mDrmState = isDrm
        if (isDrm && mDrmDrawable == null) {
            mDrmDrawable = ContextCompat.getDrawable(context, R.drawable.ic_drm_thumb)
        }
    }

    fun setImgCShotState(isCShot: Boolean) {
        mImgCShotState = isCShot
        if (isCShot && mImgCShotDrawable == null) {
            mImgCShotDrawable = ContextCompat.getDrawable(context, R.drawable.ic_cshot_btn)
        }
    }

    override fun onDraw(canvas: Canvas) {
        canvas.apply {
            onDrawImageResource(this)
            onDrawOther(this)
        }
    }

    @SuppressLint("WrongCall")
    protected open fun onDrawImageResource(canvas: Canvas) {
        if (drawable is BitmapDrawable) {
            (drawable as BitmapDrawable).bitmap.apply {
                when {
                    ((this == null) || isRecycled) -> {
                        Log.e(TAG, "onDraw: BitmapDrawable is null or recycled, recycled=$isRecycled")
                        if (mErrorLoadTimes < MAX_RELOAD_TIMES) {
                            mErrorLoadTimes++
                            mCallBack?.onError()
                        }
                    }
                    (byteCount > MAX_BITMAP_SIZE) -> {
                        // MAX_BITMAP_SIZE defined in android/frameworks/base/graphics/java/android/graphics/RecordingCanvas.java
                        Log.e(TAG, "onDraw: BitmapDrawable size is big than $MAX_BITMAP_SIZE")
                    }
                    else -> {
                        super.onDraw(canvas)
                    }
                }
            }
        } else {
            super.onDraw(canvas)
        }
    }

    protected open fun onDrawOther(canvas: Canvas) {
        drawImgCShot(canvas)
        drawDrm(canvas)
        drawFileLabel(canvas)
        if (isRoundCornerByCornerType) {
            drawCornerBorderByType(canvas, mRoundCornerType)
        } else {
            drawBorder(canvas)
        }
    }

    private fun drawDrm(canvas: Canvas) {
        if (mDrmState) {
            mDrmDrawable?.let {
                drawDrawable(canvas, it, 0, 0, it.intrinsicWidth, it.intrinsicHeight)
            }
        }
    }

    private fun drawImgCShot(canvas: Canvas) {
        if (mImgCShotState) {
            mImgCShotDrawable?.let {
                drawDrawable(canvas, it, 0, height - it.intrinsicHeight, it.intrinsicWidth, height)
            }
        }
    }

    private fun drawBorder(canvas: Canvas) {
        val strokeWith = mPaint.strokeWidth / 2
        if (strokeWith > 0) {
            roundCornerAll(canvas, strokeWith)
        }
    }

    private fun drawCornerBorderByType(canvas: Canvas, roundCornerType: Int) {
        val strokeWith = mPaint.strokeWidth / 2
        if (strokeWith > 0) {
            when (roundCornerType) {
                ROUND_CONNER_ALL -> {
                    roundCornerAll(canvas, strokeWith)
                }
                ROUND_CONNER_LEFT_TOP_ONLY -> {
                    roundCornerLeftTopOnly(strokeWith, canvas)
                }
                ROUND_CONNER_RIGHT_TOP_ONLY -> {
                    roundCornerRightTopOnly(strokeWith, canvas)
                }
                ROUND_CONNER_LEFT_BOTTOM_ONLY -> {
                    roundCornerLeftBottomOnly(strokeWith, canvas)
                }
                ROUND_CONNER_RIGHT_BOTTOM_ONLY -> {
                    roundCornerRightBottomOnly(strokeWith, canvas)
                }
                ROUND_CONNER_LEFT_TOP_BOTTOM_ONLY -> {
                    roundCornerLeftTopBottomOnly(strokeWith, canvas)
                }
                ROUND_CONNER_RIGHT_TOP_BOTTOM_ONLY -> {
                    roundCornerRightTopBottomOnly(strokeWith, canvas)
                }
                ROUND_CONNER_NONE -> {
                    // No rounded corners, no need for smooth rounded corners.
                }
                ROUND_CONNER_NONE_RECENT_IMAGE -> roundCornerNoneRecentImage(canvas, strokeWith)
            }
        }
    }

    private fun roundCornerAll(canvas: Canvas, strokeWith: Float) {
        drawSmoothRoundedCorners(
            canvas,
            mBorderRadius,
            strokeWith,
            mPaint,
            tl = true,
            tr = true,
            bl = true,
            br = true
        )
    }

    private fun roundCornerNoneRecentImage(canvas: Canvas, strokeWith: Float) {
        drawSmoothRoundedCorners(
            canvas,
            0f,
            strokeWith,
            mPaint,
            tl = true,
            tr = true,
            bl = true,
            br = true
        )
    }

    private fun roundCornerRightTopBottomOnly(
        strokeWith: Float,
        canvas: Canvas
    ) {
        drawSmoothRoundedCorners(
            canvas,
            mBorderRadius,
            strokeWith,
            mPaint,
            tl = false,
            tr = true,
            bl = false,
            br = true
        )
    }

    private fun roundCornerLeftTopBottomOnly(
        strokeWith: Float,
        canvas: Canvas
    ) {
        drawSmoothRoundedCorners(
            canvas,
            mBorderRadius,
            strokeWith,
            mPaint,
            tl = true,
            tr = false,
            bl = true,
            br = false
        )
    }

    private fun roundCornerRightBottomOnly(strokeWith: Float, canvas: Canvas) {
        drawSmoothRoundedCorners(
            canvas,
            mBorderRadius,
            strokeWith,
            mPaint,
            tl = false,
            tr = false,
            bl = false,
            br = true
        )
    }

    private fun roundCornerLeftBottomOnly(strokeWith: Float, canvas: Canvas) {
        drawSmoothRoundedCorners(
            canvas,
            mBorderRadius,
            strokeWith,
            mPaint,
            tl = false,
            tr = false,
            bl = true,
            br = false
        )
    }

    private fun roundCornerRightTopOnly(strokeWith: Float, canvas: Canvas) {
        drawSmoothRoundedCorners(
            canvas,
            mBorderRadius,
            strokeWith,
            mPaint,
            tl = false,
            tr = true,
            bl = false,
            br = false
        )
    }

    private fun roundCornerLeftTopOnly(strokeWith: Float, canvas: Canvas) {
        drawSmoothRoundedCorners(
            canvas,
            mBorderRadius,
            strokeWith,
            mPaint,
            tl = true,
            tr = false,
            bl = false,
            br = false
        )
    }

    protected fun drawDrawable(canvas: Canvas, drawable: Drawable, left: Int, top: Int, right: Int, bottom: Int) {
        if (Utils.isRtl()) {
            drawable.setBounds(width - right, top, width - left, bottom)
        } else {
            drawable.setBounds(left, top, right, bottom)
        }
        drawable.draw(canvas)
    }

    fun setBorderRoundCornerType(roundCornerByCornerType: Boolean = false, roundCornerType: Int = ROUND_CONNER_ALL) {
        isRoundCornerByCornerType = roundCornerByCornerType
        mRoundCornerType = roundCornerType
    }

    @Suppress("LongParameterList")
    private fun drawSmoothRoundedCorners(
        c: Canvas,
        radius: Float,
        strokeWith: Float,
        paint: Paint,
        tl: Boolean,
        tr: Boolean,
        bl: Boolean,
        br: Boolean
    ) {
        val save = c.save()
        val path = RoundRectUtil.getPath(
            strokeWith,
            strokeWith,
            width - strokeWith,
            height - strokeWith,
            radius,
            if (Utils.isRtl()) tr else tl,
            if (Utils.isRtl()) tl else tr,
            if (Utils.isRtl()) br else bl,
            if (Utils.isRtl()) bl else br
        )
        c.drawPath(path, paint)
        c.restoreToCount(save)
    }

    fun setCallBack(loadCallBack: LoadCallBack) {
        mCallBack = loadCallBack
    }

    fun setFileLabelFlag(isShowLabelFlag: Boolean) {
        mIsShowLabelFlag = isShowLabelFlag
        if (isShowLabelFlag && mFileLabelFlagDrawable == null) {
            mFileLabelFlagDrawable = ContextCompat.getDrawable(context, R.drawable.ic_file_label_flag_on_img)
        }
    }

    private fun drawFileLabel(canvas: Canvas) {
        if (!mIsShowLabelFlag) return
        mFileLabelFlagDrawable?.let { drawable ->
            val marginTopAndEnd = resources.getDimension(R.dimen.dimen_2dp).toInt()
            drawDrawable(
                canvas, drawable,
                width - drawable.intrinsicWidth - marginTopAndEnd,
                marginTopAndEnd,
                width - marginTopAndEnd,
                drawable.intrinsicHeight + marginTopAndEnd
                        )
        }
    }

    interface LoadCallBack {
        fun onError()
    }
}