/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : EdgeToEdgeDialogFragment
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/6/11
 * * Author      : ********
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.base.edge

import android.os.Bundle
import android.view.View
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.filemanager.common.utils.Log

open class EdgeToEdgeDialogFragment : COUIBottomSheetDialogFragment() {
    companion object {
        const val TAG = "EdgeDialogFragment"
    }

    var content: View? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        Log.d(TAG, "onViewCreated")
        content = view
    }
}