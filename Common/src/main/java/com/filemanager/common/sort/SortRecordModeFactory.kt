/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.filemanager.common.sort
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/9/28
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.sort

import com.filemanager.common.MyApplication
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.Log
import java.util.Objects
import java.util.concurrent.CopyOnWriteArrayList

object SortRecordModeFactory {

    private const val TAG = "SortRecordModeFactory"

    private const val KEY_EMULATED = "/storage/emulated/0"
    private const val KEY_CATEGORY = "category_sort_"
    private const val KEY_SUPER = "super_sort_"
    private const val KEY_LABEL = "label_sort"
    private const val KEY_SHORTCUT_FOLDER = "shortcut_folder"
    private var allPath = CopyOnWriteArrayList<String?>()
    private var rootInternalPath: String? = ""

    fun resetAllPath() {
        allPath.clear()
        rootInternalPath = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
        rootInternalPath?.let {
            allPath.add(it)
        }
        val rootExternalPath = VolumeEnvironment.getExternalSdPath(MyApplication.sAppContext)
        rootExternalPath?.let {
            allPath.add(rootExternalPath)
        }
        val rootOtgPath = VolumeEnvironment.getOTGPath(MyApplication.sAppContext)
        rootOtgPath?.forEach { path ->
            path?.let {
                allPath.add(it)
            }
        }
    }

    fun getCategoryKey(category: Int): String {
        return "$KEY_CATEGORY$category"
    }

    fun getSuperKey(path: Array<String>?): String {
        val value = path?.let { list ->
            val pathValue = StringBuffer()
            list.forEach { path ->
                pathValue.append(path)
            }
            pathValue
        } ?: ""
        Log.d(TAG, "getSuperKey value = $value")
        return "$KEY_SUPER$value"
    }

    /**
     * @param directory 目录，包括：otg、/storage/emulated/0、/storage/xxx(sd card)
     */
    fun getBrowserKey(directory: String?): String {
        Log.d(TAG, "getBrowserKey directory = $directory")
        return "${SortModeUtils.BROWSER_SORT_RECORD}_${getFinalPath(directory)}"
    }

    /**
     * 代表在remote文件列表界面的key
     */
    fun getRemoteMacKey(): String {
        Log.d(TAG, "getRemoteMacKey")
        return SortModeUtils.REMOTE_MAC_SORT_RECORD
    }

    /**
     * 获取当前目录的根目录
     */
    private fun getFinalPath(directory: String?): String? {
        directory?.let {
            if (allPath.isEmpty()) {
                Log.d(TAG, "resetAllPath")
                resetAllPath()
            }
            allPath.forEach {
                it?.let {
                    if (directory.startsWith(it)) {
                        Log.d(TAG, "getBrowserKey getFinalPath = $it")
                        return it
                    }
                }
            }
        }
        return directory ?: rootInternalPath
    }

    fun getLabelKey(): String {
        return getBrowserKey(KEY_LABEL)
    }

    fun getShortcutFolderKey(): String {
        return getBrowserKey(KEY_SHORTCUT_FOLDER)
    }

    /**
     * 选择文件夹排序，跟随设备存储页面
     */
    fun getSelectPathKey(): String {
        return getBrowserKey(KEY_EMULATED)
    }

    fun getRecycleBinKey(): String {
        return SortModeUtils.RECYCLE_SORT_RECORD
    }

    @JvmStatic
    fun getAlbumKey(): String {
        return SortModeUtils.ALBUM_FILE_SORT_RECORD
    }

    @JvmStatic
    fun getRecentFileKey(): String {
        return SortModeUtils.RECENT_FILE_SORT_RECORD
    }

    fun getCloudFileKey(category: Int): String {
        return "${SortModeUtils.CLOUD_FILE_SORT_RECORD}_$category"
    }

    fun isCloudFileKey(key: String): Boolean {
        return key.startsWith(SortModeUtils.CLOUD_FILE_SORT_RECORD)
    }

    fun isLocalDocs(key: String): Boolean {
        return Objects.equals(key, getCategoryKey(CategoryHelper.CATEGORY_DOC))
    }

    fun isTencentDocs(key: String): Boolean {
        return Objects.equals(key, "${SortModeUtils.CLOUD_FILE_SORT_RECORD}_${CategoryHelper.CATEGORY_TENCENT_DOCS}")
    }

    fun isKingSoftDocs(key: String): Boolean {
        return Objects.equals(key, "${SortModeUtils.CLOUD_FILE_SORT_RECORD}_${CategoryHelper.CATEGORY_K_DOCS}")
    }

    fun isRemoteMacKey(key: String): Boolean {
        return Objects.equals(key, SortModeUtils.REMOTE_MAC_SORT_RECORD)
    }

    fun getDefaultValue(recordMode: String): Int {
        Log.d(TAG, "getDefaultValue:$recordMode")
        if (recordMode == "$KEY_CATEGORY${CategoryHelper.CATEGORY_DOC}") {
            return SortHelper.FILE_LAST_OPEN_TIME_ORDER
        } else if (recordMode.startsWith(KEY_CATEGORY)) {
            return SortHelper.FILE_TIME_REVERSE_ORDER
        } else if (recordMode == SortModeUtils.RECYCLE_SORT_RECORD) {
            return SortHelper.RECYCLE_BIN_DEFAULT_ORDER
        } else if (isTencentDocs(recordMode)) {
            return SortHelper.FILE_LAST_OPEN_TIME_ORDER
        } else if (isKingSoftDocs(recordMode)) {
            return SortHelper.FILE_TIME_REVERSE_ORDER
        } else if (recordMode.startsWith(KEY_SUPER)) {
            return SortHelper.FILE_TIME_REVERSE_ORDER
        } else if (recordMode == SortModeUtils.ALBUM_FILE_SORT_RECORD) {
            return SortHelper.ALBUM_DEFAULT_ORDER
        }
        return SortHelper.FILE_NAME_ORDER
    }

    fun getDFMKey(): String {
        return getCategoryKey(CategoryHelper.CATEGORY_DFM)
    }
}