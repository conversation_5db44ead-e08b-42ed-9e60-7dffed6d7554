/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : SingleFadingScorllView.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2023/6/19
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2023/6/19       1      create
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.util.AttributeSet
import android.widget.ScrollView

class SingleFadingScrollView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ScrollView(context, attrs, defStyleAttr) {

    override fun getTopFadingEdgeStrength(): Float {
        return 0f
    }
}