/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.utils
 * * Version     : 1.0
 * * Date        : 2020/5/14
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.ApplicationInfo
import android.content.pm.OplusPackageManager
import android.net.Uri
import android.os.Bundle
import androidx.annotation.VisibleForTesting
import androidx.appcompat.app.AlertDialog
import com.coui.appcompat.dialog.COUIAlertDialogBuilder
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.compat.CompatUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.fileutils.JavaFileHelper
import com.oplus.compat.content.pm.ApplicationInfoNative
import com.oplus.compat.content.pm.PackageManagerNative
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import java.util.*

object KtAppUtils {
    private const val TAG = "KtAppUtils"
    private const val SETTING_DETAIL_ACTION = "android.settings.APPLICATION_DETAILS_SETTINGS"
    private const val ACTION_OWORK = "oplus.intent.action.owork.OPEN_OWORK"
    private const val OWOKR_PACKAGE = "com.oplus.owork"
    private const val ENTRANCE_FLAG = "entrance_flag"

    const val ENTRANCE_FILE_MANAGER_MAIN = "entrance_file_manager_main"
    const val ENTRANCE_FILE_MANAGER_WORK_SPACE = "entrance_file_manager_work_space"

    enum class ConnectionResult {
        SUCCESS, DISABLED, MISSED
    }

    fun getInstalledMap(): HashMap<String, Boolean> {
        if (PermissionUtils.hasGetInstalledAppsPermission()) {
            val info = MyApplication.sAppContext.packageManager.getInstalledPackages(0)
            Log.d(TAG, "getInstalledMap info: ${info.size}")
            if (info.size > 0) {
                val hashMap = HashMap<String, Boolean>()
                for (packageInfo in info) {
                    hashMap[packageInfo.packageName] = true
                }
                return hashMap
            }
        }
        return HashMap<String, Boolean>()
    }

    fun getAppName(packageName: String): String {
        return try {
            MyApplication.sAppContext.packageManager.let {
                it.getApplicationLabel(
                    it.getApplicationInfo(
                        packageName,
                        0
                    )
                ).toString()
            }
        } catch (e: Exception) {
            Log.w(TAG, "getAppName: exception : $e")
            ""
        }
    }

    /**
     * check app enabled with dialog to setting
     */
    fun checkAppEnabledWithDialog(context: Context, pkgName: String, msgId: Int): Boolean {
        return when (JavaFileHelper.safeCheck({ checkAppForceEnabled(pkgName) }, ConnectionResult.MISSED)) {
            ConnectionResult.DISABLED -> {
                showAppEnabledWithDialog(context, pkgName, msgId)
                false
            }

            ConnectionResult.SUCCESS -> true
            ConnectionResult.MISSED -> false
        }
    }

    /**
     * check app enabled with dialog to setting
     */
    @JvmStatic
    fun checkAppEnabledWithDialogReturn(context: Context, pkgName: String, msgId: Int): AlertDialog? {
        return when (JavaFileHelper.safeCheck({ checkAppForceEnabled(pkgName) }, ConnectionResult.MISSED)) {
            ConnectionResult.DISABLED -> {
                return showAppEnabledWithDialog(context, pkgName, msgId)
            }

            ConnectionResult.SUCCESS -> null
            ConnectionResult.MISSED -> null
        }
    }

    /**
     * show app enabled with dialog to setting
     */
    @JvmStatic
    fun showAppEnabledWithDialog(context: Context, pkgName: String, msgId: Int): AlertDialog? {
        var dialog: AlertDialog? = null
        val appName = getAppName(pkgName)
        (context as? Activity)?.runOnUiThread {
            dialog = COUIAlertDialogBuilder(context)
                .setTitle(
                    String.format(
                        MyApplication.sAppContext.getString(R.string.enable_request_title),
                        appName
                    )
                )
                .setMessage(
                    String.format(
                        MyApplication.sAppContext.getString(msgId),
                        appName
                    )
                )
                .setPositiveButton(R.string.app_enable_button) { _, _ ->
                    startAppDetailWithSetting(context, pkgName)
                }
                .setNegativeButton(R.string.dialog_cancel) { _, _ -> }
                .create().also {
                    it.show()
                }
        } ?: Log.d(TAG, " checkAppEnabledWithDialog: context is not activity")

        return dialog
    }

    /**
     * start target app setting detail activity
     */
    private fun startAppDetailWithSetting(context: Context, packageName: String) {
        Intent(SETTING_DETAIL_ACTION)
            .setData(Uri.fromParts("package", packageName, null)).let {
                try {
                    if (context !is Activity) {
                        it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(it)
                } catch (e: Exception) {
                    Log.w(TAG, "startAppDetailWithSetting: exception: $e")
                }
            }
    }

    /**
     * @attention check target app is enable to be called
     */
    fun checkAppForceEnabled(packageName: String): ConnectionResult {
        if (packageName.isEmpty()) return ConnectionResult.MISSED
        return try {
            val applicationInfo =
                MyApplication.sAppContext.packageManager.getApplicationInfo(packageName, 0)
            val enable = applicationInfo.enabled || isAppFrozen(applicationInfo)
            if (enable) {
                ConnectionResult.SUCCESS
            } else {
                ConnectionResult.DISABLED
            }
        } catch (e: Exception) {
            Log.w(TAG, "checkAppForceEnabled error: $e")
            ConnectionResult.MISSED
        }
    }

    /**
     * @attention when app is frozen, the applicationInfo params $enabled will be false either.
     */
    @VisibleForTesting
    @JvmStatic
    fun isAppFrozen(applicationInfo: ApplicationInfo): Boolean {
        return try {
            CompatUtils.compactApi(CompatUtils.OS_14, CompatUtils.OS_14_SUB_V, {
                val pkgMgr = OplusPackageManager.getOplusPackageManager(MyApplication.sAppContext)
                val uid = Utils.getUserId()
                val state = pkgMgr.getOplusFreezePackageState(applicationInfo.packageName, uid)
                OplusPackageManager.STATE_OPLUS_FREEZE_FREEZED == state
            }, {
                ApplicationInfoNative.getOplusFreezeState(applicationInfo) ==
                        PackageManagerNative.OPLUS_STATE_FREEZE_FREEZED
            })
        } catch (e: Exception) {
            Log.w(TAG, "isAppFrozen error: $e")
            false
        }
    }

    @JvmStatic
    fun getApplicationId(flavorBand: String = MyApplication.flavorBrand, flavorRegion: String = MyApplication.flavorRegion): String {
        val applicationId = if ((flavorBand == "oneplus") && (flavorRegion != "domestic")) {
            "com.oneplus.filemanager"
        } else {
            "com.coloros.filemanager"
        }
        Log.d(TAG, "applicationId=$applicationId")
        return applicationId
    }


    val mIsOnePlusOverSea by lazy {
        isOnePlus && (FeatureCompat.sIsExpRom)
    }

    val mIsOnePlusDomestic by lazy {
        isOnePlus && !FeatureCompat.sIsExpRom
    }

    /**
     * 是否有清除操作
     */
    val hasCleanupFunction by lazy {
        (FeatureCompat.sPhoneManagerStartInfo != null)
    }

    /**
     * 是否是外销小屏直板手机
     */
    @JvmStatic
    fun isExportSmallScreenPhone(): Boolean {
        return FeatureCompat.sIsExpRom.and(FeatureCompat.isSmallScreenPhoneNew())
    }

    val isOnePlus by lazy {
        (MyApplication.flavorBrand == KtUtils.FLAVOR_ONEPLUS)
    }

    fun startPhoneManager(context: Context, bundle: Bundle? = null) {
        try {
            FeatureCompat.sPhoneManagerStartInfo?.apply {
                if (!checkAppEnabledWithDialog(context, first, R.string.phone_manager_disable_message)) {
                    return
                }

                val intent = Intent(second)
                intent.setPackage(first)
                CollectPrivacyUtils.collectInstalledAppList(first)
                intent.putExtra("enter_from", context.packageName)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                bundle?.let {
                    intent.putExtras(bundle)
                }
                context.startActivity(intent)
            }
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, "startPhoneManager failed: $e")
        }
    }

    @JvmStatic
    fun startOWork(context: Context, entranceFlag: String) {
        try {
            val intent = Intent()
            intent.setPackage(OWOKR_PACKAGE)
            CollectPrivacyUtils.collectInstalledAppList(OWOKR_PACKAGE)
            intent.action = ACTION_OWORK
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
            intent.putExtra(ENTRANCE_FLAG, entranceFlag)
            context.startActivity(intent)
        } catch (e: ActivityNotFoundException) {
            Log.e(TAG, "startOWork failed: $e")
        }
    }
}
