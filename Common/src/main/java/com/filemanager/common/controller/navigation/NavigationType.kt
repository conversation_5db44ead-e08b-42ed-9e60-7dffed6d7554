/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - NavigationType.kt
 ** Description: Navigation Menu Type Define
 **
 ** Version: 1.0
 ** Date: 2021-04-07
 ** Author: W9001165
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ********************************************************************************/
package com.filemanager.common.controller.navigation

import android.annotation.SuppressLint
import android.view.MenuItem
import com.coui.appcompat.bottomnavigation.COUINavigationView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.Log

enum class NavigationType {
    RECYCLE_NORMAL {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                inflateMenu(R.menu.navigation_tool_recycle_bin_normal)
                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.recycle_bin_navigation_delete_all] = menu.findItem(R.id.recycle_bin_navigation_delete_all)
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                NavigationController.setMenuItemEnable(menuItems[it], isEnable)
            }
        }
    },

    RECYCLE_EDIT {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                inflateMenu(R.menu.navigation_tool_recycle_bin)
                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.recycle_bin_navigation_details] = menu.findItem(R.id.recycle_bin_navigation_details)
                        items[R.id.recycle_bin_navigation_delete_forever] = menu.findItem(R.id.recycle_bin_navigation_delete_forever)
                        items[R.id.recycle_bin_navigation_restore] = menu.findItem(R.id.recycle_bin_navigation_restore)
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                NavigationController.setMenuItemEnable(menuItems[it], isEnable)
            }
        }
    },

    FILE_PICK {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                inflateMenu(R.menu.navigation_safe_file_picker)
                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.action_encryption] = menu.findItem(R.id.action_encryption).also {
                            NavigationController.setMenuItemEnable(it, false)
                        }
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                NavigationController.setMenuItemEnable(menuItems[it], isEnable)
            }
        }
    },

    DECOMPRESS_PREVIEW {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                inflateMenu(R.menu.navigation_tool_decompress)
                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.navigation_decompress] = menu.findItem(R.id.navigation_decompress)
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                NavigationController.setMenuItemEnable(menuItems[it], isEnable)
            }
        }
    },

    FILE_LABEL {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                val maxCount = naviTool.getMaxItemCount(naviTool.width)
                Log.d("NavigationType", "FILE_LABEL maxCount: $maxCount")
                inflateMenu(R.menu.navigation_tool_file_label_list)
                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.navigation_send] = menu.findItem(R.id.navigation_send)
                        items[R.id.navigation_pin] = menu.findItem(R.id.navigation_pin)
                        items[R.id.navigation_rename] = menu.findItem(R.id.navigation_rename)
                        items[R.id.navigation_delete_label] = menu.findItem(R.id.navigation_delete_label)
                        items[R.id.navigation_upload_cloud] = menu.findItem(R.id.navigation_upload_cloud)
                        items[R.id.navigation_label_more] = menu.findItem(R.id.navigation_label_more)
                        items
                    }
                }
            }
            return null
        }

        @SuppressLint("UseCompatLoadingForDrawables")
        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                when (it) {
                    R.id.navigation_pin -> {
                        menuItems[it]?.title = if (mHasSelectedLabelsAllPin) {
                            appContext.getString(R.string.label_unpin)
                        } else {
                            appContext.getString(R.string.label_pin)
                        }
                        menuItems[it]?.icon = if (mHasSelectedLabelsAllPin) {
                            appContext.getDrawable(R.drawable.ic_menu_label_unpin)
                        } else {
                            appContext.getDrawable(R.drawable.ic_menu_label_pin)
                        }
                        NavigationController.setMenuItemEnable(menuItems[it], isEnable)
                    }
                    R.id.navigation_rename -> NavigationController.setMenuItemEnable(menuItems[it], isEnable && !mHasSelectedMultiLabels)

                    R.id.navigation_upload_cloud -> NavigationController.setMenuItemEnable(menuItems[it], isEnable && !mHasSelectedFileEmpty)

                    R.id.navigation_send -> NavigationController.setMenuItemEnable(menuItems[it], isEnable && !mHasSelectedFileEmpty)

                    else -> NavigationController.setMenuItemEnable(menuItems[it], isEnable)
                }
            }
        }
    },

    FILE_DRIVE {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                val maxCount = naviTool.getMaxItemCount(naviTool.width)
                Log.d("NavigationType", "FILE_DRIVE maxCount: $maxCount")
                inflateMenu(R.menu.navigation_tool_file_drive)

                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.navigation_download] = menu.findItem(R.id.navigation_download)
                        items[R.id.navigation_rename] = menu.findItem(R.id.navigation_rename)
                        items[R.id.navigation_delete] = menu.findItem(R.id.navigation_delete)
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                when (it) {
                    R.id.navigation_rename -> {
                        NavigationController.setMenuItemEnable(
                            menuItems[it],
                            isEnable && !mHasSelectedMultiLabels
                        )
                    }
                    R.id.navigation_download -> {
                        NavigationController.setMenuItemEnable(
                            menuItems[it],
                            isEnable && !mHasSelectedMultiLabels && !mHasSelectedFileEmpty
                        )
                    }
                    R.id.navigation_delete -> {
                        NavigationController.setMenuItemEnable(
                            menuItems[it],
                            isEnable
                        )
                    }
                }
            }
        }
    },
    DFM {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                val maxCount = naviTool.getMaxItemCount(naviTool.width)
                Log.d("NavigationType", "DFM maxCount: $maxCount")
                inflateMenu(R.menu.navigation_tool_dfm)

                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.navigation_send] = menu.findItem(R.id.navigation_send)
                        items[R.id.navigation_cut] = menu.findItem(R.id.navigation_cut)
                        items[R.id.navigation_copy] = menu.findItem(R.id.navigation_copy)
                        items[R.id.navigation_delete] = menu.findItem(R.id.navigation_delete)
                        items[R.id.navigation_detail] = menu.findItem(R.id.navigation_detail)
                        items[R.id.navigation_more] = menu.findItem(R.id.navigation_more)
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                when (it) {
                    R.id.navigation_send -> {
                        NavigationController.setMenuItemEnable(
                            menuItems[it],
                            !mHasDrm && isEnable
                        )
                    }

                    else -> NavigationController.setMenuItemEnable(menuItems[it], isEnable)
                }
            }
        }
    },

    REMOTE_MAC {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {

                inflateMenu(R.menu.navigation_tool_remote_mac)

                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.navigation_download] = menu.findItem(R.id.navigation_download)
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                NavigationController.setMenuItemEnable(menuItems[it], isEnable)
            }
        }
    },

    DEFAULT {
        override fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>? {
            naviTool?.apply {
                val maxCount = naviTool.getMaxItemCount(naviTool.width)
                Log.d("NavigationType", "maxCount: $maxCount")
                inflateMenu(R.menu.navigation_tool_replace)
                if (menu.size() > 0) {
                    return HashMap<Int, MenuItem>(menu.size()).let { items ->
                        items[R.id.navigation_send] = menu.findItem(R.id.navigation_send)
                        items[R.id.navigation_cut] = menu.findItem(R.id.navigation_cut)
                        items[R.id.navigation_label] = menu.findItem(R.id.navigation_label)
                        items[R.id.navigation_delete] = menu.findItem(R.id.navigation_delete)
                        items[R.id.navigation_copy] = menu.findItem(R.id.navigation_copy)
                        items[R.id.navigation_detail] = menu.findItem(R.id.navigation_detail)
                        items[R.id.navigation_more] = menu.findItem(R.id.navigation_more)
                        items
                    }
                }
            }
            return null
        }

        override fun setNavigateItemAble(
            menuItems: HashMap<Int, MenuItem>,
            isEnable: Boolean,
            mHasDrm: Boolean,
            mHasSelectedMultiLabels: Boolean,
            mHasSelectedLabelsAllPin: Boolean,
            mHasSelectedFileEmpty: Boolean,
            hasAndroidData: Boolean
        ) {
            menuItems.keys.forEach {
                when (it) {
                    R.id.navigation_send -> {
                        NavigationController.setMenuItemEnable(menuItems[it], !mHasDrm && isEnable)
                    }
                    R.id.navigation_delete -> {
                        NavigationController.setMenuItemEnable(menuItems[it], (AndroidDataHelper.allowEditAndroidData || !hasAndroidData) && isEnable)
                    }
                    R.id.navigation_cut -> {
                        NavigationController.setMenuItemEnable(menuItems[it], (AndroidDataHelper.allowEditAndroidData || !hasAndroidData) && isEnable)
                    }
                    else -> {
                        NavigationController.setMenuItemEnable(menuItems[it], isEnable)
                    }
                }
            }
        }
    };

    abstract fun buildMenu(naviTool: COUINavigationView?): HashMap<Int, MenuItem>?

    /**
     * set navigate item able
     */
    abstract fun setNavigateItemAble(
        menuItems: HashMap<Int, MenuItem>,
        isEnable: Boolean,
        mHasDrm: Boolean,
        mHasSelectedMultiLabels: Boolean = false,
        mHasSelectedLabelsAllPin: Boolean = false,
        mHasSelectedFileEmpty: Boolean = false,
        hasAndroidData: Boolean = false
    )

    /**
     * 设置type中的enable状态
     */
    fun setNavigateItemAble(
        menuItems: HashMap<Int, MenuItem>,
        memuEnable: IMenuEnable
    ) {
        menuItems.keys.forEach {
            NavigationController.setMenuItemEnable(menuItems[it], memuEnable.isMenuEnable(this, it))
        }
    }
}