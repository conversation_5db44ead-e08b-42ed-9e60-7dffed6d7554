/***********************************************************
 ** Copyright (C), 2008-2017 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: PermissionUtils.kt
 ** Description: The until for permission check
 ** Version: 1.0
 ** Date: 2020/4/16
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils

import android.Manifest
import android.app.Activity
import android.app.AppOpsManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Process
import android.provider.Settings
import androidx.annotation.VisibleForTesting
import androidx.core.app.NotificationManagerCompat
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.componentutils.ExportPrivacyUtils.openExportPrivacyPolicy
import com.oplus.filemanager.interfaze.privacy.CollectPrivacyUtils
import java.util.function.Consumer

object PermissionUtils {
    const val REQUEST_MANAGE_ALL_FILES_PERMISSIONS = 9090
    const val REQUEST_GET_INSTALLED_APPS_PERMISSIONS = 9092
    const val SETTING_PACKAGE_NAME = "com.android.settings"
    const val ACTION_SAFE_CENTER_PERMISSION = "oplus.intent.action.PERMISSION_APP_DETAIL"
    private const val TAG = "PermissionUtils"
    private const val ACTION_STATEMENT_PAGE = "com.coloros.bootreg.activity.statementpage"
    private const val STATEMENT_INTENT_FLAG = "statement_intent_flag"
    private const val STATEMENT_PACKAGE_NAME_R = "com.coloros.bootreg"
    private const val ACTION_MANAGE_APP_PERMISSIONS = "android.intent.action.MANAGE_APP_PERMISSIONS"
    private const val ACTION_MANAGE_APP_DETAIL = "android.settings.APPLICATION_DETAILS_SETTINGS"
    private const val EXTRA_PACKAGE_NAME = "android.intent.extra.PACKAGE_NAME"
    const val GET_INSTALLED_APPS = "com.android.permission.GET_INSTALLED_APPS"

    @VisibleForTesting
    const val PERMISSION_NOTIFICATION = "android.permission.POST_NOTIFICATIONS"

    @JvmStatic
    fun requestStoragePermission(activity: Activity, flags: Int? = null): Boolean {
        return try {
            if (SdkUtils.isAtLeastR()) {
                val intent = Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION)
                intent.data = Uri.parse("package:".plus(activity.packageName))
                intent.`package` = SETTING_PACKAGE_NAME
                if (flags != null) {
                    intent.setFlags(flags)
                }
                activity.startActivity(intent)
            } else {
                activity.requestPermissions(
                    arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE),
                    REQUEST_MANAGE_ALL_FILES_PERMISSIONS
                )
            }
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to requestStoragePermission: ${e.message}")
            false
        }
    }

    fun hasStoragePermission(): Boolean {
        val context = appContext
        return hasStoragePermission(context)
    }

    @JvmStatic
    fun hasStoragePermission(context: Context): Boolean {
        try {
            if (SdkUtils.isAtLeastR()) {
                val appOps = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
                val mode = appOps.noteOpNoThrow(
                    "android:manage_external_storage",
                    Process.myUid(),
                    context.packageName,
                    null,
                    null
                )
                if (mode == AppOpsManager.MODE_DEFAULT) {
                    return context.checkPermission(
                        Manifest.permission.MANAGE_EXTERNAL_STORAGE,
                        Process.myPid(),
                        Process.myUid()
                    ) == PackageManager.PERMISSION_GRANTED
                }
                return mode == AppOpsManager.MODE_ALLOWED
            } else {
                return context.checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to check storage permission: ${e.message}")
        }
        return false
    }

    fun openPrivacyPolicy(activity: Activity) {
        if (Utils.isQuickClick()) return
        val result = if (KtAppUtils.mIsOnePlusOverSea) {
            openExportPrivacyPolicy(activity)
        } else null
        if (result == true) return
        if (checkBootWizardEnabled(activity).not()) return
        try {
            val intent = Intent(ACTION_STATEMENT_PAGE)
            intent.putExtra(STATEMENT_INTENT_FLAG, 2)
            if (SdkUtils.isAtLeastR()) {
                intent.setPackage(STATEMENT_PACKAGE_NAME_R)
                CollectPrivacyUtils.collectInstalledAppList(STATEMENT_PACKAGE_NAME_R)
            }
            activity.startActivity(intent)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to open privacy page: ${e.message}")
        }
    }

    private fun checkBootWizardEnabled(activity: Activity): Boolean {
        return KtAppUtils.checkAppEnabledWithDialog(
            activity, STATEMENT_PACKAGE_NAME_R, R.string.boot_wizard_disable_message
        )
    }

    fun openPermissionSetting(activity: Activity): Boolean {
        try {
            val intent = Intent()
            return if (SdkUtils.isAtLeastR()) {
                requestStoragePermission(activity)
            } else {
                if (FeatureCompat.sIsExpRom) {
                    intent.action = ACTION_MANAGE_APP_PERMISSIONS
                    intent.putExtra(EXTRA_PACKAGE_NAME, activity.packageName)
                } else {
                    intent.action = ACTION_MANAGE_APP_DETAIL
                    intent.data = Uri.fromParts("package", activity.packageName, null)
                }
                activity.startActivityForResult(intent, REQUEST_MANAGE_ALL_FILES_PERMISSIONS)
                true
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to openPermissionSetting: ${e.message}")
            return false
        }
    }

    private fun checkedPermissionGetInstalledApps(): Boolean {
        return try {
            val permissionInfo = appContext.packageManager.getPermissionInfo(GET_INSTALLED_APPS, 0)
            (permissionInfo != null)
        } catch (nameNF: PackageManager.NameNotFoundException) {
            Log.d(TAG, "permission GET_INSTALLED_APPS not find")
            false
        }
    }

    fun hasGetInstalledAppsPermission(): Boolean {
        return if (FeatureCompat.sIsExpRom.not() && checkedPermissionGetInstalledApps()) {
            val context = appContext
            context.checkSelfPermission(GET_INSTALLED_APPS) == PackageManager.PERMISSION_GRANTED
        } else {
            true
        }
    }

    fun requestGetInstalledAppsPermission(activity: Activity): Boolean {
        if (SdkUtils.isAtLeastR()) {
            activity.requestPermissions(
                arrayOf(GET_INSTALLED_APPS), REQUEST_GET_INSTALLED_APPS_PERMISSIONS
            )
            return true
        }
        return false
    }

    @JvmStatic
    fun hasNotificationPermission(context: Context): Boolean {
        return if (SdkUtils.isAtLeastT()) {
            context.checkSelfPermission(PERMISSION_NOTIFICATION) == PackageManager.PERMISSION_GRANTED
        } else {
            NotificationManagerCompat.from(context).areNotificationsEnabled()
        }
    }

    /**
     * 跳转到通知管理界面
     * @param activity
     * @param callback 从通知界面回来后的回调，boolean：表示是否已经授予通知权限
     */
    @JvmStatic
    fun openNotifySettings(activity: Activity, callback: Consumer<Boolean>?) {
        val intent = Intent()
        intent.action = Settings.ACTION_APP_NOTIFICATION_SETTINGS
        intent.putExtra(Settings.EXTRA_APP_PACKAGE, activity.packageName)
        (activity as? BaseVMActivity)?.launchActivity(intent) {
            val grant = hasNotificationPermission(activity)
            Log.d(TAG, "openNotifySettings onActivityResult:$it grant:$grant")
            callback?.accept(grant)
        }
    }
}