/*
 * ********************************************************************
 *  * * Copyright (C), 2019 Oplus. All rights reserved..
 *  * * VENDOR_EDIT
 *  * * File        :  ApplicationInfoDetail.java
 *  * * Description : ApplicationInfoDetail.java
 *  * * Version     : 1.0
 *  * * Date        : 19-9-20 下午4:58
 *  * * Author      : <EMAIL>
 *  * *
 *  * * ---------------------Revision History: ----------------------------
 *  * *  <author>                  <data>     <version>  <desc>
 *  **********************************************************************
 */

package com.filemanager.common.imageloader.application;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.Serializable;

public class ApplicationInfoDetail implements Serializable {
    private static final long serialVersionUID = -8184727572578222776L;
    public final static int APPLICATION_QUALITY = 100;
    public String mPath;
    public String mApkVersion;
    public String mApkName;
    public transient Bitmap mBitmap;

    public int getSize() {
        if (mBitmap != null) {
            int mPathLength = (mPath != null) ? (mPath.getBytes().length) : (0);
            int mApkVersionLength = (mApkVersion != null) ? (mApkVersion.getBytes().length) : (0);
            int mApkNameLength = (mApkName != null) ? (mApkName.getBytes().length) : (0);
            return (mBitmap.getByteCount() + mPathLength + mApkVersionLength + mApkNameLength);
        }
        return 1;
    }

    private void writeObject(ObjectOutputStream out) throws IOException {
        out.writeObject(mPath);
        out.writeObject(mApkVersion);
        out.writeObject(mApkName);
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        mBitmap.compress(Bitmap.CompressFormat.PNG, APPLICATION_QUALITY, stream);
        byte[] bytes = stream.toByteArray();
        out.writeInt(bytes.length);
        out.write(bytes);
    }

    private void readObject(ObjectInputStream in) throws IOException, ClassNotFoundException {
        mPath = (String) in.readObject();
        mApkVersion = (String) in.readObject();
        mApkName = (String) in.readObject();
        int bufferLength = in.readInt();
        byte[] byteArray = new byte[bufferLength];
        int pos = 0;
        do {
            int read = in.read(byteArray, pos, bufferLength - pos);
            if (read != -1) {
                pos += read;
            } else {
                break;
            }
        } while (pos < bufferLength);
        mBitmap = BitmapFactory.decodeByteArray(byteArray, 0, bufferLength);
    }

    @Override
    public String toString() {
        return "ApplicationInfoDetail{"
                + "mPath='" + mPath + '\''
                + ", mApkVersion='" + mApkVersion + '\''
                + ", mApkName='" + mApkName + '\''
                + ", mBitmap=" + mBitmap
                + '}';
    }
}
