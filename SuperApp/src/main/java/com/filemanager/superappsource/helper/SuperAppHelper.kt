/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.ui.main.category
 * * Version     : 1.0
 * * Date        : 2020/6/8
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.superappsource.helper

import android.content.Context
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.graphics.drawable.Drawable
import android.os.Environment
import android.os.oplusdevicepolicy.OplusDevicepolicyManager
import android.text.TextUtils
import android.util.SparseArray
import androidx.annotation.VisibleForTesting
import com.filemanager.common.MyApplication
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.R
import com.filemanager.common.compat.CompatUtils
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.constants.CommonConstants
import com.filemanager.common.constants.Constants
import com.filemanager.common.constants.FilterConstants
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.fileutils.JavaFileHelper
import com.filemanager.common.helper.CategoryHelper
import com.filemanager.common.helper.VolumeEnvironment
import com.filemanager.common.utils.AppUtils
import com.filemanager.common.utils.BlacklistParser
import com.filemanager.common.utils.CategoryAppConfig
import com.filemanager.common.utils.GsonUtil
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.KtUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.PCConnectAction
import com.filemanager.common.utils.PreferencesUtils
import com.filemanager.common.utils.ShortCutUtils
import com.filemanager.common.utils.StatisticsUtils
import com.filemanager.common.utils.Utils
import com.oplus.filemanager.interfaze.cloudconfig.ICloudConfigApi
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.CATEGORY_INVALID_FILE_SIZE
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.CATEGORY_INVALID_ITEM_COUNT
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.QQ
import com.oplus.filemanager.interfaze.superapp.MainCategoryItemsBean.WECHAT
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withTimeout
import java.io.File
import java.util.Locale


object SuperAppHelper {
    private val OWORK_PATHS = CommonConstants.OWORK_DEFAULT_PATHS

    private const val TAG = "SuperAppHelper"
    private const val FLAG_APP_MESSAGER = "1"
    private const val FLAG_APP_WHATSAPP = "2"
    private const val FLAG_APP_BBM = "3"
    private const val FLAG_APP_ZALO = "4"
    private const val FLAG_APP_LINE = "5"
    private const val FLAG_APP_QQ = "6"
    private const val FLAG_APP_WECHAT = "7"
    private const val FLAG_IMO = "8"
    private const val FLAG_PLUS_MESSAGER = "9"
    private const val INSTALL_CACHE_SPLITTER = ","
    private const val MESSAGER = "com.facebook.orca"
    private const val BBM = "com.bbm"
    private const val LINE = "jp.naver.line.android"
    private const val ZALO = "com.zing.zalo"
    private const val WHATSAPP = "com.whatsapp"
    private const val FILE_TIME_OUT = 1000L

    private var PKG_REMOTE_PC_CONTROL = Constants.PKG_REMOTE_MAC_CONTROL

    private const val TELEGRAM = "org.telegram.messenger"
    private const val IMO = "com.imo.android.imoim"
    private const val PLUS_MESSAGER = "org.telegram.plus"
    private const val APP_INSTALL_STATE_CACHE = "app_install_state_cache"
    private val MESSAGER_PATH = arrayOf("Pictures/Messenger/", "Movies/Messenger/")
    private val LINE_PATH = arrayOf("Pictures/LINE/", "Pictures/LINE_MOVIE/")
    private val ZALO_PATH = arrayOf(
        "zalo/picture/", "zalo/video/download/", "zalo/paint/", "zalo/audio/",
        "Zalo/downloads/", "Downloads/zalo/", "Download/Zalo/", "Pictures/Zalo/", "Movies/Zalo/"
    )
    private val REMOTE_PC_CONTROL_PATH = arrayOf("Download/Remote PC Control/")
    private val WHATSAPP_PATH = arrayOf(
        "WhatsApp/Media/WhatsApp Images/", "WhatsApp/Media/WhatsApp Video/",
        "WhatsApp/Media/WhatsApp Documents/", "WhatsApp/Media/WhatsApp Audio/",
        "Android/media/com.whatsapp/WhatsApp/Media/WhatsApp Images/",
        "Android/media/com.whatsapp/WhatsApp/Media/WhatsApp Video/",
        "Android/media/com.whatsapp/WhatsApp/Media/WhatsApp Documents/",
        "Android/media/com.whatsapp/WhatsApp/Media/WhatsApp Audio/"
    )

    @VisibleForTesting
    val WECHAT_PATH = arrayOf(
        "Tencent/MicroMsg/WeiXin/",
        "Tencent/MicroMsg/Download/",
        "Tencent/MicroMsg/WeChat/",
        "Pictures/Weixin/",
        "Pictures/Wechat/",
        "Android/data/com.tencent.mm/MicroMsg/Download/",
        "Download/WeiXin/"
    )

    @VisibleForTesting
    val QQ_PATH = arrayOf(
        "Tencent/QQfile_recv/",
        "Tencent/QQ_Images/",
        "Android/data/com.tencent.mobileqq/Tencent/QQfile_recv/",
        "Pictures/QQ/",
        "Download/QQ/"
    )
    private val IMO_PATH = arrayOf(
        "IMO/IMO images/",
        "IMO/IMO audio/",
        "IMO/IMO documents/",
        "IMO/IMO apps/",
        "DCIM/imo/",
        "IMO/IMO archives/"
    )
    private val PLUS_MESSAGER_PATH = arrayOf(
        "Telegram/Telegram Audio/",
        "Telegram/Telegram Documents/",
        "Telegram/Telegram Video/",
        "Telegram/Telegram Images/",
        "Telegram/Telegram Themes/",
        "Pictures/Telegram/",
        "Music/Telegram/"
    )
    private val TELEGRAM_PATH = arrayOf(
        "Telegram/Telegram Audio/", "Telegram/Telegram Documents/", "Telegram/Telegram Images/",
        "Telegram/Telegram Video/",
        "Movies/Telegram/", "Pictures/Telegram/", "Download/Telegram/", "Music/Telegram/"
    )
    private val CATEGORY_PATH_DOWNLOAD = arrayOf("Download/")
    private val CATEGORY_PATH_OPPO_SHARE = arrayOf(
        "OPPO Share/", "Documents/OPPO Share/",
        "Movies/OPPO Share/", "Music/OPPO Share/", "Pictures/OPPO Share/", "Download/OPPO Share/"
    )
    private val CATEGORY_PATH_REALME_SHARE = arrayOf(
        "Realme Share/",
        "Documents/Realme Share/",
        "Movies/Realme Share/",
        "Music/Realme Share/",
        "Pictures/Realme Share/",
        "Download/Realme Share/"
    )
    private val CATEGORY_PATH_ONEPLUS_SHARE = arrayOf(
        "OnePlusShare/",
        "OnePlus Share/",
        "Documents/OnePlus Share/",
        "Movies/OnePlus Share/",
        "Music/OnePlus Share/",
        "Pictures/OnePlus Share/",
        "Download/OnePlus Share/"
    )
    private val CATEGORY_PATH_BT = arrayOf("bluetooth/", "Download/Bluetooth/")

    private val DRAWABLE_IDS = arrayOf(
        com.filemanager.common.R.drawable.file_facebook_icon,
        com.filemanager.common.R.drawable.file_whatsapp_icon,
        com.filemanager.common.R.drawable.file_bbm_icon,
        com.filemanager.common.R.drawable.file_zalo_icon,
        com.filemanager.common.R.drawable.file_line_icon,
        com.filemanager.common.R.drawable.main_category_qq,
        com.filemanager.common.R.drawable.main_category_wechat,
        com.filemanager.common.R.drawable.file_telegram_icon,
        com.filemanager.common.R.drawable.file_imo_icon,
        com.filemanager.common.R.drawable.file_messager_plus_icon
    )
    private val APP_ORDER =
        arrayOf(MESSAGER, WHATSAPP, BBM, ZALO, LINE, QQ, WECHAT, TELEGRAM, IMO, PLUS_MESSAGER)
    private const val COMMON_REGION = "ALL"
    private var sRegion = "CN"
    private val sIconMap by lazy {
        val iconMap = HashMap<String, Int>()
        for (i in APP_ORDER.indices) {
            iconMap[APP_ORDER[i]] = DRAWABLE_IDS[i]
        }
        iconMap
    }

    private const val OSHARE_DISABLED_KEY = "customize_set_oshare_disabled"

    private const val PRIVATE_WECHAT_PATH = "MicroMsg"
    private const val PRIVATE_QQ_PATH = "Tencent/QQfile_recv"
    private val iPath by lazy { VolumeEnvironment.getInternalSdPath(appContext) }

    @JvmStatic
    val sIsOShareDisabled: Boolean by lazy {
        isOShareDisabledByCustomize()
    }

    fun preloadSuperSharePreferences(context: Context) {
        context.getSharedPreferences(APP_INSTALL_STATE_CACHE, Context.MODE_PRIVATE)
    }

    /**
     * 通过sourcePath路径，在已有的CategoryItems中查找路径存在的item
     */
    fun findSourceBeanFromDocAction(context: Context, sourcePath: String?): MainCategoryItemsBean? {
        if (sourcePath.isNullOrEmpty()) {
            return null
        }
        val beanList = getCategoryItems(context)
        return beanList.find { beans ->
            val path = sourcePath.let {
                beans.fileList.find { it.contains(sourcePath, ignoreCase = false) }
            }
            path != null
        }.apply {
            Log.d(TAG, "find source bean from path $sourcePath result:$this")
        }
    }

    /**
     * 通过"三方"包名，在已有的CategoryItems中查找包名匹配的item
     */
    fun findSourceBeanByThirdPkgName(context: Context, pkgName: String?): MainCategoryItemsBean? {
        if (pkgName.isNullOrEmpty()) {
            return null
        }
        val beanList = getCategoryItems(context)
        return beanList.find { beans ->
            if (beans.thirdPkgName != null) {
                beans.thirdPkgName.equals(pkgName, ignoreCase = false)
            } else {
                beans.packageName.equals(pkgName, ignoreCase = false)
            }
        }.apply {
            Log.d(TAG, "find source bean from pkgName $pkgName result:$this")
        }
    }

    /**
     * 通过包名，在已有的CategoryItems中查找包名匹配的item
     */
    fun findSourceBeanByPkgName(context: Context, pkgName: String?): MainCategoryItemsBean? {
        if (pkgName.isNullOrEmpty()) {
            return null
        }
        val beanList = getCategoryItems(context)
        return beanList.find { beans ->
            beans.packageName.equals(pkgName, ignoreCase = false)
        }.apply {
            Log.d(TAG, "find source bean from pkgName $pkgName result:$this")
        }
    }

    @Synchronized
    @JvmStatic
    fun getCategoryItems(context: Context): MutableList<MainCategoryItemsBean> {
        val region = if (FeatureCompat.sIsExpRom) {
            PropertyCompat.sSystemRegion
        } else {
            "CN"
        }
        sRegion = region
        Log.i(TAG, "getCategoryItems region= $region")
        val mainCategoryItemList = mutableListOf<MainCategoryItemsBean>()
        val installAppList = ArrayList<SaveInitItem>()
        if (region.equals("CN", ignoreCase = true)) {
            val cloudConfigApi = Injector.injectFactory<ICloudConfigApi>()
            val cloudConfigCategoryListStr = cloudConfigApi?.getCategoryListGson()
            val cloudConfigCategoryList = cloudConfigCategoryListStr?.let {
                GsonUtil.toListSafeCall<CategoryAppBean>(it, CategoryAppBean::class.java)
            }
            val configCategoryList =
                if ((cloudConfigCategoryList == null) || (cloudConfigCategoryList?.size == 0)) {
                Log.i(TAG, "getCategoryItems cn cloudConfigCategoryList is empty")
                val spConfigCategoryListStr = PreferencesUtils.getString(
                    CommonConstants.PREF_CN_CATEGORY_APP_LIST,
                    CommonConstants.KEY_CATEGORY_APP_LIST
                )
                spConfigCategoryListStr?.let {
                    GsonUtil.toListSafeCall<CategoryAppBean>(it, CategoryAppBean::class.java)
                }
            } else {
                cloudConfigCategoryList
            }
            Log.i(TAG, "getCategoryItems cn configCategoryList size is ${configCategoryList?.size}")
            configCategoryList?.let { list ->
                val length = list.size
                if (length > 0) {
                    for (i in 0 until length) {
                        handleCategoryItem(
                            context,
                            list[i].mPackageName,
                            list[i].mPaths,
                            isCnRegion = true,
                            mainCategoryItemList,
                            installAppList
                        )
                    }
                } else {
                    handleCategoryDefault(region, mainCategoryItemList, installAppList, context)
                }
            } ?: kotlin.run {
                Log.d(TAG, "cloudConfigApi configCategoryList is null")
                handleCategoryDefault(region, mainCategoryItemList, installAppList, context)
            }
        } else {
            var packagenames = PreferencesUtils.getString(CategoryAppConfig.APP_CONFIG, region)
            if (TextUtils.isEmpty(packagenames)) {
                packagenames = PreferencesUtils.getString(CategoryAppConfig.APP_CONFIG, COMMON_REGION)
            }
            Log.d(TAG, "getCategoryItems: $sRegion + package=$packagenames")
            if (!TextUtils.isEmpty(packagenames)) {
                val packageNameList = packagenames!!.split("#".toRegex()).toTypedArray()
                val length = packageNameList.size
                Log.i(
                    TAG,
                    "getCategoryItems packagenameList size= " + length + " " + packageNameList[1]
                )
                for (i in 0 until length) {
                    handleCategoryItem(
                        context,
                        packageNameList[i],
                        null,
                        isCnRegion = false,
                        mainCategoryItemList,
                        installAppList
                    )
                }
            } else {
                handleCategoryDefault(region, mainCategoryItemList, installAppList, context)
            }
        }
        mainCategoryItemList.forEachIndexed { index, mainCategoryItemsBean ->
            if (mainCategoryItemsBean.searchId == 0) {
                mainCategoryItemsBean.searchId =
                    FilterConstants.FILTER_FROM_SUPER_BASE_VALUE + index
            }
        }
        addOtherSuperItem(context, mainCategoryItemList)
        saveInstallCacheState(context, installAppList, sRegion)
        return mainCategoryItemList
    }

    @Suppress("LongMethod")
    private fun handleCategoryDefault(
        region: String,
        mainCategoryItemList: MutableList<MainCategoryItemsBean>,
        installAppList: ArrayList<SaveInitItem>,
        context: Context
    ) {
        Log.i(TAG, "addDefaultItems= $region")
        if (!FeatureCompat.sIsExpRom) {
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                WECHAT,
                R.drawable.main_category_wechat,
                R.string.string_wechat,
                WECHAT_PATH
            )
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                QQ,
                R.drawable.main_category_qq,
                R.string.string_qq,
                QQ_PATH
            )
        } else if (region.equals("ID", ignoreCase = true)) {
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                MESSAGER,
                R.drawable.file_facebook_icon,
                R.string.facebook,
                MESSAGER_PATH
            )
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                WHATSAPP,
                R.drawable.file_whatsapp_icon,
                R.string.whatapp,
                WHATSAPP_PATH
            )
        } else if (region.equals("VN", ignoreCase = true)) {
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                ZALO,
                R.drawable.file_zalo_icon,
                R.string.zalo,
                ZALO_PATH
            )
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                MESSAGER,
                R.drawable.file_facebook_icon,
                R.string.facebook,
                MESSAGER_PATH
            )
        } else if (region.equals("TH", ignoreCase = true) || region.equals(
                "TW",
                ignoreCase = true
            )
        ) {
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                LINE,
                R.drawable.file_line_icon,
                R.string.line,
                LINE_PATH
            )
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                MESSAGER,
                R.drawable.file_facebook_icon,
                R.string.facebook,
                MESSAGER_PATH
            )
        } else if (region.equals("UZ", ignoreCase = true)) {
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                IMO,
                R.drawable.file_imo_icon,
                R.string.imo,
                IMO_PATH
            )
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                PLUS_MESSAGER,
                R.drawable.file_messager_plus_icon,
                R.string.messenger_plus,
                PLUS_MESSAGER_PATH
            )
        }
        // #ifdef
        // For ROM.App.FileManager, UserID.239444,239449, 2020/8/19, Modify
        // South Africa and Moldova import application file categories
        else if (region.equals("ZA", ignoreCase = true) || region.equals(
                "MD",
                ignoreCase = true
            ) || region.equals("RU", ignoreCase = true)
        ) {
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                TELEGRAM,
                R.drawable.file_telegram_icon,
                R.string.telegram,
                TELEGRAM_PATH
            )
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                WHATSAPP,
                R.drawable.file_whatsapp_icon,
                R.string.whatapp,
                WHATSAPP_PATH
            )
        }
        // #endif
        else {
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                MESSAGER,
                R.drawable.file_facebook_icon,
                R.string.facebook,
                MESSAGER_PATH
            )
            checkAndAddApkInfo(
                mainCategoryItemList,
                installAppList,
                context,
                WHATSAPP,
                R.drawable.file_whatsapp_icon,
                R.string.whatapp,
                WHATSAPP_PATH
            )
        }
    }

    @Suppress("LongMethod")
    private fun handleCategoryItem(
        context: Context,
        packageName: String,
        path: String?,
        isCnRegion: Boolean,
        mainCategoryItemList: MutableList<MainCategoryItemsBean>,
        installAppList: ArrayList<SaveInitItem>
    ) {
        var appName: String
        var packageInfo: PackageInfo?
        val packageManager = context.packageManager
        try {
            packageInfo = context.packageManager.getPackageInfo(packageName, 0)
            appName = packageInfo?.applicationInfo?.loadLabel(packageManager) as? String ?: ""
        } catch (e: PackageManager.NameNotFoundException) {
            Log.i(TAG, "$packageName is not installed")
            return
        }
        val paths = if (TextUtils.isEmpty(path)) {
            PreferencesUtils.getString(CategoryAppConfig.APP_CONFIG, packageName)
        } else {
            path
        }
        if (!TextUtils.isEmpty(paths)) {
            val pathsList = paths!!.split("#".toRegex()).toTypedArray()
            var isShowSuperApp = false
            for (index in pathsList.indices) {
                if (pathsList[index].endsWith(File.separator).not()) {
                    pathsList[index] += File.separator
                }
                if (isCnRegion && !isShowSuperApp) {
                    isShowSuperApp = ShortCutUtils.checkFileIsExist(
                        Environment.getExternalStorageDirectory().path
                                + File.separator + pathsList[index]
                    )
                }
            }
            Log.d(TAG, "$packageName: isShowSuperApp = $isShowSuperApp")
            var resourceId = sIconMap[packageName]
            var appIcon: Drawable? = null
            if (resourceId == null) {
                resourceId = 0
                try {
                    if (Utils.isNeededSdk24()) {
                        appIcon = packageManager.getApplicationIcon(packageName)
                    }
                } catch (e: Exception) {
                    Log.i(TAG, "getIcon failed")
                    return
                }
            }
            Log.i(
                TAG,
                "initMainCategoryItems appName = $appName icon i = ${packageName.length}"
            )
            //不是CN地区  或者 是CN地区并安装且下载
            if (!isCnRegion || (isCnRegion && isShowSuperApp)) {
                val itemBean = MainCategoryItemsBean(
                    CategoryHelper.CATEGORY_QQ,
                    appName,
                    resourceId,
                    0,
                    0,
                    appIcon,
                    0
                )
                itemBean.packageName = packageInfo?.packageName
                itemBean.fileList = pathsList
                if (itemBean.packageName.equals(WECHAT)) {
                    mainCategoryItemList.add(0, itemBean)
                    installAppList.add(0, SaveInitItem(itemBean.packageName, appName))
                } else {
                    mainCategoryItemList.add(itemBean)
                    installAppList.add(SaveInitItem(itemBean.packageName, appName))
                }
            }
        } else {
            installAppList.add(SaveInitItem(packageName))
        }
    }

    @Suppress("LongMethod")
    private fun addOtherSuperItem(
        context: Context,
        mainCategoryItemList: MutableList<MainCategoryItemsBean>
    ) {
        //add download source
        val downloadBean = MainCategoryItemsBean(
            CategoryHelper.CATEGORY_DOWNLOAD,
            context.getString(com.filemanager.common.R.string.download),
            com.filemanager.common.R.drawable.main_category_download,
            0,
            0,
            null,
            0
        )
        downloadBean.packageName = KtConstants.PKG_DOWNLOAD
        downloadBean.thirdPkgName = KtConstants.PKG_DOWNLOAD
        downloadBean.nameResId = com.filemanager.common.R.string.download
        downloadBean.fileList = CATEGORY_PATH_DOWNLOAD
        downloadBean.searchId = FilterConstants.FILTER_FROM_DOWNLOAD
        mainCategoryItemList.add(downloadBean)
        //add share source,except oneplus exp
        addShareItem(context, mainCategoryItemList)
        //Add remote PC control
        addRemotePcControl(context, mainCategoryItemList)
        //add bluetooth source
        val blueToothBean = MainCategoryItemsBean(
            CategoryHelper.CATEGORY_BLUETOOTH,
            context.getString(com.filemanager.common.R.string.bluetooth),
            com.filemanager.common.R.drawable.main_category_bluetooth,
            0,
            0,
            null,
            0
        )
        blueToothBean.packageName = KtConstants.PKG_BLUE_TOOTH
        blueToothBean.thirdPkgName = KtConstants.PKG_BLUE_TOOTH
        blueToothBean.nameResId = com.filemanager.common.R.string.bluetooth
        blueToothBean.fileList = CATEGORY_PATH_BT
        blueToothBean.searchId = FilterConstants.FILTER_FROM_BLUETOOTH
        mainCategoryItemList.add(blueToothBean)
        //add multi screen connect
        if (PCConnectAction.isMultiScreenConnectSupport() && PCConnectAction.isMultiScreenDirExist()) {
            val pcConnectBean = MainCategoryItemsBean(
                CategoryHelper.CATEGORY_PCCONNECT,
                context.getString(com.filemanager.common.R.string.hey_pc_name),
                com.filemanager.common.R.drawable.pc_connect_icon,
                0,
                0,
                null,
                0
            )
            pcConnectBean.packageName =
                KtConstants.PKG_PCCONNECT
            pcConnectBean.thirdPkgName = KtConstants.PKG_PCCONNECT
            pcConnectBean.nameResId = com.filemanager.common.R.string.hey_pc_name
            pcConnectBean.searchId = FilterConstants.FILTER_FROM_PC_CONNECT
            pcConnectBean.fileList = PCConnectAction.getMultiScreenConnectDirList()
            mainCategoryItemList.add(pcConnectBean)
            StatisticsUtils.onCommon(context, StatisticsUtils.HEY_PC_SHOW)
        }
        //add owork
        val oWorkName = KtUtils.getOWorkName(KtUtils.OWORK_NAME_TYPE_2)
        if (checkOWorkSuperAppCondition(context)) {
            val oWorkBean = MainCategoryItemsBean(
                CategoryHelper.CATEGORY_OWORK,
                oWorkName,
                com.filemanager.common.R.drawable.main_category_owork,
                0,
                0,
                null,
                0
            )
            oWorkBean.packageName = KtConstants.PKG_OWORK
            oWorkBean.thirdPkgName = KtConstants.PKG_OWORK
            oWorkBean.nameResId = KtUtils.getOWorkNameResId(KtUtils.OWORK_NAME_TYPE_2)
            oWorkBean.searchId = FilterConstants.FILTER_FROM_OWORK
            oWorkBean.fileList = OWORK_PATHS
            mainCategoryItemList.add(oWorkBean)
        }
    }

    @JvmStatic
    @VisibleForTesting
    private fun addRemotePcControl(context: Context, mainCategoryItemList: MutableList<MainCategoryItemsBean>) {
        val volume = VolumeEnvironment.getInternalSdPath(MyApplication.sAppContext)
        val hasFileDirPaths = REMOTE_PC_CONTROL_PATH.filter { path ->
            val dir = File(volume, path)
            hasFileInDir(dir)
        }
        Log.d(TAG, "addRemotePcControl hasFile dir count:${hasFileDirPaths.size}")
        val remotePcControlBean = MainCategoryItemsBean(
            CategoryHelper.CATEGORY_REMOTE_PC_CONTROL,
            context.getString(com.filemanager.common.R.string.remote_computer_file),
            R.drawable.remote_mac_file,
            0,
            0,
            null,
            0
        )
        remotePcControlBean.packageName = PKG_REMOTE_PC_CONTROL
        remotePcControlBean.thirdPkgName = PKG_REMOTE_PC_CONTROL
        remotePcControlBean.nameResId = com.filemanager.common.R.string.remote_computer_file
        remotePcControlBean.fileList = REMOTE_PC_CONTROL_PATH
        remotePcControlBean.searchId = FilterConstants.FILTER_FROM_REMOTE_PC_CONTROL
        remotePcControlBean.isShow = hasFileDirPaths.isNotEmpty()
        mainCategoryItemList.add(remotePcControlBean)
    }
    @JvmStatic
    @VisibleForTesting
    fun addShareItem(context: Context, mainCategoryItemList: MutableList<MainCategoryItemsBean>) {
        val pathList: Array<String>
        var thirdPkgName = ""
        val name = when (MyApplication.flavorBrand) {
            KtUtils.FLAVOR_ONEPLUS -> {
                thirdPkgName = KtConstants.PKG_ONE_PLUS_SHARE
                pathList = CATEGORY_PATH_ONEPLUS_SHARE
                com.filemanager.common.R.string.oneplus_share
            }

            KtUtils.FLAVOR_REALME -> {
                thirdPkgName = KtConstants.PKG_REALME_SHARE
                pathList = CATEGORY_PATH_REALME_SHARE
                com.filemanager.common.R.string.realme_share
            }

            else -> {
                thirdPkgName = KtConstants.PKG_OPPO_SHARE
                pathList = CATEGORY_PATH_OPPO_SHARE
                com.filemanager.common.R.string.oppo_share
            }
        }
        val shareBean = MainCategoryItemsBean(
            CategoryHelper.CATEGORY_SHARE,
            context.getString(name),
            com.filemanager.common.R.drawable.main_category_share,
            0,
            0,
            null,
            0
        )
        shareBean.packageName = thirdPkgName
        shareBean.thirdPkgName = thirdPkgName
        shareBean.nameResId = name
        shareBean.fileList = pathList
        shareBean.searchId = FilterConstants.FILTER_FROM_TRANSFER
        if (!FeatureCompat.sIsExpRom && sIsOShareDisabled.not()) {
            mainCategoryItemList.add(shareBean)
        } else if (FeatureCompat.sIsExpRom) {
            var hasFile = false
            runBlocking {
                try {
                    withTimeout(FILE_TIME_OUT) {
                        for (dir in pathList) {
                            val file = File(Environment.getExternalStorageDirectory(), dir)
                            if (hasFileInDir(file)) {
                                hasFile = true
                                break
                            }
                        }
                    }
                } catch (ex: TimeoutCancellationException) {
                    Log.d(TAG, "check hasFileInDir timeout")
                }
            }
            if (hasFile) {
                mainCategoryItemList.add(shareBean)
            }
        }
    }

    private fun isOShareDisabledByCustomize(): Boolean {
        return CompatUtils.compactSApi({
            OplusDevicepolicyManager.getInstance()
                .getBoolean(OSHARE_DISABLED_KEY, OplusDevicepolicyManager.CUSTOMIZE_DATA_TYPE, false)
                .also {
                    Log.d(TAG, "isOShareDisabledByCustomize $it")
                }
        }, {
            false
        })
    }

    private fun checkAndAddApkInfo(
        mainCategoryItemList: MutableList<MainCategoryItemsBean>,
        installAppList: ArrayList<SaveInitItem>,
        context: Context,
        pkgName: String,
        iconResId: Int,
        nameResId: Int,
        filePath: Array<String>
    ): Boolean {
        val isInstalled = AppUtils.isAppInstalledByPkgName(context, pkgName)
        Log.d(TAG, "addOneApkInfo $pkgName installed $isInstalled")
        if (isInstalled) {
            val itemBean = MainCategoryItemsBean(
                CategoryHelper.CATEGORY_QQ, context.getString(nameResId), iconResId,
                0, 0, null, 0
            )
            itemBean.packageName = pkgName
            itemBean.fileList = filePath
            mainCategoryItemList.add(itemBean)
            installAppList.add(SaveInitItem(pkgName))
        }
        return isInstalled
    }

    fun getMainSuperInitList(): ArrayList<MainCategoryItemsBean>? {
        val items = getInitAppList(appContext)
        if (items.isNullOrEmpty()) {
            return null
        }
        val appList = ArrayList<MainCategoryItemsBean>()
        for (item in items) {
            val bean = MainCategoryItemsBean(
                CategoryHelper.CATEGORY_QQ,
                item.appName,
                item.resourceId,
                CATEGORY_INVALID_ITEM_COUNT,
                CATEGORY_INVALID_FILE_SIZE,
                null,
                0
            )
            bean.packageName = item.appName
            appList.add(bean)
        }
        //add default items
        addOtherSuperItem(appContext, appList)

        Log.d(TAG, "getMainSuperInitList appList: " + appList.size)
        return appList
    }

    @Synchronized
    fun getInitAppList(context: Context?): ArrayList<AppItem>? {
        if (context == null) {
            Log.d(TAG, "getInitAppList context is null")
            return null
        }
        var region = PropertyCompat.sSystemRegion
        if (!FeatureCompat.sIsExpRom) {
            region = "CN"
        }
        val appCache = PreferencesUtils.getString(APP_INSTALL_STATE_CACHE, region, null)
        Log.d(TAG, "getInitAppList appCache: $appCache region: $region")
        if (TextUtils.isEmpty(appCache)) {
            Log.e(TAG, "getInitAppList appCache = null")
            return null
        }
        val appList = appCache!!.split(INSTALL_CACHE_SPLITTER.toRegex()).toTypedArray()
        val appItems = ArrayList<AppItem>()
        for (app in appList) {
            if (TextUtils.isEmpty(app)) {
                continue
            }
            val saveItemString = app.split(":").toTypedArray()
            if (saveItemString.isNotEmpty()) {
                when (saveItemString[0]) {
                    FLAG_APP_MESSAGER -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.file_facebook_icon,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.facebook)
                            }
                        )
                    )

                    FLAG_APP_WHATSAPP -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.file_whatsapp_icon,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.whatapp)
                            }
                        )
                    )

                    FLAG_APP_BBM -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.file_bbm_icon,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.bbm)
                            }
                        )
                    )

                    FLAG_APP_ZALO -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.file_zalo_icon,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.zalo)
                            }
                        )
                    )

                    FLAG_APP_LINE -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.file_line_icon,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.line)
                            }
                        )
                    )

                    FLAG_APP_QQ -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.main_category_qq,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.string_qq)
                            }
                        )
                    )

                    FLAG_APP_WECHAT -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.main_category_wechat,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.string_wechat)
                            }
                        )
                    )

                    FLAG_IMO -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.file_imo_icon,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.imo)
                            }
                        )
                    )

                    FLAG_PLUS_MESSAGER -> appItems.add(
                        AppItem(
                            com.filemanager.common.R.drawable.file_messager_plus_icon,
                            if (saveItemString.size > 1) {
                                saveItemString[1]
                            } else {
                                context.getString(com.filemanager.common.R.string.string_wechat)
                            }
                        )
                    )

                    else -> {
                    }
                }
            }
        }
        return appItems
    }

    private fun saveInstallCacheState(
        context: Context?,
        appList: ArrayList<SaveInitItem>,
        region: String
    ) {
        if (context == null) {
            Log.w(TAG, "saveInstallCacheState invalid paramters")
            return
        }
        try {
            val builder = StringBuilder()
            var flag: String?
            for (app in appList) {
                flag = getAppFlag(context, app.packageName).plus(":").plus(app.appName)
                builder.append(flag)
                builder.append(INSTALL_CACHE_SPLITTER)
            }
            Log.d(TAG, "saveInstallCacheState flagList: $builder")
            PreferencesUtils.put(APP_INSTALL_STATE_CACHE, region, builder.toString())
        } catch (ex: Exception) {
            Log.w(TAG, "saveInstallCacheState error: $ex")
        }
    }

    fun getAppFlag(context: Context?, packageName: String?): String? {
        if (context == null || TextUtils.isEmpty(packageName)) {
            Log.w(TAG, "getAppFlag invalidate parameters")
            return null
        }
        var flag: String? = null
        when (packageName) {
            MESSAGER -> flag = FLAG_APP_MESSAGER
            WHATSAPP -> flag = FLAG_APP_WHATSAPP
            BBM -> flag = FLAG_APP_BBM
            ZALO -> flag = FLAG_APP_ZALO
            LINE -> flag = FLAG_APP_LINE
            QQ -> flag = FLAG_APP_QQ
            WECHAT -> flag = FLAG_APP_WECHAT
            IMO -> flag = FLAG_IMO
            PLUS_MESSAGER -> flag = FLAG_PLUS_MESSAGER
            else -> {
            }
        }
        Log.d(TAG, "getAppFlag flag : $flag")
        return flag
    }

    fun updateSupperAppPaths(context: Context?): java.util.ArrayList<String?> {
        val supperAppPaths: java.util.ArrayList<String?> = java.util.ArrayList<String?>()
        if (context == null) {
            return supperAppPaths
        }
        val items: List<MainCategoryItemsBean> = getCategoryItems(appContext)
        if (items.isNotEmpty()) {
            for (mainCategoryItemsBean in items) {
                supperAppPaths.addAll(BlacklistParser.getOneAppPaths(mainCategoryItemsBean.fileList))
            }
        }
        return supperAppPaths
    }

    fun isIgnoredPath(
        context: Context,
        type: Int,
        path: String?,
        internalPath: String?,
        ignoredPaths: SparseArray<String?>?,
        categoryItems: List<MainCategoryItemsBean>?
    ): Boolean {
        when (type) {
            CategoryHelper.CATEGORY_IMAGE -> return CategoryHelper.isIgnoredPathWithType(
                path,
                internalPath,
                ignoredPaths
            )

            CategoryHelper.CATEGORY_DOC -> return (CategoryHelper.isIgnoredPathWithType(
                path,
                internalPath,
                ignoredPaths
            ) && isNotQQAndWeChatFile(path, context, categoryItems ?: mutableListOf()))

            else -> {
            }
        }
        return false
    }

    fun isNotQQAndWeChatFile(
        inputPath: String?,
        context: Context?,
        items: List<MainCategoryItemsBean>
    ): Boolean {
        var path = inputPath
        if (path == null || context == null) {
            return true
        }
        var string: String?
        if (items.isNotEmpty()) {
            for (mainCategoryItemsBean in items) {
                val sevenPaths = mainCategoryItemsBean.fileList
                path = path!!.lowercase(Locale.getDefault())
                if (sevenPaths != null) {
                    for (sevenPath in sevenPaths) {
                        string = iPath + File.separator + sevenPath
                        string = string.lowercase(Locale.getDefault())
                        if (sevenPath != null && path.startsWith(string)) {
                            Log.v(TAG, "isNotQQAndWeChatFile  yes it is  path = $path")
                            return false
                        }
                    }
                }
            }
        }
        return true
    }

    fun getPrivatePath(name: String?): String {
        return when (name) {
            QQ -> PRIVATE_QQ_PATH
            WECHAT -> PRIVATE_WECHAT_PATH
            else -> ""
        }
    }

    data class AppItem(val resourceId: Int, val appName: String)
    data class SaveInitItem(val packageName: String, val appName: String? = null)

    @JvmStatic
    @VisibleForTesting
    fun hasFileInDir(dir: File): Boolean {
        if (!dir.exists()) {
            return false
        }
        if (!dir.isDirectory) {
            return false
        }
        val children = JavaFileHelper.listFiles(dir)
        if (children.isNullOrEmpty()) {
            return false
        }
        for (child in children) {
            if (child.isDirectory) {
                val result = hasFileInDir(child)
                if (result) {
                    return true
                }
            } else if (child.exists()) {
                return true
            }
        }
        return false
    }
}