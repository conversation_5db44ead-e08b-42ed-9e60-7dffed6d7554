/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SortMode
 ** Description : 排序类型
 ** Version     : 1.0
 ** Date        : 2024/01/12 09:51
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue        2024/01/12       1.0      create
 ***********************************************************************/
package com.filemanager.common.sort

import androidx.annotation.ArrayRes
import com.filemanager.common.R

/**
 * 普通排序：修改时间、大小、名称、类型
 */
private val FILE_ORDER_INDEX_ARRAY = listOf(
    SortHelper.FILE_TIME_REVERSE_ORDER, SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER,
    SortHelper.FILE_NAME_ORDER, SortHelper.FILE_TYPE_ORDER
)

/**
 * 有打开时间排序：打开时间、修改时间、大小、名称、类型
 * 目前文档页面使用
 */
private val FILE_LAST_TIME_ORDER_INDEX_ARRAY = listOf(
    SortHelper.FILE_LAST_OPEN_TIME_ORDER, SortHelper.FILE_TIME_REVERSE_ORDER,
    SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER,
    SortHelper.FILE_NAME_ORDER, SortHelper.FILE_TYPE_ORDER
)

/**
 * 剩余时间排序：剩余时间、大小、名称、类型
 * 最近删除页面使用
 */
private val RECYCLE_FILE_ORDER_INDEX_ARRAY = listOf(
    SortHelper.FILE_TIME_DELETE_ORDER, SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER,
    SortHelper.FILE_NAME_ORDER, SortHelper.FILE_TYPE_ORDER
)

/**
 * 腾讯文档排序：打开时间，修改时间，名称
 * 三方云文档界面使用
 */
private val TENCENT_DOCS_ORDER_INDEX_ARRAY =
    listOf(SortHelper.FILE_LAST_OPEN_TIME_ORDER, SortHelper.FILE_TIME_REVERSE_ORDER, SortHelper.FILE_NAME_ORDER)

/**
 * 金山文档排序：修改时间，大小，名称
 * 三方云文档界面使用
 */
private val KINGSOFT_DOCS_ORDER_INDEX_ARRAY =
    listOf(SortHelper.FILE_TIME_REVERSE_ORDER, SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER, SortHelper.FILE_NAME_ORDER)

/**
 * 图集内页图片排序：拍摄时间、修改时间、名称
 * 图集页面使用
 */
private val ALBUM_FILE_ORDER_INDEX_ARRAY = listOf(
    SortHelper.FILE_DATE_TAKEN_ORDER,
    SortHelper.FILE_TIME_REVERSE_ORDER, SortHelper.FILE_NAME_ORDER
)

private val REMOTE_MAC_ORDER_INDEX_ARRAY = listOf(
    SortHelper.FILE_TIME_REVERSE_ORDER,
    SortHelper.FILE_LAST_OPEN_TIME_ORDER,
    SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER,
    SortHelper.FILE_NAME_ORDER,
    SortHelper.FILE_TYPE_ORDER
)

enum class SortMode(var orderIndexArray: List<Int>, @ArrayRes val displayId: Int) {

    /**
     * 默认显示：修改时间，大小，名称，类型
     */
    DEFAULT(FILE_ORDER_INDEX_ARRAY, R.array.sort_popup_item_list),

    /**
     * 文件界面：打开时间，修改时间，大小，名称，类型
     */
    LOCAL_DOCS(FILE_LAST_TIME_ORDER_INDEX_ARRAY, R.array.last_open_time_sort_popup_item_list),

    /**
     * 最近删除：剩余时间，大小，名称，类型
     */
    RECYCLE_BIN(RECYCLE_FILE_ORDER_INDEX_ARRAY, R.array.recycle_sort_popup_item_list),

    /**
     * 腾讯文档：打开时间，修改时间，名称
     */
    TENCENT_DOCS(TENCENT_DOCS_ORDER_INDEX_ARRAY, R.array.tencent_docs_sort_popup_item_list),

    /**
     * 金山文档：修改时间，大小，名称
     */
    KINGSOFT_DOCS(KINGSOFT_DOCS_ORDER_INDEX_ARRAY, R.array.kingsoft_docs_sort_popup_item_list),

    /**
     * 图集内页：拍摄时间，文件修改时间，名称
     */
    ALBUM(ALBUM_FILE_ORDER_INDEX_ARRAY, R.array.album_docs_sort_popup_item_list),

    /**
     * 远程Mac文件列表页：修改时间，上次打开时间，大小，名称，类型
     */
    REMOTE_MAC(REMOTE_MAC_ORDER_INDEX_ARRAY, R.array.remote_mac_sort_popup_item_list);

    fun indexOf(sortMode: Int): Int {
        return orderIndexArray.indexOf(sortMode)
    }

    fun getSortMode(position: Int): Int {
        return orderIndexArray.get(position)
    }
}