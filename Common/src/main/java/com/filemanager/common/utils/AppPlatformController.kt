/*********************************************************************
 * * Copyright (C), 2021, OPlus. All rights reserved.
 * * VENDOR_EDIT
 * * File        :  -
 * * Version     : 1.0
 * * Date        : 2021/8/2
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.utils

import android.app.Activity
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.LifecycleOwner
import com.filemanager.common.R
import com.filemanager.common.compat.OplusBuildCompat
import com.filemanager.common.compat.PropertyCompat
import com.filemanager.common.controller.BaseLifeController

class AppPlatformController(lifecycleOwner: LifecycleOwner?) {

    companion object {
        private const val APP_PLATFORM_PACKAGE_NAME = "com.oplus.appplatform"
        private const val APP_PLATFORM_PACKAGE_NAME_OLD = "com.heytap.appplatform"
    }

    private var mDialog: AlertDialog? = null

    private val mAppPlatformName by lazy {
        if (PropertyCompat.sColorOSVersionCode < OplusBuildCompat.OS_11_3) {
            APP_PLATFORM_PACKAGE_NAME_OLD
        } else {
            APP_PLATFORM_PACKAGE_NAME
        }
    }

    init {
        lifecycleOwner?.lifecycle?.addObserver(object : BaseLifeController {

            override fun onDestroy() {
                if(mDialog != null) {
                    mDialog!!.dismiss()
                    mDialog = null
                }
            }

        })
    }

    fun checkAppPlatformEnabled(activity: Activity) {
        if(mDialog?.isShowing == true) {
            return
        }

        mDialog = KtAppUtils.checkAppEnabledWithDialogReturn(
            activity,
            mAppPlatformName,
            R.string.private_safe_disable_message
        )
    }
}