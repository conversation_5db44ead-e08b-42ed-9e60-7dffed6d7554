/*********************************************************************************
 * Copyright (C), 2008-2025, Oplus, All rights reserved.
 *
 * File: SortAnimationHelper
 * Description:
 * Version: 1.0
 * Date: 2025/3/6
 * Author: W9017232
 *
 * ------------------------------- Revision History: ----------------------------
 * <author>                        <date>       <version>   <desc>
 * ------------------------------------------------------------------------------
 * W9017232                         2025/3/6      1.0         Create this module
 *********************************************************************************/
package com.filemanager.common.helper

import android.view.animation.PathInterpolator
import androidx.recyclerview.widget.RecyclerView
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.animation.BaseItemAnimator
import com.filemanager.common.animation.SortMoveAnimator
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.sort.SortHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.utils.Log

class SortAnimationHelper(type: String) {

    companion object {
        const val TAG = "SortAnimationHelper"
        const val MOVE_DURATION = 500L
        const val ADD_OR_REMOVE_DURATION = 167L
        const val CONTROL_X1 = 0.31f
        const val CONTROL_Y1 = 0f
        const val CONTROL_X2 = 0.11f
        const val CONTROL_Y2 = 1f
    }

    private var sortType: Int = SortHelper.FILE_NAME_ORDER
    private var isDesc = true
    private var categoryType: String = type

    private var pChangeDuration = 0L
    private var pMoveDuration = 0L
    private var pAddDuration = 0L
    private var pRemoveDuration = 0L

    val sortAnimator by lazy {
        SortMoveAnimator().apply {
            setInterpolator(
                PathInterpolator(CONTROL_X1, CONTROL_Y1, CONTROL_X2, CONTROL_Y2),
                BaseItemAnimator.TYPE_MOVE
            )
            setAddAnimationNoDelay()
            setRemoveAnimationNoDelay()
            moveDuration = MOVE_DURATION
            addDuration = ADD_OR_REMOVE_DURATION
            removeDuration = ADD_OR_REMOVE_DURATION
            pChangeDuration = changeDuration
            pMoveDuration = moveDuration
            pAddDuration = addDuration
            pRemoveDuration = removeDuration
        }
    }

    init {
        initData()
    }

    fun initData() {
        sortType = SortModeUtils.getSharedSortMode(appContext, categoryType)
        isDesc = SortModeUtils.getSharedSortOrder(categoryType)
        Log.d(TAG, "init categoryType=$categoryType sortType=$sortType isDesc=$isDesc")
    }

    fun needDoListAnimate(): Boolean {
        val nowSortType = SortModeUtils.getSharedSortMode(appContext, categoryType)
        val nowIsDesc = SortModeUtils.getSharedSortOrder(categoryType)
        Log.d(TAG, "preSortType=$sortType preIsDesc=$isDesc nowSortType=$nowSortType nowIsDesc=$nowIsDesc")
        if (nowIsDesc != isDesc || nowSortType != sortType) {
            sortType = nowSortType
            isDesc = nowIsDesc
            return true
        }
        return false
    }

    fun setAnimatorDurationWithScanModeChange(recyclerView: RecyclerView?, scanMode: Int) {
        Log.d(TAG, "scanMode=$scanMode")
        initData()
        // 切换预览模式后，切换宫格模式itemAnimator为空，在这里重新赋值
        recyclerView?.itemAnimator = sortAnimator
        if (scanMode == KtConstants.SCAN_MODE_LIST) {
            recyclerView?.itemAnimator?.apply {
                changeDuration = 0
                removeDuration = 0
                moveDuration = 0
                addDuration = 0
            }
        } else {
            recyclerView?.itemAnimator?.apply {
                changeDuration = pChangeDuration
                moveDuration = pMoveDuration
                addDuration = pAddDuration
                removeDuration = pRemoveDuration
            }
        }
    }
}

