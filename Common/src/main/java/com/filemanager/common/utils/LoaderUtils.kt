/***********************************************************
 ** Copyright (C), 2008-2017, OPPO Mobile Comm Corp., Ltd.
 ** VENDOR_EDIT
 ** File:
 ** Description:
 ** Version: 1.0
 ** Date: 2020/7/25
 ** Author: <PERSON><PERSON><PERSON>(<EMAIL>)
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.utils

import java.util.regex.Pattern
import java.util.regex.PatternSyntaxException

object LoaderUtils {

    @JvmStatic
    fun getPattern(filter: String): Pattern? {
        var nameFilter = filter
        if (!nameFilter.contains("*")) {
            nameFilter = "*$nameFilter*"
        }

        // below turn off the regex symbols turn to a normal'\'at first
        nameFilter = nameFilter.replace("\\", "\\\\\\\\")
        nameFilter = nameFilter.replace("^", "\\^")
        nameFilter = nameFilter.replace("$", "\\$")
        nameFilter = nameFilter.replace("+", "\\+")
        nameFilter = nameFilter.replace("{", "\\{")
        nameFilter = nameFilter.replace("}", "\\}")
        nameFilter = nameFilter.replace("[", "\\[")
        nameFilter = nameFilter.replace("]", "\\]")
        nameFilter = nameFilter.replace("-", "\\-")
        // between first & last
        nameFilter = nameFilter.replace(".", "\\.")
        // must at the last
        nameFilter = nameFilter.replace("*", ".*")

        return try {
            Pattern.compile(nameFilter)
        } catch (e: PatternSyntaxException) {
            // then stop the search
            null
        }
    }

    @JvmStatic
    fun createFuzzySearch(source: String?): String {
        return if (source.isNullOrEmpty()) {
            ""
        } else {
            var searchKey = source.replace("'", "''")
            val escArray = arrayOf("/", "[", "]", "%", "&", "_", "(", ")")
            for (esc in escArray) {
                searchKey = searchKey.replace(esc, "/$esc")
            }
            val builder = StringBuilder()
            //去除头尾空格
            searchKey = searchKey.trim()
            builder.append(" LIKE '%").append(searchKey).append("%' ESCAPE '/'")
            Log.d("LoaderUtils", "createFuzzySearch: $builder")
            builder.toString()
        }
    }
}