/*********************************************************************
 ** Copyright (C), 2010-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : FileLabelMappingDBHelper.kt
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/7/15
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <date>       <version>  <desc>
 **  dustin.shu      2022/7/15      1.0        create
 ***********************************************************************/
package com.oplus.filemanager.provider

import android.annotation.SuppressLint
import androidx.annotation.WorkerThread
import com.filemanager.common.MyApplication
import com.filemanager.common.pagination.PageUtil
import com.filemanager.common.utils.AndroidDataHelper
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.closeQuietly
import com.oplus.filemanager.room.AppDatabase
import com.oplus.filemanager.room.model.FileLabelEntity
import com.oplus.filemanager.room.model.FileLabelMappingEntity

object FileLabelMappingDBHelper {

    const val TAG = "FileLabelMappingDBHelper"

    private fun getAppDatabase(): AppDatabase {
        return AppDatabase.getInstance(MyApplication.sAppContext)
    }

    @WorkerThread
    fun insertFileLabelMappingEntity(entity: FileLabelMappingEntity) {
        kotlin.runCatching {
            if (entity.filePath == AndroidDataHelper.ANDROID_DATA_PATH) {
                entity.filePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
            }
            getAppDatabase().fileLabelMappingDao()?.insertItem(entity)
        }.onFailure {
            Log.d(TAG, "insertFileLabelMappingEntity error ${it.message}")
        }
    }

    @WorkerThread
    fun insertFileLabelMappingEntities(entities: List<FileLabelMappingEntity>) {
        kotlin.runCatching {
            entities.filter { it.filePath == AndroidDataHelper.ANDROID_DATA_PATH }
                .forEach {
                    Log.d(TAG, "insertFileLabelMappingEntities data replace")
                    it.filePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
                }
            PageUtil.page(entities).forEach { getAppDatabase().fileLabelMappingDao()?.insertItems(it.pageData) }
        }.onFailure {
            Log.d(TAG, "insertFileLabelMappingEntities error ${it.message}")
        }
    }

    @WorkerThread
    fun updateFileLabelMappingEntity(entity: FileLabelMappingEntity) {
        kotlin.runCatching {
            getAppDatabase().fileLabelMappingDao()?.updateItem(entity)
        }.onFailure {
            Log.d(TAG, "updateFileLabelMappingEntity error ${it.message}")
        }
    }

    @WorkerThread
    fun getFileListByLabelId(fileLabelId: Long): List<FileLabelMappingEntity>? {
        val labelList = getAppDatabase().fileLabelMappingDao()?.getFileListByLabelId(fileLabelId)
        Log.d(TAG, "getFileListByLabelId ${labelList?.size}")
        return labelList?.filter {
            if (AndroidDataHelper.isAndroidData(it.filePath)) {
                if (AndroidDataHelper.openAndroidData == true) {
                    it.filePath = AndroidDataHelper.ANDROID_DATA_PATH
                } else {
                    it.filePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
                }
                Log.d(TAG, "getFileListByLabelId data replace ${AndroidDataHelper.openAndroidData}")
                true
            } else {
                AndroidDataHelper.openAndroidData == true || !AndroidDataHelper.isAndroidDataPath(it.filePath)
            }
        }
    }

    @WorkerThread
    fun getFileLabelsByPath(path: String): List<FileLabelEntity>? {
        var newPath = path
        if (path == AndroidDataHelper.ANDROID_DATA_PATH) {
            Log.d(TAG, "getFileLabelsByPath data replace")
            newPath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
        }
        val mappingEntityListByPath = getAppDatabase().fileLabelMappingDao()?.getFileListByPath(newPath)
            ?.filter {
                if (AndroidDataHelper.isAndroidData(it.filePath)) {
                    if (AndroidDataHelper.openAndroidData == true) {
                        it.filePath = AndroidDataHelper.ANDROID_DATA_PATH
                    } else {
                        it.filePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
                    }
                    Log.d(TAG, "getFileLabelsByPath data replace ${AndroidDataHelper.openAndroidData}")
                    true
                } else {
                    AndroidDataHelper.openAndroidData == true
                            || !AndroidDataHelper.isAndroidDataPath(it.filePath)
                }
            }
        return if (mappingEntityListByPath.isNullOrEmpty()) {
            null
        } else {
            val result = arrayListOf<FileLabelEntity>()
            PageUtil.page(mappingEntityListByPath.map { it.labelId }).forEach { pageInfo ->
                result.addAll(getAppDatabase().fileLabelDao()?.getItemsByIds(pageInfo.pageData) ?: arrayListOf())
            }
            result
        }
    }

    @WorkerThread
    fun getCommonFileLabelsByPaths(paths: List<String>): List<FileLabelEntity>? {
        var commonLabelList: MutableSet<Long> = mutableSetOf()
        val filePathAndLabelIdMap = mutableMapOf<String, MutableList<Long>>()
        paths.forEach { path ->
            var newPath = path
            if (path == AndroidDataHelper.ANDROID_DATA_PATH) {
                Log.d(TAG, "getCommonFileLabelsByPaths data replace")
                newPath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
            }
            val mappingEntityListByPath = getAppDatabase().fileLabelMappingDao()?.getFileListByPath(newPath)
            val labelIds: MutableList<Long> = arrayListOf()
            mappingEntityListByPath?.forEach { labelIds.add(it.labelId) }
            filePathAndLabelIdMap[path] = labelIds
        }

        filePathAndLabelIdMap.onEachIndexed { index: Int, entry: Map.Entry<String, MutableList<Long>> ->
            commonLabelList = if (index == 0) {
                entry.value.toMutableSet()
            } else {
                if (commonLabelList.isEmpty()) {
                    return null
                }
                commonLabelList.intersect(entry.value.toSet()) as MutableSet<Long>
            }
        }
        return getAppDatabase().fileLabelDao()?.getItemsByIds(commonLabelList.toList())
    }

    @WorkerThread
    fun getFileByLabelIdAndPath(labelId: Long, filePath: String): FileLabelMappingEntity? {
        var newPath = filePath
        if (filePath == AndroidDataHelper.ANDROID_DATA_PATH) {
            Log.d(TAG, "getFileByLabelIdAndPath data replace")
            newPath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
        }
        val fileLabelMappingEntity = getAppDatabase().fileLabelMappingDao()?.getFileByLabelIdAndPath(labelId, newPath)
        fileLabelMappingEntity?.apply {
            if (AndroidDataHelper.isAndroidData(this.filePath)) {
                if (AndroidDataHelper.openAndroidData == true) {
                    this.filePath = AndroidDataHelper.ANDROID_DATA_PATH
                } else {
                    this.filePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
                }
                Log.d(TAG, "getFileByLabelIdAndPath data replace ${AndroidDataHelper.openAndroidData}")
            }
        }
        return fileLabelMappingEntity
    }

    @WorkerThread
    fun getFileByLabelIdAndPathList(labelId: Long, filePath: ArrayList<String>): List<String>? {
        val pathList = mutableListOf<String>()
        filePath.forEach {
            if (it == AndroidDataHelper.ANDROID_DATA_PATH) {
                pathList.add(AndroidDataHelper.ANDROID_DATA_NORMAL_PATH)
            } else {
                pathList.add(it)
            }
        }
        val resultPath = mutableListOf<String>()
        getAppDatabase().fileLabelMappingDao()?.getFileByLabelIdAndPathList(labelId, pathList)?.forEach {
            if (AndroidDataHelper.isAndroidData(it)) {
                if (AndroidDataHelper.openAndroidData == true) {
                    resultPath.add(AndroidDataHelper.ANDROID_DATA_PATH)
                } else {
                    resultPath.add(AndroidDataHelper.ANDROID_DATA_NORMAL_PATH)
                }
                Log.d(TAG, "getFileByLabelIdAndPathList data replace ${AndroidDataHelper.openAndroidData}")
            } else {
                resultPath.add(it)
            }
        }
        return resultPath
    }

    @WorkerThread
    fun updateMappingByPath(oldPath: String, newPath: String) {
        kotlin.runCatching {
            var replaceOldPath = oldPath
            var replaceNewPath = newPath
            if (oldPath == AndroidDataHelper.ANDROID_DATA_PATH) {
                Log.d(TAG, "updateMappingByPath replaceOldPath replace")
                replaceOldPath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
            }
            if (newPath == AndroidDataHelper.ANDROID_DATA_PATH) {
                Log.d(TAG, "updateMappingByPath replaceNewPath replace")
                replaceNewPath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
            }
            val mappingResultList = getAppDatabase().fileLabelMappingDao()?.getFileListByPathFuzzy(replaceOldPath)
            mappingResultList?.forEach { it.filePath = it.filePath.replace(replaceOldPath, replaceNewPath) }
            getAppDatabase().fileLabelMappingDao()?.updateItems(mappingResultList)
        }.onFailure {
            Log.d(TAG, "updateMappingByPath error ${it.message}")
        }
    }

    @WorkerThread
    fun removeMappingByPath(path: String): ArrayList<FileLabelMappingEntity> {
        var replacePath = path
        if (path == AndroidDataHelper.ANDROID_DATA_PATH) {
            Log.d(TAG, "removeMappingByPath data replace")
            replacePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
        }
        var mappingResultList: List<FileLabelMappingEntity>? = mutableListOf()
        kotlin.runCatching {
            mappingResultList = getAppDatabase().fileLabelMappingDao()?.getFileListByPathFuzzy(replacePath)
            val result = getAppDatabase().fileLabelMappingDao()?.deleteItems(mappingResultList)
            Log.d(TAG, "removeMappingByPath:$replacePath  result:$result ")
        }.onFailure {
            Log.d(TAG, "removeMappingByPath error ${it.message}")
        }
        return mappingResultList as ArrayList<FileLabelMappingEntity>
    }

    @WorkerThread
    fun removeMappingByIdAndPath(labelId: Long, path: String) {
        var replacePath = path
        if (path == AndroidDataHelper.ANDROID_DATA_PATH) {
            Log.d(TAG, "removeMappingByIdAndPath data replace")
            replacePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
        }
        kotlin.runCatching {
            getAppDatabase().fileLabelMappingDao()?.removeMappingByIdAndPath(labelId, replacePath)
        }.onFailure {
            Log.d(TAG, "removeMappingByIdAndPath error ${it.message}")
        }
    }

    @WorkerThread
    fun getFilesHasLabel(paths: List<String>): List<String> {
        val pagePath = PageUtil.page(paths)
        val result = arrayListOf<String>()
        pagePath.forEach { pageInfo ->
            val pathList = mutableListOf<String>()
            pageInfo.pageData?.forEach { path ->
                if (path == AndroidDataHelper.ANDROID_DATA_PATH) {
                    pathList.add(AndroidDataHelper.ANDROID_DATA_NORMAL_PATH)
                } else {
                    pathList.add(path)
                }
            }
            val pageResult =
                getAppDatabase().fileLabelMappingDao()?.getFileListByPathList(pathList)
                    ?.map {
                        if (AndroidDataHelper.isAndroidData(it.filePath)) {
                            if (AndroidDataHelper.openAndroidData == true) {
                                it.filePath = AndroidDataHelper.ANDROID_DATA_PATH
                            } else {
                                it.filePath = AndroidDataHelper.ANDROID_DATA_NORMAL_PATH
                            }
                            Log.d(TAG, "getFilesHasLabel data open ${AndroidDataHelper.openAndroidData}")
                        }
                        it.filePath
                    } ?: arrayListOf()
            result.addAll(pageResult)
        }
        return result
    }

    @WorkerThread
    fun removeMappingFileByPaths(paths: List<String>) {
        kotlin.runCatching {
            PageUtil.page(paths).forEach {
                val pathList = mutableListOf<String>()
                it.pageData.forEach {
                    if (it == AndroidDataHelper.ANDROID_DATA_PATH) {
                        pathList.add(AndroidDataHelper.ANDROID_DATA_NORMAL_PATH)
                    } else {
                        pathList.add(it)
                    }
                }
                getAppDatabase().fileLabelMappingDao()?.removeMappingFileByPaths(pathList)
            }
        }.onFailure {
            Log.d(TAG, "removeMappingFileByPaths error ${it.message}")
        }
    }

    @WorkerThread
    @SuppressLint("Range")
    fun getTopMappingFileCountLabelIds(): List<Long> {
        val ids = mutableListOf<Long>()
        val cursor = getAppDatabase()
            .query("select label_id as labelId, count(*) as cnt from file_label_mapping group by label_id order by cnt desc",
                null
            )
        while (cursor.moveToNext()) {
            val id = cursor.getLong(cursor.getColumnIndex("labelId"))
            val cnt = cursor.getInt(cursor.getColumnIndex("cnt"))
            Log.v(TAG, "getTopMappingFileCountLabelIds id = $id, cnt = $cnt")
            ids.add(id)
        }
        cursor.closeQuietly()
        return ids
    }

    fun getRecentMappingFilesById(id: Long, recentCount: Int): List<FileLabelMappingEntity>? {
        return getAppDatabase().fileLabelMappingDao()?.getRecentMappingFilesById(id, recentCount)
    }

    fun getAllMappings(): List<FileLabelMappingEntity>? {
        return getAppDatabase().fileLabelMappingDao()?.getAllMappings()
    }
}