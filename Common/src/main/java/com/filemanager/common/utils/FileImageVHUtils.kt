/***********************************************************
 ** Copyright (C), 2020-2030, OPLUS Mobile Comm Corp., Ltd.
 ** File: FileImageVHUtils
 ** Description: File image utils for grid or list
 ** Version: 1.0
 ** Date : 2024/10/01
 ** Author: 80249800
 **
 ** ---------------------Revision History: ---------------------
 **  <author> <data>   <version >    <desc>
 *
 ****************************************************************/

package com.filemanager.common.utils

import android.content.Context
import android.text.TextUtils
import android.util.Size
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.BaseVMActivity
import com.filemanager.common.constants.KtConstants
import com.filemanager.common.filepreview.util.PreviewUtils
import com.filemanager.common.helper.MimeTypeHelper
import com.filemanager.common.helper.ViewHelper
import com.filemanager.common.view.FileThumbView
import com.filemanager.thumbnail.ThumbnailManager

object FileImageVHUtils {

    private const val TAG = "FileImageVHUtils"

    private val gridVideoImageWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_video_image_size)
    private val gridApkIconWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_apk_image_size)
    private val gridDefaultIconWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_default_icon_size)
    private val gridDocThumbnailWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_doc_thumbnail_size)
    private val gridDirIconWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_grid_dir_icon_size)

    private val listVideoImageWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_list_video_image_size)
    private val listApkIconWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_list_apk_image_size)
    private val listDefaultIconWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_list_default_icon_size)
    private val listDocThumbnailWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_list_doc_thumbnail_size)
    private val listDirIconWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.file_list_dir_icon_size)
    private val listSearchThirdAppIconWith = MyApplication.appContext.resources.getDimensionPixelSize(R.dimen.main_image_width)

    private var listAdapterMargin = MyApplication.appContext.resources?.getDimensionPixelSize(R.dimen.dimen_16dp) ?: 0
    private var showPreview = PreviewUtils.isPreviewOpen() && !WindowUtils.isSmallScreen(MyApplication.appContext)
    private var sideNavigationLeftMargin = MyApplication.appContext.resources?.getDimensionPixelSize(R.dimen.dimen_24dp) ?: 0
    private var sideNavigationStatus: Int = KtConstants.SIDE_NAVIGATION_OPEN
    private var isSmallScreen = false

    @JvmStatic
    fun changedListMargin(activity: BaseVMActivity, sideNavigationWidth: Int, sideNavigationStatus: Int) {
        this.sideNavigationStatus = sideNavigationStatus
        if (WindowUtils.isSmallScreen(activity)) {
            listAdapterMargin = activity.resources?.getDimensionPixelSize(R.dimen.dimen_16dp) ?: 0
            Log.d(TAG, "changedListMargin small")
            isSmallScreen = true
            showPreview = false
            return
        }
        val windowWidth = KtViewUtils.getWindowSize(activity).x
        val widthDp = if (sideNavigationStatus == KtConstants.SIDE_NAVIGATION_OPEN) {
            ViewHelper.px2dip(MyApplication.appContext, windowWidth - sideNavigationWidth)
        } else {
            ViewHelper.px2dip(MyApplication.appContext, windowWidth)
        }
        val windowType = WindowUtils.getScreenWindowType(widthDp, 0)
        Log.d(TAG, "changedListMargin windowWidth=$windowWidth, sideWidth=$sideNavigationWidth," +
                " sideStatus=$sideNavigationStatus, widthDp=$widthDp, windowType: $windowType")
        listAdapterMargin = when (windowType) {
            WindowUtils.LARGE -> activity.resources?.getDimensionPixelSize(R.dimen.dimen_40dp) ?: 0
            WindowUtils.SMALL -> activity.resources?.getDimensionPixelSize(R.dimen.dimen_16dp) ?: 0
            else -> activity.resources?.getDimensionPixelSize(R.dimen.dimen_24dp) ?: 0
        }
    }

    @JvmStatic
    fun getListLeftMargin(): Int {
        if (sideNavigationStatus == KtConstants.SIDE_NAVIGATION_CLOSE && !isSmallScreen) {
            //侧导收起时，左边对齐侧导的左间距，固定24dp
            return sideNavigationLeftMargin
        }
        return listAdapterMargin
    }

    @JvmStatic
    fun setShowPreview(isShown: Boolean) {
        showPreview = isShown
    }

    @JvmStatic
    fun getListRightMargin(): Int {
        return if (showPreview) {
            //预览下，右边固定20dp
            return MyApplication.appContext.resources?.getDimensionPixelSize(R.dimen.dimen_20dp) ?: 0
        } else {
            listAdapterMargin
        }
    }

    @JvmStatic
    fun updateFileGridImgSize(context: Context, img: FileThumbView, file: BaseFileBean) {
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "updateFileGridImgSize path null")
            return
        }
        val layoutParams = img.layoutParams
        if (type == MimeTypeHelper.DRM_TYPE) {
            val typeString = MimeTypeHelper.getDrmMimeType(context, path)
            if (!TextUtils.isEmpty(typeString) && (typeString!!.startsWith("video/") || typeString.startsWith("image/"))) {
                layoutParams.width = gridVideoImageWith
                layoutParams.height = gridVideoImageWith
            } else {
                layoutParams.width = gridDefaultIconWith
                layoutParams.height = gridDefaultIconWith
            }
        } else {
            when {
                (type == MimeTypeHelper.IMAGE_TYPE) || (type == MimeTypeHelper.VIDEO_TYPE) -> {
                    layoutParams.width = gridVideoImageWith
                    layoutParams.height = gridVideoImageWith
                }
                (MimeTypeHelper.isDocType(type) && ThumbnailManager.isDocThumbnailSupported(context)) -> {
                    layoutParams.width = gridDocThumbnailWith
                    layoutParams.height = gridDocThumbnailWith
                }
                (type == MimeTypeHelper.APPLICATION_TYPE) -> {
                    layoutParams.width = gridApkIconWith
                    layoutParams.height = gridApkIconWith
                }
                (type == MimeTypeHelper.DIRECTORY_TYPE) -> {
                    layoutParams.width = gridDirIconWith
                    layoutParams.height = gridDirIconWith
                }
                else -> {
                    layoutParams.width = gridDefaultIconWith
                    layoutParams.height = gridDefaultIconWith
                }
            }
        }
        Log.d(TAG, "updateFileGridImgSize ${file.mDisplayName} ${Size(layoutParams.width, layoutParams.height)}")
    }

    @JvmStatic
    fun updateFileListImgSize(context: Context, img: FileThumbView, file: BaseFileBean) {
        val path = file.mData
        val type = file.mLocalType
        if (path == null) {
            Log.d(TAG, "updateFileListImgSize path null")
            return
        }
        val layoutParams = img.layoutParams
        if (type == MimeTypeHelper.DRM_TYPE) {
            val typeString = MimeTypeHelper.getDrmMimeType(context, path)
            if (!TextUtils.isEmpty(typeString) && (typeString!!.startsWith("video/") || typeString.startsWith("image/"))) {
                layoutParams.width = listVideoImageWith
                layoutParams.height = listVideoImageWith
            } else {
                layoutParams.width = listDefaultIconWith
                layoutParams.height = listDefaultIconWith
            }
        } else if (BaseFileBean.TYPE_SEARCH_THIRD_APP_FILE == file.mFileWrapperViewType) {
            layoutParams.width = listSearchThirdAppIconWith
            layoutParams.height = listSearchThirdAppIconWith
        } else {
            when {
                (type == MimeTypeHelper.IMAGE_TYPE) || (type == MimeTypeHelper.VIDEO_TYPE) -> {
                    layoutParams.width = listVideoImageWith
                    layoutParams.height = listVideoImageWith
                }
                (MimeTypeHelper.isDocType(type) && ThumbnailManager.isDocThumbnailSupported(context)) -> {
                    layoutParams.width = listDocThumbnailWith
                    layoutParams.height = listDocThumbnailWith
                }
                (type == MimeTypeHelper.APPLICATION_TYPE) -> {
                    layoutParams.width = listApkIconWith
                    layoutParams.height = listApkIconWith
                }
                (type == MimeTypeHelper.DIRECTORY_TYPE) -> {
                    layoutParams.width = listDirIconWith
                    layoutParams.height = listDirIconWith
                }
                else -> {
                    layoutParams.width = listDefaultIconWith
                    layoutParams.height = listDefaultIconWith
                }
            }
        }
        Log.d(TAG, "updateFileListImgSize ${file.mDisplayName} ${Size(layoutParams.width, layoutParams.height)}")
    }

    @JvmStatic
    fun updateCloudDocGridImgSize(img: FileThumbView, file: BaseFileBean) {
        val type = file.mLocalType
        val layoutParams = img.layoutParams
        when {
            (type == MimeTypeHelper.DIRECTORY_TYPE) -> {
                layoutParams.width = gridDirIconWith
                layoutParams.height = gridDirIconWith
            }
            else -> {
                layoutParams.width = gridDefaultIconWith
                layoutParams.height = gridDefaultIconWith
            }
        }
    }

    @JvmStatic
    fun updateCloudDocListImgSize(img: FileThumbView, file: BaseFileBean) {
        val type = file.mLocalType
        val layoutParams = img.layoutParams
        when {
            (type == MimeTypeHelper.DIRECTORY_TYPE) -> {
                layoutParams.width = listDirIconWith
                layoutParams.height = listDirIconWith
            }
            else -> {
                layoutParams.width = listDefaultIconWith
                layoutParams.height = listDefaultIconWith
            }
        }
    }
}