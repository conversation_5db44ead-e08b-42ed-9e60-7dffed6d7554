/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ZipUtil.kt
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/12/23
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 * zhengshuai                2022/12/23       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import java.io.BufferedInputStream
import java.io.BufferedOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.util.Enumeration
import java.util.zip.ZipEntry
import java.util.zip.ZipFile

object ZipUtil {

    private const val TAG = "ZipUtil"

    /**
     * 解压缩功能.
     * 将zipFile文件解压到destPath目录下
     */
    fun upZipFile(file: File, destPath: String): Int {
        val desDir = File(destPath)
        if (desDir.exists().not()) {
            desDir.mkdirs()
        }
        val zipFile = ZipFile(file)
        try {
            val zList: Enumeration<*> = zipFile.entries()
            var zipEntry: ZipEntry?
            val buf = ByteArray(1024)
            while (zList.hasMoreElements()) {
                zipEntry = zList.nextElement() as ZipEntry
                if (zipEntry.isDirectory) {
                    var dirstr = destPath + zipEntry.name
                    dirstr = String(dirstr.toByteArray(charset("8859_1")), charset("GB2312"))
                    val f = File(dirstr)
                    if (f.exists().not()) {
                        f.mkdirs()
                    }
                    continue
                }
                val output = BufferedOutputStream(FileOutputStream(File(destPath, zipEntry.name)))
                val input = BufferedInputStream(zipFile.getInputStream(zipEntry))
                var readLen: Int
                while (input.read(buf, 0, 1024).also { readLen = it } != -1) {
                    output.write(buf, 0, readLen)
                }
                output.flush()
                input.close()
                output.close()
            }
        } catch (e: IOException) {
            Log.e(TAG, "upZipFile e:${e.message}")
        } finally {
            zipFile.close()
        }
        return 0
    }
}