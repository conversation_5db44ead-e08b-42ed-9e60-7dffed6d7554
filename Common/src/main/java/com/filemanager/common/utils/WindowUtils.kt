/*********************************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : WindowUtils
 ** Description :
 ** Version     : 1.0
 ** Date        : 2022/4/2 10:35
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  ternence        2022/4/2       1.0      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.res.Configuration.ORIENTATION_LANDSCAPE
import android.graphics.Point
import android.view.Display
import android.view.WindowManager
import androidx.annotation.Dimension
import androidx.annotation.IntDef
import com.coui.appcompat.grid.COUIResponsiveUtils
import com.coui.responsiveui.config.ResponsiveUIConfig
import com.coui.responsiveui.config.UIConfig
import com.filemanager.common.MyApplication
import com.filemanager.common.compat.FeatureCompat
import com.filemanager.common.filepreview.util.PreviewUtils
import com.oplus.flexiblewindow.FlexibleWindowManager

object WindowUtils {

    const val SMALL = 0x01
    const val MIDDLE = 0x02
    const val LARGE = 0x03
    const val TAG = "WindowUtils"

    @IntDef(SMALL, MIDDLE, LARGE)
    @Retention(AnnotationRetention.SOURCE)
    annotation class WindowType

    // 小屏的最大宽度
    private const val SMALL_SCREEN_WIDTH_RANGE = 600
    // 小屏的最大高度
    private const val SMALL_SCREEN_HEIGHT_RANGE = 480
    // 中屏的最大宽度
    private const val MIDDLE_SCREEN_MAX_WIDTH = 840
    // 最大高度
    private const val SCREEN_MAX_HEIGHT = 900

    @Target(AnnotationTarget.FIELD, AnnotationTarget.TYPE_PARAMETER, AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FUNCTION)
    @Retention(AnnotationRetention.SOURCE)
    @Dimension(unit = Dimension.DP)
    annotation class Dp

    @SuppressLint("WrongConstant")
    @JvmStatic
    fun getScreenHeight(context: Context): Int {
        val windowManager = context.getSystemService("window") as WindowManager
        val defaultDisplay: Display? = windowManager.defaultDisplay
        val outPoint = Point()
        defaultDisplay?.getRealSize(outPoint)
        return outPoint.y
    }

    @SuppressLint("WrongConstant")
    @JvmStatic
    fun getScreenWidth(context: Context): Int {
        val windowManager = context.getSystemService("window") as WindowManager
        val defaultDisplay: Display? = windowManager.defaultDisplay
        val outPoint = Point()
        defaultDisplay?.getRealSize(outPoint)
        return outPoint.x
    }

    /**
     * 获取屏幕大小类型
     * 小屏 ：宽度 < 600 或者 高度 < 480
     * 中屏：600 <=宽度  < 840
     * 大屏：宽度 >= 840 或者 高度 >= 900
     */
    @WindowType
    @JvmStatic
    fun getScreenWindowType(@Dp screenWidth: Int, screenHeight: Int): Int {
        if (FeatureCompat.isSmallScreenPhone) {
            return SMALL
        }
        if (screenWidth < SMALL_SCREEN_WIDTH_RANGE) {
            return SMALL
        }
        if (screenWidth >= MIDDLE_SCREEN_MAX_WIDTH || screenHeight >= SCREEN_MAX_HEIGHT) {
            return LARGE
        }
        return MIDDLE
    }

    /**
     * 是否显示底部tab
     * W < 840 dp，使用底部导航栏
     * 840 ≤ W ≤ 1200 dp，或者 W < 840 dp，但是 H ≥ 900 dp，也依然使用纯父子级结构，无底部导航栏
     */
    fun isShowBottomTab(@Dp width: Int, @Dp height: Int): Boolean {
        if (width < MIDDLE_SCREEN_MAX_WIDTH) {
            if (height >= SCREEN_MAX_HEIGHT) {
                return false
            }
            return true
        }
        return false
    }

    @JvmStatic
    fun isSmallScreen(context: Context): Boolean {
        val width = context.resources.configuration.screenWidthDp
        return COUIResponsiveUtils.isSmallScreenDp(width)
    }

    @JvmStatic
    fun isMiddleAndLargeScreen(context: Context?): Boolean {
        val activity = if (context is Activity) {
            context
        } else {
            MyApplication.activities.lastOrNull()
        }
        activity?.let {
            if (!isFullScreenWindow(activity)) { // 浮窗或者分屏
                return isSmallScreen(activity).not()
            }
        }
        val screenWidthDP = context?.resources?.configuration?.screenWidthDp ?: 0
        val smallestScreenWidthDp = context?.resources?.configuration?.smallestScreenWidthDp ?: 0
        Log.d(TAG, "screenWidthDP $screenWidthDP smallestScreenWidthDp $smallestScreenWidthDp")
        return (FeatureCompat.isSmallScreenPhone.not() && screenWidthDP >= SMALL_SCREEN_WIDTH_RANGE)
                || smallestScreenWidthDp >= SMALL_SCREEN_WIDTH_RANGE
    }

    @JvmStatic
    fun isLandscape(context: Context?): Boolean {
        return context?.resources?.configuration?.orientation == ORIENTATION_LANDSCAPE
    }

    /**
     * 判断是否支持大屏布局
     */
    @JvmStatic
    fun supportLargeScreenLayout(context: Context): Boolean {
        val isPreView = PreviewUtils.isPreviewOpen()
        val windowType = getCurrentWindowType(context)
        return SMALL != windowType && isPreView.not()
    }

    /**
     * 获取屏幕类型 大中小
     */
    @JvmStatic
    fun getCurrentWindowType(context: Context?): Int {
        var result = SMALL
        if (context == null) {
            return result
        }
        val windowType = ResponsiveUIConfig.getDefault(context)?.uiConfig?.value?.windowType
        when (windowType) {
            UIConfig.WindowType.SMALL -> result = SMALL
            UIConfig.WindowType.MEDIUM -> result = MIDDLE
            UIConfig.WindowType.LARGE -> result = LARGE
            else -> result
        }
        Log.d(TAG, "getCurrentWindowType $windowType -》 result:$result")
        return result
    }

    /**
     * 获取窗口状态
     * FLEXIBLE_WINDOW_INVALID_MODE = -1   非分屏也非浮窗
     * FLEXIBLE_WINDOW_FREEFORM_MODE = 1    浮窗态
     * FLEXIBLE_WINDOW_SPLIT_SCREEN_MODE = 2  分屏态
     */
    @JvmStatic
    fun getWindowState(activity: Activity): Int {
        if (!SdkUtils.isAtLeastOS15()) { //os 15支持
            return FlexibleWindowManager.FLEXIBLE_WINDOW_INVALID_MODE
        }
        val state = FlexibleWindowManager.getInstance().getFlexibleWindowState(activity)
        Log.w(TAG, "getWindowState $state")
        return state
    }

    /**
     * 判断是否是分屏
     */
    @JvmStatic
    fun isSplitScreenWindow(activity: Activity): Boolean {
        val state = getWindowState(activity)
        return FlexibleWindowManager.FLEXIBLE_WINDOW_SPLIT_SCREEN_MODE == state
    }

    /**
     * 判断是否是自由浮窗
     */
    @JvmStatic
    fun isFreeFormWindow(activity: Activity): Boolean {
        val state = getWindowState(activity)
        return FlexibleWindowManager.FLEXIBLE_WINDOW_FREEFORM_MODE == state
    }

    /**
     * 判断是不是全屏
     */
    @JvmStatic
    fun isFullScreenWindow(activity: Activity): Boolean {
        val state = getWindowState(activity)
        return FlexibleWindowManager.FLEXIBLE_WINDOW_INVALID_MODE == state
    }
}