/***********************************************************
 * Copyright (C), 2010-2020 Oplus. All rights reserved.
 * File:  - MediaScannerCompat.java
 * Description: compat class for Media Scanner,support on android Q and R
 * Version: 1.0
 * Date : 2020/04/13
 * Author: Jiafei.Liu
 *
 * ---------------------Revision History: ---------------------
 * <author>     <date>      <version>    <desc>
 * <EMAIL>      2020/04/13    1.0     create
 ****************************************************************/
package com.filemanager.common.compat

import android.content.ComponentName
import android.content.Intent
import android.media.MediaScannerConnection
import android.media.MediaScannerConnection.OnScanCompletedListener
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import com.filemanager.common.MyApplication
import com.filemanager.common.constants.Constants
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.SdkUtils
import com.filemanager.common.utils.Utils

object MediaScannerCompat {
    private const val TAG = "MediaScannerCompat"
    private const val KEY_CHECK_DISK_SPACE = "needCheckDiskSpace"
    private const val KEY_SINGLE_DIR = "singleDir"
    private const val KEY_MULTI_DIR = "multiDir"
    private const val KEY_SCAN_FLAG = "scanFlag"

    /**
     * @param singleDir target path for scan
     * @param scanScene scan scene for oppo media scan on android Q. deprecated on android R
     */
    @JvmStatic
    fun sendMediaScanner(singleDir: String?, scanScene: String?) {
        Log.w(TAG, "sendMediaScanner $singleDir")
        if (SdkUtils.isAtLeastR()) {
            // singleDir must be NotNull on android R to make sure scanfile work fine
            if (singleDir.isNullOrEmpty()) {
                Log.d(TAG, "sendMediaScanner empty path to scan")
                return
            }
            internalMediaScanner(singleDir)
        } else {
            internalOppoMediaScanner(singleDir, scanScene)
        }
    }

    /**
     * @param paths     path list for scan
     * @param scanScene scan scene for oppo media scan on android Q. deprecated on android R
     * @param callback the scan result callback in R, deprecated on android Q.
     */
    @JvmStatic
    fun sendMultiDirMediaScanner(paths: ArrayList<String>, scanScene: String, callback: OnScanCompletedListener? = null) {
        Log.w(TAG, "sendMultiDirMediaScanner $paths")
        if (SdkUtils.isAtLeastR()) {
            // paths must be NotNull on android R to make sure scanfile work fine
            if (paths.isEmpty()) {
                Log.e(TAG, "sendMultiDirMediaScanner empty path to scan")
                return
            }
            internalMultiDirMediaScanner(ArrayList(paths), callback)
        } else {
            internalMultiDirOppoMediaScanner(paths, scanScene)
        }
    }

    /**
     * @return true if mediascanner is scanning,or false
     */
    @JvmStatic
    fun isMediaScannerScanning(): Boolean {
        var result = false
        try {
            val contentResolver = MyApplication.sAppContext.contentResolver
            contentResolver.query(MediaStore.getMediaScannerUri(), arrayOf(MediaStore.MEDIA_SCANNER_VOLUME), null,
                    null, null).use { cursor ->
                if (cursor != null) {
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        if (cursor.count > 0 && cursor.moveToFirst()) {
                            val volume = cursor.getString(0)
                            // for Android Q,empty volume means MediaScanner is free.
                            result = !volume.isNullOrEmpty()
                        }
                    } else {
                        result = cursor.count == 1
                    }
                }
            }
        } catch (ex: Exception) {
            result = false
            Log.e(TAG, "isMediaScannerScanning error: $ex")
        }
        Log.i(TAG, "isMediaScannerScanning result:$result")
        return result
    }

    /**
     * scan interface of OppoMediaScan on android Q
     * @param path      target path for scan
     * @param scanScene scan scene for OppoMediaScan
     */
    private fun internalOppoMediaScanner(path: String?, scanScene: String?) {
        val intent = Intent(OplusIntentCompat.ACTION_MEDIA_SCANNER_SCAN_ALL)
        val bundle = Bundle()
        bundle.putBoolean(KEY_CHECK_DISK_SPACE, false)
        if (!path.isNullOrEmpty()) {
            bundle.putString(KEY_SINGLE_DIR, path)
        }
        intent.component = ComponentName(Constants.PROVIDERS_MEDIA_PACKAGE, Constants.PROVIDERS_MEDIA_CLASS)
        intent.putExtras(bundle)
        intent.putExtra(Utils.MEDIA_SCAN_SCENE, MyApplication.sAppContext.packageName + scanScene)
        MyApplication.sAppContext.sendBroadcast(intent)
    }

    /**
     * scan interface of MediaScan on android R
     * @param path target path for scan
     */
    private fun internalMediaScanner(path: String) {
        try {
            val paths = arrayOf(path)
            MediaScannerConnection.scanFile(MyApplication.sAppContext, paths, null, null)
        } catch (ex: Exception) {
            Log.w(TAG, "internalMediaScanner :$path, error: $ex")
        }
    }

    /**
     * scan interface of MediaScan on android R
     *
     * @param pathList path list for scan
     */
    private fun internalMultiDirMediaScanner(pathList: List<String>, callback: OnScanCompletedListener? = null) {
        try {
            MediaScannerConnection.scanFile(MyApplication.sAppContext, pathList.toTypedArray(),
                    null, callback)
        } catch (ex: Exception) {
            Log.w(TAG, "internalMultiDirMediaScanner :$pathList, error: $ex")
        }
    }

    /**
     * scan interface of OppoMediaScan on android Q
     * @param paths     path list for scan
     * @param scanScene scan scene for OppoMediaScan
     */
    private fun internalMultiDirOppoMediaScanner(paths: ArrayList<String>, scanScene: String) {
        val scanIntent = Intent(OplusIntentCompat.ACTION_MEDIA_SCANNER_SCAN_ALL)
        scanIntent.putStringArrayListExtra(KEY_MULTI_DIR, paths)
        scanIntent.putExtra(Utils.MEDIA_SCAN_SCENE, MyApplication.sAppContext.packageName + scanScene)
        scanIntent.component = ComponentName(Constants.PROVIDERS_MEDIA_PACKAGE, Constants.PROVIDERS_MEDIA_CLASS)
        MyApplication.sAppContext.sendBroadcast(scanIntent)
    }
}