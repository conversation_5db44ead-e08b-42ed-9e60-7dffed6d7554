<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ProtectedPermissions,QueryAllPackagesPermission">

    <uses-sdk tools:overrideLibrary="com.oplus.sdk.addon.sdk,com.filemanager.thumbnail.doc.wps,com.filemanager.thumbnail,com.oplus.filemanager.simulateclick.sdk,com.oplus.dropdrag" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_CACHE_FILESYSTEM" />
    <uses-permission android:name="android.permission.BATTERY_STATS" />
    <uses-permission android:name="android.permission.DEVICE_POWER" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="oppo.permission.OPPO_COMPONENT_SAFE" />
    <uses-permission android:name="com.coloros.encryption.READ_PROVIDER" />
    <uses-permission android:name="com.coloros.encryption.WRITE_PROVIDER" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="oppo.permission.gallery.ACCESS_PROVIDER" />
    <uses-permission android:name="com.oppo.permission.safe.SAU" />
    <uses-permission android:name="com.oppo.permission.safe.PRIVATE" />
    <uses-permission android:name="heytap.permission.cloud.ACCESS_CLOUD" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <uses-permission android:name="com.oppo.permission.safe.SECURITY" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="com.oplus.permission.safe.SECURITY" />
    <uses-permission android:name="com.android.permission.GET_INSTALLED_APPS" />
    <uses-permission android:name="com.oplus.permission.safe.APP_MANAGER" />
    <uses-permission android:name="com.oplus.permission.safe.AUTHENTICATE" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="com.oplus.permission.safe.SETTINGS" />
    <application>
        <uses-library
            android:name="com.coloros.statistics"
            android:required="false" />
        <meta-data
            android:name="color.support.options"
            android:value="@string/color_support_value" />
        <meta-data
            android:name="AppCode"
            android:value="20016" />
        <!--add for disable Android Beam-->
        <meta-data
            android:name="android.nfc.disable_beam_default"
            android:value="true" />
        <!--add for 8.0 color-->
        <meta-data
            android:name="color_material_enable"
            android:value="true" />
        <!--add for api-adapter-->
        <meta-data
            android:name="AppPlatformKey"
            android:value="ATBEAiBW47BKcSf1fuXcO/PTU0Fzqqu3OU5k3Rmpmw/em/TPGgIgf58Tj6ccikRPbS21d6+uYzsMgEcp2NS+otl1bCd1yq5flCUXZXBvbmEsZ2V0CQAAAA==" />
        <!--add for help and feedback -->
        <meta-data
            android:name="upgrade_product_code"
            android:value="20183" />
        <meta-data android:name="com.oplus.ocs.aiunit.authentication_style"
            android:value="interOcs" />
        <meta-data
            android:name="android.permission-group.READ_APPLIST"
            android:resource="@string/request_app_list_permission_desc" />
        <meta-data
            android:name="app_battery_visible"
            android:value="false" />
    </application>
    <queries>
        <package android:name="com.oplus.aiunit.core" />
    </queries>
</manifest>
