/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.view
 * * Version     : 1.0
 * * Date        : 2020/3/10
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.text.method.LinkMovementMethod
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.appcompat.widget.AppCompatImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.coui.appcompat.textviewcompatutil.COUITextViewCompatUtil
import com.filemanager.common.R
import com.filemanager.common.utils.FileEmptyUtils
import com.filemanager.common.utils.Log
import com.filemanager.common.utils.StatusBarUtil
import com.filemanager.common.utils.destroy
import com.oplus.anim.EffectiveAnimationView

class FileEmptyView : ConstraintLayout {
    companion object {
        private const val TAG = "FileEmptyView"
    }

    private var mOppoEmptyBottle: TextView? = null
    private var mOppoEmptyBottleSummary: TextView? = null
    private var mOppoEmptyBottleGuide: TextView? = null
    private var mOppoEmptyLayout: View? = null
    private var mContentLayout: LinearLayout? = null
    private var mOppoEmptyEffectiveView: AppCompatImageView? = null
    private var mRootOnLayoutChangeListener: OnLayoutChangeListener? = null
    private var mEmptyOnLayoutChangeListener: OnLayoutChangeListener? = null

    //以下字段，在分屏模式下，用于计算Title居中
    private var visibleTop = -1     //可见的高度，页面中有其他控件时，如Toolbar，找出此控件的底
    private var visibleBottom = -1  //可见的最高的底，页面中有其他控件时，如Bottom，找出此控件的Top值
    private var titleTop = -1       //标题控件的当前Top
    private var titleBottom = -1    //标题控件的当前Bottom
    //当前控件是否可见，在setEmptyVisible()的时候更新，用于在OnUIConfigChanged 状态变化时，是否需要执行setEmptyVisible()
    //在分屏模式下，需要隐藏ICON，在分屏恢复到全屏时，需要此判断，恢复ICON
    private var mVisibility:Int = View.GONE

    constructor(context: Context) : super(context) {
        initView(context)
    }

    constructor(context: Context, attr: AttributeSet) : super(context, attr) {
        initView(context)
    }

    constructor(context: Context, attr: AttributeSet, defStyleAttr: Int) : super(
        context,
        attr,
        defStyleAttr
    ) {
        initView(context)
    }

    fun initView(context: Context) {
        mOppoEmptyLayout = LayoutInflater.from(context).inflate(R.layout.empty_layout, this)
        mContentLayout = mOppoEmptyLayout?.findViewById(R.id.empty_content_layout)
        mOppoEmptyBottle = mOppoEmptyLayout?.findViewById(R.id.emptybottle)
        mOppoEmptyBottleSummary = mOppoEmptyLayout?.findViewById(R.id.empty_des_tv)
        mOppoEmptyBottleGuide = mOppoEmptyLayout?.findViewById(R.id.guide_tv)
        mOppoEmptyBottleGuide?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G4)
            COUITextViewCompatUtil.setPressRippleDrawable(it)
        }
        mOppoEmptyEffectiveView = mOppoEmptyLayout?.findViewById(R.id.empty_eav)
    }
    fun getEmptyVisibility() = mVisibility

    fun setEmptyVisible(visible: Int) {
        mVisibility = visible
        if (mOppoEmptyLayout?.visibility != visible) {
            mOppoEmptyLayout?.visibility = visible
        }
        //当此设备是可见时 并且是分屏的时候，隐藏掉ICON
        if (visible == View.VISIBLE) {
            mOppoEmptyLayout?.post { updateEmptyMarginTop() }
        }
        (mOppoEmptyEffectiveView as? EffectiveAnimationView)?.apply {
            if (View.VISIBLE == visible) {
                if (isAnimating.not()) {
                    playAnimation()
                }
            } else {
                if (isAnimating) {
                    cancelAnimation()
                }
                resumeAnimation()
            }
        }
    }

    fun addContentView(child: View) {
        mContentLayout?.addView(child)
    }

    /**
     * 计算外部可用高度及可用底部，调整MarginTop
     */
    private fun updateEmptyMarginTop() {
        mOppoEmptyBottle ?: return
        mOppoEmptyLayout ?: return
        visibleBottom = mOppoEmptyLayout!!.bottom
        titleTop = mOppoEmptyBottle!!.top
        titleBottom = mOppoEmptyBottle!!.top + mOppoEmptyBottle!!.measuredHeight
        if (visibleTop == -1) {
            compareWithAllViews()
            mOppoEmptyLayout?.let { emptyLayout ->
                val appBarHeight = resources.getDimension(R.dimen.appbar_layout_height)
                val layoutMarginTop = if (emptyLayout.top < appBarHeight) {
                    visibleTop - StatusBarUtil.getStatusBarHeight()
                } else {
                    visibleTop - emptyLayout.top
                }
                val layoutParams = emptyLayout.layoutParams
                if (layoutParams is CoordinatorLayout.LayoutParams) {
                    layoutParams.topMargin = layoutMarginTop
                } else if (layoutParams is RelativeLayout.LayoutParams) {
                    layoutParams.topMargin = layoutMarginTop
                }
                setLayoutParams(layoutParams)
            }
        }
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        val height = MeasureSpec.getSize(heightMeasureSpec)
        val width = MeasureSpec.getSize(widthMeasureSpec)
        mOppoEmptyEffectiveView?.visibility = FileEmptyUtils.getEmptyIconVisible(context,  height)
        FileEmptyUtils.updateIconScale(mOppoEmptyEffectiveView, width, height)
    }

    /**
     * 循环遍历整个页面中的控件,比较并找出可用于居中判断的visibleTop 及 visibleBottom
     */
    private fun compareWithAllViews(parentView: ViewGroup? = null) {
        val rootView = parentView ?: mOppoEmptyLayout?.parent as ViewGroup
        if (rootView.parent != null && rootView.parent is ViewGroup && rootView.id != android.R.id.content) {
            compareWithAllViews(rootView.parent as ViewGroup)
        }
        for (childIndex in 0..rootView.childCount) {
            val childView = rootView.getChildAt(childIndex)
            childView?.let {
                if (childView.visibility != View.VISIBLE) {
                    Log.w(TAG, "getVisibleTop childView do not need this view: " +
                            "top:${childView.top}  " +
                            "b:${childView.bottom} v:${childView.visibility} h:${childView.measuredHeight} " +
                            "y:${childView.y} $childView ")
                    return@let
                }
                val childTop = it.top + it.measuredHeight
                if (it.top < titleTop && childTop in visibleTop until visibleBottom) {
                    visibleTop = childTop
                    Log.i(TAG, "update visibleTop  $visibleTop  top:${it.top}  H:${it.measuredHeight}  y:${it.y}  bottom:${it.bottom} child:$it")
                }
                if (it.top in (titleBottom + 1) until visibleBottom) {
                    visibleBottom = it.top
                    Log.i(TAG, "update visibleBottom  $visibleBottom child:$it")
                }
            }
        }
    }

    fun setEmptyTextViewId(textId: Int) {
        mOppoEmptyBottle?.setText(textId)
    }

    fun setEmptyText(charSequence: CharSequence) {
        mOppoEmptyBottle?.apply {
            text = charSequence
            movementMethod = LinkMovementMethod.getInstance()
        }
    }

    fun setEmptyAnimation(asset: String) {
        val effectiveView = mOppoEmptyEffectiveView as? EffectiveAnimationView
        if (effectiveView?.isAnimating == true) {
            Log.d(TAG, "setEmptyAnimation: animation is already playing, skip")
            return
        }
        effectiveView?.setAnimation(asset)
    }

    fun setAnimationReset() {
        (mOppoEmptyEffectiveView as? EffectiveAnimationView)?.apply {
            if (isAnimating) {
                canAnimate()
                resumeAnimation()
            }
        }
    }

    override fun onDetachedFromWindow() {
        (mOppoEmptyEffectiveView as? EffectiveAnimationView)?.destroy()
        mRootOnLayoutChangeListener = null
        mEmptyOnLayoutChangeListener = null
        super.onDetachedFromWindow()
    }

    fun setEmptyGuidVisibilityAndContent(visible: Int, content: String, clickListener: OnClickListener?) {
        mOppoEmptyBottleGuide?.let {
            it.visibility = visible
            it.text = content
            it.setOnClickListener(clickListener)
        }
    }

    fun setEmptyGuidContent(visible: Int, content: String) {
        mOppoEmptyBottleGuide?.let {
            it.visibility = visible
            it.text = content
        }
    }

    fun setEmptySummaryVisibilityAndContent(visible: Int, content: String) {
        mOppoEmptyBottleSummary?.let {
            it.visibility = visible
            it.text = content
        }
    }
}