/***********************************************************
 ** Copyright (C), 2010-2020 Oplus. All rights reserved..
 ** File:  - SlideUtils.kt
 ** Description: Slide Utils
 ** Version: 1.0
 ** Date : 2020/06/02
 ** Author: <PERSON><PERSON><PERSON>.Liu
 **
 ** ---------------------Revision History: ---------------------
 **  <author>     <date>      <version >    <desc>
 **  <EMAIL>      2020/06/02    1.0     create
 ****************************************************************/

package com.filemanager.common.dragselection

import android.content.Context

object SlideUtils {
    private var mCheckBoxLeftOffset = -1
    private var mCheckBoxRightOffset = -1

    fun getCheckBoxLeftOffset(context: Context?): Int {
        context?.resources?.run {
            if (mCheckBoxLeftOffset == -1) {
                mCheckBoxLeftOffset = getDimensionPixelOffset(
                    com.support.listview.R.dimen.coui_listview_scrollchoice_left_offset
                )
            }
        }
        return mCheckBoxLeftOffset
    }

    fun getCheckBoxRightOffset(context: Context?): Int {
        context?.resources?.run {
            if (mCheckBoxRightOffset == -1) {
                mCheckBoxRightOffset = getDimensionPixelOffset(
                    com.support.listview.R.dimen.coui_listview_scrollchoice_right_offset
                )
            }
        }
        return mCheckBoxRightOffset
    }
}