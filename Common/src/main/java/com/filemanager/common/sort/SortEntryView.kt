/*********************************************************************
 ** Copyright (C), 2022-2029 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File        : SortEntryView
 ** Description : Sort EntryV iew
 ** Version     : 1.0
 ** Date        : 2022/9/7
 ** Author      : <EMAIL>
 **
 ** ---------------------Revision History: ----------------------------
 **  <author>        <data>       <version>  <desc>
 **  chao.xue       2022/9/7      1.0        create
 ***********************************************************************/
package com.filemanager.common.sort

import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.annotation.PluralsRes
import androidx.annotation.VisibleForTesting
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.coui.appcompat.textutil.COUIChangeTextUtil
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.utils.FileImageVHUtils
import com.filemanager.common.utils.Log

class SortEntryView : ConstraintLayout {

    companion object {
        private const val TAG = "SortEntryView"
        private const val DESC_ROTATE_ANGLE = 0.0f
        private const val ASC_ROTATE_ANGLE = 180.0f
    }

    @VisibleForTesting
    var countTv: TextView? = null

    @VisibleForTesting
    var sortOrderTv: TextView? = null

    @VisibleForTesting
    var sortOrderImg: ImageView? = null

    @VisibleForTesting
    var clickListener: View.OnClickListener? = null

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyle: Int) : super(context, attrs, defStyle) {
        initView()
        initEvent()
    }

    @VisibleForTesting
    fun initView() {
        inflate(context, R.layout.layout_sort_entry, this)

        countTv = findViewById(R.id.sort_entry_file_count_text)
        sortOrderImg = findViewById(R.id.sort_entry_order_img)
        sortOrderTv = findViewById(R.id.sort_entry_order_text)

        countTv?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G2)
        }
        sortOrderTv?.let {
            COUIChangeTextUtil.adaptFontSize(it, COUIChangeTextUtil.G2)
        }
        updateLeftRightMargin()
    }

    @VisibleForTesting
    fun initEvent() {
        sortOrderImg?.setOnClickListener {
            onClickEvent(it)
        }
        sortOrderTv?.setOnClickListener {
            onClickEvent(it)
        }
    }

    @VisibleForTesting
    fun onClickEvent(view: View) {
        this.clickListener?.onClick(view)
    }

    fun setFileCount(count: Int, @PluralsRes pluralsRes: Int = R.plurals.search_result_count) {
        countTv?.text = context.resources.getQuantityString(pluralsRes, count, count)
    }

    fun setDefaultOrder(recordMode: String) {
        val sort = SortModeUtils.getSharedSortMode(MyApplication.sAppContext, recordMode)
        val isDesc = SortModeUtils.getSharedSortOrder(recordMode)
        setSortOrder(sort, isDesc)
    }

    fun setSortOrder(sortType: Int, isDesc: Boolean) {
        Log.d(TAG, "setSortOrder sortType:$sortType isDesc:$isDesc")
        post {
            sortOrderTv?.text = getSortType(sortType)
            val imgRes = getOrderImg(isDesc)
            sortOrderImg?.setImageResource(imgRes)
        }
    }

    fun disableSortOrderView() {
        sortOrderImg?.visibility = View.INVISIBLE
        sortOrderTv?.visibility = View.INVISIBLE
    }

    fun rotateArrow() {
    }

    @VisibleForTesting
    fun getSortType(sortType: Int): String {
        val id = when (sortType) {
            SortHelper.FILE_NAME_ORDER -> R.string.sort_by_name
            SortHelper.FILE_TYPE_ORDER -> R.string.sort_by_type
            SortHelper.FILE_SIZE_SUMDIR_REVERSE_ORDER -> R.string.sort_by_size
            SortHelper.FILE_TIME_REVERSE_ORDER -> R.string.modify_time
            SortHelper.FILE_TIME_DELETE_ORDER -> R.string.sort_by_remain_time
            SortHelper.FILE_LAST_OPEN_TIME_ORDER -> R.string.sort_by_last_open_time
            else -> R.string.sort_by_name
        }
        return context.getString(id)
    }

    @DrawableRes
    fun getOrderImg(isDesc: Boolean): Int {
        return if (isDesc) {
            R.drawable.icon_sort_desc
        } else {
            R.drawable.icon_sort_asc
        }
    }

    fun setClickSortListener(listener: OnClickListener) {
        this.clickListener = listener
    }

    fun updateLeftRightMargin() {
        countTv?.updateLayoutParams<LayoutParams> {
            this.marginStart = FileImageVHUtils.getListLeftMargin()
        }
        sortOrderImg?.updateLayoutParams<LayoutParams> {
            this.marginEnd = FileImageVHUtils.getListRightMargin()
        }
    }
}