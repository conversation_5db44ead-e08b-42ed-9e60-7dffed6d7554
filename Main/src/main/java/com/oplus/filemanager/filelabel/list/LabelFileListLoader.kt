/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : com.oplus.filemanager.filelabel.list.LabelListLoader
 * * Description :
 * * Version     : 1.0
 * * Date        : 2022/8/1
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/
package com.oplus.filemanager.filelabel.list

import android.content.Context
import com.filemanager.common.MyApplication.appContext
import com.filemanager.common.base.BaseFileBean
import com.filemanager.common.base.FileTaskLoader
import com.filemanager.common.base.PathLoadResult
import com.filemanager.common.constants.Constants
import com.filemanager.common.fileutils.HiddenFileHelper
import com.filemanager.common.sort.SortModeUtils
import com.filemanager.common.sort.SortRecordModeFactory
import com.filemanager.common.utils.Injector
import com.filemanager.common.utils.Log
import com.filemanager.common.wrapper.PathFileWrapper
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.oplus.filemanager.interfaze.categorydoc.IDocumentExtensionType
import com.oplus.filemanager.provider.FileLabelMappingDBHelper
import com.oplus.filemanager.room.model.FileLabelMappingEntity
import java.io.File
import java.lang.reflect.Type
import java.util.Locale

class LabelFileListLoader(context: Context, labelId: Long, isFilter: Boolean) :
    FileTaskLoader<PathLoadResult<Int, BaseFileBean>>(context) {

    companion object {
        private const val TAG = "LabelListLoader"
    }
    private var mHideFileLabel = false
    private var mLabelId: Long = labelId
    private var mIsFilter: Boolean = isFilter

    override fun loadInBackground(): PathLoadResult<Int, BaseFileBean> {
        val fileList = ArrayList<BaseFileBean>()
        val keyMap = HashMap<Int, BaseFileBean>()
        val labelFileList: List<FileLabelMappingEntity>? = if (mIsFilter) {
            val filterMappingFile = File(appContext.filesDir, Constants.LABEL_FILTER_MAPPING_CONTENT)
            val content = filterMappingFile.readText()
            val listType: Type = object : TypeToken<List<FileLabelMappingEntity>>() {}.type
            Gson().fromJson(content, listType)
        } else {
            FileLabelMappingDBHelper.getFileListByLabelId(mLabelId)
        }

        if (labelFileList != null) {
            for (label in labelFileList) {
                if (HiddenFileHelper.isDisplayFile(label.filePath) && File(label.filePath).exists()) {
                    fileList.add(PathFileWrapper(label.filePath).apply {
                        mMediaDuration = label.duration
                    })
                } else {
                    Log.d(TAG, "loadInBackground ignore label")
                }
            }
        }
        for (item in fileList) {
            //标签列表页，默认有标签
            item.mHasLabel = true
            val key = getItemKey(item) ?: continue
            keyMap[key] = item
        }
        val sortItems = preHandleResultBackground(fileList)
        Log.d(TAG, "loadInBackground  sortItems.size：${sortItems.size}")
        return PathLoadResult(sortItems, keyMap)
    }

    private fun preHandleResultBackground(list: List<BaseFileBean>): ArrayList<BaseFileBean> {
        val currentSort = SortModeUtils.getSharedSortMode(appContext, SortRecordModeFactory.getLabelKey())
        val isDesc = SortModeUtils.getSharedSortOrder(SortRecordModeFactory.getLabelKey())
        val mLastSort = SortModeUtils.getSharedSortMode(appContext, SortModeUtils.BROWSER_LAST_SORT_RECORD)
        Injector.injectFactory<IDocumentExtensionType>()?.sortFiles(list, currentSort, mLastSort, mHideFileLabel, isDesc)
        return list as ArrayList<BaseFileBean>
    }

    fun getItemKey(item: BaseFileBean): Int? {
        val path = item.mData
        if (path.isNullOrEmpty()) {
            return null
        }
        return path.lowercase(Locale.getDefault()).hashCode()
    }

    override fun onStartLoading() {
        forceLoad()
    }

    override fun onStopLoading() {
        cancelLoad()
    }


    override fun onReset() {
        super.onReset()
        onStopLoading()
    }

    fun updateLabelId(labelId: Long) {
        mLabelId = labelId
    }
}