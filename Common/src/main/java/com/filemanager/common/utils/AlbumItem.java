package com.filemanager.common.utils;


import com.filemanager.common.helper.MimeTypeHelper;

import static com.filemanager.common.constants.KtConstants.SECONDS_TO_MILLISECONDS;

public class AlbumItem {
    private String mCoverPath;
    private int mCount;
    private String mBucketName;
    private String mKey;
    private String mBucketId;
    private int mOrientation;
    private int mType;
    private Long mDateModified;
    private int mWrapperType;

    public AlbumItem(String coverPath, int count, String bucketName, String key, String bucketId, int orientation, long mDateModified) {
        this.mCoverPath = coverPath;
        this.mCount = count;
        this.mBucketName = bucketName;
        this.mKey = key;
        this.mBucketId = bucketId;
        this.mOrientation = orientation;
        this.mDateModified = mDateModified * SECONDS_TO_MILLISECONDS;
        mType = MimeTypeHelper.Companion.getTypeFromPath(coverPath);
        this.mWrapperType = 1;
    }

    public AlbumItem(int wrapperType) {
        this.mWrapperType = wrapperType;
    }

    public long getDateModified() {
        return mDateModified;
    }

    public int getCount() {
        return mCount;
    }

    public void setCount(int count) {
        this.mCount = count;
    }

    public String getName() {
        return mBucketName;
    }

    public void setName(String bucketName) {
        this.mBucketName = bucketName;
    }

    public String getCoverPath() {
        return mCoverPath;
    }

    public void setCoverPath(String coverPath) {
        this.mCoverPath = coverPath;
    }


    public String getKey() {
        return mKey;
    }

    public int getType() {
        return mType;
    }

    public void setType(int type) {
        this.mType = type;
    }

    public String getBucketId() {
        return mBucketId;
    }

    public void setKey(String key) {
        this.mKey = key;
    }

    public int getOrientation() {
        return mOrientation;
    }

    public void setOrientation(int orientation) {
        this.mOrientation = orientation;
    }

    public int getWrapperType() {
        return mWrapperType;
    }
}
