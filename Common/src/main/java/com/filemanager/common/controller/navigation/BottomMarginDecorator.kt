/*********************************************************************************
 ** Copyright (C) 2020 Oplus. All rights reserver.
 ** COUI_EDIT, All rights reserved.
 **
 ** File: - BottomMarginDecorator.kt
 ** Description: Set margin of [.COUINavigationView] dynamically
 **
 ** Version: 1.0
 ** Date: 2021-04-02
 ** Author: W9001165
 **
 ** ------------------------------- Revision History: ----------------------------
 ** <author>                        <date>       <version>   <desc>
 ** ------------------------------------------------------------------------------
 ********************************************************************************/

package com.filemanager.common.controller.navigation

import android.view.View
import android.view.ViewGroup
import androidx.annotation.Keep
import java.lang.ref.WeakReference

class BottomMarginDecorator {
    companion object {
        const val ANIMATION_BOTTOM_MARGIN = "bottomMargin"
    }

    private var mViews: MutableList<WeakReference<View>>? = null

    fun addView(view: View) : BottomMarginDecorator {
        if (mViews == null) {
            mViews = ArrayList()
        }
        mViews!!.add(WeakReference(view))
        return this
    }

    private fun setViewBottomMargin(view: View, margin: Int) {
        val lp = view.layoutParams
        if (lp != null && lp is ViewGroup.MarginLayoutParams) {
            lp.bottomMargin = margin
            view.layoutParams = lp
        }
    }

    private fun getViewBottomMargin(view: View): Int {
        val lp = view.layoutParams
        if (lp != null && lp is ViewGroup.MarginLayoutParams) {
            return lp.bottomMargin
        }
        return 0
    }

    @Keep
    fun setBottomMargin(margin: Int) {
        if (mViews != null && mViews?.isNotEmpty()!!) {
            var v: View
            for (vRef in mViews!!) {
                if (vRef.get() != null) {
                    v = vRef.get()!!
                    setViewBottomMargin(v, margin)
                }
            }
        }
    }

    @Keep
    fun getBottomMargin(): Int {
        if (mViews != null && mViews?.isNotEmpty()!!) {
            val view: View
            for (vRef in mViews!!) {
                if (vRef.get() != null) {
                    view = vRef.get()!!
                    return getViewBottomMargin(view)
                }
            }
        }
        return 0
    }

    @Keep
    fun getPropertyName() = "bottomMargin"
}