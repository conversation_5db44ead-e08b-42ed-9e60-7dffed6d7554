/*********************************************************************
 * * Copyright (C), 2010-2020 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        :  -
 * * Description : com.coloros.filemanager.filerefactor.base
 * * Version     : 1.0
 * * Date        : 2020/4/11
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 ***********************************************************************/

package com.filemanager.common.base

import android.content.Context
import android.database.ContentObserver
import android.os.Handler
import androidx.annotation.MainThread
import androidx.loader.app.LoaderManager
import androidx.loader.content.CursorLoader

open class FileLoader<D>(context: Context) {
    /**
     * @return the ID of this loader
     */
    var id: Int = 0
        internal set
    internal var mListener: OnFileLoaderStateListener<D>? = null

    /**
     * @return an application context retrieved from the Context passed to the constructor.
     */
    var context: Context
        internal set

    /**
     * Return whether this load has been started.  That is, its [.startLoading]
     * has been called and no calls to [.stopLoading] or
     * [.reset] have yet been made.
     */
    var mStarted = false
        internal set

    /**
     * Return whether this loader has been abandoned.  In this state, the
     * loader *must not* report any new data, and *must* keep
     * its last reported data valid until it is finally reset.
     */
    var mAbandoned = false
        internal set

    /**
     * Return whether this load has been reset.  That is, either the loader
     * has not yet been started for the first time, or its [.reset]
     * has been called.
     */
    var mReset = true
        internal set
    internal var mContentChanged = false
    internal var mProcessingChange = false

    /**
     * An implementation of a ContentObserver that takes care of connecting
     * it to the Loader to have the loader re-load its data when the observer
     * is told it has changed.  You do not normally need to use this yourself;
     * it is used for you by [CursorLoader] to take care of executing
     * an update when the cursor's backing data changes.
     */
    inner class ForceLoadContentObserver : ContentObserver(Handler()) {

        override fun deliverSelfNotifications(): Boolean {
            return true
        }

        override fun onChange(selfChange: Boolean) {
            onContentChanged()
        }
    }

    /**
     * Interface that is implemented to discover when a Loader has finished
     * loading its data.  You do not normally need to implement this yourself;
     * it is used in the implementation of [LoaderManager]
     * to find out when a Loader it is managing has completed so that this can
     * be reported to its client.  This interface should only be used if a
     * Loader is not being used in conjunction with LoaderManager.
     */
    interface OnFileLoaderStateListener<D> {
        /**
         * Called on the thread that created the Loader when the load is started.
         *
         * @param loader the loader that completed the load
         */
        fun onLoadStart(loader: FileLoader<D>)

        /**
         * Called on the thread that created the Loader when the load is complete.
         *
         * @param loader the loader that completed the load
         * @param data   the result of the load
         */
        fun onLoadComplete(loader: FileLoader<D>, data: D?)

        /**
         * Called on the thread that created the Loader when the load is canceled.
         *
         * @param loader the loader that canceled the load
         */
        fun onLoadCanceled(loader: FileLoader<D>)
    }

    init {
        this.context = context.applicationContext
    }

    /**
     * Sends the result of the load to the registered listener. Should only be called by subclasses.
     *
     *
     * Must be called from the process's main thread.
     *
     * @param data the result of the load
     */
    @MainThread
    open fun deliverResult(data: D?) {
        mListener?.onLoadComplete(this, data)
    }

    /**
     * Informs the registered [androidx.loader.content.Loader.OnLoadCanceledListener] that the load has been canceled.
     * Should only be called by subclasses.
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun deliverCancellation() {
        mListener?.onLoadCanceled(this)
    }

    /**
     * Informs the registered [androidx.loader.content.Loader.OnLoadCanceledListener] that the load has been started.
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun deliverStarted() {
        mListener?.onLoadStart(this)
    }

    /**
     * Registers a class that will receive callbacks when a load is complete.
     * The callback will be called on the process's main thread so it's safe to
     * pass the results to widgets.
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun registerListener(id: Int, listener: OnFileLoaderStateListener<D>) {
        mListener = listener
        this.id = id
    }

    /**
     * Remove a listener that was previously added with [.registerListener].
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun unregisterListener() {
        mListener = null
    }

    /**
     * This function will normally be called for you automatically by
     * [LoaderManager] when the associated fragment/activity
     * is being started.  When using a Loader with [LoaderManager],
     * you *must not* call this method yourself, or you will conflict
     * with its management of the Loader.
     *
     *
     * Starts an asynchronous load of the Loader's data. When the result
     * is ready the callbacks will be called on the process's main thread.
     * If a previous load has been completed and is still valid
     * the result may be passed to the callbacks immediately.
     * The loader will monitor the source of
     * the data set and may deliver future callbacks if the source changes.
     * Calling [.stopLoading] will stop the delivery of callbacks.
     *
     *
     * This updates the Loader's internal state so that
     * [.isStarted] and [.isReset] will return the correct
     * values, and then calls the implementation's [.onStartLoading].
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun startLoading() {
        mStarted = true
        mReset = false
        mAbandoned = false
        onStartLoading()
    }

    /**
     * Subclasses must implement this to take care of loading their data,
     * as per [.startLoading].  This is not called by clients directly,
     * but as a result of a call to [.startLoading].
     * This will always be called from the process's main thread.
     */
    @MainThread
    protected open fun onStartLoading() {
    }

    /**
     * Attempt to cancel the current load task.
     * Must be called on the main thread of the process.
     *
     *
     * Cancellation is not an immediate operation, since the load is performed
     * in a background thread.  If there is currently a load in progress, this
     * method requests that the load be canceled, and notes this is the case;
     * once the background thread has completed its work its remaining state
     * will be cleared.  If another load request comes in during this time,
     * it will be held until the canceled load is complete.
     *
     * @return Returns <tt>false</tt> if the task could not be canceled,
     * typically because it has already completed normally, or
     * because [.startLoading] hasn't been called; returns
     * <tt>true</tt> otherwise.  When <tt>true</tt> is returned, the task
     * is still running and the [androidx.loader.content.Loader.OnLoadCanceledListener] will be called
     * when the task completes.
     */
    @MainThread
    fun cancelLoad(): Boolean {
        return onCancelLoad()
    }

    /**
     * Subclasses must implement this to take care of requests to [.cancelLoad].
     * This will always be called from the process's main thread.
     *
     * @return Returns <tt>false</tt> if the task could not be canceled,
     * typically because it has already completed normally, or
     * because [.startLoading] hasn't been called; returns
     * <tt>true</tt> otherwise.  When <tt>true</tt> is returned, the task
     * is still running and the [androidx.loader.content.Loader.OnLoadCanceledListener] will be called
     * when the task completes.
     */
    @MainThread
    protected open fun onCancelLoad(): Boolean {
        return false
    }

    /**
     * Force an asynchronous load. Unlike [.startLoading] this will ignore a previously
     * loaded data set and load a new one.  This simply calls through to the
     * implementation's [.onForceLoad].  You generally should only call this
     * when the loader is started -- that is, [.isStarted] returns true.
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    open fun forceLoad() {
        onForceLoad()
    }

    /**
     * Subclasses must implement this to take care of requests to [.forceLoad].
     * This will always be called from the process's main thread.
     */
    @MainThread
    protected open fun onForceLoad() {
    }

    /**
     * This function will normally be called for you automatically by
     * [LoaderManager] when the associated fragment/activity
     * is being stopped.  When using a Loader with [LoaderManager],
     * you *must not* call this method yourself, or you will conflict
     * with its management of the Loader.
     *
     *
     * Stops delivery of updates until the next time [.startLoading] is called.
     * Implementations should *not* invalidate their data at this point --
     * clients are still free to use the last data the loader reported.  They will,
     * however, typically stop reporting new data if the data changes; they can
     * still monitor for changes, but must not report them to the client until and
     * if [.startLoading] is later called.
     *
     *
     * This updates the Loader's internal state so that
     * [.isStarted] will return the correct
     * value, and then calls the implementation's [.onStopLoading].
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun stopLoading() {
        mStarted = false
        onStopLoading()
    }

    /**
     * Subclasses must implement this to take care of stopping their loader,
     * as per [.stopLoading].  This is not called by clients directly,
     * but as a result of a call to [.stopLoading].
     * This will always be called from the process's main thread.
     */
    @MainThread
    protected open fun onStopLoading() {
    }

    /**
     * This function will normally be called for you automatically by
     * [LoaderManager] when restarting a Loader.  When using
     * a Loader with [LoaderManager],
     * you *must not* call this method yourself, or you will conflict
     * with its management of the Loader.
     *
     *
     * Tell the Loader that it is being abandoned.  This is called prior
     * to [.reset] to have it retain its current data but not report
     * any new data.
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun abandon() {
        mAbandoned = true
        onAbandon()
    }

    @MainThread
    open fun onDestroy() {

    }

    /**
     * Subclasses implement this to take care of being abandoned.  This is
     * an optional intermediate state prior to [.onReset] -- it means that
     * the client is no longer interested in any new data from the loader,
     * so the loader must not report any further updates.  However, the
     * loader *must* keep its last reported data valid until the final
     * [.onReset] happens.  You can retrieve the current abandoned
     * state with [.isAbandoned].
     * This will always be called from the process's main thread.
     */
    @MainThread
    protected fun onAbandon() {
    }

    /**
     * This function will normally be called for you automatically by
     * [LoaderManager] when destroying a Loader.  When using
     * a Loader with [LoaderManager],
     * you *must not* call this method yourself, or you will conflict
     * with its management of the Loader.
     *
     *
     * Resets the state of the Loader.  The Loader should at this point free
     * all of its resources, since it may never be called again; however, its
     * [.startLoading] may later be called at which point it must be
     * able to start running again.
     *
     *
     * This updates the Loader's internal state so that
     * [.isStarted] and [.isReset] will return the correct
     * values, and then calls the implementation's [.onReset].
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun reset() {
        onReset()
        mReset = true
        mStarted = false
        mAbandoned = false
        mContentChanged = false
        mProcessingChange = false
    }

    /**
     * Subclasses must implement this to take care of resetting their loader,
     * as per [.reset].  This is not called by clients directly,
     * but as a result of a call to [.reset].
     * This will always be called from the process's main thread.
     */
    @MainThread
    protected open fun onReset() {
    }

    /**
     * Take the current flag indicating whether the loader's content had
     * changed while it was stopped.  If it had, true is returned and the
     * flag is cleared.
     */
    fun takeContentChanged(): Boolean {
        val res = mContentChanged
        mContentChanged = false
        mProcessingChange = mProcessingChange or res
        return res
    }

    /**
     * Commit that you have actually fully processed a content change that
     * was returned by [.takeContentChanged].  This is for use with
     * [.rollbackContentChanged] to handle situations where a load
     * is cancelled.  Call this when you have completely processed a load
     * without it being cancelled.
     */
    fun commitContentChanged() {
        mProcessingChange = false
    }

    /**
     * Report that you have abandoned the processing of a content change that
     * was returned by [.takeContentChanged] and would like to rollback
     * to the state where there is again a pending content change.  This is
     * to handle the case where a data load due to a content change has been
     * canceled before its data was delivered back to the loader.
     */
    fun rollbackContentChanged() {
        if (mProcessingChange) {
            onContentChanged()
        }
    }

    /**
     * Called when [androidx.loader.content.Loader.ForceLoadContentObserver] detects a change.  The
     * default implementation checks to see if the loader is currently started;
     * if so, it simply calls [.forceLoad]; otherwise, it sets a flag
     * so that [.takeContentChanged] returns true.
     *
     *
     * Must be called from the process's main thread.
     */
    @MainThread
    fun onContentChanged() {
        if (mStarted) {
            forceLoad()
        } else {
            // This loader has been stopped, so we don't want to load
            // new data right now...  but keep track of it changing to
            // refresh later if we start again.
            mContentChanged = true
        }
    }

    /**
     * For debugging, converts an instance of the Loader's data class to
     * a string that can be printed.  Must handle a null data.
     */
//    fun dataToString(data: D?): String {
//        val sb = StringBuilder(64)
//        DebugUtils.buildShortClassTag(data, sb)
//        sb.append("}")
//        return sb.toString()
//    }
//
//    override fun toString(): String {
//        val sb = StringBuilder(64)
//        DebugUtils.buildShortClassTag(this, sb)
//        sb.append(" id=")
//        sb.append(id)
//        sb.append("}")
//        return sb.toString()
//    }
}