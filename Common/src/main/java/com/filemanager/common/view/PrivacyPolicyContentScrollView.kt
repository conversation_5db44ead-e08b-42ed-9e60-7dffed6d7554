/***********************************************************
 * Copyright (C), 2008-2017 Oplus. All rights reserved.
 * VENDOR_EDIT
 * File: PrivacyPolicyContentScrollView.java
 * Description: Custom scroll widget for Privacy Policy Alert.
 * Version: V 1.0
 * Date : 2018-09-20
 * Author: <PERSON>
 *
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.view

import android.content.Context
import android.content.res.TypedArray
import android.util.AttributeSet
import android.widget.ScrollView
import com.filemanager.common.R

class PrivacyPolicyContentScrollView : ScrollView {
    private var mHeight = 0

    constructor(context: Context?) : super(context) {}

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0) {}

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        val array: TypedArray = context.obtainStyledAttributes(attrs, R.styleable.PrivacyPolicyContentScrollView)
        mHeight = array.getDimensionPixelOffset(R.styleable.PrivacyPolicyContentScrollView_max_height, 0)
        array.recycle()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        var heightMeasureSpec = heightMeasureSpec
        val heightMode: Int = MeasureSpec.getMode(heightMeasureSpec)
        var heightSize: Int = MeasureSpec.getSize(heightMeasureSpec)
        heightSize = Math.min(heightSize, mHeight)
        heightMeasureSpec = MeasureSpec.makeMeasureSpec(heightSize, heightMode)
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
    }
}