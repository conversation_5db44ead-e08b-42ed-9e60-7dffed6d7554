/***********************************************************
 ** Copyright (C), 2025-2030 Oplus. All rights reserved..
 ** VENDOR_EDIT
 ** File: TalkbackUtils.kt
 ** Description: TalkbackUtils
 ** Version: 1.0
 ** Date: 2025/6/17
 ** Author: zhangyitong
 **
 ** ---------------------Revision History: ---------------------
 **  <author>    <data>    <version >    <desc>
 ****************************************************************/
package com.filemanager.common.talkback

import android.view.View
import android.view.accessibility.AccessibilityNodeInfo

object TalkbackUtils {

    const val TALKBACK_PAUSE = "\n\n"

    @JvmStatic
    fun setAccessibilityDelegate(view: View, classType: String) {
        view.accessibilityDelegate = object : View.AccessibilityDelegate() {
            override fun onInitializeAccessibilityNodeInfo(
                host: View,
                info: AccessibilityNodeInfo
            ) {
                super.onInitializeAccessibilityNodeInfo(host, info)
                info.className = classType
            }
        }
    }
}