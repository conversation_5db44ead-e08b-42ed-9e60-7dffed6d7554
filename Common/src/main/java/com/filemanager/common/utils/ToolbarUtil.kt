/*********************************************************************
 * * Copyright (C), 2010-2022 Oplus. All rights reserved..
 * * VENDOR_EDIT
 * * File        : ToolbarUtil
 * * Description :
 * * Version     : 1.0
 * * Date        : 2024/1/12
 * * Author      : <EMAIL>
 * *
 * * ---------------------Revision History: ----------------------------
 * *  <author>                  <data>     <version>  <desc>
 *   zhengshuai                2024/1/12       1      create
 ***********************************************************************/
package com.filemanager.common.utils

import android.content.Context
import android.view.ViewGroup
import android.widget.ImageButton
import com.coui.appcompat.toolbar.COUIActionMenuItemView
import com.coui.appcompat.toolbar.COUIToolbar
import com.filemanager.common.MyApplication
import com.filemanager.common.R
import com.filemanager.common.dragselection.DropTag

object ToolbarUtil {

    @JvmStatic
    fun updateToolbarTitle(toolbar: COUIToolbar, checkedCount: Int, isSelectAll: Boolean) {
        val tempTitle = if (checkedCount > 0) {
            MyApplication.sAppContext.resources.getQuantityString(R.plurals.mark_selected_items_new, checkedCount, checkedCount)
        } else {
            MyApplication.sAppContext.resources.getString(R.string.mark_selected_no_items)
        }
        toolbar.title = tempTitle

        val title = if (isSelectAll) {
            MyApplication.sAppContext.resources.getString(R.string.unselect_all)
        } else {
            MyApplication.sAppContext.resources.getString(R.string.file_list_editor_select_all)
        }
        toolbar.menu?.findItem(R.id.action_select_all)?.title = title
    }

    @JvmStatic
    fun updateToolbarSelectAllEnable(toolbar: COUIToolbar, enable: Boolean) {
        toolbar.menu?.findItem(R.id.action_select_all)?.setEnabled(enable)
    }

    @JvmStatic
    fun setToolbarChildViewTag(view: ViewGroup?, context: Context) {
        view?.let {
            for (i in 0 until view.childCount) {
                val childAt = view.getChildAt(i)
                if (childAt is ViewGroup) {
                    setToolbarChildViewTag(childAt, context)
                }
                if (childAt is COUIActionMenuItemView
                    && childAt.text == context.resources?.getString(R.string.button_cancel_text)) {
                    childAt.tag = DropTag(-1, DropTag.Type.TOOLBAR_MENU)
                    return
                }
                if (childAt is ImageButton) {
                    childAt.tag = DropTag(-1, DropTag.Type.TOOLBAR_MENU_BACK)
                    return
                }
            }
        }
    }
}